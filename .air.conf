# Config file for [Air](https://github.com/cosmtrek/air) in TOML format.

# Working directory
# . or absolute path, please note that the directories following must be under root.
root = "." 
tmp_dir = ".air"

[build]
# Just plain old shell command. You could use `make` as well.
cmd = "make build"
# Binary file yields from `cmd`.
bin = "release/tg_app_guid"
# Customize binary.
full_bin = "./release/tg_app_guid -c tg.cfg -r 1"
# Watch these filename extensions.
include_ext = ["go"]
# Ignore these filename extensions or directories.
exclude_dir = [".air", "api", "release", "scripts"]
# Watch these directories if you specified.
include_dir = []
# Exclude files.
exclude_file = []
# This log file places in your tmp_dir.
log = "air.log"
# It's not necessary to trigger build each time file changes if it's too frequent.
delay = 2000 # ms
# Stop running old binary when build errors occur.
stop_on_error = true
# Send Interrupt signal before killing process (windows does not support this feature).
send_interrupt = false
# Delay after sending Interrupt signal.
kill_delay = 500 # ms

[log]
# Show log time.
time = true

[color]
# Customize each part's color. If no color found, use the raw app log.
main = "magenta"
build = "yellow"
runner = "green"
watcher = "cyan"

[misc]
# Delete tmp directory on exit.
clean_on_exit = true
