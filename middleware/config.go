package middleware

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/lib/config"
)

const ctxKeyConfig = "Config"

// ConfigSetter returns a middleware that sets the Config for the current context.
func ConfigSetter(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(ctxKeyConfig, cfg)
	}
}

func GetConfig(c *gin.Context) *config.Config {
	value, _ := c.Get(ctxKeyConfig)
	cfg, _ := value.(*config.Config)
	return cfg
}
