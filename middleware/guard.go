package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/dao"
)

// ImportGuard returns a middleware that guards the routes when importing is in progress.
func ImportGuard(c *gin.Context) {
	daoManager := dao.GetManager(c)
	if isImporting, _ := daoManager.IsImporting(); isImporting {
		Abort(c, http.StatusServiceUnavailable, "Importing is in progress. Please try again later.")
		return
	}
}

// GBARGuard returns a middleware that guards the routes when GBAR is not enabled
// or backup or restore is in progress.
func GBARGuard(c *gin.Context) {
	path := c.Request.URL.Path
	if !strings.HasPrefix(path, "/api/gbar") {
		return
	}

	if cfg := GetConfig(c); !cfg.GetBackupLocalEnabled() && !cfg.GetBackupS3Enabled() {
		Abort(c, http.StatusServiceUnavailable, "GBAR is not enabled.")
		return
	}

	if path != "/api/gbar/backup" && path != "/api/gbar/restore" {
		return
	}

	daoManager := dao.GetManager(c)
	if isGBARInProgress, _ := daoManager.IsGBARInProgress(); isGBARInProgress {
		Abort(
			c,
			http.StatusServiceUnavailable,
			"There is currently a GBAR backup or restore already in progress.",
		)
		return
	}
}
