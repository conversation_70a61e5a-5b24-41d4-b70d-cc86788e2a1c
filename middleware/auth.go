package middleware

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	gojwt "github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/codec"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
	"github.com/tigergraph/gus/lib/jwt"
	"github.com/tigergraph/gus/service/db"
	database "github.com/tigergraph/gus/service/db"
)

const (
	CookieName         = "TigerGraphApp"
	ctxKeyIsSuperUser  = "IsSuperUser"
	ctxKeyPrivileges   = "Privileges"
	ctxKeyUsername     = "Username"
	ctxKeyRealUsername = "RealUsername" // for saml logged in user, username is __GSQL__saml
	ctxKeyPassword     = "Password"
	ctxKeySessionID    = "SessionID"
	ctxKeyUserInfo     = "UserInfo"
	ctxKeyAuthType     = "AuthType"
)

var sessionInvalidError = errors.New("Session is invalid.")
var sessionExpError = errors.New("Session is expired.")
var sessionRevokedError = errors.New("Session is revoked.")
var gsqlAuthError = errors.New("GSQL authentication failed.")
var auth0AuthError = errors.New("Auth0 authentication failed")

func SessionInvalidError() error {
	return sessionInvalidError
}
func SessionExpError() error {
	return sessionExpError
}
func SessionRevokedError() error {
	return sessionRevokedError
}
func GSQLAuthError() error {
	return gsqlAuthError
}

func Auth0AuthError() error {
	return auth0AuthError
}

func pathPrefixWith(path string, basicTokenAuthPaths []string) string {
	for _, p := range basicTokenAuthPaths {
		if strings.HasPrefix(path, p) {
			return p
		}
	}

	return ""
}

type CookieAuthMiddleware struct {
	gsqlAuthenticator     interfaces.GSQLAuthenticator
	authenticationService interfaces.AuthenticationService
}

func NewCookieAuthMiddleware(gsqlAuthenticator interfaces.GSQLAuthenticator, authenticationService interfaces.AuthenticationService) interfaces.AuthMiddleware {
	return &CookieAuthMiddleware{gsqlAuthenticator: gsqlAuthenticator, authenticationService: authenticationService}
}

func (m *CookieAuthMiddleware) Allow(c *gin.Context) bool {
	if sessionID, _ := c.Cookie(CookieName); sessionID != "" {
		return true
	}
	return false
}

func (m *CookieAuthMiddleware) GetSessionID(c *gin.Context) string {
	sessionID, _ := c.Cookie(CookieName)
	parts := strings.Split(sessionID, "-")
	if len(parts) < 2 {
		return ""
	}
	return fmt.Sprintf("Cookie %s", strings.Join(parts[:len(parts)-1], "-"))
}

func (m *CookieAuthMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	sessionID, _ := c.Cookie(CookieName)
	claims, err := m.authenticationService.Authenticate(c, sessionID)
	if err != nil {
		log.Warn(c, "Failed to authenticate session ID: %v", err)
		// Abort(c, http.StatusUnauthorized, "You are not authorized to use this API.")
		return nil, err
	}

	cfg := GetConfig(c)

	username := claims.Username
	password, err := codec.AESCBCDecrypt(cfg.GetAuthToken(), claims.Password)
	if err != nil {
		err2 := fmt.Errorf("failed to decrypt password: %v, reqID %s", err, GetReqID(c))
		log.Error(c, err2)
		// c.AbortWithError(http.StatusBadRequest, err2)
		return nil, err2
	}

	if claims.Username == cfg.GetSSOBuiltinUser() {
		daoManager := dao.GetManager(c)
		samlResp, err := daoManager.GetSAMLResponse(password)
		if err != nil {
			log.Errorf(c, "Failed to get SAML response with hash %s: %v", password, err)
			return nil, err
		}
		password = samlResp
	}

	creds := &model.UserCredentials{Username: username, Password: password}
	userInfo, err := m.gsqlAuthenticator(c, cfg, creds, false)
	if err != nil {
		log.Warn(c, "GSQL authentication failed: ", err)
		// Abort(c, http.StatusUnauthorized, err.Error())
		return nil, GSQLAuthError()
	}
	return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds, AuthType: model.CookieAuthType}, nil

}

type BasicAuthMiddleware struct {
	authPaths         []string
	gsqlAuthenticator interfaces.GSQLAuthenticator
}

func NewBasicAuthMiddleware(authPaths []string, gsqlAuthenticator interfaces.GSQLAuthenticator) interfaces.AuthMiddleware {
	return &BasicAuthMiddleware{authPaths: authPaths, gsqlAuthenticator: gsqlAuthenticator}
}

func (m *BasicAuthMiddleware) Allow(c *gin.Context) bool {
	pathPrefix := pathPrefixWith(c.Request.URL.Path, m.authPaths)
	authorization := c.Request.Header.Get("Authorization")
	return strings.HasPrefix(authorization, "Basic") && len(pathPrefix) != 0
}

func (m *BasicAuthMiddleware) GetSessionID(c *gin.Context) string {
	if username, _, ok := c.Request.BasicAuth(); ok {
		return fmt.Sprintf("Basic %s", username)
	}
	return ""
}

func (m *BasicAuthMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	if username, password, ok := c.Request.BasicAuth(); ok {
		creds := &model.UserCredentials{Username: username, Password: password}
		userInfo, err := m.gsqlAuthenticator(c, GetConfig(c), creds, false)
		if err != nil {
			log.Warn(c, "authentication failed with Basic token: ", err)
			// Abort(c, http.StatusBadRequest, "your basic token is invalid")
			return nil, err
		} else {
			return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds, AuthType: model.BasicAuthType}, nil
		}

	} else {
		log.Warn(c, "don't have basic token for authentication")
		// Abort(c, http.StatusBadRequest, "your basic token is invalid")
		return nil, errors.New("don't have basic token for authentication")
	}

}

type APITokenAuthMiddleware struct {
	apiTokenService   interfaces.APITokenService
	gsqlAuthenticator interfaces.GSQLAuthenticator
}

func NewAPITokenAuthMiddleware(apiTokenService interfaces.APITokenService, gsqlAuthenticator interfaces.GSQLAuthenticator) interfaces.AuthMiddleware {
	return &APITokenAuthMiddleware{apiTokenService: apiTokenService, gsqlAuthenticator: gsqlAuthenticator}
}

func (m *APITokenAuthMiddleware) Allow(c *gin.Context) bool {
	authorization := c.Request.Header.Get("Authorization")
	return strings.HasPrefix(authorization, "Token")
}

func (m *APITokenAuthMiddleware) GetSessionID(c *gin.Context) string {
	// trim the latest 12 characters as token is sensitive information
	token := c.Request.Header.Get("Authorization")
	parts := strings.Split(token, "-")
	if len(parts) < 2 {
		return ""
	}

	return strings.Join(parts[:len(parts)-1], "-")
}

func (m *APITokenAuthMiddleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	token := c.Request.Header.Get("Authorization")
	token = strings.TrimPrefix(token, "Token ")
	creds, err := m.apiTokenService.Parse(c, token)
	if err != nil {
		log.Warn(c, "failed to parse API token: ", err)
		// Abort(c, http.StatusBadRequest, "API token is invalid: "+err.Error())
		return nil, err
	}
	userInfo, err := m.gsqlAuthenticator(c, GetConfig(c), creds, false)
	if err != nil {
		log.Warn(c, "authentication failed with API token: ", err)
		// Abort(c, http.StatusBadRequest, "API token is invalid: "+err.Error())
		return nil, err
	}
	return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds, AuthType: model.TokenAuthType}, nil
}

func authorize(c *gin.Context, u *model.UserInfoWithCredentials) {
	c.Set(ctxKeyIsSuperUser, u.IsSuperUser)
	c.Set(ctxKeyPrivileges, u.Privileges)
	c.Set(ctxKeyUsername, u.Username)
	c.Set(ctxKeyRealUsername, u.UserInfo.Name)
	c.Set(ctxKeyPassword, u.Password)
	c.Set(ctxKeyUserInfo, &u.UserInfo)
	c.Set(ctxKeyAuthType, u.AuthType)
}

// Authentication returns a middleware that authenticates the user.
func Authentication(
	skipPaths []string,
	authMiddlewares []interfaces.AuthMiddleware,
) gin.HandlerFunc {
	skip := make(map[string]struct{})

	for _, path := range skipPaths {
		skip[path] = struct{}{}
	}

	return func(c *gin.Context) {
		// Authenticate only when path is not being skipped.
		_, shouldSkip := skip[c.Request.URL.Path]

		for _, middleware := range authMiddlewares {
			if !middleware.Allow(c) {
				continue
			}

			c.Set(ctxKeySessionID, middleware.GetSessionID(c))
			u, err := middleware.AuthUser(c)
			if err != nil {
				if shouldSkip { // ignore authentication error when path is being skipped
					return
				}

				if err == SessionRevokedError() {
					Abort(c, http.StatusUnauthorized, err.Error())
				} else if err == SessionExpError() || err == SessionInvalidError() {
					Abort(c, http.StatusUnauthorized, "You are not authorized to use this API.")
				} else if err == GSQLAuthError() {
					Abort(c, http.StatusUnauthorized, err.Error())
				} else if err == Auth0AuthError() {
					Abort(c, http.StatusUnauthorized, err.Error())
				} else {
					Abort(c, http.StatusInternalServerError, err.Error())
				}
				return

			}
			authorize(c, u)
			return
		}

		if !shouldSkip {
			Abort(c, http.StatusUnauthorized, "You are not authorized to use this API.")
		}
	}

}

// https://graphsql.atlassian.net/browse/APPS-2581 Add userLogin for login request to create audit log in gsql.
func AuthenticateWithGSQL(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		fmt.Sprintf("/gsqlserver/gsql/simpleauth?userLogin=%v", userLogin),
	)

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, requestURL, nil)
	req.SetBasicAuth(creds.Username, creds.Password)
	h.SetFromGraphStudio(req)
	h.SetForwardedForFromContext(c, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
	})

	resp, err := client.Do(req)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get response from GSQL server.")
	}
	if resp.StatusCode == http.StatusUnauthorized {
		return nil, errors.New("GSQL authentication failed.")
	}

	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to read response from GSQL server.")
	}

	info := &model.UserInfo{}
	if err := json.Unmarshal(body, info); err != nil {
		log.Warn(c, string(body), resp.Status)
		return nil, errors.New("Failed to unmarshal response from GSQL server.")
	}

	return info, nil
}

func LoginWithGSQL(c context.Context, cfg *config.Config, creds *model.UserCredentials) (*model.GSQLLoginResponse, error) {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		"/gsqlserver/gsql/login",
	)

	credStr := fmt.Sprintf("%s:%s", creds.Username, creds.Password)
	credBase64 := base64.StdEncoding.EncodeToString([]byte(credStr))
	req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, requestURL, strings.NewReader(credBase64))
	h.SetFromGraphStudio(req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
	})

	resp, err := client.Do(req)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get response from GSQL server.")
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to read response from GSQL server.")
	}

	gsqlLoginResponse := &model.GSQLLoginResponse{}
	if err := json.Unmarshal(body, gsqlLoginResponse); err != nil {
		log.Warn(c, string(body), resp.Status, resp.StatusCode)
		return nil, errors.New("Failed to unmarshal response from GSQL server.")
	}

	return gsqlLoginResponse, nil
}

// RequiredAuthToken returns a middleware that requires AuthToken permission.
func RequiredAuthToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		reqToken := c.Request.Header.Get("Authorization")
		authToken := strings.TrimPrefix(reqToken, "Bearer ")

		cfg := GetConfig(c)
		if authToken != cfg.GetAuthToken() {
			c.AbortWithStatusJSON(http.StatusForbidden, model.Response{Message: "You are not authorized to use this internal API. Please use auth token."})

			return
		}
	}

}

// RequiredSuperUser returns a middleware that requires SuperUser permission.
func RequiredSuperUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		isSuperUser := c.GetBool(ctxKeyIsSuperUser)
		if !isSuperUser {
			Abort(c, http.StatusForbidden, "SuperUser permission required.")
			return
		}
	}
}

func HasPrivilege(c *gin.Context, graphName string, privilege model.Privilege) bool {
	privileges := c.MustGet(ctxKeyPrivileges).(model.Privileges)

	// if user has global graph privilege, he will have all graph privileges
	for _, p := range privileges["1"].Privileges {
		if p == privilege {
			return true
		}
	}

	for _, p := range privileges[graphName].Privileges {
		if p == privilege {
			return true
		}
	}
	return false
}

// RequiredPrivileges returns a middleware that whether the user has the required privileges.
func RequiredPrivileges(requiredPrivileges ...model.Privilege) gin.HandlerFunc {
	return func(c *gin.Context) {
		graphName := c.Param("graphName")
		displayGraphName := graphName
		// if graphName is not specified, set to  "1" , which stands for global graph name.
		if graphName == "" {
			graphName = "1"
			displayGraphName = "Global"
		}

		// check if graph exist
		privileges := c.MustGet(ctxKeyPrivileges).(model.Privileges)
		if _, ok := privileges[graphName]; !ok {
			c.AbortWithStatusJSON(http.StatusNotFound, model.Response{Message: fmt.Sprintf("Graph %s does not exist or you don't have privileges on it.", displayGraphName)})
			return
		}

		for _, rp := range requiredPrivileges {
			if !HasPrivilege(c, graphName, rp) {
				c.AbortWithStatusJSON(http.StatusForbidden, model.Response{Message: fmt.Sprintf("%s privilege is required on graph %s.", rp, displayGraphName)})
				return
			}
		}
	}

}

func IsSuperUser(c *gin.Context) bool {
	return c.GetBool(ctxKeyIsSuperUser)
}

func GetSessionID(c *gin.Context) string {
	t, ok := c.Get(ctxKeySessionID)
	if !ok {
		return ""
	}

	return cast.ToString(t)
}

func GetUsername(c *gin.Context) string {
	t, ok := c.Get(ctxKeyUsername)
	if !ok {
		return ""
	}

	return cast.ToString(t)
}

func GetRealUsername(c *gin.Context) string {
	t, ok := c.Get(ctxKeyRealUsername)
	if !ok {
		return ""
	}

	return cast.ToString(t)
}

func GetPassword(c *gin.Context) string {
	t, ok := c.Get(ctxKeyPassword)
	if !ok {
		return ""
	}

	return cast.ToString(t)
}

func GetUserInfo(c *gin.Context) *model.UserInfo {
	t, ok := c.Get(ctxKeyUserInfo)
	if !ok {
		return nil
	}
	return t.(*model.UserInfo)
}

func GetAuthType(c *gin.Context) model.AuthType {
	t, ok := c.Get(ctxKeyAuthType)
	if !ok {
		return ""
	}

	return t.(model.AuthType)
}

type AuthenticationService struct {
	data sync.Map
}

func InMemoryAuthenticationService() *AuthenticationService {
	return &AuthenticationService{}
}

var _ interfaces.AuthenticationService = &AuthenticationService{}

// Register a new user token in the service
func (a *AuthenticationService) Register(c context.Context, claims jwt.Claims) (string, error) {
	session := uuid.New().String()
	log.Info(c, "register", session)
	a.data.Store(session, claims)
	return session, nil
}

func (a *AuthenticationService) Authenticate(c context.Context, sessionToken string) (jwt.Claims, error) {
	if claims, ok := a.data.Load(sessionToken); ok {
		if claims.(jwt.Claims).ExpiresAt < time.Now().Unix() {
			return claims.(jwt.Claims), sessionExpError
		}
		return claims.(jwt.Claims), nil
	}
	return jwt.Claims{}, sessionInvalidError
}

// Revoke a token (delete it)
func (a *AuthenticationService) Revoke(token string) error {
	a.data.Delete(token)
	return nil
}

func (a *AuthenticationService) Purge() error {
	a.data.Range(func(key, value any) bool {
		if value.(jwt.Claims).ExpiresAt < time.Now().Unix() {
			a.data.Delete(key)
		}
		return true
	})
	return nil
}

type KVStoreAuthenticationService struct {
	db *database.Manager
}

func NewKVStoreAuthenticationService(db *database.Manager) *KVStoreAuthenticationService {
	return &KVStoreAuthenticationService{db: db}
}

var _ interfaces.AuthenticationService = &KVStoreAuthenticationService{}

// Register a new user token in the service
func (k *KVStoreAuthenticationService) Register(c context.Context, claims jwt.Claims) (string, error) {
	cfg := GetConfig(c.(*gin.Context))
	session := uuid.New().String()
	value, err := json.Marshal(claims)
	if err != nil {
		return "", err
	}
	if !cfg.GetEnableConcurrentSession() {
		k.revokeConcurrentSessions(c, claims.RealUsername)
	}
	err = k.db.Create(dao.GetDBKey(dao.SessionPrefix, session), value)
	if err != nil {
		return "", err
	}

	return session, nil
}

func (k *KVStoreAuthenticationService) revokeConcurrentSessions(c context.Context, username string) {
	// find all session with the same username, set revoked field to true
	keys, _ := k.db.GetAllKeys(dao.SessionPrefix)
	for _, key := range keys {
		_claims := jwt.Claims{}
		data, err := k.db.Get(key)
		if err != nil {
			log.Warn(c, "failed to get session: ", err)
			continue
		}
		err = json.Unmarshal(data, &_claims)
		if err != nil {
			log.Warn(c, "failed to unmarshal claims: ", err)
			continue
		}
		if _claims.RealUsername == username {
			_claims.Revoked = true
			val, err := json.Marshal(_claims)
			if err != nil {
				log.Warn(c, "failed to marshal claims: ", err)
				continue
			}
			k.db.Upsert(key, val)
		}
	}
}

func (k *KVStoreAuthenticationService) Authenticate(c context.Context, sessionToken string) (jwt.Claims, error) {
	claims := jwt.Claims{}
	data, err := k.db.Get(dao.GetDBKey(dao.SessionPrefix, sessionToken))
	if err == db.ErrNotFound {
		return claims, sessionInvalidError
	}
	if err != nil {
		return claims, err
	}

	err = json.Unmarshal(data, &claims)
	if err != nil {
		return claims, err
	}

	if claims.ExpiresAt < time.Now().Unix() {
		return claims, sessionExpError
	}
	if claims.Revoked {
		return claims, sessionRevokedError
	}
	return claims, nil

}

// Revoke a token (delete it)
func (k *KVStoreAuthenticationService) Revoke(token string) error {
	return k.db.Delete(dao.GetDBKey(dao.SessionPrefix, token))
}

func (k *KVStoreAuthenticationService) Purge() error {
	keys, err := k.db.GetAllKeys(dao.SessionPrefix)
	if err != nil {
		return err
	}
	for _, key := range keys {
		claims := jwt.Claims{}
		data, err := k.db.Get(key)
		if err != nil {
			return err
		}

		err = json.Unmarshal(data, &claims)
		if err != nil {
			return err
		}
		if claims.ExpiresAt < time.Now().Unix() {
			log.Infof("delete expired session key:%s", key)
			k.db.Delete(key)
		}
	}
	return nil
}

type Auth0Middleware struct {
	gsqlAuthenticator interfaces.GSQLAuthenticator
	cfg               *config.Config
}

func NewAuth0AuthMiddleware(cfg *config.Config, gsqlAuthenticator interfaces.GSQLAuthenticator) interfaces.AuthMiddleware {
	return &Auth0Middleware{
		gsqlAuthenticator: gsqlAuthenticator,
		cfg:               cfg,
	}
}

// Active autho middleware only when OIDC is enabled and request has Bearer token
func (i *Auth0Middleware) Allow(c *gin.Context) bool {
	authorization := c.Request.Header.Get("Authorization")
	return strings.HasPrefix(authorization, "Bearer") && i.cfg.GetOIDCEnable()
}

func (i *Auth0Middleware) GetSessionID(c *gin.Context) string {
	return "auth0"
}

func (i *Auth0Middleware) AuthUser(c *gin.Context) (*model.UserInfoWithCredentials, error) {
	tokenStr := c.Request.Header.Get("Authorization")
	tokenStr = strings.TrimPrefix(tokenStr, "Bearer ")

	_, err := gojwt.Parse(tokenStr, func(token *gojwt.Token) (interface{}, error) {
		iss := i.cfg.GetOIDCIssuer()
		// Verify 'iss' claim
		checkIss := token.Claims.(gojwt.MapClaims).VerifyIssuer(iss, true)
		if !checkIss {
			return token, errors.Errorf("invalid issuer: %s", iss)
		}

		cert, err := getPemCert(token, i.cfg.GetOIDCJWKSUrl())
		if err != nil {
			return token, fmt.Errorf("internal error: failed to get pem cert: %v", err)
		}
		result, _ := gojwt.ParseRSAPublicKeyFromPEM([]byte(cert))
		return result, nil
	})

	// Check if there was an error in parsing...
	if err != nil {
		log.Warnf(c, "Error parsing token: %v", err)
		return nil, Auth0AuthError()
	}

	creds := &model.UserCredentials{
		Username: i.cfg.GetOIDCBuiltinUser(),
		Password: tokenStr,
	}

	userInfo, err := i.gsqlAuthenticator(c, i.cfg, creds, false)
	if err != nil {
		log.Warn(c, "GSQL authentication failed: ", err)
		return nil, GSQLAuthError()
	}
	return &model.UserInfoWithCredentials{UserInfo: *userInfo, UserCredentials: *creds, AuthType: model.Auth0AuthType}, nil
}

type Jwks struct {
	Keys []JSONWebKeys `json:"keys"`
}

type JSONWebKeys struct {
	Kty string   `json:"kty"`
	Kid string   `json:"kid"`
	Use string   `json:"use"`
	N   string   `json:"n"`
	E   string   `json:"e"`
	X5c []string `json:"x5c"`
}

func getPemCert(token *gojwt.Token, jwksURL string) (string, error) {
	cert := ""

	resp, err := http.Get(jwksURL) //nolint:gosec // the url is from config file
	if err != nil {
		return cert, err
	}
	defer resp.Body.Close()

	var jwks = Jwks{}
	err = json.NewDecoder(resp.Body).Decode(&jwks)
	if err != nil {
		return cert, err
	}

	for _, key := range jwks.Keys {
		if token.Header["kid"] == key.Kid {
			cert = fmt.Sprintf("-----BEGIN CERTIFICATE-----\n%s\n-----END CERTIFICATE-----", key.X5c[0])
		}
	}

	if cert == "" {
		err = errors.New("unable to find appropriate key")
	}

	return cert, err
}
