package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/dao/model"
)

// Reply is used when the request is successful.
func Reply(c *gin.Context, code int, errMessage string) {
	ReplyWithResult(c, code, errMessage, nil)
}

// ReplyWithResult is used when the request is successful and results are returned.
func ReplyWithResult(c *gin.Context, statusCode int, errMessage string, results interface{}) {
	isErr := false
	if statusCode >= 400 {
		isErr = true
	}
	resp := &model.Response{
		Error:   isErr,
		Message: errMessage,
		Results: results,
	}
	if IsLongReq(c) {
		if isErr {
			c.SSEvent("error", resp)
		} else {
			c.SSEvent("message", resp)
		}
	} else {
		c.AbortWithStatusJSON(statusCode, resp)
	}
}

// ReplyWithResultAndProfile is used when the request is successful, results and profile are returned.
func ReplyWithResultAndProfile(c *gin.Context, statusCode int, errMessage string, results interface{}, profile interface{}) {
	isErr := false
	if statusCode >= 400 {
		isErr = true
	}
	resp := &model.Response{
		Error:   isErr,
		Message: errMessage,
		Results: results,
		Profile: profile,
	}
	if IsLongReq(c) {
		if isErr {
			c.SSEvent("error", resp)
		} else {
			c.SSEvent("message", resp)
		}
	} else {
		c.AbortWithStatusJSON(statusCode, resp)
	}
}

// Abort is used when there are errors with processing the request.
func Abort(c *gin.Context, code int, message string) {
	resp := &model.Response{
		Error:   true,
		Message: message,
		Results: nil,
	}
	if IsLongReq(c) {
		c.SSEvent("error", resp)
		c.Abort()
	} else {
		c.AbortWithStatusJSON(code, resp)
	}
}
