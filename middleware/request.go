package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/tigergraph/gotools/log"
)

const (
	ctxKeyRequestID = "RequestID"
)

// ReqIDSetter returns a middleware that assigns a unique ID for the current context.
func ReqIDSetter(c *gin.Context) {
	reqID := uuid.New()
	c.Set(ctxKeyRequestID, reqID)
	c.Set(log.LoggerCtxKey, log.WithContext(c).With("reqID", reqID))
	c.<PERSON><PERSON>("Request-ID", reqID.String()) // for client.
}

func GetReqID(c *gin.Context) uuid.UUID {
	value, _ := c.Get(ctxKeyRequestID)
	reqID, _ := value.(uuid.UUID)
	return reqID
}

func IsLongReq(c *gin.Context) bool {
	reqType := c.Get<PERSON>eader("Request-Type")
	return strings.ToLower(reqType) == "long"
}
