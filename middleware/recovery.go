package middleware

import (
	"net"
	"net/http"
	"net/http/httputil"
	"os"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
)

// Recovery returns a middleware that recovers from any panics and writes a 500 if there was one.
func Recovery(c *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			// Check for a broken connection, as it is not really a
			// condition that warrants a panic stack trace.
			var brokenPipe bool
			if ne, ok := err.(*net.OpError); ok {
				if se, ok := ne.Err.(*os.SyscallError); ok {
					if strings.Contains(
						strings.ToLower(se.Error()), "broken pipe") ||
						strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
						brokenPipe = true
					}
				}
			}

			httpReq, _ := httputil.DumpRequest(c.Request, false)
			headers := strings.Split(string(httpReq), "\r\n")
			for i, header := range headers {
				current := strings.Split(header, ":")
				if current[0] == "Authorization" {
					headers[i] = current[0] + ": *"
				}
			}

			switch {
			case brokenPipe:
				log.Warnf(c, "%v\n%s", err, httpReq)
			case log.IsDebugLevel():
				log.Errorf(c, "Panic recovered:\n%s%v", strings.Join(headers, "\r\n"), err)
			default:
				log.Errorf(c, "Panic recovered: %v", err)
			}

			// If the connection is dead, print the error.
			if brokenPipe {
				_ = c.Error(err.(error))
			}
			Abort(
				c,
				http.StatusInternalServerError,
				"An unexpected error has occurred. Please try again later.",
			)
		}
	}()
	c.Next()
}
