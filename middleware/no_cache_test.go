package middleware

import (
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
)

func TestNoCache(t *testing.T) {
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name         string
		args         args
		wantedHeader map[string]string
	}{
		{
			"normal",
			args{
				gin.CreateTestContextOnly(httptest.NewRecorder(), gin.New()),
			},
			map[string]string{
				"Cache-Control":      "no-cache, no-store, max-age=0",
				"Pragma":             "no-cache",
				"Expires":            "-1",
				"X-Download-Options": "noopen",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			NoCache(tt.args.c)
			for k, v := range tt.wantedHeader {
				require.Equal(t, tt.args.c.Writer.Header().Get(k), v)
			}
		})
	}
}
