package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

func SetCookie(c *gin.Context, token string, sameSite int32) {
	cfg := GetConfig(c)
	c.SetSameSite(http.SameSite(sameSite))
	c.<PERSON><PERSON><PERSON>ie(
		CookieName,
		token,
		int(cfg.GetCookieDuration()),
		"/",
		"",
		cfg.GetNginxSSLEnabled(),
		true,
	)
}

func DeleteCookie(c *gin.Context) {
	cfg := GetConfig(c)
	c.<PERSON><PERSON>ookie(
		<PERSON><PERSON>Name,
		"",
		-1,
		"/",
		"",
		cfg.GetNginxSSLEnabled(),
		true,
	)
}
