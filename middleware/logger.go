package middleware

import (
	"bytes"
	"fmt"
	"io"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
)

const maxLogBodySize = 1024

type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w bodyLogWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

// LoggerWithConfig returns a Logger middleware with extra configs.
func LoggerWithConfig(skipPaths []string, skipReqPath []string) gin.HandlerFunc {
	var skip map[string]struct{}

	if length := len(skipPaths); length > 0 {
		skip = make(map[string]struct{}, length)

		for _, path := range skipPaths {
			skip[path] = struct{}{}
		}
	}

	skipReq := make(map[string]struct{})
	for i := range skipReqPath {
		skipReq[skipReqPath[i]] = struct{}{}
	}

	return func(c *gin.Context) {
		start := time.Now() // start timer.
		path := c.Request.URL.Path

		var reqBody string
		if _, ok := skipReq[path]; !ok {
			byteBody, _ := io.ReadAll(c.Copy().Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(byteBody))
			reqBody = string(byteBody)
		}

		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw
		// Process request.
		c.Next()

		if _, ok := skip[path]; !ok {
			latency := time.Since(start) // stop timer.
			if latency > time.Minute {
				latency -= latency % time.Second
			}

			logResponse(c, latency, path, blw, reqBody)
		}
	}
}

func logResponse(c *gin.Context, latency time.Duration, fullPath string, blw *bodyLogWriter, reqBody string) {
	clientIP := c.ClientIP()
	username := GetRealUsername(c)
	method := c.Request.Method
	statusCode := c.Writer.Status()
	fields := []interface{}{
		"logRequest",
		"username", username,
		"client_ip", clientIP,
		"method", method,
		"path", fullPath,
		"session_id", GetSessionID(c),
		"status_code", statusCode,
		"latency", fmt.Sprintf("%dms", latency.Microseconds()/1e3),
	}
	if c.Errors.Last() != nil {
		errMsg := c.Errors.ByType(gin.ErrorTypePrivate).String()
		fields = append(fields, "error", errMsg)
	}
	if log.IsDebugLevel() || (statusCode != 200 && statusCode != 404) || fullPath == "/api/v2" {

		// Omit large request body.
		if len(reqBody) > maxLogBodySize {
			reqBody = reqBody[:maxLogBodySize] + "...	"
		}

		raw := c.Request.URL.RawQuery
		fields = append(fields, "requestBody", reqBody, "rawQuery", raw)

		body := blw.body.String()
		bodySize := blw.body.Len()
		// Omit large response body.
		if bodySize > maxLogBodySize {
			body = body[:maxLogBodySize] + "..."
		}
		fields = append(fields, "responseBody", body)
	}

	log.Infow(c, fields...)
}
