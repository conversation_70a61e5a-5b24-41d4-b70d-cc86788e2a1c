package middleware

import "github.com/gin-gonic/gin"

var noCacheHeader map[string]string = map[string]string{
	"Cache-Control":      "no-cache, no-store, max-age=0",
	"Pragma":             "no-cache",
	"Expires":            "-1",
	"X-Download-Options": "noopen",
}

// NoCache returns a middleware that set http cache header to disable cache.
func NoCache(c *gin.Context) {
	for k, v := range noCacheHeader {
		c.Header(k, v)
	}
}
