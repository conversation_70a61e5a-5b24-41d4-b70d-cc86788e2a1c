package dao

import (
	"encoding/json"

	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const ERPrefix = "exploration-result"

func (m *Manager) GetExplorationResult(graphName, eName string) (*model.ExplorationResult, error) {
	result, err := m.db.Get(GetDBKey(ERPrefix, graphName, eName))
	if err != nil {
		return nil, err
	}

	exploration := &model.ExplorationResult{}
	if err = json.Unmarshal(result, exploration); err != nil {
		return nil, err
	}

	return exploration, nil
}

func (m *Manager) GetAllExplorationResults(graphName string) ([]model.ExplorationResultPreview, error) {
	results, err := m.db.GetAll(GetDBKeyForGraphResource(ERPrefix, graphName))
	if err != nil {
		return nil, err
	}

	explorations := make([]model.ExplorationResultPreview, len(results))
	for i, result := range results {
		var exploration model.ExplorationResult
		if err = json.Unmarshal(result, &exploration); err != nil {
			return nil, err
		}
		explorations[i] = model.ExplorationResultPreview{
			Name:         exploration.Name,
			Username:     exploration.Username,
			Timestamp:    exploration.Timestamp,
			PreviewImage: exploration.PreviewImage,
			Description:  exploration.Description,
		}
	}

	return explorations, nil
}

func (m *Manager) CreateExplorationResult(graphName, eName string, exploration model.ExplorationResult) error {
	data, err := tgJSON.Marshal(&exploration)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(ERPrefix, graphName, eName), data)
}

func (m *Manager) UpsertExplorationResult(graphName, eName string, exploration model.ExplorationResult) error {
	data, err := tgJSON.Marshal(&exploration)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(ERPrefix, graphName, eName), data)
}

func (m *Manager) DeleteExplorationResult(graphName, eName string) error {
	return m.db.Delete(GetDBKey(ERPrefix, graphName, eName))
}

func (m *Manager) DeleteAllExplorationResults(graphName string) error {
	return m.db.DeleteAll(GetDBKey(ERPrefix, graphName))
}
