package dao

import (
	"encoding/json"

	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const JobInfoPrefix = "loading-job-info"

func (m *Manager) GetLoadingJobInfo(graphName string) ([]model.LoadingJobInfo, error) {
	result, err := m.db.Get(GetDBKey(JobInfoPrefix, graphName))
	if err != nil {
		return nil, err
	}

	var jobInfo []model.LoadingJobInfo
	if err = json.Unmarshal(result, &jobInfo); err != nil {
		return nil, err
	}

	return jobInfo, nil
}

func (m *Manager) CreateLoadingJobInfo(graphName string, info []interface{}) error {
	data, err := tgJSON.Marshal(&info)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(JobInfoPrefix, graphName), data)
}

func (m *Manager) UpsertLoadingJobInfo(graphName string, info []interface{}) error {
	data, err := tgJSON.Marshal(&info)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(JobInfoPrefix, graphName), data)
}

func (m *Manager) DeleteLoadingJobInfo(graphName string) error {
	return m.db.Delete(GetDBKey(JobInfoPrefix, graphName))
}
