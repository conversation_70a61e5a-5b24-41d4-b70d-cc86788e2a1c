package dao

import (
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const UserPreferencePrefix = "user-preference"

// GetUserPreference retrieves a user's preference
func (m *Manager) GetUserPreference(userID string) (map[string]interface{}, error) {
	result, err := m.db.Get(GetDBKey(UserPreferencePrefix, userID))
	if err != nil {
		return nil, err
	}

	var preference map[string]interface{}
	if err := tgJSON.Unmarshal(result, &preference); err != nil {
		return nil, err
	}

	return preference, nil
}

// UpsertUserPreference updates or creates a user preference
func (m *Manager) UpsertUserPreference(userID string, preference map[string]interface{}) error {
	data, err := tgJSON.Marshal(&preference)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(UserPreferencePrefix, userID), data)
}

// DeleteUserPreference deletes a user preference
func (m *Manager) DeleteUserPreference(userID string) error {
	return m.db.Delete(GetDBKey(UserPreferencePrefix, userID))
}
