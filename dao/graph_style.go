package dao

import (
	"encoding/json"
	"fmt"
	"regexp"

	"github.com/pkg/errors"
	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const GSPrefix = "graph-style"

func (m *Manager) GetGraphStyle(graphName string) (*model.GraphStyle, error) {
	result, err := m.db.Get(GetDBKey(GSPrefix, graphName))
	if err != nil {
		return nil, err
	}

	var gstyle = model.GraphStyle{}
	if err = json.Unmarshal(result, &gstyle); err != nil {
		return nil, err
	}

	return &gstyle, nil
}

func (m *Manager) CreateGraphStyle(graphName string, style model.GraphStyle, schema model.Schema) error {
	err := validateStyles(style, schema)
	if err != nil {
		return err
	}

	data, err := tgJSON.Marshal(&style)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(GSPrefix, graphName), data)
}

func (m *Manager) UpsertGraphStyle(graphName string, style model.GraphStyle, schema model.Schema) error {
	err := validateStyles(style, schema)
	if err != nil {
		return err
	}

	data, err := tgJSON.Marshal(&style)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(GSPrefix, graphName), data)
}

func (m *Manager) DeleteGraphStyle(graphName string) error {
	return m.db.Delete(GetDBKey(GSPrefix, graphName))
}

func validateStyles(gStyle model.GraphStyle, schema model.Schema) error {
	if ok, edgeType := checkStyle(schema.Results.EdgeTypes, gStyle.EdgeStyles); !ok {
		err := fmt.Errorf("edgeStyle invalid: %s does not exist", edgeType)
		return errors.Wrap(err, "StyleError")
	}

	if ok, vertexType := checkStyle(schema.Results.VertexTypes, gStyle.VertexStyles); !ok {
		err := fmt.Errorf("vertexStyle invalid: %s does not exist", vertexType)
		return errors.Wrap(err, "StyleError")
	}
	return nil
}

func checkStyle(Types []model.Name, styles map[string]interface{}) (bool, string) {
	r := regexp.MustCompile("^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$")

	stylesList := make(map[string]bool, 0)
	for _, Type := range Types {
		stylesList[Type.Name] = true
	}
	for k, v := range styles {
		if _, ok := stylesList[k]; !ok {
			return false, k
		}
		style, ok := v.(map[string]interface{})
		if !ok {
			return false, ""
		}
		if fillColor, exist := style["fillColor"]; exist {
			fillColorString, ok := fillColor.(string)
			if !ok || !r.MatchString(fillColorString) {
				return false, fillColorString
			}
		}
	}
	return true, ""
}
