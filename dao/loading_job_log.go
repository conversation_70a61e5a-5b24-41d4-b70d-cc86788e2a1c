package dao

import (
	"encoding/json"

	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const JobLogPrefix = "loading-job-log"

func (m *Manager) GetLoadingJobLog(graphName, jobName string) (*model.LoadingJobLog, error) {
	result, err := m.db.Get(GetDBKey(JobLogPrefix, graphName, jobName))
	if err != nil {
		return nil, err
	}

	jobLog := &model.LoadingJobLog{}
	if err = json.Unmarshal(result, jobLog); err != nil {
		return nil, err
	}

	return jobLog, nil
}

func (m *Manager) GetAllLoadingJobLogs(graphName string) ([]model.LoadingJobLog, error) {
	results, err := m.db.GetAll(GetDBKeyForGraphResource(JobLogPrefix, graphName))
	if err != nil {
		return nil, err
	}

	jobLogs := make([]model.LoadingJobLog, len(results))
	for i, result := range results {
		var jobLog model.LoadingJobLog
		if err = json.Unmarshal(result, &jobLog); err != nil {
			return nil, err
		}
		jobLogs[i] = jobLog
	}

	return jobLogs, nil
}

func (m *Manager) CreateLoadingJobLog(graphName, jobName string, log model.LoadingJobLog) error {
	data, err := tgJSON.Marshal(&log)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(JobLogPrefix, graphName, jobName), data)
}

func (m *Manager) UpsertLoadingJobLog(graphName, jobName string, log model.LoadingJobLog) error {
	data, err := tgJSON.Marshal(&log)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(JobLogPrefix, graphName, jobName), data)
}

func (m *Manager) DeleteLoadingJobLog(graphName, jobName string) error {
	return m.db.Delete(GetDBKey(JobLogPrefix, graphName, jobName))
}

func (m *Manager) DeleteAllLoadingJobLogs(graphName string) error {
	return m.db.DeleteAll(GetDBKey(JobLogPrefix, graphName))
}
