package dao

import (
	"encoding/json"

	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const QDPrefix = "query-draft"

func (m *Manager) GetQueryDraft(graphName, queryName string) (*model.QueryDraft, error) {
	result, err := m.db.Get(GetDBKey(QDPrefix, graphName, queryName))
	if err != nil {
		return nil, err
	}

	queryDraft := &model.QueryDraft{}
	if err = json.Unmarshal(result, queryDraft); err != nil {
		return nil, err
	}

	return queryDraft, nil
}

func (m *Manager) GetAllQueryDrafts(graphName string) ([]model.QueryDraft, error) {
	results, err := m.db.GetAll(GetDBKeyForGraphResource(QDPrefix, graphName))
	if err != nil {
		return nil, err
	}

	queryDrafts := make([]model.QueryDraft, len(results))
	for i, result := range results {
		var queryDraft model.QueryDraft
		if err = json.Unmarshal(result, &queryDraft); err != nil {
			return nil, err
		}
		queryDrafts[i] = queryDraft
	}

	return queryDrafts, nil
}

func (m *Manager) CreateQueryDraft(graphName, queryName string, draft model.QueryDraft) error {
	data, err := tgJSON.Marshal(&draft)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(QDPrefix, graphName, queryName), data)
}

func (m *Manager) UpsertQueryDraft(graphName, queryName string, draft model.QueryDraft) error {
	data, err := tgJSON.Marshal(&draft)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(QDPrefix, graphName, queryName), data)
}

func (m *Manager) DeleteQueryDraft(graphName, queryName string) error {
	return m.db.Delete(GetDBKey(QDPrefix, graphName, queryName))
}

func (m *Manager) DeleteAllQueryDraft(graphName string) error {
	return m.db.DeleteAll(GetDBKey(QDPrefix, graphName))
}
