package dao

import (
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	database "github.com/tigergraph/gus/service/db"
)

const ctxKeyDAO = "DAO"

type Manager struct {
	db *database.Manager
}

// Interface Implementations
var (
	m                               = Manager{}
	_ interfaces.DaoManager         = &Manager{}
	_ interfaces.UpsertSAMLResponse = m.UpsertSAMLResponse
)

func New(db *database.Manager) *Manager {
	return &Manager{db: db}
}

// GinCtxSetter returns a middleware that sets the DAO Manager for the current context.
func GinCtxSetter(m interfaces.DaoManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(ctxKeyDAO, m)
	}
}

func GetManager(c *gin.Context) interfaces.DaoManager {
	value, _ := c.Get(ctxKeyDAO)
	manager, _ := value.(interfaces.DaoManager)
	return manager
}

func (m *Manager) DeleteAll() error {
	return m.db.DeleteAll("")
}

func (m *Manager) GetAllKeys(key string) ([]string, error) {
	return m.db.GetAllKeys(key)
}

func GetDBKey(args ...string) string {
	return strings.Join(args, "/")
}

// GetDBKeyForGraphResource is intended to be used with *db.Manager.GetAll
// GetAll matches prefix by default
// GetDBKey joins strings and produce a x/x/x formatted string
// For getting all resources under a graph, needs to append a final "/"
// to make the prefix x/x/x/
// so that it won't match other graphs with names that graphName is a prefix of.
func GetDBKeyForGraphResource(prefix string, graphName string) string {
	return GetDBKey(prefix, graphName) + "/"
}
