package dao

import (
	"encoding/json"
	"strings"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/service/db"
)

const permissionKey = "GAAppPermission"

func (m *Manager) GetInsightsAppPermission(appId string) (*model.AppPermission, error) {
	result, err := m.db.Get(GetDBKey(permissionKey, appId))
	if err != nil {
		return nil, err
	}

	appPermission := &model.AppPermission{}
	if err = json.Unmarshal(result, appPermission); err != nil {
		return nil, err
	}

	return appPermission, nil
}

func (m *Manager) GetAllInsightsAppPermission() (map[string]*model.AppPermission, error) {
	kvs, err := m.db.Export(permissionKey)
	if err != nil {
		return nil, err
	}

	appsPermission := make(map[string]*model.AppPermission)
	for key, value := range kvs {
		var appPermission model.AppPermission
		splits := strings.Split(key, "/")
		if len(splits) < 2 {
			continue
		}
		appId := splits[1]
		data, err := db.Decompress(value)
		if err != nil {
			continue
		}
		err = json.Unmarshal(data, &appPermission)
		if err != nil {
			continue
		}
		appsPermission[appId] = &appPermission
	}
	return appsPermission, nil
}

func (m *Manager) UpsertInsightsAppPermission(appId string, appPermission model.AppPermission) error {
	data, err := json.Marshal(appPermission)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(permissionKey, appId), data)
}
