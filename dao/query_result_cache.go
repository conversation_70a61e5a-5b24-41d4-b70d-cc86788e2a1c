package dao

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"

	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const (
	InstalledQueryPrefix = "query-result-cache/installed"
	InterpretQueryPrefix = "query-result-cache/interpret"
)

func getDBKeyForInstalledQuery(graphName, queryName string, queryParams interface{}) (string, error) {
	h := sha256.New()
	b, err := tgJSON.Marshal(queryParams)
	if err != nil {
		return "", err
	}
	h.Write([]byte(b))
	id := hex.EncodeToString(h.Sum(nil))
	return GetDBKey(InstalledQueryPrefix, graphName, queryName, id), nil
}

func getDBKeyForInterpretQuery(query string, queryParams interface{}) (string, error) {
	h := sha256.New()
	b, err := tgJSON.Marshal(queryParams)
	if err != nil {
		return "", err
	}
	h.Write([]byte(query))
	h.Write([]byte(b))
	id := hex.EncodeToString(h.Sum(nil))
	return GetDBKey(InterpretQueryPrefix, id), nil
}

func (m *Manager) GetInstalledQueryResultCache(graphName, queryName string, queryParams interface{}) (*model.QueryResultCache, error) {
	key, err := getDBKeyForInstalledQuery(graphName, queryName, queryParams)
	if err != nil {
		return nil, err
	}
	return m.getQueryResultCache(key)
}

func (m *Manager) GetInterpretQueryResultCache(query string, queryParams interface{}) (*model.QueryResultCache, error) {
	key, err := getDBKeyForInterpretQuery(query, queryParams)
	if err != nil {
		return nil, err
	}
	return m.getQueryResultCache(key)
}

func (m *Manager) getQueryResultCache(key string) (*model.QueryResultCache, error) {
	result, err := m.db.Get(key)
	if err != nil {
		return nil, err
	}

	queryCache := &model.QueryResultCache{}
	if err = json.Unmarshal(result, queryCache); err != nil {
		return nil, err
	}

	return queryCache, nil
}

func (m *Manager) UpsertInstalledQueryResultCache(graphName, queryName string, queryParams interface{}, queryCache model.QueryResultCache) error {
	key, err := getDBKeyForInstalledQuery(graphName, queryName, queryParams)
	if err != nil {
		return err
	}
	data, err := tgJSON.Marshal(&queryCache)
	if err != nil {
		return err
	}

	return m.db.Upsert(key, data)
}

func (m *Manager) UpsertInterpretQueryResultCache(query string, queryParams interface{}, queryCache model.QueryResultCache) error {
	key, err := getDBKeyForInterpretQuery(query, queryParams)
	if err != nil {
		return err
	}
	data, err := tgJSON.Marshal(&queryCache)
	if err != nil {
		return err
	}

	return m.db.Upsert(key, data)
}

func (m *Manager) DeleteInstalledQueryResultCache(graphName, queryName string, queryParams interface{}) error {
	key, err := getDBKeyForInstalledQuery(graphName, queryName, queryParams)
	if err != nil {
		return err
	}
	return m.db.Delete(key)
}

func (m *Manager) DeleteInterpretQueryResultCache(query string, queryParams interface{}) error {
	key, err := getDBKeyForInterpretQuery(query, queryParams)
	if err != nil {
		return err
	}
	return m.db.Delete(key)
}
