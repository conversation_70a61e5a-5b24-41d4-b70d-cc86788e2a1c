package model

import (
	"time"
)

const (
	GoogleCloudStorage = "gcs"
	AmazonS3           = "s3"
	FileDataSource     = "file"
	AzureBlobStorage   = "abs"
)

type AuthType string

const (
	CookieAuthType AuthType = "cookie"
	TokenAuthType  AuthType = "token"
	BasicAuthType  AuthType = "basic"
	Auth0AuthType  AuthType = "bearer"
)

type LoadingJobLog struct {
	ID             string             `json:"id" binding:"required"`
	Error          bool               `json:"error" binding:"required"`
	Message        string             `json:"message" binding:"required"`
	Expiration     time.Time          `json:"expiration" binding:"required"`
	Progress       LoadingJobProgress `json:"progress" binding:"required"`
	ConnectorName  string             `json:"connectorName" binding:"required"`
	DataSourceName string             `json:"dataSourceName" binding:"required"`
}

type LoadingJobProgress struct {
	URI                 string  `json:"uri" binding:"required"`
	Source              string  `json:"source" binding:"required"`
	Status              string  `json:"status" binding:"required"`
	Message             string  `json:"message"`
	StartTime           int64   `json:"startTime" binding:"required"`
	EndTime             int64   `json:"endTime" binding:"required"`
	Duration            float64 `json:"duration" binding:"required"`
	Percentage          float32 `json:"percentage" binding:"required"`
	CurrentSpeed        float64 `json:"currentSpeed" binding:"required"`
	AverageSpeed        float64 `json:"averageSpeed" binding:"required"`
	LoadedSize          float64 `json:"loadedSize"`
	LoadedLines         int64   `json:"loadedLines"`
	NotEnoughTokenLines int64   `json:"notEnoughTokenLines"`
	OversizeTokenLines  int64   `json:"oversizeTokenLines"`
	RejectedLines       int64   `json:"rejectedLines"`
}

type ExplorationResultPreview struct {
	Name         string `json:"name" binding:"required"`
	Username     string `json:"username" binding:"required"`
	Timestamp    int64  `json:"timestamp" binding:"required"`
	PreviewImage string `json:"previewImage" binding:"required"`
	Description  string `json:"description"`
}

type ExplorationResult struct {
	ExplorationResultPreview

	Schema interface{} `json:"schema" binding:"required"`
	Data   interface{} `json:"data" binding:"required"`
}

type QueryDraft struct {
	Name        string `json:"name"`
	Syntax      string `json:"syntax"`
	Code        string `json:"code"`
	GraphUpdate bool   `json:"graphUpdate"`
}

type GoogleCloudStorageDataSource struct {
	ProjectId    string `json:"project_id" binding:"required"`
	PrivateKeyId string `json:"private_key_id" binding:"required"`
	PrivateKey   string `json:"private_key" binding:"required"`
	ClientEmail  string `json:"client_email" binding:"required"`
}

type AmazonS3DataSource struct {
	AccessKey string `json:"file.reader.settings.fs.s3a.access.key" binding:"required"`
	SecretKey string `json:"file.reader.settings.fs.s3a.secret.key" binding:"required"`
}

type AzureBlobStorageDataSource struct {
	AccountName string `json:"accountName" binding:"required"`
	AccountKey  string `json:"accountKey" binding:"required"`
}

type ScheduledBackupStatus struct {
	Timestamp  string `json:"timestamp" binding:"required"`
	Output     string `json:"output" binding:"required"`
	BackupName string `json:"backupName"`
	Error      string `json:"error,omitempty"`
}

// CRON expression.
type BackupSchedule struct {
	Minutes    string `json:"minutes" binding:"required"`
	Hours      string `json:"hours" binding:"required"`
	DayOfMonth string `json:"dayOfMonth" binding:"required"`
	Month      string `json:"month" binding:"required"`
	DayOfWeek  string `json:"dayOfWeek" binding:"required"`
}

type CSVDataSourceOption struct {
	Format    string `json:"format" binding:"required"`
	Separator string `json:"separator" binding:"required"`
	Eol       string `json:"eol" binding:"required"`
	Header    bool   `json:"header" binding:"required"`
	Quote     string `json:"quote"`
}

type DBDataSourceJson struct {
	DataSourceType string              `json:"type" binding:"required"`
	Uri            string              `json:"uri" binding:"required"`
	Auth           interface{}         `json:"auth"`
	Options        CSVDataSourceOption `json:"options" binding:"required"`
	Position       interface{}         `json:"position" binding:"required"`
	DataSourceName string              `json:"dataSourceName"`
}

type DBDataSetJson struct {
	DataFormat string        `json:"dataFormat" binding:"required"`
	DataSchema []interface{} `json:"dataSchema" binding:"required"`
}

type LoadingJobInfo struct {
	LoadingJobName        string           `json:"loadingJobName" binding:"required"`
	LoadingStatementStyle interface{}      `json:"loadingStatementsStyle" binding:"required"`
	DataSourceJson        DBDataSourceJson `json:"dataSourceJson" binding:"required"`
	DBDataSetJson         DBDataSetJson    `json:"dataSetJson"`
	Header                []string         `json:"header"`
	SampleData            [][]string       `json:"sampleData"`
}

type LoadingJobCommandResult map[string]*LoadingJobLog

type StartJobInfo struct {
	Name        string           `json:"name" binding:"required"`
	Streaming   *bool            `json:"streaming" binding:"required"`
	DataSources []GSQLDataSource `json:"dataSources" binding:"required"`
}

type KafkaDataSourceConfig struct {
	Topic              string `json:"topic" binding:"required"`
	DefaultStartOffset int    `json:"default_start_offset" binding:"required"`
}

type GSQLDataSource struct {
	Filename string                `json:"filename" binding:"required"`
	Name     string                `json:"name" binding:"required"`
	Path     string                `json:"path" binding:"required"`
	Hostname string                `json:"hostname,omitempty"`
	Config   KafkaDataSourceConfig `json:"config" binding:"required"`
}

type GSQLKafkaDataSourceConfig struct {
	Broker string `json:"broker" binding:"required"`
}

type GSQLKafkaDataSource struct {
	Name           string                    `json:"name" binding:"required"`
	DataSourceType string                    `json:"type" binding:"required"`
	Config         GSQLKafkaDataSourceConfig `json:"config" binding:"required"`
}

type UserCredentials struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type UserInfo struct {
	Name        string      `json:"name" binding:"required"`
	Roles       Roles       `json:"roles" binding:"required"`
	Privileges  Privileges  `json:"privileges" binding:"required"`
	Secrets     interface{} `json:"secrets" binding:"required"`
	IsSuperUser bool        `json:"isSuperUser" binding:"required"`
}

type UserInfoWithCredentials struct {
	UserInfo
	UserCredentials
	AuthType
}

type GraphStyle struct {
	VertexStyles map[string]interface{} `json:"vertexStyles"`
	EdgeStyles   map[string]interface{} `json:"edgeStyles"`
}

type Name struct {
	Name string `json:"Name"`
}

type SchemaResults struct {
	EdgeTypes   []Name `json:"EdgeTypes"`
	VertexTypes []Name `json:"VertexTypes"`
}

type Schema struct {
	Results SchemaResults `json:"results"`
}

type GSQLLoginResponse struct {
	Error                   bool   `json:"error"`
	Message                 string `json:"message"`
	IsClientCompatible      bool   `json:"isClientCompatible"`
	WelcomeMessage          string `json:"welcomeMessage"`
	ShellPrompt             string `json:"shellPrompt"`
	SecurityRecommendations int    `json:"securityRecommendations"`
}

type GSQLCookies map[string]interface{}

type GSQLCommandRequest struct {
	Command string      `json:"command" binding:"required"`
	Cookies GSQLCookies `json:"cookie" binding:"required"`
}

type GSQLCommandResponse struct {
	// returned cookies
	Cookies GSQLCookies
	// return code of gsql command
	ReturnCode int
	// request an interaction with the user
	IsInteractive bool
	Output        []byte
}

type Roles map[string][]string

type Privileges map[string]struct {
	Privileges []Privilege `json:"privileges" binding:"required"`
}

type Privilege string

const (
	READ_SCHEMA        Privilege = "READ_SCHEMA"
	WRITE_SCHEMA       Privilege = "WRITE_SCHEMA"
	READ_LOADINGJOB    Privilege = "READ_LOADINGJOB"
	EXECUTE_LOADINGJOB Privilege = "EXECUTE_LOADINGJOB"
	WRITE_LOADINGJOB   Privilege = "WRITE_LOADINGJOB"
	READ_QUERY         Privilege = "READ_QUERY"
	WRITE_QUERY        Privilege = "WRITE_QUERY"
	READ_DATA          Privilege = "READ_DATA"
	WRITE_DATA         Privilege = "WRITE_DATA"
	WRITE_DATASOURCE   Privilege = "WRITE_DATASOURCE"
	READ_ROLE          Privilege = "READ_ROLE"
	WRITE_ROLE         Privilege = "WRITE_ROLE"
	READ_USER          Privilege = "READ_USER"
	WRITE_USER         Privilege = "WRITE_USER"
	READ_PROXYGROUP    Privilege = "READ_PROXYGROUP"
	WRITE_PROXYGROUP   Privilege = "WRITE_PROXYGROUP"
	READ_FILE          Privilege = "READ_FILE"
	WRITE_FILE         Privilege = "WRITE_FILE"
	DROP_GRAPH         Privilege = "DROP_GRAPH"
	EXPORT_GRAPH       Privilege = "EXPORT_GRAPH"
	CLEAR_GRAPHSTORE   Privilege = "CLEAR_GRAPHSTORE"
	DROP_ALL           Privilege = "DROP_ALL"
	ACCESS_TAG         Privilege = "ACCESS_TAG"
	APP_ACCESS_DATA    Privilege = "APP_ACCESS_DATA"
)

// frontend types for gbar
type BackupInfo struct {
	CreatedAt string
	Name      string
	SizeBytes float64
}

type MetadataRequest struct {
	Metadata string
}

type CloudMetaResponse struct {
	Error   bool
	Message string
	Result  struct {
		InstanceID string
		Platform   string
	}
}

// for Insights
type AppPermission struct {
	Access  int32
	Editors []string
	Viewers []string
}
