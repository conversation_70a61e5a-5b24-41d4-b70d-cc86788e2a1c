package model

import "net/url"

type Request struct {
	Method          string
	Url             string
	Header          map[string]string
	QueryParameters url.Values
	Body            interface{}
}

type Response struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Results interface{} `json:"results"`
	Done    *bool       `json:"done,omitempty"`
}

type GetSchemaRequest struct {
	Request

	// TODO: Extend it.
}

type GetSchemaResponse struct {
	Response

	// TODO: Extend it.
}
