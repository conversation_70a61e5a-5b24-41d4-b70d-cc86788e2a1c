package dao

import (
	"encoding/json"
	"strings"

	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const DSPrefix = "data-source"

func (m *Manager) GetDataSource(graphName, dataSourceType, dataSourceName string) (interface{}, error) {
	result, err := m.db.Get(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName))
	if err != nil {
		return nil, err
	}
	var dataSource interface{}

	switch dataSourceType {
	case model.AmazonS3:
		dataSource = &model.AmazonS3DataSource{}
	case model.GoogleCloudStorage:
		dataSource = &model.GoogleCloudStorageDataSource{}
	case model.AzureBlobStorage:
		dataSource = &model.AzureBlobStorageDataSource{}
	default:
		// TODO: Support more data source types in the future.
		return nil, nil
	}

	if err := json.Unmarshal(result, dataSource); err != nil {
		return nil, err
	}

	return dataSource, nil
}

func (m *Manager) CreateDataSource(graphName, dataSourceType, dataSourceName string, dataSource interface{}) error {
	data, err := tgJSON.Marshal(&dataSource)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName), data)
}

func (m *Manager) DeleteDataSource(graphName, dataSourceType, dataSourceName string) error {
	return m.db.DeleteAll(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName))
}

func (m *Manager) GetAllDataSourceNames(graphName, dataSourceType string) ([]string, error) {
	results, err := m.db.GetAllKeys(GetDBKey(DSPrefix, graphName, dataSourceType))

	dataSourceNames := make([]string, 0)
	if err == nil {
		for _, result := range results {
			s := strings.Split(string(result), "/")
			if len(s) == 4 {
				dataSourceNames = append(dataSourceNames, s[len(s)-1])
			}
		}
	}
	return dataSourceNames, err
}

func (m *Manager) UpsertDataSourceDataset(graphName, dataSourceType, dataSourceName string, dataSourceUri string, dataset model.DataSourceDataset) error {
	data, err := tgJSON.Marshal(&dataset)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName, dataSourceUri), data)
}

func (m *Manager) DeleteDataSourceDataset(graphName, dataSourceType, dataSourceName string, dataSourceUri string) error {
	return m.db.Delete(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName, dataSourceUri))
}

func (m *Manager) GetDataSourceTypeUris(graphName, dataSourceType string) (map[string][]string, error) {
	results, err := m.db.GetAllKeys(GetDBKey(DSPrefix, graphName, dataSourceType))

	dataSourceTypeUris := make(map[string][]string)
	if err == nil {
		for _, result := range results {
			s := strings.Split(string(result), "/")
			if len(s) >= 5 {
				dataSourceTypeUris[s[3]] = append(dataSourceTypeUris[s[3]], strings.Join(s[4:], "/"))
			}
		}
	}
	return dataSourceTypeUris, err
}

func (m *Manager) GetDataSourceUris(graphName, dataSourceType string, dataSourceName string) ([]string, error) {
	results, err := m.db.GetAllKeys(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName))

	dataSourceNameUris := make([]string, 0)
	if err == nil {
		for _, result := range results {
			s := strings.Split(string(result), "/")
			if len(s) >= 5 {
				dataSourceNameUris = append(dataSourceNameUris, strings.Join(s[4:], "/"))
			}
		}
	}
	return dataSourceNameUris, err
}

func (m *Manager) GetDataSourceDataset(graphName, dataSourceType string, dataSourceName string, dataSourceUri string) (*model.DataSourceDataset, error) {
	result, err := m.db.Get(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName, dataSourceUri))
	if err != nil {
		return nil, err
	}
	dataset := &model.DataSourceDataset{}

	if err := json.Unmarshal(result, dataset); err != nil {
		return nil, err
	}

	return dataset, nil
}

func (m *Manager) DeleteAllDataSources(graphName string) error {
	return m.db.DeleteAll(GetDBKey(DSPrefix, graphName))
}
