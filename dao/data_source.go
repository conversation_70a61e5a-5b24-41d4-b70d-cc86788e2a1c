package dao

import (
	"encoding/json"
	"strings"

	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const DSPrefix = "data-source"

func (m *Manager) GetDataSource(graphName, dataSourceType, dataSourceName string) (interface{}, error) {
	result, err := m.db.Get(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName))
	if err != nil {
		return nil, err
	}
	var dataSource interface{}

	switch dataSourceType {
	case model.AmazonS3:
		dataSource = &model.AmazonS3DataSource{}
	case model.GoogleCloudStorage:
		dataSource = &model.GoogleCloudStorageDataSource{}
	case model.AzureBlobStorage:
		dataSource = &model.AzureBlobStorageDataSource{}
	default:
		// TODO: Support more data source types in the future.
		return nil, nil
	}

	if err := json.Unmarshal(result, dataSource); err != nil {
		return nil, err
	}

	return dataSource, nil
}

func (m *Manager) CreateDataSource(graphName, dataSourceType, dataSourceName string, dataSource interface{}) error {
	data, err := tgJSON.Marshal(&dataSource)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName), data)
}

func (m *Manager) DeleteDataSource(graphName, dataSourceType, dataSourceName string) error {
	return m.db.Delete(GetDBKey(DSPrefix, graphName, dataSourceType, dataSourceName))
}

func (m *Manager) GetAllDataSourceNames(graphName, dataSourceType string) ([]string, error) {
	results, err := m.db.GetAllKeys(GetDBKey(DSPrefix, graphName, dataSourceType))

	dataSourceNames := make([]string, len(results))
	if err == nil {
		for i, result := range results {
			s := strings.Split(string(result), "/")
			dataSourceNames[i] = s[len(s)-1]
		}
	}
	return dataSourceNames, err
}

func (m *Manager) DeleteAllDataSources(graphName string) error {
	return m.db.DeleteAll(GetDBKey(DSPrefix, graphName))
}
