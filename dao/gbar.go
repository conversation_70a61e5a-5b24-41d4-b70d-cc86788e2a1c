package dao

import (
	"encoding/json"
	"errors"
	"sort"
	"strconv"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/codec"
	tgJSON "github.com/tigergraph/gus/lib/json"
	"github.com/tigergraph/gus/service/db"
)

const (
	GBARInProgressKey    = "gbar/in-progress"
	BackupStatusPrefix   = "scheduled-backup-status"
	BackupSchedulePrefix = "backup-schedule"
	GSQLPasswordPrefix   = "gsql-password"
)

func (m *Manager) GetScheduledBackupStatus(backupName, timeStamp string) (*model.ScheduledBackupStatus, error) {
	result, err := m.db.Get(GetDBKey(BackupStatusPrefix, backupName, timeStamp))
	if err != nil {
		return nil, err
	}

	status := &model.ScheduledBackupStatus{}
	if err = json.Unmarshal(result, status); err != nil {
		return nil, err
	}

	return status, nil
}

func (m *Manager) GetAllScheduledBackupStatus() ([]model.ScheduledBackupStatus, error) {
	results, err := m.db.GetAll(GetDBKey(BackupStatusPrefix))
	if err != nil {
		return nil, err
	}

	scheduledBackupStatuses := make([]model.ScheduledBackupStatus, len(results))
	for i, result := range results {
		var status model.ScheduledBackupStatus
		if err = json.Unmarshal(result, &status); err != nil {
			return nil, err
		}
		scheduledBackupStatuses[i] = status
	}
	// Show newest status first.
	sort.Slice(scheduledBackupStatuses, func(i, j int) bool {
		return scheduledBackupStatuses[i].Timestamp > scheduledBackupStatuses[j].Timestamp
	})

	return scheduledBackupStatuses, nil
}

func (m *Manager) CreateScheduledBackupStatus(backupStatus model.ScheduledBackupStatus) error {
	data, err := tgJSON.Marshal(&backupStatus)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(BackupStatusPrefix, backupStatus.BackupName, backupStatus.Timestamp), data)
}

func (m *Manager) UpsertScheduledBackupStatus(backupStatus model.ScheduledBackupStatus) error {
	data, err := tgJSON.Marshal(&backupStatus)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(BackupStatusPrefix, backupStatus.BackupName, backupStatus.Timestamp), data)
}

func (m *Manager) DeleteScheduledBackupStatus(backupName, timestamp string) error {
	return m.db.Delete(GetDBKey(BackupStatusPrefix, backupName, timestamp))
}

func (m *Manager) DeleteAllScheduledBackupStatus() error {
	return m.db.DeleteAll(GetDBKey(BackupStatusPrefix))
}

func (m *Manager) GetBackupSchedule() (*model.BackupSchedule, error) {
	result, err := m.db.Get(GetDBKey(BackupSchedulePrefix))
	if err != nil {
		if errors.Is(err, db.ErrNotFound) {
			// If no backup schedule found, return a default daily frequency schedule.
			return &model.BackupSchedule{
				Minutes:    "0",
				Hours:      "0",
				DayOfMonth: "*",
				Month:      "*",
				DayOfWeek:  "*",
			}, nil
		}
		return nil, err
	}

	backupSchedule := &model.BackupSchedule{}
	if err = json.Unmarshal(result, backupSchedule); err != nil {
		return nil, err
	}

	return backupSchedule, nil
}

func (m *Manager) CreateBackupSchedule(schedule model.BackupSchedule) error {
	data, err := tgJSON.Marshal(&schedule)
	if err != nil {
		return err
	}
	return m.db.Create(GetDBKey(BackupSchedulePrefix), data)
}

func (m *Manager) UpsertBackupSchedule(schedule model.BackupSchedule) error {
	data, err := tgJSON.Marshal(&schedule)
	if err != nil {
		return err
	}
	return m.db.Upsert(GetDBKey(BackupSchedulePrefix), data)
}

func (m *Manager) DeleteBackupSchedule() error {
	return m.db.Delete(GetDBKey(BackupSchedulePrefix))
}

func (m *Manager) GetPassword(authToken string) (string, error) {
	encryptedPwd, err := m.db.Get(GetDBKey(GSQLPasswordPrefix))
	if err != nil {
		// Password is only upserted into DB when password changes;
		// so if password not found, it returns the default tigergraph password.
		if errors.Is(err, db.ErrNotFound) {
			return "tigergraph", nil
		}
		return "", err
	}

	decryptedPwd, err := codec.AESCBCDecrypt(authToken, string(encryptedPwd))
	if err != nil {
		return "", err
	}
	return decryptedPwd, nil
}

func (m *Manager) CreatePassword(password string, authToken string) error {
	encryptedPwd, err := codec.AESCBCEncrypt(authToken, password)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(GSQLPasswordPrefix), []byte(encryptedPwd))
}

func (m *Manager) UpsertPassword(password string, authToken string) error {
	encryptedPwd, err := codec.AESCBCEncrypt(authToken, password)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(GSQLPasswordPrefix), []byte(encryptedPwd))
}

func (m *Manager) DeletePassword() error {
	return m.db.Delete(GetDBKey(GSQLPasswordPrefix))
}

// IsGBARInProgress checks whether a GBAR backup or restore is in progress.
func (m *Manager) IsGBARInProgress() (bool, error) {
	result, err := m.db.Get(GBARInProgressKey)
	if err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return false, nil
		}
		return false, err
	}

	s := string(result)
	return strconv.ParseBool(s)
}

// SetGBARInProgress sets the current status of GBAR.
func (m *Manager) SetGBARInProgress(inProgress bool) error {
	return m.db.Upsert(GBARInProgressKey, []byte(strconv.FormatBool(inProgress)))
}
