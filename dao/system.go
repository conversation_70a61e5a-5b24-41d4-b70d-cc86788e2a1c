package dao

import (
	"errors"
	"time"

	"github.com/tigergraph/gus/service/db"
)

const (
	SystemImportKey     = "solution/import"
	SystemImportTimeout = 5 * time.Minute
)

func (m *Manager) Import(data map[string][]byte) error {
	return m.db.Import(data)
}

func (m *Manager) Export(key string) (map[string][]byte, error) {
	return m.db.Export(key)
}

// IsImporting checks if importing is in progress.
func (m *Manager) IsImporting() (bool, error) {
	result, err := m.db.Get(SystemImportKey)
	if err == nil {
		var timestamp time.Time
		if err = timestamp.UnmarshalJSON(result); err != nil {
			return false, err
		}
		return timestamp.After(time.Now()), nil
	} else if errors.Is(err, db.ErrNotFound) {
		return false, nil
	}

	return false, err
}

// SetImport sets the current status of import.
func (m *Manager) SetImport(isImporting bool) error {
	if isImporting {
		timestamp, _ := time.Now().Add(SystemImportTimeout).MarshalJSON()
		return m.db.Upsert(SystemImportKey, timestamp)
	}
	return m.db.Delete(SystemImportKey)
}
