package dao

import (
	"encoding/json"

	tgJSON "github.com/tigergraph/gus/lib/json"
)

const VPPrefix = "visual-pattern"

func (m *Manager) GetVisualPattern(graphName, patternName string) (interface{}, error) {
	result, err := m.db.Get(GetDBKey(VPPrefix, graphName, patternName))
	if err != nil {
		return nil, err
	}

	var pattern interface{}
	if err = json.Unmarshal(result, &pattern); err != nil {
		return nil, err
	}

	return pattern, nil
}

func (m *Manager) GetAllVisualPatterns(graphName string) ([]interface{}, error) {
	results, err := m.db.GetAll(GetDBKeyForGraphResource(VPPrefix, graphName))
	if err != nil {
		return nil, err
	}

	patterns := make([]interface{}, len(results))
	for i, result := range results {
		var pattern interface{}
		if err = json.Unmarshal(result, &pattern); err != nil {
			return nil, err
		}
		patterns[i] = pattern
	}

	return patterns, nil
}

func (m *Manager) CreateVisualPattern(graphName, patternName string, pattern interface{}) error {
	data, err := tgJSON.Marshal(&pattern)
	if err != nil {
		return err
	}

	return m.db.Create(GetDBKey(VPPrefix, graphName, patternName), data)
}

func (m *Manager) UpsertVisualPattern(graphName, patternName string, pattern interface{}) error {
	data, err := tgJSON.Marshal(&pattern)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(VPPrefix, graphName, patternName), data)
}

func (m *Manager) DeleteVisualPattern(graphName, patternName string) error {
	return m.db.Delete(GetDBKey(VPPrefix, graphName, patternName))
}

func (m *Manager) DeleteAllVisualPatterns(graphName string) error {
	return m.db.DeleteAll(GetDBKey(VPPrefix, graphName))
}
