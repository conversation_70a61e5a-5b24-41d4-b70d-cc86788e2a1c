package dao

const SAMLRespPrefix = "saml-response"

func (m *Manager) GetSAMLResponse(hash string) (string, error) {
	samlResp, err := m.db.Get(GetDBKey(SAMLRespPrefix, hash))
	if err != nil {
		return "", err
	}

	return string(samlResp), nil
}

func (m *Manager) CreateSAMLResponse(hash string, samlResp string) error {
	return m.db.Create(GetDBKey(SAMLRespPrefix, hash), []byte(samlResp))
}

func (m *Manager) UpsertSAMLResponse(hash string, samlResp string) error {
	return m.db.Upsert(GetDBKey(SAMLRespPrefix, hash), []byte(samlResp))
}

func (m *Manager) DeleteSAMLResponse(hash string) error {
	return m.db.Delete(GetDBKey(SAMLRespPrefix, hash))
}
