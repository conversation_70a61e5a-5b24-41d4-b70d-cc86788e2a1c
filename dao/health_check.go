package dao

import (
	"encoding/json"

	"github.com/tigergraph/gus/dao/model"
	tgJSON "github.com/tigergraph/gus/lib/json"
)

const RunningHealthCheckPrefix = "running-health-check"

func (m *Manager) GetRunningHealthChecks() ([]model.RunningHealthCheck, error) {
	result, err := m.db.Get(GetDBKey(RunningHealthCheckPrefix))
	if err != nil {
		return nil, err
	}

	var healthChecks []model.RunningHealthCheck
	if err = json.Unmarshal(result, &healthChecks); err != nil {
		return nil, err
	}

	return healthChecks, nil
}

func (m *Manager) SaveRunningHealthChecks(healthChecks []model.RunningHealthCheck) error {
	data, err := tgJSON.Marshal(&healthChecks)
	if err != nil {
		return err
	}

	return m.db.Upsert(GetDBKey(RunningHealthCheckPrefix), data)
}

func (m *Manager) DeleteRunningHealthChecks() error {
	return m.db.Delete(Get<PERSON><PERSON><PERSON>ey(RunningHealthCheckPrefix))
}
