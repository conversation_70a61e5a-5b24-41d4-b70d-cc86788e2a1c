package tests

import (
	"encoding/json"
	"fmt"
	"sync"
	"testing"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql"
	"github.com/tigergraph/graphql/gsql/client"
)

func TestDataRace(t *testing.T) {
	wg := sync.WaitGroup{}
	wg.Add(2)
	{
		go f(&wg)
		go f(&wg)
	}
	wg.Wait()
}

const IntrospectionQuery = "\n    query IntrospectionQuery {\n      __schema {\n        \n        queryType { name }\n        mutationType { name }\n        subscriptionType { name }\n        types {\n          ...FullType\n        }\n        directives {\n          name\n          description\n          \n          locations\n          args {\n            ...InputValue\n          }\n        }\n      }\n    }\n\n    fragment FullType on __Type {\n      kind\n      name\n      description\n      \n      fields(includeDeprecated: true) {\n        name\n        description\n        args {\n          ...InputValue\n        }\n        type {\n          ...TypeRef\n        }\n        isDeprecated\n        deprecationReason\n      }\n      inputFields {\n        ...InputValue\n      }\n      interfaces {\n        ...TypeRef\n      }\n      enumValues(includeDeprecated: true) {\n        name\n        description\n        isDeprecated\n        deprecationReason\n      }\n      possibleTypes {\n        ...TypeRef\n      }\n    }\n\n    fragment InputValue on __InputValue {\n      name\n      description\n      type { ...TypeRef }\n      defaultValue\n      \n      \n    }\n\n    fragment TypeRef on __Type {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n                ofType {\n                  kind\n                  name\n                  ofType {\n                    kind\n                    name\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  "

func f(wg *sync.WaitGroup) {
	gsqlResponse, err := getSchema()
	schemaInfo := gsql.SchemaInfo{}
	_, err = gsqlResponse.Results.ToGraphQLSchema("demo", &schemaInfo)
	if err != nil {
		fmt.Println(err)
	}
	wg.Done()
}

func getSchema() (client.GsqlSchemaResponse, error) {
	bytes := `{"error":false,"message":"","results":{"GraphName":"demo","VertexTypes":[{"Config":{"TAGGABLE":false,"STATS":"OUTDEGREE_BY_EDGETYPE","PRIMARY_ID_AS_ATTRIBUTE":false},"IsLocal":true,"Attributes":[],"PrimaryId":{"AttributeType":{"Name":"STRING"},"IsPartOfCompositeKey":false,"PrimaryIdAsAttribute":false,"AttributeName":"id","HasIndex":false,"internalAttribute":false,"IsPrimaryKey":false},"Name":"vertex_type_1"}],"EdgeTypes":[]}}`
	result := client.GsqlSchemaResponse{}
	err := json.Unmarshal([]byte(bytes), &result)
	if err != nil {
		return result, errors.WithMessagef(err, "can not unmarshal %+v", string(bytes))
	}
	return result, nil
}
