# TigerGraph GraphQL Integration
This project translates GraphQL to GSQL to smoothen the learning curve of TigerGraph.

## Design
[Overall Architecture](https://docs.google.com/document/d/1BpiKifCJ76JoCsq8fXSmqk7NUkUZi3xv5eqeCyFEnoQ)  
[Translation Algorithm](https://docs.google.com/document/d/1_4dAaEj2_vVEx09qrO484kJB0yRIWQ7pxheFQ9XV9bI)

## Development
This project is written in Go and the test toolchain contains NodeJS.

`go`,`node`,`npm` are required to develop.

|Tested Go Version|
|-|
|1.16|
|1.17|
|1.18(release)|
### Test
```
make test
```
