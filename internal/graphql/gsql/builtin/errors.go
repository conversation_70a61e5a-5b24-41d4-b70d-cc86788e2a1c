package builtin

import "fmt"

func NewErrUnacceptableInputRepresentation(input interface{}, targetType interface{}) *ErrUnacceptableInputRepresentation {
	return &ErrUnacceptableInputRepresentation{
		Msg: fmt.Sprintf("value %v of type %T is not an acceptable represenation for %T",
			input, input, targetType),
		Doc: "https://docs.tigergraph.com/dev/gsql-ref/querying/func/datetime-functions",
	}
}

type ErrUnacceptableInputRepresentation struct {
	Msg string
	Doc string
}

func (err *ErrUnacceptableInputRepresentation) Error() string {
	return err.Msg
}
