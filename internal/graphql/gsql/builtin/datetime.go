// GSQL has types that are not natively supported by GraphQL
// Need to define Type Mappings

package builtin

import (
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
)

type Datetime struct {
	T time.Time
	// https://docs.tigergraph.com/dev/gsql-ref/querying/operators-functions-and-expressions/func/datetime-functions#parameters-2
	Format string
}

func ParseDatetime(raw string) (time.Time, error) {
	return time.Parse("2006-01-02 15:04:05", raw)
}

// UnmarshalGraphQL decode input string to Go object
func (dt *Datetime) UnmarshalGraphQL(input interface{}) error {
	switch v := input.(type) {
	case string:
		t, err := time.Parse(time.RFC3339, v)
		if err != nil {
			return errors.WithStack(err)
		}
		dt.T = t
		return nil
	default:
		return errors.WithStack(NewErrUnacceptableInputRepresentation(input, Datetime{}))
	}
}

// MarshalJSON encode output Go object to string
func (i Datetime) MarshalJSON() ([]byte, error) {
	var formattedTime string
	var format = DatetimeFormatConvert(i.Format)
	if format == time.RFC3339 ||
		format == time.Stamp {
		formattedTime = i.T.Format(format)
	} else {
		formattedTime = FormatDatetime(i.T, format)
	}
	return []byte(`"` + formattedTime + `"`), nil
}

// Convert GraphQL enum DatetimeFormat to go Format
func DatetimeFormatConvert(format string) string {
	if format == "" {
		return DatetimeFormatDefault
	}
	var m = map[string]string{
		"RFC3339": time.RFC3339,
		"Stamp":   time.Stamp,
		"Default": DatetimeFormatDefault,
	}
	v, ok := m[format]
	if !ok {
		return format
	}
	return v
}

const DatetimeFormatDefault = "%Y-%m-%d %H:%M:%S"

// Format this Time by GSQL format convention
func FormatDatetime(t time.Time, format string) string {
	year := t.Year()
	month := t.Month()
	day := t.Day()
	hour := t.Hour()
	min := t.Minute()
	sec := t.Second()

	formatted := strings.ReplaceAll(format, "%Y", strconv.FormatInt(int64(year), 10))
	formatted = strings.ReplaceAll(formatted, "%m", strconv.FormatInt(int64(month), 10))
	formatted = strings.ReplaceAll(formatted, "%d", strconv.FormatInt(int64(day), 10))
	formatted = strings.ReplaceAll(formatted, "%H", strconv.FormatInt(int64(hour), 10))
	formatted = strings.ReplaceAll(formatted, "%M", strconv.FormatInt(int64(min), 10))
	formatted = strings.ReplaceAll(formatted, "%S", strconv.FormatInt(int64(sec), 10))
	return formatted
}
