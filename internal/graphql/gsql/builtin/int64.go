package builtin

import (
	"strconv"

	"github.com/pkg/errors"
	"github.com/vektah/gqlparser/v2/ast"
)

type Int64 int64

func (i Int64) MarshalJSON() ([]byte, error) {
	// always serrialize to string to prevent precision problem because either
	// 1. JavaScript doesn't have integer
	// 2. some client languages might defautl to int32
	return []byte(`"` + strconv.FormatInt(int64(i), 10) + `"`), nil
}

func (i *Int64) ParseArgument(arg *ast.Argument) error {
	switch arg.Value.Kind {
	case ast.IntValue:
	case ast.StringValue:
	default:
		return errors.Errorf("%v of %v is not an acceptable represenation for Int64",
			arg.Value.Raw, valueKind(arg.Value.Kind))
	}
	integer, err := strconv.ParseInt(arg.Value.Raw, 10, 64)
	if err != nil {
		return errors.WithStack(err)
	}
	*i = Int64(integer)
	return nil
}
