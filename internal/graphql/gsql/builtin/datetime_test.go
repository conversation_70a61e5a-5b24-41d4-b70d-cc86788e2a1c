package builtin

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestFormatDatetime(t *testing.T) {
	datetime := time.Date(-1, 1, 2, 3, 4, 5, 6, time.Local)
	formatted := FormatDatetime(datetime, DatetimeFormatDefault)
	require.Equal(t, "-1-1-2 3:4:5", formatted)

	formatted = FormatDatetime(datetime, "%m/%d/%Y")
	require.Equal(t, "1/2/-1", formatted)

	formatted = FormatDatetime(datetime, "%S%S")
	require.Equal(t, "55", formatted)
}
