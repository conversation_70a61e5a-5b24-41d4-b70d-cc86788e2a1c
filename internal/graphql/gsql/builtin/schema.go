package builtin

import (
	_ "embed"

	"github.com/vektah/gqlparser/v2"
	"github.com/vektah/gqlparser/v2/ast"
)

//go:embed schema.gql
var builtinSchema []byte

var ScalarTypes = []string{"Boolean", "Int", "Int64", "Uint64", "Float", "String", "Datetime"}
var ComparisonOps = []string{"_eq", "_neq", "_gt", "_gte", "_lt", "_lte"}

func Schema() *ast.Schema {
	gqlSchema := gqlparser.MustLoadSchema(&ast.Source{
		Input: string(builtinSchema),
	})
	for _, scalarType := range ScalarTypes {
		filterTypeName := scalarType + "Filter"
		filterType := &ast.Definition{
			Kind: ast.InputObject,
			Name: filterTypeName,
		}
		for _, compOp := range ComparisonOps {
			filterType.Fields = append(filterType.Fields, &ast.FieldDefinition{
				Name: compOp,
				Type: &ast.Type{
					NamedType: scalarType,
				},
			})
		}
		gqlSchema.Types[filterTypeName] = filterType
	}
	return gqlSchema
}

func SchemaString() string {
	return string(builtinSchema)
}

func valueKind(kind ast.ValueKind) string {
	return map[ast.ValueKind]string{
		ast.Variable:     "Variable",
		ast.IntValue:     "IntValue",
		ast.FloatValue:   "FloatValue",
		ast.StringValue:  "BlockValue",
		ast.BlockValue:   "BlockValue",
		ast.BooleanValue: "BooleanValue",
		ast.NullValue:    "NullValue",
		ast.EnumValue:    "EnumValue",
		ast.ListValue:    "ListValue",
		ast.ObjectValue:  "ObjectValue",
	}[kind]
}
