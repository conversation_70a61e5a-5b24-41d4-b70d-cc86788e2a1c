package builtin

import (
	"strconv"

	"github.com/pkg/errors"
	"github.com/vektah/gqlparser/v2/ast"
)

type Uint64 uint64

func (i Uint64) MarshalJSON() ([]byte, error) {
	return []byte(`"` + strconv.FormatUint(uint64(i), 10) + `"`), nil
}

func (i *Uint64) ParseArgument(arg *ast.Argument) error {
	switch arg.Value.Kind {
	case ast.IntValue:
	case ast.StringValue:
	default:
		return errors.Errorf("%v of %v is not an acceptable represenation for Uint64",
			arg.Value.Raw, valueKind(arg.Value.Kind))
	}
	integer, err := strconv.ParseUint(arg.Value.Raw, 10, 64)
	if err != nil {
		return errors.WithStack(err)
	}
	*i = Uint64(integer)
	return nil
}
