# GSQL Built-in Types

# https://docs.tigergraph.com/dev/gsql-ref/querying/data-types
scalar Int64
scalar Uint64
scalar Datetime

enum DatetimeFormat {
  RFC3339
  Stamp
  # GSQL Default
  # https://docs.tigergraph.com/dev/gsql-ref/querying/operators-functions-and-expressions/func/datetime-functions#parameters-2
  Default
}

input Pagination {
  offset:  Int64
  limit:   Int64
  orderBy: OrderBy
}

enum OrderBy {
  asc
  desc
}