package gsql

import "github.com/vektah/gqlparser/v2/ast"

func filterTypeNameOfType(typeName *ast.Type) string {
	return typeName.NamedType + "Filter"
}

func filterTypeNameOfDefinition(def *ast.Definition) string {
	return def.Name + "Filter"
}

func orderByTypeNameOfDefinition(def *ast.Definition) string {
	return def.Name + "OrderBy"
}

func createOrderByType(typeDefinition *ast.Definition, schemaInfo *SchemaInfo) (ast.Definition, bool) {
	orderBy := ast.Definition{
		Kind: ast.InputObject,
		Name: orderByTypeNameOfDefinition(typeDefinition),
	}

loop:
	for _, field := range typeDefinition.Fields {
		if field.Type.NamedType == "" {
			continue
		}
		if primaryID, isFound := schemaInfo.PrimaryKeysByVertices[typeDefinition.Name]; isFound {
			for _, p := range primaryID {
				if p == field.Name && schemaInfo.LegacyPrimaryID[typeDefinition.Name] {
					continue loop
				}
			}
		}
		if !isPrimitveType(field.Type.NamedType) {
			continue
		}
		f := &ast.FieldDefinition{
			Name: field.Name,
			Type: &ast.Type{
				NamedType: "OrderBy",
			},
		}

		orderBy.Fields = append(orderBy.Fields, f)
	}

	if len(orderBy.Fields) > 0 {
		return orderBy, true
	}

	return orderBy, false
}

// The filter type for a vertex/edge typed field
// The overall shape is
/*
	input SomeVertexFilter {
		...all attributes of this vertex type...
	}
*/
func createFilterType(typeDefinition *ast.Definition, schemaInfo *SchemaInfo) ast.Definition {
	filter := ast.Definition{
		Kind: ast.InputObject,
		Name: filterTypeNameOfDefinition(typeDefinition),
	}
loop:
	for _, field := range typeDefinition.Fields {
		if primaryID, isFound := schemaInfo.PrimaryKeysByVertices[typeDefinition.Name]; isFound {
			for _, p := range primaryID {
				if p == field.Name && schemaInfo.LegacyPrimaryID[typeDefinition.Name] {
					continue loop
				}
			}
		}
		f := &ast.FieldDefinition{
			Name: field.Name,
			Type: &ast.Type{
				NamedType: field.Type.NamedType,
			},
		}
		if f.Type.NamedType != "" {
			f.Type.NamedType = filterTypeNameOfType(f.Type)
		} else if field.Type.Elem != nil {
			f.Type.NamedType = filterTypeNameOfType(field.Type.Elem)
		}
		filter.Fields = append(filter.Fields, f)
	}
	filter.Fields = append(filter.Fields,
		&ast.FieldDefinition{
			Name: "_and",
			Type: &ast.Type{
				NamedType: "",
				Elem: &ast.Type{
					NamedType: filter.Name,
				},
			},
		},
		&ast.FieldDefinition{
			Name: "_or",
			Type: &ast.Type{
				NamedType: "",
				Elem: &ast.Type{
					NamedType: filter.Name,
				},
			},
		},
		&ast.FieldDefinition{
			Name: "_not",
			Type: &ast.Type{
				NamedType: filter.Name,
			},
		},
	)
	return filter
}
