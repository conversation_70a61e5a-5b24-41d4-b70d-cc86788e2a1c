// Go client that connects to GSQL server

package client

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/log"
)

/*
Example:
curl --fail -u tigergraph:tigergraph -X POST "http://localhost:14240/gsql/v1/queries/interpret?a=10" \
-d 'INTERPRET QUERY (INT a) FOR GRAPH ldbc_snb {
    PRINT a;
}'
{"version":{"edition":"enterprise","api":"v2","schema":0},"error":false,"message":"","results":[{"a":10}]}
*/

const InterpretedQueryURL = "/gsql/v1/queries/interpret"

type InterpretedQueryResult struct {
	Version struct {
		Edition string `json:"edition"`
		API     string `json:"api"`
		Schema  int    `json:"schema"`
	} `json:"version"`
	Error   bool   `json:"error"`
	Message string `json:"message"`
	// An array of json object. Because Go doesn't have a natural JSON data structure, use interface{} as place holder
	Results interface{} `json:"results"`
}

func (r *InterpretedQueryResult) JSON() (string, error) {
	b, err := json.Marshal(r)
	return string(b), err
}

func (r *InterpretedQueryResult) HasError() bool {
	if r == nil {
		return false
	}
	return r.Error
}

type Client struct {
	Host               string
	AuthorizationToken string
	SkipSSL            bool
}

func (c Client) RunInterpretedQuery(ctx context.Context, query string, arguments url.Values) (*InterpretedQueryResult, error) {
	// construct client
	client := http.Client{Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: c.SkipSSL},
	}}

	// construct request
	args := arguments.Encode()
	args = strings.Replace(args, "+", "%20", -1) // space must be encoded as %20 instead of +
	request, err := http.NewRequestWithContext(ctx, http.MethodPost, c.Host+InterpretedQueryURL+"?"+args, strings.NewReader(query))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	request.Header.Set("Content-Type", "text/plain")
	request.Header.Set("Authorization", c.AuthorizationToken)
	if timeout := ctx.Value("GSQL-TIMEOUT"); timeout != nil {
		timeoutStr, ok := timeout.(string)
		if ok && timeoutStr != "" {
			request.Header.Set("GSQL-TIMEOUT", timeoutStr)
		}
	}

	// do the request
	response, err := client.Do(request)
	defer func() {
		if response == nil {
			log.Info("the client might closed the connection")
			return
		}
		if response.Body == nil {
			return
		}
		err := response.Body.Close()
		if err != nil {
			log.Error(err)
		}
	}()
	if err != nil {
		log.Info(ctx.Err())
		return nil, errors.WithStack(err)
	}

	// read response body
	bytes, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// check status code
	if response.StatusCode != http.StatusOK {
		return nil, errors.Errorf("status %+v, body %+v", response.StatusCode, string(bytes))
	}

	var result InterpretedQueryResult
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		return nil, errors.WithMessagef(err, "can not unmarshal %+v", string(bytes))
	}
	if result.Error {
		return nil, errors.WithStack(&QueryError{Query: query, Msg: result.Message})
	}
	return &result, nil
}

// TDB
func (c Client) RunInterpretedQueryWithParams(ctx context.Context, query string, queryParams []string) (*InterpretedQueryResult, error) {
	// construct client
	client := http.Client{Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: c.SkipSSL},
	}}

	// construct url arguments from queryParams
	queryParamsUrlValues := url.Values{}
	for i, p := range queryParams {
		queryParamsUrlValues.Set(fmt.Sprintf("v%d", i), p)
	}
	args := queryParamsUrlValues.Encode()
	args = strings.Replace(args, "+", "%20", -1)

	// construct request
	url := c.Host + InterpretedQueryURL
	if len(queryParams) != 0 {
		url += "?" + args
	}
	request, err := http.NewRequestWithContext(ctx, http.MethodPost, url, strings.NewReader(query))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	request.Header.Set("Content-Type", "text/plain")
	request.Header.Set("Authorization", c.AuthorizationToken)
	if timeout := ctx.Value("GSQL-TIMEOUT"); timeout != nil {
		timeoutStr, ok := timeout.(string)
		if ok && timeoutStr != "" {
			request.Header.Set("GSQL-TIMEOUT", timeoutStr)
		}
	}

	// do the request
	response, err := client.Do(request)
	defer func() {
		if response == nil {
			log.Info("the client might closed the connection")
			return
		}
		if response.Body == nil {
			return
		}
		err := response.Body.Close()
		if err != nil {
			log.Error(err)
		}
	}()
	if err != nil {
		log.Info(ctx.Err())
		return nil, errors.WithStack(err)
	}

	// read response body
	bytes, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// check status code
	if response.StatusCode != http.StatusOK {
		return nil, errors.Errorf("status %+v, body %+v", response.StatusCode, string(bytes))
	}

	var result InterpretedQueryResult
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		return nil, errors.WithMessagef(err, "can not unmarshal %+v", string(bytes))
	}
	if result.Error {
		return nil, errors.WithStack(&QueryError{Query: query, Msg: result.Message})
	}
	return &result, nil
}

type QueryError struct {
	Query string
	Msg   string
}

func (err *QueryError) Error() string {
	return err.Msg
}
