// Go client that connects to GSQL server

package client

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql"
	"github.com/tigergraph/graphql/log"
)

// JSON response of REST API /gsql/v1/schema/graphs
type GsqlSchemaResponse struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Results gsql.Schema `json:"results"`
}

func GetSchema(
	ctx context.Context, insecureSkipVerify bool, host string, authorizationToken string, graphName string,
) (*GsqlSchemaResponse, error) {
	// construct client
	client := http.Client{Transport: &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: insecureSkipVerify},
	}}

	// construct request
	request, err := http.NewRequestWithContext(ctx, http.MethodGet, host+"/gsql/v1/schema/graphs/"+graphName, nil)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	request.Header.Set("Authorization", authorizationToken)

	// do the request
	response, err := client.Do(request)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	defer func() {
		err := response.Body.Close()
		if err != nil {
			log.Error(err)
		}
	}()

	// read response body
	bytes, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// check status code
	if response.StatusCode != http.StatusOK {
		return nil, errors.Errorf("status %+v, body %+v", response.StatusCode, string(bytes))
	}

	var result GsqlSchemaResponse
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		log.Warn(err.Error(), string(bytes))
		errMsg := fmt.Sprintf("Graph name %v cannot be found. Please provide a valid graph name", graphName)
		if strings.Contains(string(bytes), errMsg) {
			return nil, errors.New(errMsg)
		}
		return nil, errors.WithMessagef(err, "can not unmarshal %+v", string(bytes))
	}
	return &result, nil
}
