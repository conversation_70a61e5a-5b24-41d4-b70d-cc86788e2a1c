package gsql

import (
	"sort"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql/builtin"
	"github.com/vektah/gqlparser/v2"
	"github.com/vektah/gqlparser/v2/ast"
	"github.com/vektah/gqlparser/v2/gqlerror"
)

const (
	EdgeCollectionTypeName   = "Edges"
	VertexCollectionTypeName = "Vertices"
)

func contains(slice []string, name string) bool {
	for _, s := range slice {
		if s == name {
			return true
		}
	}
	return false
}

func removeAttrByIndex(slice []Attribute, s int) []Attribute {
	return append(slice[:s], slice[s+1:]...)
}

func MergeSchema(schema1, schema2 *ast.Schema) *ast.Schema {
	for _, def := range schema2.Types {
		schema1.Types[def.Name] = def
	}
	schema1.Query = schema2.Query
	schema1.Mutation = schema2.Mutation
	return schema1
}

func BuildSchemaFromSource(schemas ...string) (schema *ast.Schema, gqlerr *gqlerror.Error) {
	schema, err := gqlparser.LoadSchema(&ast.Source{
		Input: strings.Join(schemas, "\n"),
	})

	// https://github.com/vektah/gqlparser/blob/v2.5.14/gqlparser.go#L13-L23
	// If there is error, it will always be the type *gqlerror.Error
	if err != nil {
		gqlerr = err.(*gqlerror.Error)
	}

	return
}

func isEdge(schema *ast.Schema, t *ast.Type) bool {
	edgeCollection := schema.Types[EdgeCollectionTypeName]
	if edgeCollection == nil {
		return false
	}
	for _, field := range edgeCollection.Fields {
		if field.Type.Name() == t.Name() {
			return true
		}
	}
	return false
}

func generateWhereServiceField(fields ast.FieldList, whereName string) ast.FieldList {
	whereFieldList := make(ast.FieldList, 0)
	for _, t := range fields {
		if t.Name == "_or" || t.Name == "_and" {
			pred := ast.FieldDefinition{
				Name: t.Name,
				Type: &ast.Type{
					NamedType: "",
					Elem: &ast.Type{
						NamedType: whereName,
					},
				},
			}
			whereFieldList = append(whereFieldList, &pred)
		} else if t.Name == "_not" {
			pred := ast.FieldDefinition{
				Name: t.Name,
				Type: &ast.Type{
					NamedType: whereName,
				},
			}
			whereFieldList = append(whereFieldList, &pred)
		}
	}
	return whereFieldList
}

func generateMutationEntry(schema *ast.Schema, queryFields ast.FieldList, schemaInfo *SchemaInfo) ast.FieldList {
	var mutationFieldList ast.FieldList

	for _, v := range queryFields {
		// main entry type for mutations
		var fieldDef = ast.FieldDefinition{
			Name: v.Name,
			Type: &ast.Type{
				NamedType: "mutation_" + v.Type.NamedType,
			},
		}
		mutationFieldList = append(mutationFieldList, &fieldDef)
		schema.Types["mutation_"+v.Name] = &ast.Definition{
			Kind: ast.Object,
			Name: "mutation_" + v.Name,
		}
		// !main entry type for mutations

		var mutationsFieldList = make(ast.FieldList, 0)
		// Mutation generation for Vertices
		sort.Slice(schema.Types[VertexCollectionTypeName].Fields, func(i, j int) bool {
			return schema.Types[VertexCollectionTypeName].Fields[i].Name < schema.Types[VertexCollectionTypeName].Fields[j].Name
		})
		for _, f := range schema.Types[VertexCollectionTypeName].Fields {
			// fields list for mutation objects
			var returningFields = make(ast.FieldList, 0)
			var edges = make(ast.FieldList, 0)
			for _, returningField := range schema.Types[f.Name].Fields {
				if isEdge(schema, returningField.Type) {
					edges = append(edges, returningField)
					continue
				}
				returningFields = append(returningFields, &ast.FieldDefinition{
					Name: returningField.Name,
					Type: returningField.Type,
				})
			}
			// !fields list for mutation objects

			// generate input/output/where objects
			inputName, outputName := generateInputOutputObjects(f, schema, returningFields)
			whereName := generateWhereArgForMutations(f, schema, schemaInfo)
			legacyPrimaryIdType := getLegacyPrimaryIdType(f, schema, schemaInfo)

			// inserts
			mutationsFieldList = append(mutationsFieldList, generateInsertMutationEntry(f.Name, inputName, outputName))
			// !inserts

			// deletes
			mutationsFieldList = append(mutationsFieldList, generateDeleteMutationEntry(f.Name, inputName, outputName, whereName))
			if len(legacyPrimaryIdType) > 0 {
				mutationsFieldList = append(mutationsFieldList, generateDeleteVertexByIdMutationEntry(f.Name, legacyPrimaryIdType, outputName))
			}
			// !deletes

			// TODO: updates
			// !TODO: updates

			// edges routine
			for _, edge := range edges {
				needDeleteByID, from, to := needGenerateDeleteByIdEdge(edge, schema, f, schemaInfo)
				edgeInput, edgeOutput := generateInputOutputObjectsForEdges(edge, schema, f, returningFields, schemaInfo)
				edgeWhereName := generateEdgeWhereArgForMutations(edge, f, schema, needDeleteByID)

				// inserts
				mutationsFieldList = append(mutationsFieldList, generateInsertMutationEntry(edge.Type.Elem.NamedType, edgeInput, edgeOutput))
				// !inserts

				// deletes
				mutationsFieldList = append(mutationsFieldList, generateDeleteMutationEntry(edge.Type.Elem.NamedType, edgeInput, edgeOutput, edgeWhereName))

				if needDeleteByID {
					mutationsFieldList = append(mutationsFieldList, generateDeleteEdgeByIdMutationEntry(edge.Type.Elem.NamedType, from, to, schemaInfo, schema, edgeOutput))
				}
				// !deletes
			}
		}

		schema.Types["mutation_"+v.Name].Fields = mutationsFieldList
	}

	return mutationFieldList
}

func needGenerateDeleteByIdEdge(edge *ast.FieldDefinition, schema *ast.Schema, fromVertext *ast.FieldDefinition, schemaInfo *SchemaInfo) (res bool, from *ast.FieldDefinition, to *ast.FieldDefinition) {
	res = false
	from = fromVertext

	if schemaInfo.LegacyPrimaryID[fromVertext.Name] {
		res = true
	}

	for _, edgeField := range schema.Types[edge.Type.Elem.NamedType].Fields {
		if edgeField.Name == "to" {
			if schemaInfo.LegacyPrimaryID[edgeField.Type.NamedType] {
				res = true
			}

			for _, v := range schema.Types[VertexCollectionTypeName].Fields {
				if v.Name == edgeField.Type.NamedType {
					to = v
					break
				}
			}

			break
		}
	}

	return res, from, to
}

func generateInputOutputObjectsForEdges(edge *ast.FieldDefinition, schema *ast.Schema, fromVertex *ast.FieldDefinition, returningFields ast.FieldList, schemaInfo *SchemaInfo) (edgeInput string, edgeOutput string) {
	edgeInput = "mutation_" + edge.Type.Elem.NamedType + "_input"
	edgeOutput = "mutation_" + edge.Type.Elem.NamedType + "_output"

	var edgeInputFields = make(ast.FieldList, 0)
	var edgeOutputFields = make(ast.FieldList, 0)

	for _, edgeField := range schema.Types[edge.Type.Elem.NamedType].Fields {
		if edgeField.Name == "to" {
			// to
			toObjectInput := "mutation_" + edge.Type.Elem.NamedType + "_to_input"
			toObjectOutput := "mutation_" + edge.Type.Elem.NamedType + "_to_output"
			edgeInputFields = append(edgeInputFields, &ast.FieldDefinition{
				Name: "to",
				Type: &ast.Type{
					NamedType: toObjectInput,
				},
			})

			edgeOutputFields = append(edgeOutputFields, &ast.FieldDefinition{
				Name: "to",
				Type: &ast.Type{
					NamedType: toObjectOutput,
				},
			})

			toFields := make(ast.FieldList, 0)

			for _, toField := range schema.Types[edgeField.Type.NamedType].Fields {
				if primaryID, isPrimaryOnly := schemaInfo.PrimaryKeysByVertices[edgeField.Type.NamedType]; isPrimaryOnly {
					for _, p := range primaryID {
						if p == toField.Name {
							toFields = append(toFields, toField)
						}
					}
				}
			}
			schema.Types[toObjectInput] = &ast.Definition{
				Name:   toObjectInput,
				Fields: toFields,
				Kind:   ast.InputObject,
			}

			schema.Types[toObjectOutput] = &ast.Definition{
				Name:   toObjectOutput,
				Fields: toFields,
				Kind:   ast.Object,
			}

			// !to

			// from
			fromObjectInput := "mutation_" + edge.Type.Elem.NamedType + "_from_input"
			fromObjectOutput := "mutation_" + edge.Type.Elem.NamedType + "_from_output"
			edgeInputFields = append(edgeInputFields, &ast.FieldDefinition{
				Name: "from",
				Type: &ast.Type{
					NamedType: fromObjectInput,
				},
			})

			edgeOutputFields = append(edgeOutputFields, &ast.FieldDefinition{
				Name: "from",
				Type: &ast.Type{
					NamedType: fromObjectOutput,
				},
			})

			fromFields := make(ast.FieldList, 0)

			for _, fromField := range returningFields {
				if primaryID, isPrimaryOnly := schemaInfo.PrimaryKeysByVertices[fromVertex.Name]; isPrimaryOnly {
					for _, p := range primaryID {
						if p == fromField.Name {
							fromFields = append(fromFields, fromField)
						}
					}
				}
			}

			schema.Types[fromObjectInput] = &ast.Definition{
				Name:   fromObjectInput,
				Fields: fromFields,
				Kind:   ast.InputObject,
			}

			schema.Types[fromObjectOutput] = &ast.Definition{
				Name:   fromObjectOutput,
				Fields: fromFields,
				Kind:   ast.Object,
			}

			// !from

		} else {
			edgeInputFields = append(edgeInputFields, edgeField)
			edgeOutputFields = append(edgeOutputFields, edgeField)
		}
	}

	schema.Types[edgeInput] = &ast.Definition{
		Kind:   ast.InputObject,
		Name:   edgeInput,
		Fields: edgeInputFields,
	}

	schema.Types[edgeOutput] = &ast.Definition{
		Kind:   ast.Object,
		Name:   edgeOutput,
		Fields: edgeOutputFields,
	}
	return edgeInput, edgeOutput

}

func generateInputOutputObjects(object *ast.FieldDefinition, schema *ast.Schema, returningFields ast.FieldList) (input string, output string) {
	input = "mutation_" + object.Name + "_input"
	output = "mutation_" + object.Name + "_output"
	schema.Types[input] = &ast.Definition{
		Kind:   ast.InputObject,
		Name:   input,
		Fields: returningFields,
	}

	schema.Types[output] = &ast.Definition{
		Kind:   ast.Object,
		Name:   output,
		Fields: returningFields,
	}

	return input, output
}

func getLegacyPrimaryIdType(object *ast.FieldDefinition, schema *ast.Schema, schemaInfo *SchemaInfo) string {
	for _, f := range schema.Types[object.Name].Fields {
		// Legacy Primary ID can be just single valued
		if schemaInfo.LegacyPrimaryID[object.Name] {
			for _, primaryIdField := range schemaInfo.PrimaryKeysByVertices[object.Name] {
				if primaryIdField == f.Name {
					return f.Type.Name()
				}
			}
		}
	}
	return ""
}

func generateEdgeWhereArgForMutations(edge *ast.FieldDefinition, vertext *ast.FieldDefinition, schema *ast.Schema, needDeleteByID bool) string {
	for _, v := range edge.Arguments {
		if v.Name == "where" {
			whereName := edge.Name + "Mutation"
			whereFieldList := make(ast.FieldList, 0)

			for _, t := range schema.Types[edge.Type.Elem.NamedType].Fields {
				if t.Name == "to" {
					continue

					/*
						Temporarly disabled as we don't fully support filter for from/to's fields
						if needDeleteByID {
							continue
						}

						f := &ast.FieldDefinition{
							Name: "to",
							Type: &ast.Type{
								NamedType: t.Type.NamedType + "FilterMutation", // already defined filter for vertices
							},
						}
						whereFieldList = append(whereFieldList, f)
					*/
				} else {
					f := &ast.FieldDefinition{
						Name: t.Name,
						Type: &ast.Type{
							NamedType: t.Type.NamedType,
						},
					}
					if f.Type.NamedType != "" {
						f.Type.NamedType = filterTypeNameOfType(f.Type)
					} else if t.Type.Elem != nil {
						f.Type.NamedType = filterTypeNameOfType(t.Type.Elem)
					}

					whereFieldList = append(whereFieldList, f)
				}
			}

			/*
				Temporarly disabled as we don't fully support filter for from/to's fields
				if !needDeleteByID {
					// adding from filter
					f := &ast.FieldDefinition{
						Name: "from",
						Type: &ast.Type{
							NamedType: vertext.Name + "FilterMutation", // already defined filter for vertices
						},
					}
					whereFieldList = append(whereFieldList, f)
				}
			*/

			whereFieldList = append(whereFieldList, generateWhereServiceField(schema.Types[v.Type.NamedType].Fields, whereName)...)

			schema.Types[whereName] = &ast.Definition{
				Name:   whereName,
				Fields: whereFieldList,
				Kind:   ast.InputObject,
			}

			return whereName
		}
	}

	return ""
}

func generateWhereArgForMutations(object *ast.FieldDefinition, schema *ast.Schema, schemaInfo *SchemaInfo) string {
	for _, v := range object.Arguments {
		if v.Name == "where" {
			whereName := v.Type.NamedType + "Mutation"
			whereFieldList := make(ast.FieldList, 0)

			// generating filter fields including PK fields
			for _, t := range schema.Types[object.Name].Fields {
				if isEdge(schema, t.Type) {
					continue
				}
				if contains(schemaInfo.PrimaryKeysByVertices[object.Name], t.Name) && schemaInfo.LegacyPrimaryID[object.Name] {
					// We don't allow to specify single attribute Primary IDs to support legacy Primary IDs
					continue
				}
				f := &ast.FieldDefinition{
					Name: t.Name,
					Type: &ast.Type{
						NamedType: t.Type.NamedType,
					},
				}
				if f.Type.NamedType != "" {
					f.Type.NamedType = filterTypeNameOfType(f.Type)
				} else if t.Type.Elem != nil {
					f.Type.NamedType = filterTypeNameOfType(t.Type.Elem)
				}

				whereFieldList = append(whereFieldList, f)
			}

			whereFieldList = append(whereFieldList, generateWhereServiceField(schema.Types[v.Type.NamedType].Fields, whereName)...)

			schema.Types[whereName] = &ast.Definition{
				Name:   whereName,
				Fields: whereFieldList,
				Kind:   ast.InputObject,
			}
			return whereName
		}
	}
	return ""
}

func generateInsertMutationEntry(objectName string, input string, output string) *ast.FieldDefinition {
	var mutationArguments = make(ast.ArgumentDefinitionList, 0)
	mutationArguments = append(mutationArguments, &ast.ArgumentDefinition{
		Name: "objects",
		Type: &ast.Type{
			NamedType: "",
			Elem: &ast.Type{
				NamedType: input,
			},
		},
	})

	return &ast.FieldDefinition{
		Name: "insert_" + objectName,
		Type: &ast.Type{
			NamedType: "",
			Elem: &ast.Type{
				NamedType: output,
			},
		},
		Arguments: mutationArguments,
	}
}

func generateDeleteMutationEntry(objectName string, input string, output string, whereName string) *ast.FieldDefinition {
	var mutationArguments = make(ast.ArgumentDefinitionList, 0)
	mutationArguments = append(mutationArguments, &ast.ArgumentDefinition{
		Name: "where",
		Type: &ast.Type{
			NamedType: whereName,
		},
	})

	return &ast.FieldDefinition{
		Name: "delete_" + objectName,
		Type: &ast.Type{
			NamedType: "",
			Elem: &ast.Type{
				NamedType: output,
			},
		},
		Arguments: mutationArguments,
	}
}

func generateDeleteEdgeByIdMutationEntry(objectName string, from *ast.FieldDefinition, to *ast.FieldDefinition, schemaInfo *SchemaInfo, schema *ast.Schema, output string) *ast.FieldDefinition {
	fromFieldList := make(ast.FieldList, 0)
	for _, f := range schema.Types[from.Name].Fields {
		if contains(schemaInfo.PrimaryKeysByVertices[from.Name], f.Name) {
			fromFieldList = append(fromFieldList, f)
		}
	}

	fromObject := &ast.FieldDefinition{
		Name: "from",
		Type: &ast.Type{
			NamedType: objectName + "FromIds",
		},
	}

	schema.Types[objectName+"FromIds"] = &ast.Definition{
		Name:   objectName + "FromIds",
		Fields: fromFieldList,
		Kind:   ast.InputObject,
	}

	toFieldList := make(ast.FieldList, 0)
	for _, f := range schema.Types[to.Name].Fields {
		if contains(schemaInfo.PrimaryKeysByVertices[to.Name], f.Name) {
			toFieldList = append(toFieldList, f)
		}
	}

	toObject := &ast.FieldDefinition{
		Name: "to",
		Type: &ast.Type{
			NamedType: objectName + "ToIds",
		},
	}

	schema.Types[objectName+"ToIds"] = &ast.Definition{
		Name:   objectName + "ToIds",
		Fields: toFieldList,
		Kind:   ast.InputObject,
	}

	fromToPair := make(ast.FieldList, 0)
	fromToPair = append(fromToPair, fromObject)
	fromToPair = append(fromToPair, toObject)

	schema.Types[objectName+"FromToPair"] = &ast.Definition{
		Name:   objectName + "FromToPair",
		Fields: fromToPair,
		Kind:   ast.InputObject,
	}

	var mutationArguments = make(ast.ArgumentDefinitionList, 0)
	mutationArguments = append(mutationArguments, &ast.ArgumentDefinition{
		Name: "ids",
		Type: &ast.Type{
			NamedType: "",
			Elem: &ast.Type{
				NamedType: objectName + "FromToPair",
			},
		},
	})

	return &ast.FieldDefinition{
		Name: "delete_by_id_" + objectName,
		Type: &ast.Type{
			NamedType: "",
			Elem: &ast.Type{
				NamedType: output,
			},
		},
		Arguments: mutationArguments,
	}
}

func generateDeleteVertexByIdMutationEntry(objectName string, legacyPrimaryIdType string, output string) *ast.FieldDefinition {
	var mutationArguments = make(ast.ArgumentDefinitionList, 0)
	mutationArguments = append(mutationArguments, &ast.ArgumentDefinition{
		Name: "ids",
		Type: &ast.Type{
			NamedType: "",
			Elem: &ast.Type{
				NamedType: legacyPrimaryIdType,
			},
		},
	})

	return &ast.FieldDefinition{
		Name: "delete_by_id_" + objectName,
		Type: &ast.Type{
			NamedType: "",
			Elem: &ast.Type{
				NamedType: output,
			},
		},
		Arguments: mutationArguments,
	}
}

func (gsqlSchema *Schema) ToGraphQLSchema(graphName string, schemaInfo *SchemaInfo) (*ast.Schema, error) {

	schema, err := buildVerticesAndEdges(gsqlSchema, graphName, schemaInfo)
	if err != nil {
		return nil, err
	}
	if len(gsqlSchema.EdgeTypes) > 0 {
		schema, err = connectVerticesWithEdges(schema, graphName)
		if err != nil {
			return nil, err
		}
	}
	schema, err = constructParameters(schema, schemaInfo)
	if err != nil {
		return nil, err
	}

	// Remove `from` fields from edge types
	for _, gqlType := range schema.Types {
		if isEdgeDefinition(schema, gqlType) {
			gqlType.Fields = removeField(gqlType.Fields, "from")
		}
	}

	// Create root Query
	m, err := pickFromMap(schema.Types, graphName)
	if err != nil {
		return nil, err
	}
	queryFields := definitionMapToFieldList(m)

	schema.Query = &ast.Definition{
		Kind:   ast.Object,
		Name:   "Query",
		Fields: queryFields,
	}
	schema.Types["Query"] = schema.Query

	var mutationFieldList = generateMutationEntry(schema, queryFields, schemaInfo)

	schema.Mutation = &ast.Definition{
		Kind:   ast.Object,
		Name:   "Mutation",
		Fields: mutationFieldList,
	}

	schema.Types["Mutation"] = schema.Mutation

	finalSchema := MergeSchema(builtin.Schema(), schema)

	createPossibleTypes(finalSchema)

	// Deduplicate fields
	for _, def := range finalSchema.Types {
		fieldDefMap := fieldListToFieldDefinitionMap(def.Fields)
		def.Fields = fieldDefinitionMapToFieldList(fieldDefMap)
	}

	// Sort fields
	for _, def := range finalSchema.Types {
		sort.Slice(def.Fields, func(i int, j int) bool {
			return def.Fields[i].Name < def.Fields[j].Name
		})
	}

	// f := formatter.NewFormatter(os.Stdout)
	// f.FormatSchema(finalSchema)
	return finalSchema, nil
}

func (gsqlSchema *Schema) GenerateMapping(schemaInfo *SchemaInfo) {
	schemaInfo.AttributeMap = make(map[string][]Attribute)
	schemaInfo.AllAttributeMap = make(map[string][]Attribute)

	for _, v := range gsqlSchema.VertexTypes {
		schemaInfo.AttributeMap[v.Name] = v.Attributes
		schemaInfo.AllAttributeMap[v.Name] = v.Attributes
		schemaInfo.AllAttributeMap[v.Name] = append(schemaInfo.AllAttributeMap[v.Name], v.PrimaryID)
	}
	for _, e := range gsqlSchema.EdgeTypes {
		schemaInfo.AttributeMap[e.Name] = e.Attributes
		schemaInfo.AllAttributeMap[e.Name] = e.Attributes

	}
}

func buildVerticesAndEdges(gsqlSchema *Schema, graphName string, schemaInfo *SchemaInfo) (*ast.Schema, error) {
	if len(gsqlSchema.VertexTypes) == 0 {
		return nil, errors.New("GSQL Schema does not have vertices")
	}
	gsqlSchema = mapBuiltInTypes(*gsqlSchema)

	schema := ast.Schema{}
	schema.Types = make(map[string]*ast.Definition)

	// Create individual vertex type
	verticesMap := verticesToDefinitionMap(gsqlSchema.VertexTypes, schemaInfo)
	// create Vertices type
	vertices := definitionMapToGraphQLType(graphName, verticesMap, "The collection of all vertices in this graph", Vertex)
	// put type Vertices into the type map
	schema.Types[vertices.Name] = vertices

	vertices = definitionMapToGraphQLType(VertexCollectionTypeName, verticesMap, "The collection of all vertices in this graph", Vertex)
	schema.Types[VertexCollectionTypeName] = vertices

	// put individual vertex type and their input types into the type map
	for name, def := range verticesMap {
		if name == "*" {
			panic(name)
		}
		schema.Types[name] = def
	}

	// Create individual edge type
	if len(gsqlSchema.EdgeTypes) == 0 {
		return &schema, nil
	}
	edgesMap := edgesToDefinitionMap(gsqlSchema.EdgeTypes, schemaInfo)
	for name, def := range edgesMap {
		if name == "*" {
			panic(name)
		}
		schema.Types[name] = def
	}
	// create Edges type
	edgesType := definitionMapToGraphQLType(EdgeCollectionTypeName, edgesMap, "The collection of all edges in this schema", Edge)
	// put type edges into the type map
	schema.Types[edgesType.Name] = edgesType

	// put individual edge type into the type map
	for name, def := range edgesMap {
		if name == "*" {
			panic(name)
		}
		schema.Types[name] = def
	}
	return &schema, nil
}

func connectVerticesWithEdges(schema *ast.Schema, graphName string) (*ast.Schema, error) {
	// Add edges to vertices
	edges := schema.Types[EdgeCollectionTypeName]
	for _, field := range edges.Fields {
		eachEdge := schema.Types[field.Name]
		fromVertexField := eachEdge.Fields.ForName("from")
		if fromVertexField == nil {
			return nil, errors.Errorf("edge %s does not have 'from' field", eachEdge.Name)
		}
		verticeName := fromVertexField.Type.Name()

		fromVertex, ok := schema.Types[verticeName]
		if !ok {
			return nil, errors.Errorf("GraphQL Type %s does not exit", verticeName)
		}
		fieldName := eachEdge.Name
		// If the field is an edge of a set of multi edge pairs, remove the "From_vertexName_" prefix
		prefixLength := 5 + len(fromVertex.Name) + 1
		if prefixLength <= len(fieldName) && fieldName[:prefixLength] == "From_"+fromVertex.Name+"_" {
			fieldName = fieldName[prefixLength:]
		}
		fromVertex.Fields = append(fromVertex.Fields, &ast.FieldDefinition{
			Name: fieldName,
			Type: ast.ListType(&ast.Type{NamedType: eachEdge.Name}, nil),
		})
	}

	// check that the schema doesn't contain any input types yet
	for _, gqlType := range schema.Types {
		if gqlType.Kind == ast.InputObject {
			return nil, errors.Errorf("should not contain input types in Connection stage: %s", gqlType.Name)
		}
		// check that no fields contain arguments
		for _, field := range gqlType.Fields {
			if len(field.Arguments) > 0 {
				return nil, errors.Errorf("should not contain arguments in Connection stage: %s %+v", gqlType.Name, field.Arguments[0])
			}
		}
	}

	return schema, nil
}

func constructParameters(schema *ast.Schema, schemaInfo *SchemaInfo) (*ast.Schema, error) {
	isVertex := func(t *ast.Type) bool {
		vertexCollection := schema.Types[VertexCollectionTypeName]
		for _, field := range vertexCollection.Fields {
			if field.Type.Name() == t.Name() {
				return true
			}
		}
		return false
	}

	isEdge := func(t *ast.Type) bool {
		edgeCollection := schema.Types[EdgeCollectionTypeName]
		if edgeCollection == nil {
			return false
		}
		for _, field := range edgeCollection.Fields {
			if field.Type.Name() == t.Name() {
				return true
			}
		}
		return false
	}

	getDefOfType := func(t *ast.Type) *ast.Definition {
		for _, def := range schema.Types {
			if def.Name == t.Name() {
				return def
			}
		}
		return nil
	}

	// construct input types
	// Vertices
	vertexCollection := schema.Types[VertexCollectionTypeName]
	for _, field := range vertexCollection.Fields {
		vertexTypeName := field.Type.Name()
		vertexType := schema.Types[vertexTypeName]
		// where clause
		whereClause := createFilterType(vertexType, schemaInfo)
		schema.Types[whereClause.Name] = &whereClause
		// order by
		orderBy, orderByFound := createOrderByType(vertexType, schemaInfo)
		if orderByFound {
			schema.Types[orderBy.Name] = &orderBy
		}
	}
	// Edges
	// If edges exist in the graph
	if schema.Types[EdgeCollectionTypeName] != nil {
		for _, field := range schema.Types[EdgeCollectionTypeName].Fields {
			edgeTypeName := field.Type.Name()
			edgeType := schema.Types[edgeTypeName]
			// where cluase
			whereClause := createFilterType(edgeType, schemaInfo)
			schema.Types[whereClause.Name] = &whereClause
		}
	}

	// construct field arguments
	for _, gqlType := range schema.Types {
		if gqlType.Kind != ast.Object {
			continue
		}
		for _, field := range gqlType.Fields {
			// For all fields whose type is a vertex, add where & whereExp
			if isVertex(field.Type) && field.Name != "from" {
				field.Arguments = ast.ArgumentDefinitionList{
					&ast.ArgumentDefinition{
						Name: "where",
						Type: &ast.Type{
							NamedType: filterTypeNameOfDefinition(getDefOfType(field.Type)),
						},
					},
					&ast.ArgumentDefinition{
						Name: "whereExpr",
						Type: &ast.Type{
							NamedType: "String",
						},
					},
				}
				if field.Name != "to" { // if it's the top level vertex field
					// as GSQL doesn't have common approach for ordering edges, decided to support ORDER BY just for vertices
					// Also, if vertex doesn't have any available field for ordering, then we don't need to add orderBy operators
					if _, ok := schema.Types[orderByTypeNameOfDefinition(getDefOfType(field.Type))]; !ok {
						continue
					}
					field.Arguments = append(field.Arguments,
						&ast.ArgumentDefinition{
							Name: "order_by",
							Type: &ast.Type{
								NamedType: "",
								Elem: &ast.Type{
									NamedType: orderByTypeNameOfDefinition(getDefOfType(field.Type)),
								},
							},
						},
						&ast.ArgumentDefinition{
							Name: "limit",
							Type: &ast.Type{
								NamedType: "Int64",
							},
						},
						&ast.ArgumentDefinition{
							Name: "offset",
							Type: &ast.Type{
								NamedType: "Int64",
							},
						},
					)
				}
			}
			if isEdge(field.Type) {
				field.Arguments = ast.ArgumentDefinitionList{
					&ast.ArgumentDefinition{
						Name: "where",
						Type: &ast.Type{
							NamedType: filterTypeNameOfDefinition(getDefOfType(field.Type)),
						},
					},
					&ast.ArgumentDefinition{
						Name: "whereExpr",
						Type: &ast.Type{
							NamedType: "String",
						},
					},
				}
			}
		}
	}
	return schema, nil
}

func vertexToGraphQL(vertex VertexType, schemaInfo *SchemaInfo) *ast.Definition {
	var attrs = vertex.Attributes
	if vertex.PrimaryID.IsPartOfCompositeKey {
		for _, p := range vertex.Attributes {
			if p.IsPartOfCompositeKey {
				schemaInfo.PrimaryKeysByVertices[vertex.Name] = append(schemaInfo.PrimaryKeysByVertices[vertex.Name], p.AttributeName)
			}
		}
		schemaInfo.LegacyPrimaryID[vertex.Name] = false
	} else {
		schemaInfo.PrimaryKeysByVertices[vertex.Name] = append(schemaInfo.PrimaryKeysByVertices[vertex.Name], vertex.PrimaryID.AttributeName)
		attrs = append(attrs, vertex.PrimaryID)

		if vertex.PrimaryID.PrimaryIDAsAttribute {
			schemaInfo.LegacyPrimaryID[vertex.Name] = false
		} else {
			schemaInfo.LegacyPrimaryID[vertex.Name] = true
		}
	}

	def := ast.Definition{
		Name:        vertex.Name,
		Kind:        ast.Object,
		Description: "",
		Fields:      vertexAttributesToFields(attrs),
	}
	return &def
}

// Convert an EdgeType to 1 or many GraphQL Type Definitions
//
//  An edge in GSQL can have multiple source and target vertex types.
// In GraphQL we disambiguate all these cases and only create edges with
// specific vertex source and target types.
// Also, when we have an undirected edge between different vertex types, we need
// to disambiguate it as well, so we specifiy the from vertex in the edge name in GraphQL.

func edgeToGraphQL(edge EdgeType, schemaInfo *SchemaInfo) (defs []*ast.Definition) {
	if edge.FromVertexTypeName == "*" || edge.ToVertexTypeName == "*" {
		for _, pair := range edge.EdgePairs {
			def := ast.Definition{
				Name:        "From_" + pair.From + "_" + edge.Name + "_To_" + pair.To,
				Kind:        ast.Object,
				Description: "",
				Fields:      edgeAttributesToFields(edge.Attributes),
			}
			def.Fields = append(def.Fields,
				createField("from", pair.From), // needed for later stage code to connect edges and vertices
				createField("to", pair.To),
			)
			defs = append(defs, &def)
			schemaInfo.EdgeMap[def.Name] = EdgeInfo{FromVertexName: pair.From,
				ToVertexName:   pair.To,
				Name:           edge.Name,
				FromIsWildcard: edge.FromVertexTypeName == "*",
				ToIsWildcard:   edge.ToVertexTypeName == "*",
				IsDirectional:  edge.IsDirected,
				Flipped:        false,
			}

			if !edge.IsDirected && pair.From != pair.To {
				def := ast.Definition{
					Name:        "From_" + pair.To + "_" + edge.Name + "_To_" + pair.From,
					Kind:        ast.Object,
					Description: "",
					Fields:      edgeAttributesToFields(edge.Attributes),
				}
				def.Fields = append(def.Fields,
					createField("from", pair.To), // needed for later stage code to connect edges and vertices
					createField("to", pair.From),
				)
				defs = append(defs, &def)
				schemaInfo.EdgeMap[def.Name] = EdgeInfo{FromVertexName: pair.To,
					ToVertexName:   pair.From,
					Name:           edge.Name,
					FromIsWildcard: edge.FromVertexTypeName == "*",
					ToIsWildcard:   edge.ToVertexTypeName == "*",
					IsDirectional:  edge.IsDirected,
					Flipped:        true,
				}
			}
		}
	} else if !edge.IsDirected && edge.FromVertexTypeName != edge.ToVertexTypeName {
		def := ast.Definition{
			Name:        "From_" + edge.FromVertexTypeName + "_" + edge.Name,
			Kind:        ast.Object,
			Description: "",
			Fields:      edgeAttributesToFields(edge.Attributes),
		}
		def.Fields = append(def.Fields,
			createField("from", edge.FromVertexTypeName), // needed for later stage code to connect edges and vertices
			createField("to", edge.ToVertexTypeName),
		)
		defs = append(defs, &def)
		schemaInfo.EdgeMap[def.Name] = EdgeInfo{FromVertexName: edge.FromVertexTypeName,
			ToVertexName:   edge.ToVertexTypeName,
			Name:           edge.Name,
			FromIsWildcard: edge.FromVertexTypeName == "*",
			ToIsWildcard:   edge.ToVertexTypeName == "*",
			IsDirectional:  edge.IsDirected,
			Flipped:        false,
		}

		def2 := ast.Definition{
			Name:        "From_" + edge.ToVertexTypeName + "_" + edge.Name,
			Kind:        ast.Object,
			Description: "",
			Fields:      edgeAttributesToFields(edge.Attributes),
		}
		def2.Fields = append(def2.Fields,
			createField("from", edge.ToVertexTypeName), // needed for later stage code to connect edges and vertices
			createField("to", edge.FromVertexTypeName),
		)
		defs = append(defs, &def2)
		schemaInfo.EdgeMap[def2.Name] = EdgeInfo{FromVertexName: edge.ToVertexTypeName,
			ToVertexName:   edge.FromVertexTypeName,
			Name:           edge.Name,
			FromIsWildcard: edge.FromVertexTypeName == "*",
			ToIsWildcard:   edge.ToVertexTypeName == "*",
			IsDirectional:  edge.IsDirected,
			Flipped:        true,
		}
	} else {
		def := ast.Definition{
			Name:        edge.Name,
			Kind:        ast.Object,
			Description: "",
			Fields:      edgeAttributesToFields(edge.Attributes),
		}
		def.Fields = append(def.Fields,
			createField("from", edge.FromVertexTypeName), // needed for later stage code to connect edges and vertices
			createField("to", edge.ToVertexTypeName),
		)
		defs = append(defs, &def)
		schemaInfo.EdgeMap[def.Name] = EdgeInfo{FromVertexName: edge.FromVertexTypeName,
			ToVertexName:   edge.ToVertexTypeName,
			Name:           edge.Name,
			FromIsWildcard: edge.FromVertexTypeName == "*",
			ToIsWildcard:   edge.ToVertexTypeName == "*",
			IsDirectional:  edge.IsDirected,
			Flipped:        false,
		}
	}
	return defs
}

func definitionMapToFieldList(defs map[string]*ast.Definition) ast.FieldList {
	var fl ast.FieldList
	for _, def := range defs {
		fl = append(fl, &ast.FieldDefinition{
			Name: def.Name,
			Type: &ast.Type{
				NamedType: def.Name,
			},
		})
	}
	return fl
}

func fieldListToFieldDefinitionMap(list ast.FieldList) map[string]*ast.FieldDefinition {
	m := make(map[string]*ast.FieldDefinition)
	for _, fieldDef := range list {
		m[fieldDef.Name] = fieldDef
	}
	return m
}

func fieldDefinitionMapToFieldList(m map[string]*ast.FieldDefinition) ast.FieldList {
	var fl ast.FieldList
	for _, def := range m {
		fl = append(fl, def)
	}
	return fl
}

func vertexAttributesToFields(attributes []Attribute) ast.FieldList {
	var fl ast.FieldList
	var dups = make(map[string]bool) // here we can occur duplication of attrs between attributes and pk attributes
	for _, attr := range attributes {

		if _, ok := dups[attr.AttributeName]; ok {
			continue
		} else {
			dups[attr.AttributeName] = true
		}
		f := &ast.FieldDefinition{
			Name:        attr.AttributeName,
			Description: "",
			Type:        &ast.Type{},
		}
		if attr.AttributeType.Name == "SET" {
			f.Type.Elem = &ast.Type{
				NamedType: primitiveTypeMap(attr.AttributeType.ValueTypeName),
			}
		} else {
			f.Type.NamedType = attr.AttributeType.Name
		}
		fl = append(fl, f)
	}
	return fl
}

func edgeAttributesToFields(attributes []Attribute) ast.FieldList {
	var fl ast.FieldList
	for _, attr := range attributes {
		f := &ast.FieldDefinition{
			Name:        attr.AttributeName,
			Description: "",
			Type:        &ast.Type{},
		}
		if attr.AttributeType.Name == "SET" {
			f.Type.Elem = &ast.Type{
				NamedType: primitiveTypeMap(attr.AttributeType.ValueTypeName),
			}
		} else {
			f.Type.NamedType = attr.AttributeType.Name
		}
		fl = append(fl, f)
	}
	return fl
}

func removeUnsupportedAttrs(attrsToRemove []string, attributes *[]Attribute) {
	for _, attr := range attrsToRemove {
		indexToDelete := -1
		for ind, el := range *attributes {
			if el.AttributeName == attr {
				indexToDelete = ind
				break
			}
		}

		if indexToDelete != -1 {
			*attributes = removeAttrByIndex(*attributes, indexToDelete)
		}
	}
}

func createField(name string, typeName string) *ast.FieldDefinition {
	if typeName == "*" {
		panic(typeName)
	}
	field := &ast.FieldDefinition{
		Name: name,
		Type: &ast.Type{
			NamedType: typeName,
		},
	}
	return field
}

// pass by value
func mapBuiltInTypes(gsqlSchema Schema) *Schema {
	for i, vertex := range gsqlSchema.VertexTypes {
		switch vertex.PrimaryID.AttributeType.Name {
		case "UINT":
			gsqlSchema.VertexTypes[i].PrimaryID.AttributeType.Name = "Uint64"
		case "INT":
			gsqlSchema.VertexTypes[i].PrimaryID.AttributeType.Name = "Int64"
		case "FLOAT", "DOUBLE":
			gsqlSchema.VertexTypes[i].PrimaryID.AttributeType.Name = "Float"
		case "STRING", "STRING COMPRESS":
			gsqlSchema.VertexTypes[i].PrimaryID.AttributeType.Name = "String"
		case "DATETIME":
			gsqlSchema.VertexTypes[i].PrimaryID.AttributeType.Name = "Datetime"
		case "BOOL":
			gsqlSchema.VertexTypes[i].PrimaryID.AttributeType.Name = "Boolean"
		case "SET":
			gsqlSchema.VertexTypes[i].PrimaryID.AttributeType.Name = "List"
		default:
			// fmt.Println(vertex.PrimaryID.AttributeType.Name)
		}

		var attrsToRemove = make([]string, 0)
		for j, attr := range vertex.Attributes {
			switch attr.AttributeType.Name {
			case "UINT":
				gsqlSchema.VertexTypes[i].Attributes[j].AttributeType.Name = "Uint64"
			case "INT":
				gsqlSchema.VertexTypes[i].Attributes[j].AttributeType.Name = "Int64"
			case "FLOAT", "DOUBLE":
				gsqlSchema.VertexTypes[i].Attributes[j].AttributeType.Name = "Float"
			case "STRING", "STRING COMPRESS":
				gsqlSchema.VertexTypes[i].Attributes[j].AttributeType.Name = "String"
			case "DATETIME":
				gsqlSchema.VertexTypes[i].Attributes[j].AttributeType.Name = "Datetime"
			case "BOOL":
				gsqlSchema.VertexTypes[i].Attributes[j].AttributeType.Name = "Boolean"
			case "MAP", "SET", "LIST", "UDT":
				// need to filter out MAP/SET types
				attrsToRemove = append(attrsToRemove, gsqlSchema.VertexTypes[i].Attributes[j].AttributeName)
			default:
				// fmt.Println(attr.AttributeType.Name)
			}
		}

		removeUnsupportedAttrs(attrsToRemove, &gsqlSchema.VertexTypes[i].Attributes)
	}
	for i, edge := range gsqlSchema.EdgeTypes {

		var attrsToRemove = make([]string, 0)
		for j, attr := range edge.Attributes {
			switch attr.AttributeType.Name {
			case "UINT":
				gsqlSchema.EdgeTypes[i].Attributes[j].AttributeType.Name = "Uint64"
			case "INT":
				gsqlSchema.EdgeTypes[i].Attributes[j].AttributeType.Name = "Int64"
			case "FLOAT", "DOUBLE":
				gsqlSchema.EdgeTypes[i].Attributes[j].AttributeType.Name = "Float"
			case "STRING", "STRING COMPRESS":
				gsqlSchema.EdgeTypes[i].Attributes[j].AttributeType.Name = "String"
			case "DATETIME":
				gsqlSchema.EdgeTypes[i].Attributes[j].AttributeType.Name = "Datetime"
			case "BOOL":
				gsqlSchema.EdgeTypes[i].Attributes[j].AttributeType.Name = "Boolean"
			case "MAP", "SET", "LIST", "UDT":
				// need to filter out MAP/SET types
				attrsToRemove = append(attrsToRemove, gsqlSchema.EdgeTypes[i].Attributes[j].AttributeName)
			default:
				// fmt.Println(attr.AttributeType.Name)
			}
		}

		removeUnsupportedAttrs(attrsToRemove, &gsqlSchema.EdgeTypes[i].Attributes)
	}

	return &gsqlSchema
}

func isPrimitveType(dataType string) bool {
	switch dataType {
	case "Uint64", "Int64", "Float", "String", "Datetime", "Boolean":
		return true
	default:
		return false
	}
}

func primitiveTypeMap(s string) string {
	m := map[string]string{
		"STRING":   "String",
		"INT":      "Int64",
		"UINT":     "Uint64",
		"DATETIME": "Datetime",
		"BOOL":     "Boolean",
	}
	s = m[s]
	return s
}

func verticesToDefinitionMap(verties []VertexType, schemaInfo *SchemaInfo) map[string]*ast.Definition {
	m := make(map[string]*ast.Definition)
	schemaInfo.PrimaryKeysByVertices = make(map[string][]string)
	schemaInfo.LegacyPrimaryID = make(map[string]bool)
	schemaInfo.VertexMap = make(map[string]bool)
	for _, vertex := range verties {
		if vertex.Name == "*" {
			panic(vertex.Name)
		}
		m[vertex.Name] = vertexToGraphQL(vertex, schemaInfo)
		schemaInfo.VertexMap[vertex.Name] = true
	}
	return m
}

func edgesToDefinitionMap(edges []EdgeType, schemaInfo *SchemaInfo) map[string]*ast.Definition {
	m := make(map[string]*ast.Definition)
	schemaInfo.EdgeMap = make(map[string]EdgeInfo)
	for _, edge := range edges {
		for _, edgeType := range edgeToGraphQL(edge, schemaInfo) {
			m[edgeType.Name] = edgeType
		}
		if edge.Config.ReverseEdge != "" {
			var reverseEdge = EdgeType{
				Name:               edge.Config.ReverseEdge,
				FromVertexTypeName: edge.ToVertexTypeName,
				ToVertexTypeName:   edge.FromVertexTypeName,
				Attributes:         edge.Attributes,
				IsDirected:         edge.IsDirected,
				EdgePairs:          make([]EdgePair, 0),
			}
			for _, p := range edge.EdgePairs {
				pair := EdgePair{
					From: p.To,
					To:   p.From,
				}
				reverseEdge.EdgePairs = append(reverseEdge.EdgePairs, pair)
			}
			res := edgeToGraphQL(reverseEdge, schemaInfo)
			for _, reverseEdgeType := range res {
				m[reverseEdgeType.Name] = reverseEdgeType
			}
		}
	}
	return m
}

func definitionMapToGraphQLType(typeName string, defs map[string]*ast.Definition, desp string, nodeType NodeType) *ast.Definition {
	gqlType := &ast.Definition{
		Name:        typeName,
		Kind:        ast.Object,
		Description: desp,
	}

	for defName := range defs {
		field := &ast.FieldDefinition{
			Name: defName,
			Type: ast.ListType(
				&ast.Type{
					NamedType: defName,
				},
				nil,
			),
		}
		gqlType.Fields = append(gqlType.Fields, field)

	}
	return gqlType
}

func pickFromMap(defs map[string]*ast.Definition, names ...string) (map[string]*ast.Definition, error) {
	m := make(map[string]*ast.Definition)
	for _, name := range names {
		def, ok := defs[name]
		if !ok {
			return nil, errors.Errorf("%s does not exit in map", name)
		}
		m[name] = def
	}
	return m, nil
}

func isEdgeDefinition(schema *ast.Schema, def *ast.Definition) bool {
	edgeCollection := schema.Types[EdgeCollectionTypeName]
	if edgeCollection == nil {
		return false
	}
	for _, field := range edgeCollection.Fields {
		if field.Type.Name() == def.Name {
			return true
		}
	}
	return false
}

func removeField(fields ast.FieldList, field string) ast.FieldList {
	index := 0
	for i, f := range fields {
		if f.Name == field {
			index = i
		}
	}
	return append(fields[:index], fields[index+1:]...)
}

func createPossibleTypes(schema *ast.Schema) {
	for _, def := range schema.Types {
		switch def.Kind {
		case ast.Union, ast.InputObject, ast.Object:
			schema.AddPossibleType(def.Name, def)
		}
	}
}
