package gsql

type Schema struct {
	VertexTypes []VertexType `json:"VertexTypes"`
	EdgeTypes   []EdgeType   `json:"EdgeTypes"`
}

type VertexType struct {
	Name       string      `json:"Name"`
	PrimaryID  Attribute   `json:"PrimaryId"`
	Usage      []string    `json:"Usage"`
	Attributes []Attribute `json:"Attributes"`
	Edges      []*EdgeType
	Config     struct {
		Taggable             bool   `json:"TAGGABLE"`
		Stats                string `json:"STATS"`
		PrimaryIDAsAttribute bool   `json:"PRIMARY_ID_AS_ATTRIBUTE"`
	} `json:"Config"`
	IsLocal bool `json:"IsLocal"`
}

type EdgeType struct {
	Name               string      `json:"Name"`
	FromVertexTypeName string      `json:"FromVertexTypeName"`
	ToVertexTypeName   string      `json:"ToVertexTypeName"`
	IsDirected         bool        `json:"IsDirected"`
	Usage              []string    `json:"Usage"`
	Attributes         []Attribute `json:"Attributes"`
	EdgePairs          []EdgePair  `json:"EdgePairs"`
	Config             struct {
		ReverseEdge string `json:"REVERSE_EDGE"`
	} `json:"Config"`
	IsLocal bool `json:"IsLocal"`
}

type Attribute struct {
	AttributeType        AttributeType `json:"AttributeType"`
	IsPartOfCompositeKey bool          `json:"IsPartOfCompositeKey"`
	PrimaryIDAsAttribute bool          `json:"PrimaryIdAsAttribute"`
	AttributeName        string        `json:"AttributeName"`
	HasIndex             bool          `json:"HasIndex"`
	InternalAttribute    bool          `json:"internalAttribute"`
	IsPrimaryKey         bool          `json:"IsPrimaryKey"`
}

type AttributeType struct {
	Name          string `json:"Name"`
	ValueTypeName string `json:"ValueTypeName"`
}

type EdgePair struct {
	From string `json:"From"`
	To   string `json:"To"`
}

type NodeType int

const (
	Vertex NodeType = iota
	Edge
)

type EdgeInfo struct {
	FromVertexName string
	ToVertexName   string
	Name           string
	FromIsWildcard bool
	ToIsWildcard   bool
	IsDirectional  bool
	Flipped        bool
}

type SchemaInfo struct {
	PrimaryKeysByVertices map[string][]string
	LegacyPrimaryID       map[string]bool
	AttributeMap          map[string][]Attribute
	AllAttributeMap       map[string][]Attribute
	VertexMap             map[string]bool
	EdgeMap               map[string]EdgeInfo
}

func (schemaInfo *SchemaInfo) GetPrimaryKeysForVertex(vertex string) map[string]bool {
	allkeys := make(map[string]bool)
	// Record all keys of this vertex
	for _, k := range schemaInfo.PrimaryKeysByVertices[vertex] {
		allkeys[k] = true
	}
	return allkeys
}
