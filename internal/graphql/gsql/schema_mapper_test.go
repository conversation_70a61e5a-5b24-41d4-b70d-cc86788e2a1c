package gsql_test

import (
	_ "embed"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/tigergraph/graphql/formatter"
	"github.com/tigergraph/graphql/gsql"
	"github.com/tigergraph/graphql/gsql/client"
	"github.com/vektah/gqlparser/v2"
	"github.com/vektah/gqlparser/v2/ast"
	"github.com/vektah/gqlparser/v2/gqlerror"
)

func TestEmptySchema(t *testing.T) {
	s := loadGsqlSchemaFromFile(t, "testdata/empty-gsql-schema.json")
	gqlSchema, err := s.ToGraphQLSchema("test", &gsql.SchemaInfo{})
	// Note: It's ok to compare string error value in tests
	//       But not adviced in production
	require.EqualError(t, err, "GSQL Schema does not have vertices")
	require.Nil(t, gqlSchema)
}

//go:embed testdata/udt_attr_remove-gsql-schema.json
var bytes string

func TestUDTAttributesRemove(t *testing.T) {
	gsqlResponse := client.GsqlSchemaResponse{}
	err := json.Unmarshal([]byte(bytes), &gsqlResponse)
	require.NoError(t, err, fmt.Sprintf("GsqlSchemaResponse can not unmarshal %+v", string(bytes)))

	schemaInfo := gsql.SchemaInfo{}
	paincF := func() {
		_, err = gsqlResponse.Results.ToGraphQLSchema("demo", &schemaInfo)
	}
	require.NotPanics(t, paincF)
}

func TestFilters(t *testing.T) {
	gsqlSchema := loadGsqlSchemaFromFile(t, "testdata/demo_graph.json")
	gqlSchema, err := gsqlSchema.ToGraphQLSchema("demo_graph", &gsql.SchemaInfo{})
	require.Nil(t, err)

	checkResult := func(t *testing.T, doc *ast.QueryDocument, errlist gqlerror.List) {
		if !assert.Equal(t, 0, len(errlist)) {
			t.Fatal(errlist[0])
		}
		require.NotNil(t, doc)
	}

	t.Run("EqualOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : { name: {_eq: "RUBBER"} })
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})

	t.Run("NOT_EqualOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : { name: {_neq: "RUBBER"} })
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})

	t.Run("GtOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : { price: {_gt: 10} })
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})

	t.Run("GteOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : { price: {_gte: 10} })
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})

	t.Run("LtOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : { price: {_lt: 10} })
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})

	t.Run("LteOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : { price: {_lte: 10} })
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})

	t.Run("AndOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : {
					_and: [
						{price: {_lte: 10} },
						{name: {_eq: "RUBBER"} }
					]})
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})

	t.Run("OrOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : {
					_or: [
						{price: {_lte: 10} },
						{name: {_eq: "RUBBER"} }
					]})
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})

	t.Run("NotOperator", func(t *testing.T) {
		doc, errlist := gqlparser.LoadQuery(gqlSchema, `
			{demo_graph
				{product (where : {_not: {price: {_lte: 10} }})
					{name, price}
				}
			}`)
		checkResult(t, doc, errlist)
	})
}

func TestSchemas(t *testing.T) {
	schemas := []string{
		"testdata/2_vertices_1_edge.json",
		"testdata/2_vertices_1_edge_primary_id_as_attribute.json",
		"testdata/demo_graph.json",
		"testdata/ldbc_snb_schema.json",
	}
	for _, filePath := range schemas {
		s := loadGsqlSchemaFromFile(t, filePath)
		gqlSchema, err := s.ToGraphQLSchema("demo_graph", &gsql.SchemaInfo{})
		require.NoError(t, err)
		builder := &strings.Builder{}
		f := formatter.NewFormatter(builder)
		f.FormatSchema(gqlSchema)
		_, gqlErr := gqlparser.LoadSchema(&ast.Source{
			Input:   builder.String(),
			BuiltIn: false,
		})
		require.Equal(t, "", gqlErr.Error(), filePath+"\n"+builder.String())
	}
}

func TestVerticeOnlySchema(t *testing.T) {
	s := loadGsqlSchemaFromFile(t, "testdata/vertex_only.json")
	gqlSchema, err := s.ToGraphQLSchema("demo_graph", &gsql.SchemaInfo{})
	require.NoError(t, err)
	builder := &strings.Builder{}
	f := formatter.NewFormatter(builder)
	f.FormatSchema(gqlSchema)
	_, err = gqlparser.LoadSchema(&ast.Source{
		Input:   builder.String(),
		BuiltIn: false,
	})
	require.Nil(t, err)
}

func TestLDBC(t *testing.T) {
	s := loadGsqlSchemaFromFile(t, "testdata/ldbc_snb_schema.json")
	gqlSchema, err := s.ToGraphQLSchema("demo_graph", &gsql.SchemaInfo{})
	require.NoError(t, err)
	builder := &strings.Builder{}
	f := formatter.NewFormatter(builder)
	f.FormatSchema(gqlSchema)
	_, gqlErr := gqlparser.LoadSchema(&ast.Source{
		Input:   builder.String(),
		BuiltIn: false,
	})
	require.Equal(t, "", gqlErr.Error())
}

func loadGsqlSchemaFromFile(t *testing.T, path string) *gsql.Schema {
	b, err := ioutil.ReadFile(path)
	require.Nil(t, err)
	gsqlSchema := client.GsqlSchemaResponse{}

	err = json.Unmarshal(b, &gsqlSchema)
	require.Nil(t, err)

	require.False(t, gsqlSchema.Error, gsqlSchema.Message)
	return &gsqlSchema.Results
}

func loadFile(t *testing.T, path string) string {
	b, err := ioutil.ReadFile(path)
	require.Nil(t, err)
	return string(b)
}
