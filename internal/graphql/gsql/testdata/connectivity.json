{"error": false, "message": "", "results": {"VertexTypes": [{"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "eventDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "event_id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "BusRide"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "eventDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "event_id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "TrainRide"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "eventDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "event_id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Flight"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "Float"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "amount", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "transferEvent", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "bank_transfer_id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "FundsTransfer"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "eventDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "call<PERSON><PERSON><PERSON>", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "callType", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "phone_call_id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "PhoneCall"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "fullName", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "dob", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "email", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "gender", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "ethic_group", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id_card_no", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Person"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "eventDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "eventId", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "HotelStay"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": true}, "Attributes": [], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": true, "AttributeName": "phoneNumber", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Phone"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "accountId", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "BankAccount"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "caseId", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "CaseReport"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "addr_id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Address"}], "EdgeTypes": [{"IsDirected": false, "ToVertexTypeName": "CaseReport", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasCaseReport"}, {"IsDirected": false, "ToVertexTypeName": "FundsTransfer", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "BankAccount", "Name": "hasFundsTransfer"}, {"IsDirected": false, "ToVertexTypeName": "HotelStay", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasHotelStay"}, {"IsDirected": false, "ToVertexTypeName": "BusRide", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasBusRide"}, {"IsDirected": false, "ToVertexTypeName": "Flight", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasFlight"}, {"IsDirected": false, "ToVertexTypeName": "Phone", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasPhone"}, {"IsDirected": false, "ToVertexTypeName": "TrainRide", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasTrainRide"}, {"IsDirected": false, "ToVertexTypeName": "BankAccount", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasBankAccount"}, {"IsDirected": false, "ToVertexTypeName": "Address", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasHomeAddress"}, {"IsDirected": false, "ToVertexTypeName": "PhoneCall", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Phone", "Name": "hasPhoneCall"}]}}