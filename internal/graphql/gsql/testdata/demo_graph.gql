input BooleanFilter {
	_eq: Boolean
	_neq: Boolean
	_gt: Boolean
	_gte: Boolean
	_lt: Boolean
	_lte: Boolean
}
scalar Datetime
input DatetimeFilter {
	_eq: Datetime
	_neq: Datetime
	_gt: Datetime
	_gte: Datetime
	_lt: Datetime
	_lte: Datetime
}
enum DatetimeFormat {
	RFC3339
	Stamp
	Default
}
"""
The collection of all edges in this schema
"""
type Edges {
	From_p_order_prodOrder(where: From_p_order_prodOrderFilter, whereExpr: String): [From_p_order_prodOrder]
	From_product_prodOrder(where: From_product_prodOrderFilter, whereExpr: String): [From_product_prodOrder]
	From_product_prodStocking(where: From_product_prodStockingFilter, whereExpr: String): [From_product_prodStocking]
	From_stocking_prodStocking(where: From_stocking_prodStockingFilter, whereExpr: String): [From_stocking_prodStocking]
	deliver(where: deliverFilter, whereExpr: String): [deliver]
	produce(where: produceFilter, whereExpr: String): [produce]
	reverseDeliver(where: reverseDeliverFilter, whereExpr: String): [reverseDeliver]
	reverseProduce(where: reverseProduceFilter, whereExpr: String): [reverseProduce]
	reverseUsedBy(where: reverseUsedByFilter, whereExpr: String): [reverseUsedBy]
	usedBy(where: usedByFilter, whereExpr: String): [usedBy]
}
input FloatFilter {
	_eq: Float
	_neq: Float
	_gt: Float
	_gte: Float
	_lt: Float
	_lte: Float
}
type From_p_order_prodOrder {
	amount: Int64
	to(where: productFilter, whereExpr: String): product
}
input From_p_order_prodOrderFilter {
	_and: [From_p_order_prodOrderFilter]
	_not: From_p_order_prodOrderFilter
	_or: [From_p_order_prodOrderFilter]
	amount: Int64Filter
	from: p_orderFilter
	to: productFilter
}
type From_product_prodOrder {
	amount: Int64
	to(where: p_orderFilter, whereExpr: String): p_order
}
input From_product_prodOrderFilter {
	_and: [From_product_prodOrderFilter]
	_not: From_product_prodOrderFilter
	_or: [From_product_prodOrderFilter]
	amount: Int64Filter
	from: productFilter
	to: p_orderFilter
}
type From_product_prodStocking {
	amount: Int64
	to(where: stockingFilter, whereExpr: String): stocking
}
input From_product_prodStockingFilter {
	_and: [From_product_prodStockingFilter]
	_not: From_product_prodStockingFilter
	_or: [From_product_prodStockingFilter]
	amount: Int64Filter
	from: productFilter
	to: stockingFilter
}
type From_stocking_prodStocking {
	amount: Int64
	to(where: productFilter, whereExpr: String): product
}
input From_stocking_prodStockingFilter {
	_and: [From_stocking_prodStockingFilter]
	_not: From_stocking_prodStockingFilter
	_or: [From_stocking_prodStockingFilter]
	amount: Int64Filter
	from: stockingFilter
	to: productFilter
}
scalar Int64
input Int64Filter {
	_eq: Int64
	_neq: Int64
	_gt: Int64
	_gte: Int64
	_lt: Int64
	_lte: Int64
}
input IntFilter {
	_eq: Int
	_neq: Int
	_gt: Int
	_gte: Int
	_lt: Int
	_lte: Int
}
type Mutation {
	demo_graph: mutation_demo_graph
}
enum OrderBy {
	asc
	desc
}
input Pagination {
	offset: Int64
	limit: Int64
	orderBy: OrderBy
}
type Query {
	demo_graph: demo_graph
}
input StringFilter {
	_eq: String
	_neq: String
	_gt: String
	_gte: String
	_lt: String
	_lte: String
}
scalar Uint64
input Uint64Filter {
	_eq: Uint64
	_neq: Uint64
	_gt: Uint64
	_gte: Uint64
	_lt: Uint64
	_lte: Uint64
}
"""
The collection of all vertices in this graph
"""
type Vertices {
	p_order(where: p_orderFilter, whereExpr: String): [p_order]
	product(where: productFilter, whereExpr: String, order_by: [productOrderBy], limit: Int64, offset: Int64): [product]
	site(where: siteFilter, whereExpr: String, order_by: [siteOrderBy], limit: Int64, offset: Int64): [site]
	stocking(where: stockingFilter, whereExpr: String): [stocking]
}
type deliver {
	itemId: String
	to(where: siteFilter, whereExpr: String): site
}
input deliverFilter {
	_and: [deliverFilter]
	_not: deliverFilter
	_or: [deliverFilter]
	from: siteFilter
	itemId: StringFilter
	to: siteFilter
}
input deliverMutation {
	_and: deliverMutation
	_not: deliverMutation
	_or: deliverMutation
	from: siteFilterMutation
	itemId: StringFilter
	to: siteFilterMutation
}
"""
The collection of all vertices in this graph
"""
type demo_graph {
	p_order(where: p_orderFilter, whereExpr: String): [p_order]
	product(where: productFilter, whereExpr: String, order_by: [productOrderBy], limit: Int64, offset: Int64): [product]
	site(where: siteFilter, whereExpr: String, order_by: [siteOrderBy], limit: Int64, offset: Int64): [site]
	stocking(where: stockingFilter, whereExpr: String): [stocking]
}
input mutation_deliver_from_input {
	sid: String
}
type mutation_deliver_from_output {
	sid: String
}
input mutation_deliver_input {
	from: mutation_deliver_from_input
	itemId: String
	to: mutation_deliver_to_input
}
type mutation_deliver_output {
	from: mutation_deliver_from_output
	itemId: String
	to: mutation_deliver_to_output
}
input mutation_deliver_to_input {
	sid: String
}
type mutation_deliver_to_output {
	sid: String
}
type mutation_demo_graph {
	delete_deliver(where: deliverMutation): [mutation_deliver_output]
	delete_p_order(where: p_orderFilterMutation): [mutation_p_order_output]
	delete_prodOrder(where: prodOrderMutation): [mutation_prodOrder_output]
	delete_prodOrder(where: prodOrderMutation): [mutation_prodOrder_output]
	delete_prodStocking(where: prodStockingMutation): [mutation_prodStocking_output]
	delete_prodStocking(where: prodStockingMutation): [mutation_prodStocking_output]
	delete_produce(where: produceMutation): [mutation_produce_output]
	delete_product(where: productFilterMutation): [mutation_product_output]
	delete_reverseDeliver(where: reverseDeliverMutation): [mutation_reverseDeliver_output]
	delete_reverseProduce(where: reverseProduceMutation): [mutation_reverseProduce_output]
	delete_reverseUsedBy(where: reverseUsedByMutation): [mutation_reverseUsedBy_output]
	delete_site(where: siteFilterMutation): [mutation_site_output]
	delete_stocking(where: stockingFilterMutation): [mutation_stocking_output]
	delete_usedBy(where: usedByMutation): [mutation_usedBy_output]
	insert_deliver(objects: [mutation_deliver_input]): [mutation_deliver_output]
	insert_p_order(objects: [mutation_p_order_input]): [mutation_p_order_output]
	insert_prodOrder(objects: [mutation_prodOrder_input]): [mutation_prodOrder_output]
	insert_prodOrder(objects: [mutation_prodOrder_input]): [mutation_prodOrder_output]
	insert_prodStocking(objects: [mutation_prodStocking_input]): [mutation_prodStocking_output]
	insert_prodStocking(objects: [mutation_prodStocking_input]): [mutation_prodStocking_output]
	insert_produce(objects: [mutation_produce_input]): [mutation_produce_output]
	insert_product(objects: [mutation_product_input]): [mutation_product_output]
	insert_reverseDeliver(objects: [mutation_reverseDeliver_input]): [mutation_reverseDeliver_output]
	insert_reverseProduce(objects: [mutation_reverseProduce_input]): [mutation_reverseProduce_output]
	insert_reverseUsedBy(objects: [mutation_reverseUsedBy_input]): [mutation_reverseUsedBy_output]
	insert_site(objects: [mutation_site_input]): [mutation_site_output]
	insert_stocking(objects: [mutation_stocking_input]): [mutation_stocking_output]
	insert_usedBy(objects: [mutation_usedBy_input]): [mutation_usedBy_output]
}
input mutation_p_order_input {
	orderId: String
}
type mutation_p_order_output {
	orderId: String
}
input mutation_prodOrder_from_input {
	pid: String
}
type mutation_prodOrder_from_output {
	pid: String
}
input mutation_prodOrder_input {
	amount: Int64
	from: mutation_prodOrder_from_input
	to: mutation_prodOrder_to_input
}
type mutation_prodOrder_output {
	amount: Int64
	from: mutation_prodOrder_from_output
	to: mutation_prodOrder_to_output
}
input mutation_prodOrder_to_input {
	orderId: String
}
type mutation_prodOrder_to_output {
	orderId: String
}
input mutation_prodStocking_from_input {
	stockingId: String
}
type mutation_prodStocking_from_output {
	stockingId: String
}
input mutation_prodStocking_input {
	amount: Int64
	from: mutation_prodStocking_from_input
	to: mutation_prodStocking_to_input
}
type mutation_prodStocking_output {
	amount: Int64
	from: mutation_prodStocking_from_output
	to: mutation_prodStocking_to_output
}
input mutation_prodStocking_to_input {
	pid: String
}
type mutation_prodStocking_to_output {
	pid: String
}
input mutation_produce_from_input {
	sid: String
}
type mutation_produce_from_output {
	sid: String
}
input mutation_produce_input {
	from: mutation_produce_from_input
	to: mutation_produce_to_input
}
type mutation_produce_output {
	from: mutation_produce_from_output
	to: mutation_produce_to_output
}
input mutation_produce_to_input {
	pid: String
}
type mutation_produce_to_output {
	pid: String
}
input mutation_product_input {
	formula: String
	name: String
	pid: String
	price: Float
}
type mutation_product_output {
	formula: String
	name: String
	pid: String
	price: Float
}
input mutation_reverseDeliver_from_input {
	sid: String
}
type mutation_reverseDeliver_from_output {
	sid: String
}
input mutation_reverseDeliver_input {
	from: mutation_reverseDeliver_from_input
	itemId: String
	to: mutation_reverseDeliver_to_input
}
type mutation_reverseDeliver_output {
	from: mutation_reverseDeliver_from_output
	itemId: String
	to: mutation_reverseDeliver_to_output
}
input mutation_reverseDeliver_to_input {
	sid: String
}
type mutation_reverseDeliver_to_output {
	sid: String
}
input mutation_reverseProduce_from_input {
	pid: String
}
type mutation_reverseProduce_from_output {
	pid: String
}
input mutation_reverseProduce_input {
	from: mutation_reverseProduce_from_input
	to: mutation_reverseProduce_to_input
}
type mutation_reverseProduce_output {
	from: mutation_reverseProduce_from_output
	to: mutation_reverseProduce_to_output
}
input mutation_reverseProduce_to_input {
	sid: String
}
type mutation_reverseProduce_to_output {
	sid: String
}
input mutation_reverseUsedBy_from_input {
	pid: String
}
type mutation_reverseUsedBy_from_output {
	pid: String
}
input mutation_reverseUsedBy_input {
	formula_order: String
	from: mutation_reverseUsedBy_from_input
	to: mutation_reverseUsedBy_to_input
	useAmount: Float
}
type mutation_reverseUsedBy_output {
	formula_order: String
	from: mutation_reverseUsedBy_from_output
	to: mutation_reverseUsedBy_to_output
	useAmount: Float
}
input mutation_reverseUsedBy_to_input {
	pid: String
}
type mutation_reverseUsedBy_to_output {
	pid: String
}
input mutation_site_input {
	name: String
	sid: String
}
type mutation_site_output {
	name: String
	sid: String
}
input mutation_stocking_input {
	stockingId: String
}
type mutation_stocking_output {
	stockingId: String
}
input mutation_usedBy_from_input {
	pid: String
}
type mutation_usedBy_from_output {
	pid: String
}
input mutation_usedBy_input {
	formula_order: String
	from: mutation_usedBy_from_input
	to: mutation_usedBy_to_input
	useAmount: Float
}
type mutation_usedBy_output {
	formula_order: String
	from: mutation_usedBy_from_output
	to: mutation_usedBy_to_output
	useAmount: Float
}
input mutation_usedBy_to_input {
	pid: String
}
type mutation_usedBy_to_output {
	pid: String
}
type p_order {
	orderId: String
	prodOrder(where: From_p_order_prodOrderFilter, whereExpr: String): [From_p_order_prodOrder]
}
input p_orderFilter {
	_and: [p_orderFilter]
	_not: p_orderFilter
	_or: [p_orderFilter]
	prodOrder: From_p_order_prodOrderFilter
}
input p_orderFilterMutation {
	_and: p_orderFilterMutation
	_not: p_orderFilterMutation
	_or: p_orderFilterMutation
	orderId: StringFilter
}
input prodOrderMutation {
	_and: prodOrderMutation
	_not: prodOrderMutation
	_or: prodOrderMutation
	amount: Int64Filter
	from: productFilterMutation
	to: p_orderFilterMutation
}
input prodStockingMutation {
	_and: prodStockingMutation
	_not: prodStockingMutation
	_or: prodStockingMutation
	amount: Int64Filter
	from: stockingFilterMutation
	to: productFilterMutation
}
type produce {
	to(where: productFilter, whereExpr: String): product
}
input produceFilter {
	_and: [produceFilter]
	_not: produceFilter
	_or: [produceFilter]
	from: siteFilter
	to: productFilter
}
input produceMutation {
	_and: produceMutation
	_not: produceMutation
	_or: produceMutation
	from: siteFilterMutation
	to: productFilterMutation
}
type product {
	formula: String
	name: String
	pid: String
	price: Float
	prodOrder(where: From_product_prodOrderFilter, whereExpr: String): [From_product_prodOrder]
	prodStocking(where: From_product_prodStockingFilter, whereExpr: String): [From_product_prodStocking]
	reverseProduce(where: reverseProduceFilter, whereExpr: String): [reverseProduce]
	reverseUsedBy(where: reverseUsedByFilter, whereExpr: String): [reverseUsedBy]
	usedBy(where: usedByFilter, whereExpr: String): [usedBy]
}
input productFilter {
	_and: [productFilter]
	_not: productFilter
	_or: [productFilter]
	formula: StringFilter
	name: StringFilter
	price: FloatFilter
	prodOrder: From_product_prodOrderFilter
	prodStocking: From_product_prodStockingFilter
	reverseProduce: reverseProduceFilter
	reverseUsedBy: reverseUsedByFilter
	usedBy: usedByFilter
}
input productFilterMutation {
	_and: productFilterMutation
	_not: productFilterMutation
	_or: productFilterMutation
	formula: StringFilter
	name: StringFilter
	pid: StringFilter
	price: FloatFilter
}
input productOrderBy {
	formula: OrderBy
	name: OrderBy
	price: OrderBy
}
type reverseDeliver {
	itemId: String
	to(where: siteFilter, whereExpr: String): site
}
input reverseDeliverFilter {
	_and: [reverseDeliverFilter]
	_not: reverseDeliverFilter
	_or: [reverseDeliverFilter]
	from: siteFilter
	itemId: StringFilter
	to: siteFilter
}
input reverseDeliverMutation {
	_and: reverseDeliverMutation
	_not: reverseDeliverMutation
	_or: reverseDeliverMutation
	from: siteFilterMutation
	itemId: StringFilter
	to: siteFilterMutation
}
type reverseProduce {
	to(where: siteFilter, whereExpr: String): site
}
input reverseProduceFilter {
	_and: [reverseProduceFilter]
	_not: reverseProduceFilter
	_or: [reverseProduceFilter]
	from: productFilter
	to: siteFilter
}
input reverseProduceMutation {
	_and: reverseProduceMutation
	_not: reverseProduceMutation
	_or: reverseProduceMutation
	from: productFilterMutation
	to: siteFilterMutation
}
type reverseUsedBy {
	formula_order: String
	to(where: productFilter, whereExpr: String): product
	useAmount: Float
}
input reverseUsedByFilter {
	_and: [reverseUsedByFilter]
	_not: reverseUsedByFilter
	_or: [reverseUsedByFilter]
	formula_order: StringFilter
	from: productFilter
	to: productFilter
	useAmount: FloatFilter
}
input reverseUsedByMutation {
	_and: reverseUsedByMutation
	_not: reverseUsedByMutation
	_or: reverseUsedByMutation
	formula_order: StringFilter
	from: productFilterMutation
	to: productFilterMutation
	useAmount: FloatFilter
}
type site {
	deliver(where: deliverFilter, whereExpr: String): [deliver]
	name: String
	produce(where: produceFilter, whereExpr: String): [produce]
	reverseDeliver(where: reverseDeliverFilter, whereExpr: String): [reverseDeliver]
	sid: String
}
input siteFilter {
	_and: [siteFilter]
	_not: siteFilter
	_or: [siteFilter]
	deliver: deliverFilter
	name: StringFilter
	produce: produceFilter
	reverseDeliver: reverseDeliverFilter
}
input siteFilterMutation {
	_and: siteFilterMutation
	_not: siteFilterMutation
	_or: siteFilterMutation
	name: StringFilter
	sid: StringFilter
}
input siteOrderBy {
	name: OrderBy
}
type stocking {
	prodStocking(where: From_stocking_prodStockingFilter, whereExpr: String): [From_stocking_prodStocking]
	stockingId: String
}
input stockingFilter {
	_and: [stockingFilter]
	_not: stockingFilter
	_or: [stockingFilter]
	prodStocking: From_stocking_prodStockingFilter
}
input stockingFilterMutation {
	_and: stockingFilterMutation
	_not: stockingFilterMutation
	_or: stockingFilterMutation
	stockingId: StringFilter
}
type usedBy {
	formula_order: String
	to(where: productFilter, whereExpr: String): product
	useAmount: Float
}
input usedByFilter {
	_and: [usedByFilter]
	_not: usedByFilter
	_or: [usedByFilter]
	formula_order: StringFilter
	from: productFilter
	to: productFilter
	useAmount: FloatFilter
}
input usedByMutation {
	_and: usedByMutation
	_not: usedByMutation
	_or: usedByMutation
	formula_order: StringFilter
	from: productFilterMutation
	to: productFilterMutation
	useAmount: FloatFilter
}
