{"error": false, "message": "", "results": {"VertexTypes": [{"Name": "product", "PrimaryId": {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "pid", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Usage": null, "Attributes": [{"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "FLOAT", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "price", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "formula", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "Edges": null, "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}}, {"Name": "site", "PrimaryId": {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "sid", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Usage": null, "Attributes": [{"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "Edges": null, "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}}, {"Name": "p_order", "PrimaryId": {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "orderId", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Usage": null, "Attributes": [], "Edges": null, "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}}, {"Name": "stocking", "PrimaryId": {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "stockingId", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Usage": null, "Attributes": [], "Edges": null, "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}}], "EdgeTypes": null}}