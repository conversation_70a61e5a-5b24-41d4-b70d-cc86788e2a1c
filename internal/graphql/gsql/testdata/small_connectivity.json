{"error": false, "message": "", "results": {"VertexTypes": [{"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "eventDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "event_id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "BusRide"}, {"Usage": ["connectivity"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "fullName", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "dob", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "email", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "gender", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "ethic_group", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id_card_no", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Person"}], "EdgeTypes": [{"IsDirected": false, "ToVertexTypeName": "BusRide", "Usage": ["connectivity"], "Config": {}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "hasBusRide"}]}}