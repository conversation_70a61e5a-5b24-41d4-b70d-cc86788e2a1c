{"error": false, "message": "", "results": {"EdgeTypes": [{"Attributes": [], "Config": {}, "FromVertexTypeName": "vertex_type_1", "IsDirected": false, "IsLocal": true, "Name": "edge_type_1", "ToVertexTypeName": "vertex_type_2"}], "GraphName": "x", "VertexTypes": [{"Attributes": [], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "TAGGABLE": false}, "IsLocal": true, "Name": "vertex_type_1", "PrimaryId": {"AttributeName": "id", "AttributeType": {"Name": "STRING"}, "HasIndex": false, "IsPartOfCompositeKey": false, "IsPrimaryKey": false, "PrimaryIdAsAttribute": false, "internalAttribute": false}}, {"Attributes": [], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "TAGGABLE": false}, "IsLocal": true, "Name": "vertex_type_2", "PrimaryId": {"AttributeName": "id", "AttributeType": {"Name": "STRING"}, "HasIndex": false, "IsPartOfCompositeKey": false, "IsPrimaryKey": false, "PrimaryIdAsAttribute": true, "internalAttribute": false}}]}}