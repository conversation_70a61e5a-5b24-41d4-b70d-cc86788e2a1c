{"error": false, "message": "", "results": {"VertexTypes": [{"Name": "product", "PrimaryId": {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "pid", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Usage": null, "Attributes": [{"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "FLOAT", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "price", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "formula", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "Edges": null, "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}}, {"Name": "site", "PrimaryId": {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "sid", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Usage": null, "Attributes": [{"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "Edges": null, "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}}, {"Name": "p_order", "PrimaryId": {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "orderId", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Usage": null, "Attributes": [], "Edges": null, "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}}, {"Name": "stocking", "PrimaryId": {"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "stockingId", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Usage": null, "Attributes": [], "Edges": null, "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}}], "EdgeTypes": [{"Name": "usedBy", "FromVertexTypeName": "product", "ToVertexTypeName": "product", "IsDirected": true, "Usage": null, "Attributes": [{"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "formula_order", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "FLOAT", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "useAmount", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "EdgePairs": null, "Config": {"REVERSE_EDGE": "reverseUsedBy"}}, {"Name": "deliver", "FromVertexTypeName": "site", "ToVertexTypeName": "site", "IsDirected": true, "Usage": null, "Attributes": [{"AttributeType": {"Name": "STRING", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "itemId", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "EdgePairs": null, "Config": {"REVERSE_EDGE": "reverseDeliver"}}, {"Name": "produce", "FromVertexTypeName": "site", "ToVertexTypeName": "product", "IsDirected": true, "Usage": null, "Attributes": [], "EdgePairs": null, "Config": {"REVERSE_EDGE": "reverseProduce"}}, {"Name": "prodOrder", "FromVertexTypeName": "p_order", "ToVertexTypeName": "product", "IsDirected": false, "Usage": null, "Attributes": [{"AttributeType": {"Name": "INT", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "amount", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "EdgePairs": null, "Config": {"REVERSE_EDGE": ""}}, {"Name": "prodStocking", "FromVertexTypeName": "stocking", "ToVertexTypeName": "product", "IsDirected": false, "Usage": null, "Attributes": [{"AttributeType": {"Name": "INT", "ValueTypeName": ""}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "amount", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "EdgePairs": null, "Config": {"REVERSE_EDGE": ""}}]}}