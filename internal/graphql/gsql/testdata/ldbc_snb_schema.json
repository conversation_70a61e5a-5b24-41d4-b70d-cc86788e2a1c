{"error": false, "message": "", "results": {"VertexTypes": [{"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "creationDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "locationIP", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "browserUsed", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "content", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "length", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Comment"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "imageFile", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "creationDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "locationIP", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "browserUsed", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "lang", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "content", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "length", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Post"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "url", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Company"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "url", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "University"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "url", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "City"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "url", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Country"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "url", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Continent"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "title", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "creationDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Forum"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "firstName", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "lastName", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "gender", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "birthday", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "creationDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "locationIP", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "browserUsed", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"ValueTypeName": "STRING", "Name": "SET"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "speaks", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"ValueTypeName": "STRING", "Name": "SET"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "email", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Person"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "url", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "Tag"}, {"Usage": ["ldbc_snb"], "Config": {"TAGGABLE": false, "STATS": "OUTDEGREE_BY_EDGETYPE", "PRIMARY_ID_AS_ATTRIBUTE": false}, "Attributes": [{"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "name", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, {"AttributeType": {"Name": "STRING"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "url", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "PrimaryId": {"AttributeType": {"Name": "UINT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "id", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}, "Name": "TagClass"}], "EdgeTypes": [{"IsDirected": true, "ToVertexTypeName": "Post", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Forum_CONTAINER_OF_Post_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Forum", "Name": "Forum_CONTAINER_OF_Post"}, {"IsDirected": true, "ToVertexTypeName": "Person", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Comment_HAS_CREATOR_Person_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Comment", "Name": "Comment_HAS_CREATOR_Person"}, {"IsDirected": true, "ToVertexTypeName": "Person", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Post_HAS_CREATOR_Person_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Post", "Name": "Post_HAS_CREATOR_Person"}, {"IsDirected": true, "ToVertexTypeName": "Tag", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Person_HAS_INTEREST_Tag_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "Person_HAS_INTEREST_Tag"}, {"IsDirected": true, "ToVertexTypeName": "Person", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Forum_HAS_MEMBER_Person_REVERSE"}, "Attributes": [{"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "joinDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "FromVertexTypeName": "Forum", "Name": "Forum_HAS_MEMBER_Person"}, {"IsDirected": true, "ToVertexTypeName": "Person", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Forum_HAS_MODERATOR_Person_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Forum", "Name": "Forum_HAS_MODERATOR_Person"}, {"IsDirected": true, "ToVertexTypeName": "Tag", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Comment_HAS_TAG_Tag_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Comment", "Name": "Comment_HAS_TAG_Tag"}, {"IsDirected": true, "ToVertexTypeName": "Tag", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Post_HAS_TAG_Tag_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Post", "Name": "Post_HAS_TAG_Tag"}, {"IsDirected": true, "ToVertexTypeName": "Tag", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Forum_HAS_TAG_Tag_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Forum", "Name": "Forum_HAS_TAG_Tag"}, {"IsDirected": true, "ToVertexTypeName": "TagClass", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Tag_HAS_TYPE_TagClass_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Tag", "Name": "Tag_HAS_TYPE_TagClass"}, {"IsDirected": true, "ToVertexTypeName": "Country", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Company_IS_LOCATED_IN_Country_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Company", "Name": "Company_IS_LOCATED_IN_Country"}, {"IsDirected": true, "ToVertexTypeName": "Country", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Comment_IS_LOCATED_IN_Country_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Comment", "Name": "Comment_IS_LOCATED_IN_Country"}, {"IsDirected": true, "ToVertexTypeName": "Country", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Post_IS_LOCATED_IN_Country_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Post", "Name": "Post_IS_LOCATED_IN_Country"}, {"IsDirected": true, "ToVertexTypeName": "City", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Person_IS_LOCATED_IN_City_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Person", "Name": "Person_IS_LOCATED_IN_City"}, {"IsDirected": true, "ToVertexTypeName": "City", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "University_IS_LOCATED_IN_City_REVERSE"}, "Attributes": [], "FromVertexTypeName": "University", "Name": "University_IS_LOCATED_IN_City"}, {"IsDirected": true, "ToVertexTypeName": "Country", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "City_IS_PART_OF_Country_REVERSE"}, "Attributes": [], "FromVertexTypeName": "City", "Name": "City_IS_PART_OF_Country"}, {"IsDirected": true, "ToVertexTypeName": "Continent", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Country_IS_PART_OF_Continent_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Country", "Name": "Country_IS_PART_OF_Continent"}, {"IsDirected": true, "ToVertexTypeName": "TagClass", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "TagClass_IS_SUBCLASS_OF_TagClass_REVERSE"}, "Attributes": [], "FromVertexTypeName": "TagClass", "Name": "TagClass_IS_SUBCLASS_OF_TagClass"}, {"IsDirected": true, "ToVertexTypeName": "Person", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Person_KNOWS_Person_REVERSE"}, "Attributes": [{"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "creationDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "FromVertexTypeName": "Person", "Name": "Person_KNOWS_Person"}, {"IsDirected": true, "ToVertexTypeName": "Comment", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Person_LIKES_Comment_REVERSE"}, "Attributes": [{"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "creationDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "FromVertexTypeName": "Person", "Name": "Person_LIKES_Comment"}, {"IsDirected": true, "ToVertexTypeName": "Post", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Person_LIKES_Post_REVERSE"}, "Attributes": [{"AttributeType": {"Name": "DATETIME"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "creationDate", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "FromVertexTypeName": "Person", "Name": "Person_LIKES_Post"}, {"IsDirected": true, "ToVertexTypeName": "Comment", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Comment_REPLY_OF_Comment_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Comment", "Name": "Comment_REPLY_OF_Comment"}, {"IsDirected": true, "ToVertexTypeName": "Post", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Comment_REPLY_OF_Post_REVERSE"}, "Attributes": [], "FromVertexTypeName": "Comment", "Name": "Comment_REPLY_OF_Post"}, {"IsDirected": true, "ToVertexTypeName": "University", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Person_STUDY_AT_University_REVERSE"}, "Attributes": [{"AttributeType": {"Name": "INT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "classYear", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "FromVertexTypeName": "Person", "Name": "Person_STUDY_AT_University"}, {"IsDirected": true, "ToVertexTypeName": "Company", "Usage": ["ldbc_snb"], "Config": {"REVERSE_EDGE": "Person_WORK_AT_Company_REVERSE"}, "Attributes": [{"AttributeType": {"Name": "INT"}, "IsPartOfCompositeKey": false, "PrimaryIdAsAttribute": false, "AttributeName": "workFrom", "HasIndex": false, "internalAttribute": false, "IsPrimaryKey": false}], "FromVertexTypeName": "Person", "Name": "Person_WORK_AT_Company"}]}}