package log

import (
	"bytes"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

const defaultLevel = "INFO"

var (
	mux       = &sync.Mutex{}
	separator = ", "
)
var _logger *zapLogger

func getLogger() *zapLogger {
	return _logger
}

type nopWriter struct{}

func (nopWriter) Write(p []byte) (n int, err error) { return }
func (nopWriter) Sync() error                       { return nil }

func Init(ws zapcore.WriteSyncer) {
	mux.Lock()
	defer mux.Unlock()
	if ws == nil {
		ws = nopWriter{}
	}
	_logger = newZapLogger(defaultLevel, ws)
}

// Debug uses fmt.Sprint to construct and log a message.
func Debug(args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Debug(concatSeparator(args)...)
}

// Debugf uses fmt.Sprintf to log a templated message.
func Debugf(template string, args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Debugf(template, args...)
}

// Debugw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func Debugw(msg string, keysAndValues ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Debugw(msg, keysAndValues...)
}

// Info uses fmt.Sprint to construct and log a message.
func Info(args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Info(concatSeparator(args)...)
}

// Infof uses fmt.Sprintf to log a templated message.
func Infof(template string, args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Infof(template, args...)
}

// Infow logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func Infow(msg string, keysAndValues ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Infow(msg, keysAndValues...)
}

// Warn uses fmt.Sprint to construct and log a message.
func Warn(args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Warn(concatSeparator(args)...)
}

// Warnf uses fmt.Sprintf to log a templated message.
func Warnf(template string, args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Warnf(template, args...)
}

// Warnw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func Warnw(msg string, keysAndValues ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Warnw(msg, keysAndValues...)
}

// Error uses fmt.Sprint to construct and log a message.
func Error(args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Error(concatSeparator(args)...)
}

// Errorf uses fmt.Sprintf to log a templated message.
func Errorf(template string, args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Errorf(template, args...)
}

// Errorw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func Errorw(msg string, keysAndValues ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Errorw(msg, keysAndValues...)
}

// Panic uses fmt.Sprint to construct and log a message.
func Panic(args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Panic(concatSeparator(args)...)
}

// Panicf uses fmt.Sprintf to log a templated message.
func Panicf(template string, args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Panicf(template, args...)
}

// Panicw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func Panicw(msg string, keysAndValues ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Panicw(msg, keysAndValues...)
}

// Fatal uses fmt.Sprint to construct and log a message.
func Fatal(args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Fatal(concatSeparator(args)...)
}

// Fatalf uses fmt.Sprintf to log a templated message.
func Fatalf(template string, args ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Fatalf(template, args...)
}

// Fatalw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func Fatalw(msg string, keysAndValues ...interface{}) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().Fatalw(msg, keysAndValues...)
}

// SetLevel changes the minimum enabled log level.
func SetLevel(level string) {
	mux.Lock()
	defer mux.Unlock()
	getLogger().SetLevel(level)
}

// IsDebugLevel checks if the minimum enabled log level is DEBUG.
func IsDebugLevel() bool {
	mux.Lock()
	defer mux.Unlock()
	return getLogger().IsDebugLevel()
}

// SetSeparator changes the separator that separate args.
// If separator is set to "", then there is no separator.
func SetSeparator(sep string) {
	mux.Lock()
	defer mux.Unlock()
	separator = sep
}

// concatSeparator concats args using the separator.
func concatSeparator(args []interface{}) []interface{} {
	if separator == "" {
		return args
	}
	if len(args) == 0 {
		return []interface{}{""}
	}

	var sb strings.Builder
	sb.WriteString(fmt.Sprint(args[0]))
	for i := 1; i < len(args); i++ {
		sb.WriteString(separator)
		sb.WriteString(fmt.Sprint(args[i]))
	}
	return []interface{}{sb.String()}
}

const callerSkip = 2

type zapLogger struct {
	logger *zap.SugaredLogger
	level  *zap.AtomicLevel
}

func newZapLogger(level string, ws zapcore.WriteSyncer) *zapLogger {
	encoderCfg := zapcore.EncoderConfig{
		TimeKey:        "ts",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapLevelEncoder,
		EncodeTime:     zapTimeEncoder,
		EncodeDuration: zapcore.NanosDurationEncoder,
		EncodeCaller:   zapCallerEncoder,
	}

	encoder := zapcore.NewConsoleEncoder(encoderCfg)
	writer := &zapWriter{ws, " "}
	atom := zap.NewAtomicLevelAt(strToZapLevel(level))

	core := zapcore.NewCore(encoder, writer, atom)
	// Skip 2 callers because the log call is wrapped inside two function calls.
	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(callerSkip), zap.AddStacktrace(zap.ErrorLevel))
	return &zapLogger{
		logger: logger.Sugar(),
		level:  &atom,
	}
}

// zapLevelEncoder uses the first letter to represent log level.
func zapLevelEncoder(l zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(l.CapitalString()[0:1])
}

// zapTimeEncoder changes the time format to "2006-01-02 15:04:05.000".
func zapTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006-01-02 15:04:05.000"))
}

// zapCallerEncoder adds "|" to separate log info and "]" to separate caller.
func zapCallerEncoder(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString("| " + caller.TrimmedPath() + "]")
}

type zapWriter struct {
	ws  zapcore.WriteSyncer
	sep string
}

// Write writes the log string but replaces tabs with space.
func (w *zapWriter) Write(bs []byte) (int, error) {
	cbs := bytes.Replace(bs, []byte("\t"), []byte(w.sep), 3)
	return w.ws.Write(cbs)
}

func (w *zapWriter) Sync() error {
	return w.ws.Sync()
}

// Debug uses fmt.Sprint to construct and log a message.
func (z *zapLogger) Debug(args ...interface{}) {
	defer z.sync()
	z.logger.Debug(args...)
}

// Debugf uses fmt.Sprintf to log a templated message.
func (z *zapLogger) Debugf(template string, args ...interface{}) {
	defer z.sync()
	z.logger.Debugf(template, args...)
}

// Debugw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func (z *zapLogger) Debugw(msg string, keysAndValues ...interface{}) {
	defer z.sync()
	z.logger.Debugw(msg, keysAndValues...)
}

// Info uses fmt.Sprint to construct and log a message.
func (z *zapLogger) Info(args ...interface{}) {
	defer z.sync()
	z.logger.Info(args...)
}

// Infof uses fmt.Sprintf to log a templated message.
func (z *zapLogger) Infof(template string, args ...interface{}) {
	defer z.sync()
	z.logger.Infof(template, args...)
}

// Infow logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func (z *zapLogger) Infow(msg string, keysAndValues ...interface{}) {
	defer z.sync()
	z.logger.Infow(msg, keysAndValues...)
}

// Warn uses fmt.Sprint to construct and log a message.
func (z *zapLogger) Warn(args ...interface{}) {
	defer z.sync()
	z.logger.Warn(args...)
}

// Warnf uses fmt.Sprintf to log a templated message.
func (z *zapLogger) Warnf(template string, args ...interface{}) {
	defer z.sync()
	z.logger.Warnf(template, args...)
}

// Warnw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func (z *zapLogger) Warnw(msg string, keysAndValues ...interface{}) {
	defer z.sync()
	z.logger.Warnw(msg, keysAndValues...)
}

// Error uses fmt.Sprint to construct and log a message.
func (z *zapLogger) Error(args ...interface{}) {
	defer z.sync()
	z.logger.Error(args...)
}

// Errorf uses fmt.Sprintf to log a templated message.
func (z *zapLogger) Errorf(template string, args ...interface{}) {
	defer z.sync()
	z.logger.Errorf(template, args...)
}

// Errorw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func (z *zapLogger) Errorw(msg string, keysAndValues ...interface{}) {
	defer z.sync()
	z.logger.Errorw(msg, keysAndValues...)
}

// Panic uses fmt.Sprint to construct and log a message.
func (z *zapLogger) Panic(args ...interface{}) {
	defer z.sync()
	z.logger.Panic(args...)
}

// Panicf uses fmt.Sprintf to log a templated message.
func (z *zapLogger) Panicf(template string, args ...interface{}) {
	defer z.sync()
	z.logger.Panicf(template, args...)
}

// Panicw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func (z *zapLogger) Panicw(msg string, keysAndValues ...interface{}) {
	defer z.sync()
	z.logger.Panicw(msg, keysAndValues...)
}

// Fatal uses fmt.Sprint to construct and log a message.
func (z *zapLogger) Fatal(args ...interface{}) {
	defer z.sync()
	z.logger.Fatal(args...)
}

// Fatalf uses fmt.Sprintf to log a templated message.
func (z *zapLogger) Fatalf(template string, args ...interface{}) {
	defer z.sync()
	z.logger.Fatalf(template, args...)
}

// Fatalw logs a message with some additional context.
// The variadic key-value pairs are treated as they are in With.
func (z *zapLogger) Fatalw(msg string, keysAndValues ...interface{}) {
	defer z.sync()
	z.logger.Fatalw(msg, keysAndValues...)
}

// SetLevel changes the minimum enabled log level.
func (z *zapLogger) SetLevel(level string) {
	z.level.SetLevel(strToZapLevel(level))
}

// IsDebugLevel checks if the minimum enabled log level is DEBUG.
func (z *zapLogger) IsDebugLevel() bool {
	return z.level.Level().Enabled(zapcore.DebugLevel)
}

func (z *zapLogger) sync() {
	_ = z.logger.Sync()
}

// strToZapLevel converts the level string to Zap level.
// If the string is not valid, strToZapLevel uses INFO level.
func strToZapLevel(level string) zapcore.Level {
	var l zapcore.Level
	if err := l.UnmarshalText([]byte(level)); err != nil {
		return zapcore.InfoLevel
	}
	return l
}
