package executor

import (
	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql"
	"github.com/vektah/gqlparser/v2/ast"
)

const SchemaVerticesTag = "Vertices"
const SchemaEdgesTag = "Edges"
const TypeNameTag = "__typename"
const VariableCommonSuffix = "_var_"

type NodesInfo struct {
	nodes                []*ast.Field
	parents              map[int]*ast.Field
	children             map[int][]*ast.Field
	node_vars            map[int]int
	edge_accums          map[int]int
	filtered_node_vars   map[int]int
	childless            map[int]bool
	ordered_node_vars    map[int]int
	limit_node_vars      map[int]int
	offset_node_vars     map[int]int
	ordered_node_args    map[int][]OrderByArg
	graphTypeNameDefined bool
	graphAliasName       string
	includeDirs          map[int]bool
	skipDirs             map[int]bool
	requestedFields      map[string]bool // needed to track requested fields in case when we have vertices or edges with the same name
}

func NewNodesInfo() NodesInfo {
	return NodesInfo{
		nodes:                make([]*ast.Field, 0),
		parents:              make(map[int]*ast.Field),
		children:             make(map[int][]*ast.Field),
		node_vars:            make(map[int]int),
		edge_accums:          make(map[int]int),
		filtered_node_vars:   make(map[int]int),
		childless:            make(map[int]bool),
		ordered_node_vars:    make(map[int]int),
		limit_node_vars:      make(map[int]int),
		offset_node_vars:     make(map[int]int),
		ordered_node_args:    map[int][]OrderByArg{},
		graphTypeNameDefined: false,
		graphAliasName:       "",
		includeDirs:          make(map[int]bool),
		skipDirs:             make(map[int]bool),
		requestedFields:      make(map[string]bool)}
}

type SelectClause int

const (
	Where SelectClause = iota
	OrderBy
	Limit
	Offset
)

var selectClauses []SelectClause

func init() {
	// helper array for processing select clauses in correct order
	// during translation we must iterate it from 1st to last to specify the correct vertex id
	// during stitching we must iterate it from last to 1st to get the correct vertex id which was set on transaltion phase
	selectClauses = make([]SelectClause, 0)
	selectClauses = append(selectClauses, Where)
	selectClauses = append(selectClauses, OrderBy)
	selectClauses = append(selectClauses, Limit)
	selectClauses = append(selectClauses, Offset)
}

func IsEdge(node *ast.Field, edgeMap map[string]gsql.EdgeInfo) bool {
	if node.Definition.Type.NamedType == "" {
		if _, ok := edgeMap[node.Definition.Type.Elem.NamedType]; ok {
			return true
		}
		return false
	}
	return false
}

func GetEdgeInfo(node *ast.Field, edgeMap map[string]gsql.EdgeInfo) gsql.EdgeInfo {
	return edgeMap[node.Definition.Type.Elem.NamedType]
}

func processQuery(selection ast.Selection, types map[string]*ast.Definition, vmaker *VariableMaker, nodeIdGen *NodeIdGenerator,
	graphName string, vars map[string]interface{}, schemaInfo *gsql.SchemaInfo, isParentVertex bool) (NodesInfo, error) {
	query, ok := selection.(*ast.Field)
	if !ok {
		return NodesInfo{}, errors.Errorf("operation's selection set is not of type %T", selection)
	}

	var res = NewNodesInfo()

	newNode := copyNode(query)
	newNode.Position.Start = nodeIdGen.getId()

	nodes, err := DFS_Traversal(newNode,
		&res,
		schemaInfo,
		vmaker,
		nodeIdGen,
		graphName,
		vars,
		isParentVertex)

	if err != nil {
		return NodesInfo{}, err
	}
	res.nodes = nodes

	for _, n := range res.nodes {
		res.childless[getKey(n)] = true
	}
	for _, v := range res.parents {
		res.childless[getKey(v)] = false
	}

	return res, nil
}

func (nodesInfo *NodesInfo) calcVertexIdForTranslation(node *ast.Field, clause SelectClause) int {
	vertexSetId := -1
	key := getKey(node)

	switch clause {
	case Where:
		{
			vertexSetId = nodesInfo.filtered_node_vars[key]
		}
	case OrderBy:
		{
			vertexSetId = nodesInfo.ordered_node_vars[key]
		}
	case Limit:
		{
			vertexSetId = nodesInfo.limit_node_vars[key]
		}
	case Offset:
		{
			vertexSetId = nodesInfo.offset_node_vars[key]
		}
	}

	return vertexSetId
}

func (nodesInfo *NodesInfo) calcVertextIdForStitching(node *ast.Field) int {
	key := getKey(node)
	vertextId := nodesInfo.node_vars[key]

	// check was vertex filtered with 'where' or ordered with 'order_by'. If yes, then we need to use filtered node instead of main node etc

	for clause := len(selectClauses) - 1; clause >= 0; clause-- {
		switch selectClauses[clause] {
		case Offset:
			{
				if _, ok := nodesInfo.offset_node_vars[key]; ok {
					vertextId = nodesInfo.offset_node_vars[key]
				}
			}
		case Limit:
			{
				if _, ok := nodesInfo.limit_node_vars[key]; ok {
					vertextId = nodesInfo.limit_node_vars[key]
				}
			}
		case OrderBy:
			{
				if _, ok := nodesInfo.ordered_node_vars[key]; ok {
					vertextId = nodesInfo.ordered_node_vars[key]
				}
			}
		case Where:
			{
				if _, ok := nodesInfo.filtered_node_vars[key]; ok {
					vertextId = nodesInfo.filtered_node_vars[key]
				}
			}
		}
	}

	return vertextId
}
