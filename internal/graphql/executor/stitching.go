package executor

import (
	"fmt"
	"sort"
	"strconv"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql"
	"github.com/vektah/gqlparser/v2/ast"
	//"unicode"
	//"strings"
)

const edgeNodeTag = "to"
const vertexResultTag = "vertex_set_%v"
const edgeResultTag = "@@edges_%v"

func stitchResults(gsqlResult []interface{},
	gsqlQuery TranslationResult,
	graphName string) map[string]interface{} {

	var schemaInfo = gsqlQuery.schemaInfo

	var result = make(map[string]interface{})

	var nodesInfo = &gsqlQuery.nodesInfo

	result[nodesInfo.graphAliasName] = make(map[string]interface{})

	partialResultMaps := make(map[int]map[string]interface{})
	var vertices = result[nodesInfo.graphAliasName].(map[string]interface{})
	if nodesInfo.graphTypeNameDefined {
		vertices[TypeNameTag] = graphName
	}

	childrenCounts := make(map[int]int)
	for k, v := range nodesInfo.children {
		childrenCounts[k] = len(v)
	}

	for i, n := range nodesInfo.nodes {
		if needSkip(n, nodesInfo) {
			continue
		}

		var resultChunk = gsqlResult[i].(map[string]interface{})

		nodeName := getNodeName(n)
		key := getKey(n)

		alias := nodeName
		if n.Alias != "" {
			alias = n.Alias
		}

		parentNode, hasParent := nodesInfo.parents[key]
		parentId := 0
		isChildless := nodesInfo.childless[key]

		if schemaInfo.VertexMap[nodeName] {
			vertexId := getVertexId(n, nodesInfo)

			resultNodeChunk := getGSQLObjectResult(resultChunk, true, vertexId)

			for _, v := range resultNodeChunk {
				v_id := mapIdToString(v.(map[string]interface{})["v_id"])

				if hasParent {
					// process common nodes and leaf nodes here
					parentKey := getKey(parentNode)
					parentId = nodesInfo.edge_accums[parentKey]
					parentResult := partialResultMaps[parentId]

					// skip child if parent vertex was limited and edge for it was not processed
					if _, found := parentResult[v_id]; !found {
						continue
					}
					if _, ok := partialResultMaps[vertexId]; !ok {
						partialResultMaps[vertexId] = make(map[string]interface{})
					}
					for _, obj := range parentResult[v_id].([]map[string]interface{}) {
						saveVertex(n, vertexId, v_id, v, obj, nodesInfo, &gsqlQuery.schemaInfo, isChildless, partialResultMaps)
					}
				} else {
					// process root nodes and acount if it childless
					// allocate main entry for node in the result. All next changes would done via partialResultMaps by reference
					obj := allocateResultRootEntry(vertices, alias)

					if _, ok := partialResultMaps[vertexId]; !ok {
						partialResultMaps[vertexId] = make(map[string]interface{})
					}

					saveVertex(n, vertexId, v_id, v, obj, nodesInfo, &gsqlQuery.schemaInfo, isChildless, partialResultMaps)
				}
			}

			// execution of sorting for root vertices which have orderBy clause
			if !hasParent {
				if _, ok := vertices[alias]; ok {
					sortResultForRootVertex(vertices[alias].([]map[string]interface{}), nodesInfo, key)
				}
			}
		} else if IsEdge(n, schemaInfo.EdgeMap) {
			var edgeId = nodesInfo.edge_accums[key]
			partialResultMaps[edgeId] = make(map[string]interface{})
			resultEdgeChunk := getGSQLObjectResult(resultChunk, false, edgeId)

			var parentResult map[string]interface{}
			parentKey := -1
			if hasParent {
				parentId = getVertexId(nodesInfo.parents[key], nodesInfo)
				parentResult = partialResultMaps[parentId]
				parentKey = getKey(parentNode)
			} else { // case when user requested information just about edges (example: mutation for insertation of edges)
				vertices[nodeName] = make([]map[string]interface{}, 0)
				for _, e := range resultEdgeChunk {
					var obj = make(map[string]interface{})
					for _, s := range n.SelectionSet {
						fnode := s.(*ast.Field)
						alias := fnode.Name
						if fnode.Alias != "" {
							alias = fnode.Alias
						}
						var edge = e.(map[string]interface{})

						switch alias {
						case "from":
							{
								from := make(map[string]interface{})
								from["id"] = edge["from_id"]
								obj[alias] = from
							}
						case "to":
							{
								to := make(map[string]interface{})
								to["id"] = edge["to_id"]
								obj[alias] = to
							}
						default:
							{
								obj[alias] = edge["attributes"].(map[string]interface{})[fnode.Name]
							}
						}
					}
					vertices[nodeName] = append(vertices[nodeName].([]map[string]interface{}), obj)
				}
				continue
			}

			notNilEdges := make(map[string]bool)
			if !isChildless {
				notNilEdges = getNotNillEdges(key, nodesInfo, gsqlResult)
			}

			// need to allocate edge entry in result. It is important for cases when edge result is empty
			for k := range parentResult {
				for _, o := range parentResult[k].([]map[string]interface{}) {
					if _, ok := o[alias]; !ok {
						o[alias] = make([]interface{}, 0)
					}
				}
			}

			for _, e := range resultEdgeChunk {
				var edge = e.(map[string]interface{})
				var from_id = mapIdToString(edge["from_id"])
				var to_id = mapIdToString(edge["to_id"])

				// check if parent node was limited and no all results are available for edges
				// so, need to skip edge if parent result doesn't contain element for it
				if _, ok := nodesInfo.limit_node_vars[parentKey]; ok {
					if _, found := parentResult[from_id]; !found {
						continue
					}
				}

				for _, o := range parentResult[from_id].([]map[string]interface{}) {
					var newObj = make(map[string]interface{})
					projectFields(n, edge, newObj, nodesInfo, &gsqlQuery.schemaInfo)

					toTag := getToTag(n)

					if needToSkipTo(n, nodesInfo) {
						continue
					}

					if !isChildless {
						to_id := edge["to_id"].(string)
						if notNilEdges[to_id] {
							newObj[toTag] = make(map[string]interface{})
						} else {
							newObj[toTag] = nil
						}
					}
					o[alias] = append(o[alias].([]interface{}), newObj)

					if !isChildless {
						if newObj[toTag] == nil {
							addNilToIndex(partialResultMaps[edgeId], to_id)
						} else {
							addToIndex(partialResultMaps[edgeId], to_id, newObj[toTag].(map[string]interface{}))
						}
					}
				}
			}
		}
		cleanupPartialResultMap(partialResultMaps, hasParent, parentNode, parentId, childrenCounts)
	}

	return result
}

func getToTag(n *ast.Field) string {
	var selSet = n.SelectionSet
	toTag := "to"
	for _, s := range selSet {
		var fnode = s.(*ast.Field)
		if fnode.Name == "to" && fnode.Alias != "" {
			toTag = fnode.Alias
		}
	}

	return toTag
}

func needToSkipTo(n *ast.Field, nodesInfo *NodesInfo) bool {
	var selSet = n.SelectionSet
	for _, s := range selSet {
		var fnode = s.(*ast.Field)
		if fnode.Name == "to" {
			return needSkip(fnode, nodesInfo)
		}
	}

	return false
}

func sortResultForRootVertex(res []map[string]interface{}, nodesInfo *NodesInfo, key int) {
	if _, ok := nodesInfo.ordered_node_vars[key]; ok {
		orderByArgs := nodesInfo.ordered_node_args[key]
		sort.Slice(res[:], func(i, j int) bool {
			var compRes = false
			var orderType = "asc"
		loop:
			for _, orderByArg := range orderByArgs {
				orderType = orderByArg.order
				switch orderByArg.dataType {
				case "Uint64":
					uintLeft, _ := res[i][orderByArg.field].(float64)
					uintRight, _ := res[j][orderByArg.field].(float64)
					if uintLeft == uintRight {
						continue
					}
					compRes = uintLeft < uintRight
					break loop
				case "Int64":
					intLeft, _ := res[i][orderByArg.field].(float64)
					intRight, _ := res[j][orderByArg.field].(float64)

					if intLeft == intRight {
						continue
					}
					compRes = intLeft < intRight
					break loop
				case "Float":
					floatLeft, _ := res[i][orderByArg.field].(float64)
					floatRight, _ := res[j][orderByArg.field].(float64)
					if floatLeft == floatRight {
						continue
					}
					compRes = floatLeft < floatRight
					break loop
				case "String", "Datetime":
					stringLeft, _ := res[i][orderByArg.field].(string)
					stringRight, _ := res[j][orderByArg.field].(string)
					if stringLeft == stringRight {
						continue
					}
					compRes = stringLeft < stringRight
					break loop
				}
			}
			if orderType == "asc" {
				return compRes
			}
			return !compRes
		})
	}
}

func getNotNillEdges(key int, nodesInfo *NodesInfo, gsqlResult []interface{}) map[string]bool {
	// check was edge fitlered out
	notNilEdges := make(map[string]bool)
	children := nodesInfo.children[key]

	for _, ch := range children {
		nodeIndex := indexOf(ch, nodesInfo.nodes)
		childResultChunk := gsqlResult[nodeIndex].(map[string]interface{})

		childVertexId := getVertexId(ch, nodesInfo)

		resultChildNodeChunk := getGSQLObjectResult(childResultChunk, true, childVertexId)

		for _, v := range resultChildNodeChunk {
			v_id := mapIdToString(v.(map[string]interface{})["v_id"])
			notNilEdges[v_id] = true
		}
	}

	return notNilEdges
}

func cleanupPartialResultMap(partialResultMaps map[int]map[string]interface{}, hasParent bool, parentNode *ast.Field, parentId int, childrenCounts map[int]int) {
	// Here we cleanup partialResultMap in case when we saved all children for parent node and we don't need to keep them for all stitiching execution
	// At the end in total we will get empty partialResultMap
	if hasParent {
		parentKey := getKey(parentNode)
		childrenCounts[parentKey]--

		if childrenCounts[parentKey] == 0 {
			delete(partialResultMaps, parentId)
		}
	}
}

// FIXME: Need to revisit this method after the demo, at
// least we should support Datetime as id, and make sure
// we hadnle all the Go datatypes
func mapIdToString(id interface{}) string {
	switch v := id.(type) {
	case int:
		return strconv.Itoa(v)
	case int8:
		return strconv.FormatInt(int64(v), 10)
	case int16:
		return strconv.FormatInt(int64(v), 10)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int64:
		return strconv.FormatInt(v, 10)
	case string:
		return v
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case fmt.Stringer:
		return v.String()
	default:
		panic("Illegal edge or vertex id type encountered")
	}
}

func initIndex(index map[string]interface{}, key string) {
	if _, ok := index[key]; !ok {
		index[key] = make([]map[string]interface{}, 0)
	}
}

func addToIndex(index map[string]interface{}, key string, value map[string]interface{}) {
	initIndex(index, key)
	index[key] = append(index[key].([]map[string]interface{}), value)
}

func addNilToIndex(index map[string]interface{}, key string) {
	initIndex(index, key)
	index[key] = append(index[key].([]map[string]interface{}), nil)
}

func projectFields(node *ast.Field,
	gsqlObj map[string]interface{},
	resultObj map[string]interface{},
	nodesInfo *NodesInfo,
	schemaInfo *gsql.SchemaInfo) {
loop:
	for _, s := range node.SelectionSet {
		fnode := s.(*ast.Field)
		if needSkip(fnode, nodesInfo) {
			continue
		}

		alias := fnode.Name
		if fnode.Alias != "" {
			alias = fnode.Alias
		}

		if _, ok := nodesInfo.requestedFields[fnode.Name]; ok {
			pks := schemaInfo.PrimaryKeysByVertices[getNodeName(node)]
			for _, p := range pks {
				if p == fnode.Name {
					if val, ok := gsqlObj["attributes"].(map[string]interface{})[fnode.Name]; ok {
						resultObj[alias] = val
					} else {
						resultObj[alias] = gsqlObj["v_id"]
					}
					continue loop
				}
			}
			if fnode.Name == TypeNameTag {
				resultObj[alias] = node.Name
			} else {
				resultObj[alias] = gsqlObj["attributes"].(map[string]interface{})[fnode.Name]
			}
		}
	}
}

func getNodeName(node *ast.Field) string {
	nodeName := node.Name
	// "to" - is the service tag to define the edge's target
	if nodeName == edgeNodeTag {
		nodeName = node.Definition.Type.NamedType
	}
	return nodeName
}

func getVertexId(node *ast.Field, nodesInfo *NodesInfo) int {
	return nodesInfo.calcVertextIdForStitching(node)
}

func saveVertex(node *ast.Field, vertexId int, v_id string, gsqlResult interface{}, obj map[string]interface{}, nodesInfo *NodesInfo, schemaInfo *gsql.SchemaInfo, childless bool, partialResultMaps map[int]map[string]interface{}) {
	projectFields(node, gsqlResult.(map[string]interface{}), obj, nodesInfo, schemaInfo)
	if !childless {
		addToIndex(partialResultMaps[vertexId], v_id, obj)
	}
}

func getGSQLObjectResult(resultChunk map[string]interface{}, isVertex bool, id int) []interface{} {
	if isVertex {
		return resultChunk[fmt.Sprintf(vertexResultTag, id)].([]interface{})
	} else {
		return resultChunk[fmt.Sprintf(edgeResultTag, id)].([]interface{})
	}
}

func allocateResultRootEntry(vertices map[string]interface{}, nodeName string) map[string]interface{} {
	if _, ok := vertices[nodeName]; !ok {
		vertices[nodeName] = make([]map[string]interface{}, 0)
	}
	obj := make(map[string]interface{})
	vertices[nodeName] = append(vertices[nodeName].([]map[string]interface{}), obj)
	return obj
}

func indexOf(element *ast.Field, data []*ast.Field) int {
	for k, v := range data {
		if element == v {
			return k
		}
	}
	panic(fmt.Sprintf("Cannot find node '%v' in list of nodes", element.Name))
}

func needSkipNode(node *ast.Field, nodesInfo *NodesInfo) bool {
	key := getKey(node)
	if val, ok := nodesInfo.includeDirs[key]; ok {
		if !val {
			return true
		}
	}

	if val, ok := nodesInfo.skipDirs[key]; ok {
		if val {
			return true
		}
	}

	return false
}

func needSkip(node *ast.Field, nodesInfo *NodesInfo) bool {
	if needSkipNode(node, nodesInfo) {
		return true
	}

	parentNode, hasParent := nodesInfo.parents[getKey(node)]

	if hasParent {
		if needSkipNode(parentNode, nodesInfo) {
			return true
		}
	}

	return false
}

func remove(slice []ast.Selection, s int) []ast.Selection {
	return append(slice[:s], slice[s+1:]...)
}

func stitchMutationResults(gsqlResult []interface{},
	gsqlQuery *MutationTranslationResult,
	graphName string,
	mutationObject string,
	mutationNamedType string,
	selection ast.Selection,
	schema *ast.Schema,
	vars map[string]interface{},
	schemaInfo gsql.SchemaInfo) (map[string]interface{}, error) {

	var vmaker VariableMaker
	var nodeIdGen NodeIdGenerator

	query, ok := selection.(*ast.Field)
	if !ok {
		return nil, errors.Errorf("Incorrect selection set")

	}

	var mutationTag = ""
	s := query.SelectionSet[0]
	switch node := s.(type) {
	case *ast.Field:
		mutationTag = node.Name
		if node.Alias != "" {
			mutationTag = node.Alias
		}
		node.Name = mutationObject
		node.Alias = mutationObject

		if node.Definition.Type.NamedType == "" {
			node.Definition.Type.Elem.NamedType = mutationNamedType
		}
	}

	isParentVertex := false

	if !schemaInfo.VertexMap[mutationObject] {
		isParentVertex = true
	}

	nodeInfo, err := processQuery(selection, schema.Types, &vmaker, &nodeIdGen, graphName, vars, &schemaInfo, isParentVertex)
	if err != nil {
		return nil, err
	}

	query.SelectionSet = remove(query.SelectionSet, 0)

	var resultForStitching = TranslationResult{
		Query:      gsqlQuery.Query,
		nodesInfo:  nodeInfo,
		schemaInfo: schemaInfo,
	}
	res, err := stitchResults(gsqlResult, resultForStitching, graphName), nil

	result := make(map[string]interface{})
	result[mutationTag] = res
	return result, err
}
