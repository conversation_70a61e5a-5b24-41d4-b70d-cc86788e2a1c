package executor

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql"
	"github.com/vektah/gqlparser/v2/ast"
)

func deleteVerticesAllVertices(graphName string, objectName string) (
	query1 *MutationTranslationResult,
	query2 *MutationTranslationResult,
	mutationObject string,
	err error) {

	var queryStr = &strings.Builder{}
	queryStr.WriteString(fmt.Sprintf("INTERPRET QUERY() FOR GRAPH %s SYNTAX V2\n{\n", graphName))
	queryStr.WriteString(fmt.Sprintf("vertex_set_service = {%v.*};\n", objectName))
	queryStr.WriteString("vertex_set_0 = SELECT s FROM vertex_set_service:s\n")
	queryStr.WriteString("POST-ACCUM delete(s);\nprint vertex_set_0;\n}")
	// fmt.Println(queryStr.String())
	query := MutationTranslationResult{
		Query:  queryStr.String(),
		Params: make([]Param, 0),
	}
	return &query, nil, objectName, nil
}

func deleteVerticesBySimpleWhereClause(graphName string, objectName string, whereStr string) (
	query1 *MutationTranslationResult,
	query2 *MutationTranslationResult,
	mutationObject string,
	err error) {

	var queryStr = &strings.Builder{}
	queryStr.WriteString(fmt.Sprintf("INTERPRET QUERY() FOR GRAPH %s \n SYNTAX V2{\n", graphName))
	queryStr.WriteString(fmt.Sprintf("vertex_set_0 = {%v.*};\n", objectName))
	queryStr.WriteString(fmt.Sprintf("vertex_set_1 = SELECT s FROM vertex_set_0:s WHERE %v\n", whereStr))
	queryStr.WriteString("POST-ACCUM delete(s);\nprint vertex_set_1;\n}")
	// fmt.Println(queryStr.String())
	query := MutationTranslationResult{
		Query:  queryStr.String(),
		Params: make([]Param, 0),
	}
	return &query, nil, objectName, nil
}

func deleteVerticesByAttibutes(graphName string, objectName string, mutation *ast.Field, schemaInfo gsql.SchemaInfo) (
	query1 *MutationTranslationResult,
	query2 *MutationTranslationResult,
	mutationObject string,
	err error) {

	whereClause := ""
	if len(mutation.Arguments) != 0 {
		var whereStr = &strings.Builder{}
		whereStr, err := ConvertWhere(mutation, "s", schemaInfo.AttributeMap[objectName])
		if err != nil {
			return nil, nil, "", err
		}
		whereClause = whereStr.String()
	}
	if len(whereClause) == 0 {
		return deleteVerticesAllVertices(graphName, objectName)
	} else {
		return deleteVerticesBySimpleWhereClause(graphName, objectName, whereClause)
	}
}

func collectPrimaryIdValues(mutation *ast.Field) []string {
	primaryIdValues := make([]string, 0)
	for _, arg := range mutation.Arguments {
		if arg.Name == "ids" {
			for _, child := range arg.Value.Children {
				primaryIdValues = append(primaryIdValues, child.Value.Raw)
			}
		}
	}
	return primaryIdValues
}

func deleteVerticesByPrimaryId(graphName string, objectName string, mutation *ast.Field) (
	query1 *MutationTranslationResult,
	query2 *MutationTranslationResult,
	mutationObject string,
	err error) {

	queryParamStr := &strings.Builder{}
	queryBodyStr := &strings.Builder{}
	queryParams := make([]Param, 0)
	for i, primaryIdValue := range collectPrimaryIdValues(mutation) {
		if i != 0 {
			queryParamStr.WriteString(",")
			queryBodyStr.WriteString(",")
		}
		queryParamStr.WriteString(fmt.Sprintf("vertex<%v> v%d", objectName, i))
		queryBodyStr.WriteString(fmt.Sprintf("v%d", i))
		queryParams = append(queryParams, Param{Name: fmt.Sprintf("v%d", i),
			Value:      primaryIdValue,
			VertexType: objectName})
	}

	var queryStr = &strings.Builder{}
	queryStr.WriteString(fmt.Sprintf("INTERPRET QUERY(%v) FOR GRAPH %s SYNTAX V2\n{\n", queryParamStr.String(), graphName))
	queryStr.WriteString(fmt.Sprintf("vertex_set_service = { %v };\n", queryBodyStr.String()))
	queryStr.WriteString("vertex_set_0 = SELECT s FROM vertex_set_service:s;\n")
	queryStr.WriteString("print vertex_set_0;\n")
	queryStr.WriteString("DELETE s FROM vertex_set_service:s;\n}")
	query := MutationTranslationResult{
		Query:  queryStr.String(),
		Params: queryParams,
	}
	return &query, nil, objectName, nil
}

func deleteEdgesByAttributes(graphName string, objectName string, mutation *ast.Field, schemaInfo gsql.SchemaInfo) (
	query1 *MutationTranslationResult,
	query2 *MutationTranslationResult,
	mutationObject string,
	err error) {

	whereClause := ""
	if len(mutation.Arguments) != 0 {
		var whereStr = &strings.Builder{}
		whereStr, err := ConvertWhere(mutation, "e", schemaInfo.AttributeMap[objectName])
		if err != nil {
			return nil, nil, "", err
		}
		whereClause = whereStr.String()
	}

	var queryStr = &strings.Builder{}
	queryStr.WriteString(fmt.Sprintf("INTERPRET QUERY() FOR GRAPH %s SYNTAX V2\n{\n", graphName))
	queryStr.WriteString("SetAccum<EDGE> @@edges_0;\n")
	queryStr.WriteString(fmt.Sprintf("vertex_set_0 = {%s.*};\n", schemaInfo.EdgeMap[objectName].FromVertexName))
	var directionalSymbol = ""
	if schemaInfo.EdgeMap[objectName].IsDirectional {
		directionalSymbol = ">"
	}
	queryStr.WriteString(fmt.Sprintf("vertex_set_1 = SELECT s FROM vertex_set_0:s - (%s%s:e) - :tgt",
		schemaInfo.EdgeMap[objectName].Name,
		directionalSymbol))
	if len(whereClause) != 0 {
		queryStr.WriteString(fmt.Sprintf("\nWHERE %v", whereClause))
	}

	queryStr.WriteString("\nACCUM @@edges_0 += e, delete(e);\nprint @@edges_0;\n}")
	query := MutationTranslationResult{
		Query:  queryStr.String(),
		Params: make([]Param, 0),
	}
	return &query, nil, schemaInfo.EdgeMap[objectName].Name, nil

}

func findElement(child *ast.ChildValue, name string) (*ast.ChildValue, error) {
	for _, f := range child.Value.Children {
		if f.Name == name {
			return f, nil
		}
	}
	return nil, errors.Errorf("not found")
}

func extractKey(keyType string, child *ast.ChildValue, objectName string, schemaInfo gsql.SchemaInfo) (string, error) {
	for _, f := range child.Value.Children {
		if f.Name == keyType {
			keyString := strings.Builder{}

			var keys []string
			if keyType == "from" {
				keys = schemaInfo.PrimaryKeysByVertices[schemaInfo.EdgeMap[objectName].FromVertexName]
			} else {
				keys = schemaInfo.PrimaryKeysByVertices[schemaInfo.EdgeMap[objectName].ToVertexName]
			}

			for i, k := range keys {
				keyElement, err := findElement(f, k)
				if err != nil {
					return "", errors.Errorf(fmt.Sprintf("Key component '%s' not found in delete edge by id argument", k))
				}
				keyString.WriteString(FormatParameter(keyElement.Value.Raw))
				if i != len(keys)-1 {
					keyString.WriteString(",")
				}
			}
			return keyString.String(), nil
		}
	}
	return "", errors.Errorf(fmt.Sprintf("Paramter '%s' not found in delete edge by id argument", keyType))
}

func deleteEdgesByIds(graphName string, objectName string, mutation *ast.Field, schemaInfo gsql.SchemaInfo) (
	query1 *MutationTranslationResult,
	query2 *MutationTranslationResult,
	mutationObject string,
	err error) {

	queryParamStr := &strings.Builder{}
	queryPreambleStr := &strings.Builder{}
	queryBodyStr := &strings.Builder{}
	queryParams := make([]Param, 0)

	for _, arg := range mutation.Arguments {
		if arg.Name == "ids" {
			for i, child := range arg.Value.Children {
				if i != 0 {
					queryParamStr.WriteString(",")
				}

				queryParamStr.WriteString(fmt.Sprintf("vertex<%s> s%d,", schemaInfo.EdgeMap[objectName].FromVertexName, i))
				queryParamStr.WriteString(fmt.Sprintf("vertex<%s> t%d", schemaInfo.EdgeMap[objectName].ToVertexName, i))

				fromKey, err := extractKey("from", child, objectName, schemaInfo)
				if err != nil {
					return nil, nil, "", err

				}
				toKey, err := extractKey("to", child, objectName, schemaInfo)
				if err != nil {
					return nil, nil, "", err

				}

				queryParams = append(queryParams, Param{Name: fmt.Sprintf("s%d", i),
					Value:      fromKey,
					VertexType: schemaInfo.EdgeMap[objectName].FromVertexName})
				queryParams = append(queryParams, Param{Name: fmt.Sprintf("t%d", i),
					Value:      toKey,
					VertexType: schemaInfo.EdgeMap[objectName].ToVertexName})

				queryPreambleStr.WriteString(fmt.Sprintf("SetAccum<VERTEX<%s>> @@tgt_set_%d;\n",
					schemaInfo.EdgeMap[objectName].ToVertexName, i))

				queryBodyStr.WriteString(fmt.Sprintf("src_set_%d = {s%d};\n", i, i))
				queryBodyStr.WriteString(fmt.Sprintf("@@tgt_set_%d += t%d;\n", i, i))
				queryBodyStr.WriteString(fmt.Sprintf("vset_%d = SELECT s\n", i))
				var directionalSymbol = ""
				if schemaInfo.EdgeMap[objectName].IsDirectional {
					directionalSymbol = ">"
				}
				queryBodyStr.WriteString(fmt.Sprintf("    FROM src_set_%d:s - (%s%s:e) - %s:t\n",
					i, schemaInfo.EdgeMap[objectName].Name, directionalSymbol, schemaInfo.EdgeMap[objectName].ToVertexName))
				queryBodyStr.WriteString(fmt.Sprintf("    WHERE @@tgt_set_%d.contains(t)\n", i))
				queryBodyStr.WriteString("    ACCUM @@edges_0 += e,\n     delete(e);\n\n")
			}
		} else {
			return nil, nil, "", errors.Errorf("Illegal object in the argument of mutation")
		}
	}

	var queryStr = &strings.Builder{}
	queryStr.WriteString(fmt.Sprintf("INTERPRET QUERY(%v) FOR GRAPH %s SYNTAX V2 \n{\n", queryParamStr.String(), graphName))
	queryStr.WriteString("SetAccum<EDGE> @@edges_0;\n")
	queryStr.WriteString(queryPreambleStr.String())
	queryStr.WriteString(queryBodyStr.String())
	queryStr.WriteString("print @@edges_0;\n}")

	query := MutationTranslationResult{
		Query:  queryStr.String(),
		Params: queryParams,
	}
	return &query, nil, schemaInfo.EdgeMap[objectName].Name, nil
}

func TranslateDelete(mutation *ast.Field, schema *ast.Schema, graphName string, schemaInfo gsql.SchemaInfo) (
	query1 *MutationTranslationResult,
	query2 *MutationTranslationResult,
	mutationObject string,
	namedType string,
	err error) {

	isDeleteById := strings.HasPrefix(mutation.Name, "delete_by_id")

	index := len("delete_")
	if isDeleteById {
		index = len("delete_by_id_")
	}
	objectName := mutation.Name[index:]
	if mutation.Definition.Type.NamedType == "" {
		namedType = mutation.Definition.Name[index:]
	}

	if schemaInfo.VertexMap[objectName] {
		var query1 *MutationTranslationResult
		var query2 *MutationTranslationResult
		var mutationObject string
		if isDeleteById {
			query1, query2, mutationObject, err = deleteVerticesByPrimaryId(graphName, objectName, mutation)
		} else {
			query1, query2, mutationObject, err = deleteVerticesByAttibutes(graphName, objectName, mutation, schemaInfo)
		}
		return query1, query2, mutationObject, namedType, err
	} else if _, ok := schemaInfo.EdgeMap[objectName]; ok {
		var query1 *MutationTranslationResult
		var query2 *MutationTranslationResult
		var mutationObject string
		if isDeleteById {
			query1, query2, mutationObject, err = deleteEdgesByIds(graphName, objectName, mutation, schemaInfo)
		} else {
			query1, query2, mutationObject, err = deleteEdgesByAttributes(graphName, objectName, mutation, schemaInfo)
		}
		return query1, query2, mutationObject, namedType, err
	}

	return nil, nil, "", "", errors.Errorf("Mutation not implemented")
}
