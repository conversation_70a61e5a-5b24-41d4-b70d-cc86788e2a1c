package executor

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/expr_parser/e_ast"
	"github.com/tigergraph/graphql/expr_parser/e_error"
	"github.com/tigergraph/graphql/expr_parser/e_parser"
	"github.com/tigergraph/graphql/gsql"
	"github.com/tigergraph/graphql/gsql/builtin"
	"github.com/vektah/gqlparser/v2/ast"
	"github.com/vektah/gqlparser/v2/validator"
)

func getComparisonOp(graphqlCompOp string) string {
	switch graphqlCompOp {
	case "_eq":
		return "=="
	case "_neq":
		return "!="
	case "_gt":
		return ">"
	case "_gte":
		return ">="
	case "_lt":
		return "<"
	case "_lte":
		return "<="
	}
	return ""
}

func copyNode(node *ast.Field) *ast.Field {
	return &ast.Field{
		Alias:        node.Alias,
		Name:         node.Name,
		Arguments:    node.Arguments,
		Directives:   node.Directives,
		SelectionSet: node.SelectionSet,
		Position: &ast.Position{
			Line:   node.Position.Line,
			Column: node.Position.Column,
		},
		Definition:       node.Definition,
		ObjectDefinition: node.ObjectDefinition,
	}
}

func copySelection(sel ast.Selection, nodeIdGen *NodeIdGenerator) ast.SelectionSet {
	res := make(ast.SelectionSet, 0)
	switch node := sel.(type) {
	case *ast.Field:
		newNode := copyNode(node)
		newNode.Position.Start = nodeIdGen.getId()
		res = append(res, newNode)
	case *ast.FragmentSpread:
		for _, v := range node.Definition.SelectionSet {
			res = append(res, copySelection(v, nodeIdGen)...)
		}
	}
	return res
}

func calcSelection(sel ast.Selection) ast.SelectionSet {
	res := make(ast.SelectionSet, 0)
	switch node := sel.(type) {
	case *ast.Field:
		res = append(res, node)
	case *ast.FragmentSpread:
		for _, v := range node.Definition.SelectionSet {
			res = append(res, calcSelection(v)...)
		}
	}
	return res
}

type OrderByArg struct {
	field    string
	order    string
	dataType string
}

func processPaginationArgs(node *ast.Field, ordered_node_vars map[int]int, limit_node_vars map[int]int, offset_node_vars map[int]int, ordered_node_args map[int][]OrderByArg, vmaker *VariableMaker) {
	if node.Arguments != nil {
		var args = node.Arguments
		for _, a := range args {
			if a.Name == "order_by" {
				ordered_node_vars[getKey(node)] = vmaker.getId()
				orderByArgs := getOrderByArgs(node)
				var selSet = make(ast.SelectionSet, 0)
				for _, s := range node.SelectionSet {
					// need to figure out how to calc all fragments before dfs traversal
					selSet = append(selSet, calcSelection(s)...)
				}
				for i, v := range orderByArgs {
					for _, s := range selSet {
						var fnode = s.(*ast.Field)
						if fnode.Name == v.field {
							orderByArgs[i].dataType = fnode.Definition.Type.NamedType
							break
						}
					}
				}
				ordered_node_args[getKey(node)] = orderByArgs
			}

			if a.Name == "limit" {
				limit_node_vars[getKey(node)] = vmaker.getId()
			}

			if a.Name == "offset" {
				offset_node_vars[getKey(node)] = vmaker.getId()
			}
		}
	}
}

func hasWhereCondition(node *ast.Field) (bool, error) {
	whereFound := false
	whereExprFound := false
	if node.Arguments != nil {
		var args = node.Arguments
		for _, a := range args {
			if a.Name == "where" {
				whereFound = true
			} else if a.Name == "whereExpr" {
				whereExprFound = true
			}
		}
	}
	if whereFound && whereExprFound {
		return true, errors.Errorf("Both 'where' and 'whereExpr' conditions specified for node '%s'", node.Name)
	} else if whereFound || whereExprFound {
		return true, nil
	}
	return false, nil
}

func ConvertWhere(node *ast.Field, varName string, nodeAttributeList []gsql.Attribute) (*strings.Builder, error) {
	var args = node.Arguments
	for _, a := range args {
		if a.Name == "whereExpr" {
			whereClause, err := convert_gsql_where(a, varName, nodeAttributeList)
			return whereClause, err
		} else if a.Name == "where" {
			nodes := make([]*ast.ChildValue, 0)
			var value = a.Value
			var children = value.Children
			for _, whereElement := range children {
				nodes = append(nodes, whereElement)
			}
			whereClause := convert_graphql_where(nodes, varName)
			return whereClause, nil
		}
	}
	return nil, errors.Errorf("Unexpected error when processing where clause")
}

func convertLimit(node *ast.Field) (string, error) {
	var args = node.Arguments
	for _, a := range args {
		if a.Name == "limit" {
			return getArgValue(a), nil
		}
	}

	return "", errors.Errorf("Unexpected error when processing limit")
}

func convertOffset(node *ast.Field) (string, error) {
	var args = node.Arguments
	for _, a := range args {
		if a.Name == "offset" {
			return getArgValue(a), nil
		}
	}

	return "", errors.Errorf("Unexpected error when processing offset")
}

func convertOrderBy(node *ast.Field, varName string) (string, error) {

	var orderByArgs = getOrderByArgs(node)
	if len(orderByArgs) == 0 {
		return "", errors.Errorf("Unexpected error when processing order by")
	}

	return strings.Join(getOrderByStrings(orderByArgs, varName), ","), nil
}

func getOrderByStrings(orderByArgs []OrderByArg, varName string) []string {
	var result = make([]string, 0)

	for _, v := range orderByArgs {
		result = append(result, fmt.Sprintf("%v.%v %v", varName, v.field, v.order))
	}

	return result
}

func getOrderByArgs(node *ast.Field) []OrderByArg {
	var args = node.Arguments
	var result = make([]OrderByArg, 0)
	for _, a := range args {
		if a.Name == "order_by" {
			if a.Value.Kind == ast.ListValue { // case, when order by was defined for more that 1 field
				for _, child := range a.Value.Children {
					for _, orderByElem := range child.Value.Children {
						result = append(result, OrderByArg{field: orderByElem.Name, order: orderByElem.Value.Raw, dataType: ""})
					}
				}
			} else {
				for _, orderByElem := range a.Value.Children {
					result = append(result, OrderByArg{field: orderByElem.Name, order: orderByElem.Value.Raw, dataType: ""})
				}
			}
		}
	}

	return result
}

func convert_gsql_where(arg *ast.Argument, varName string, attributeList []gsql.Attribute) (*strings.Builder, error) {
	tree, err := e_parser.ParseExpression(arg.Value.Raw)
	if err != (*e_error.Error)(nil) {
		line := arg.Value.Position.Line
		column := arg.Value.Position.Column
		parse_err := err.(*e_error.Error)
		err = errors.Errorf("%s in the string at line: %d, column: %d", parse_err.Message, line, column)
		return nil, err
	}
	_, err = tree.TypeCheck(attributeList)
	if err != nil {
		line := arg.Value.Position.Line
		column := arg.Value.Position.Column
		type_error := err.(*e_error.Error)
		err = errors.Errorf("%s in the string at line: %d, column: %d", type_error.Message, line, column)
		return nil, err
	}

	result := strings.Builder{}
	result.WriteString(tree.ToString(varName))
	return &result, nil
}

func convert_graphql_where(whereNodes []*ast.ChildValue, varName string, boolOp ...string) *strings.Builder {
	result := strings.Builder{}
	boolOperator := ""
	if len(boolOp) > 0 {
		boolOperator = boolOp[0]
	}
	for i, w := range whereNodes {
		if i != 0 {
			result.WriteString(boolOperator)
		}

		switch w.Name {
		case "_and":
			boolOperator = " AND "
			whereClause := convert_graphql_where(w.Value.Children, varName, boolOperator)
			result.WriteString(whereClause.String())
			continue
		case "_or":
			boolOperator = " OR "
			whereClause := convert_graphql_where(w.Value.Children, varName, boolOperator)
			result.WriteString(whereClause.String())
			continue
		case "_not":
			whereClause := convert_graphql_where(w.Value.Children, varName, boolOperator)
			result.WriteString(" NOT (" + whereClause.String() + ") ")
			continue
		case "":
			whereClause := convert_graphql_where(w.Value.Children, varName, boolOperator)
			result.WriteString(whereClause.String())
			continue
		}

		fieldName := w.Name
		opInfo := w.Value.Children[0]
		fieldType := opInfo.Value.Definition.Name
		fieldValue := opInfo.Value.Raw
		compOp := getComparisonOp(opInfo.Name)

		if opInfo.Value.Kind == ast.Variable {
			result.WriteString(fmt.Sprintf("%s.%s %s %s", varName, fieldName, compOp, e_ast.SanitizeString(getArgVarValue(fieldValue))))
		} else {
			switch fieldType {
			case "String":
				result.WriteString(fmt.Sprintf("%s.%s %s \"%s\"", varName, fieldName, compOp, e_ast.SanitizeString(fieldValue)))
			case "Datetime":
				result.WriteString(fmt.Sprintf(`%s.%s %s to_datetime("%s")`, varName, fieldName, compOp, e_ast.SanitizeString(fieldValue)))
			default:
				result.WriteString(fmt.Sprintf("%s.%s %s %s", varName, fieldName, compOp, e_ast.SanitizeString(fieldValue)))
			}
		}
	}
	return &result
}

type TranslationResult struct {
	Query      string
	nodesInfo  NodesInfo
	schemaInfo gsql.SchemaInfo
}

func TranslateQuery(
	queryDoc *ast.QueryDocument,
	operation *ast.OperationDefinition,
	selection ast.Selection,
	schemaInfo *gsql.SchemaInfo,
	schema *ast.Schema,
	graphName string,
	vars map[string]interface{},
) (TranslationResult, error) {

	switch v := selection.(type) {
	case *ast.Field:
	default:
		return TranslationResult{}, errors.Errorf("do not support %T at the moment", v)
	}

	var vmaker VariableMaker
	var nodeIdGen NodeIdGenerator

	err := validateInput(schema, queryDoc)
	if err != nil {
		return TranslationResult{}, err
	}

	// Extract the actual query and perform depth-first traversal, filling in the auxillary data structures
	nodesInfo, err := processQuery(selection, schema.Types, &vmaker, &nodeIdGen, graphName, vars, schemaInfo, false)
	if err != nil {
		return TranslationResult{}, err
	}

	var gsqlQuery = &strings.Builder{}
	gsqlQuery.WriteString(fmt.Sprintf("INTERPRET QUERY(%v) FOR GRAPH %s SYNTAX V2 {\n", gsqlInputArgs(operation.VariableDefinitions), graphName))

	// Declare all edge accumulators
	for edgeAccum := range nodesInfo.edge_accums {
		gsqlQuery.WriteString(fmt.Sprintf("SetAccum<edge> @@edges_%d;\n", nodesInfo.edge_accums[edgeAccum]))
	}
	gsqlQuery.WriteString("\n")

	// Store vertex set id of query with applied clauses mapped to parent_vertex_set to use in edge query
	modifiedVertexSetIds := make(map[int]int)

	// Iterate through query nodes
	for _, n := range nodesInfo.nodes {
		_, hasParent := nodesInfo.parents[getKey(n)]

		// If the node has no parent that means its the root of a subquery
		// In this case we use the special syntax to obtain the vertices
		if !hasParent {
			gsqlQuery.WriteString(fmt.Sprintf("vertex_set_%d = {%s.*};\n",
				nodesInfo.node_vars[getKey(n)],
				n.Name))
		}
		// If this is a regular node with a parent, we proceed with the real translation

		var nodeName = n.Name
		if nodeName == "to" {
			nodeName = n.Definition.Type.NamedType
		}
		// In case of a vertex node, we check if there was a filtering condition on this node,
		// and introduce a filtered vertex set with the where condition. Otherwise we just print
		// the vertex set associated with this node
		if schemaInfo.VertexMap[nodeName] {
			needSelect, err := needSelect(nodesInfo, getKey(n))
			if err != nil {
				return TranslationResult{}, err
			}

			vertex_set_id := nodesInfo.node_vars[getKey(n)]
			if len(needSelect.clauses) > 0 {
				vertexSet := nodesInfo.node_vars[getKey(n)]
				selectQuery := "vertex_set_%d = SELECT v FROM vertex_set_%d:v "

				for _, clause := range needSelect.clauses {
					switch clause {
					case Where:
						{
							where, werr := ConvertWhere(n, "v", schemaInfo.AttributeMap[nodeName])
							if werr != nil {
								return TranslationResult{}, werr
							}
							selectQuery += fmt.Sprintf("WHERE %s ", where.String())
						}
					case OrderBy:
						{
							orderBy, orderByErr := convertOrderBy(n, "v")
							if orderByErr != nil {
								return TranslationResult{}, orderByErr
							}
							selectQuery += fmt.Sprintf("ORDER BY %s ", orderBy)

						}
					case Limit:
						{
							limit, limitErr := convertLimit(n)
							if limitErr != nil {
								return TranslationResult{}, limitErr
							}
							selectQuery += fmt.Sprintf("LIMIT %v ", limit)

						}
					case Offset:
						{
							offset, offsetErr := convertOffset(n)
							if offsetErr != nil {
								return TranslationResult{}, offsetErr
							}
							selectQuery += fmt.Sprintf("OFFSET %v ", offset)
						}
					default:
						{
							return TranslationResult{}, errors.Errorf(fmt.Sprintf("Unexpected clause for SELECT stmt: %v", clause))
						}
					}

					if _, exists := modifiedVertexSetIds[vertex_set_id]; !exists {
						modifiedVertexSetIds[vertex_set_id] = nodesInfo.calcVertexIdForTranslation(n, clause)
					}
				}

				selectQuery += ";"
				selectQuery += "print vertex_set_%d;"
				gsqlQuery.WriteString(fmt.Sprintf(selectQuery, modifiedVertexSetIds[vertex_set_id], vertexSet, modifiedVertexSetIds[vertex_set_id]))

			} else {
				gsqlQuery.WriteString(fmt.Sprintf("print vertex_set_%d;\n", nodesInfo.node_vars[getKey(n)]))
			}
			// Otherwise, if this is an edge node, we translate this node into a path step in GSQL
			// - We obtain the parent node and find out the vertex set which we should apply the path step to
			// - We use vertexSetId set for vertex sets with clauses
			// - We check if there is a where condition on the edge node, if so we build a where clause for the path step
			// - We check if the edge node has children:
			//    - If there are no children, we save the result of a path step to a new variable that won't be used in the GSQL query
			//    - If there are children, we find the variable associated with the child vertex node
			// - Finally we generate the GSQL path step

		} else if IsEdge(n, schemaInfo.EdgeMap) {
			edgeInfo := GetEdgeInfo(n, schemaInfo.EdgeMap)
			var parent_vertex = nodesInfo.parents[getKey(n)]
			parent_vertex_key := getKey(parent_vertex)
			var parent_vertex_set = nodesInfo.node_vars[parent_vertex_key]
			if filtered_vertex_set, exists := modifiedVertexSetIds[parent_vertex_set]; exists {
				parent_vertex_set = filtered_vertex_set
			}
			var edge_name = edgeInfo.Name
			var edge_accum = nodesInfo.edge_accums[getKey(n)]
			var whereString = ""
			hasWhere, err := hasWhereCondition(n)
			if err != nil {
				return TranslationResult{}, err
			}
			if hasWhere {
				where, werr := ConvertWhere(n, "e", schemaInfo.AttributeMap[edge_name])
				if werr != nil {
					return TranslationResult{}, werr
				}
				whereString = "WHERE " + where.String() + "\n"
			}
			var vertexVar = 0
			if vertexChildren, ok := nodesInfo.children[getKey(n)]; ok {
				vertexVar = nodesInfo.node_vars[getKey(vertexChildren[0])]
			} else {
				vertexVar = vmaker.getId()
			}

			var directionalSymbol = &strings.Builder{}
			if edgeInfo.IsDirectional {
				directionalSymbol.WriteString(">")
			}
			gsqlQuery.WriteString(fmt.Sprintf(`vertex_set_%d = SELECT tgt
FROM vertex_set_%d - (%s%s:e) - %s:tgt
%s ACCUM @@edges_%d += e;
print @@edges_%d;
`,
				vertexVar,
				parent_vertex_set,
				edge_name,
				directionalSymbol,
				edgeInfo.ToVertexName,
				whereString,
				edge_accum,
				edge_accum))
		}
	}
	gsqlQuery.WriteString("}\n")
	// fmt.Println(gsqlQuery)

	return TranslationResult{Query: gsqlQuery.String(), nodesInfo: nodesInfo}, nil
}

type SelectInfo struct {
	hasFilter  bool
	hasOrderBy bool
	hasLimit   bool
	hasOffset  bool
	clauses    []SelectClause
}

func needSelect(nodesInfo NodesInfo, key int) (SelectInfo, error) {
	selectInfo := SelectInfo{
		hasFilter:  false,
		hasOrderBy: false,
		hasLimit:   false,
		hasOffset:  false,
	}

	for _, clause := range selectClauses {
		switch clause {
		case Where:
			{
				if _, ok := nodesInfo.filtered_node_vars[key]; ok {
					selectInfo.hasFilter = true
					selectInfo.clauses = append(selectInfo.clauses, Where)
				}
			}
		case OrderBy:
			{
				if _, ok := nodesInfo.ordered_node_vars[key]; ok {
					selectInfo.hasOrderBy = true
					selectInfo.clauses = append(selectInfo.clauses, OrderBy)
				}
			}
		case Limit:
			{
				if _, ok := nodesInfo.limit_node_vars[key]; ok {
					selectInfo.hasLimit = true
					selectInfo.clauses = append(selectInfo.clauses, Limit)
				}
			}
		case Offset:
			{
				if _, ok := nodesInfo.offset_node_vars[key]; ok {
					selectInfo.hasOffset = true
					selectInfo.clauses = append(selectInfo.clauses, Offset)
				}
			}
		}
	}

	if selectInfo.hasOffset {
		if !selectInfo.hasOrderBy || !selectInfo.hasLimit {
			return selectInfo, errors.Errorf("Unsupported usage of OFFSET operator. It requires ORDER BY AND LIMIT")
		}
	}

	return selectInfo, nil
}

func processFieldNode(node *ast.Field,
	result []*ast.Field,
	query *ast.Field,
	nodesInfo *NodesInfo,
	schemaInfo *gsql.SchemaInfo,
	vmaker *VariableMaker,
	nodeIdGen *NodeIdGenerator,
	graphName string,
	vars map[string]interface{},
	isParentVertex bool) ([]*ast.Field, error) {
	var nodeName = node.Name

	if nodeName == "to" {
		nodeName = node.Definition.Type.NamedType
	}

	err := processDirectives(node, nodesInfo, vars)

	if err != nil {
		return nil, err
	}

	if query.Name == graphName {
		nodesInfo.graphAliasName = graphName
		if query.Alias != "" {
			nodesInfo.graphAliasName = query.Alias
		}

		for _, selObj := range query.SelectionSet {
			var selNode = selObj.(*ast.Field)
			if selNode.Name == TypeNameTag {
				nodesInfo.graphTypeNameDefined = true
			}
		}
	}

	if schemaInfo.VertexMap[nodeName] && !isParentVertex {
		// Check if this is a vertex node. However there could be a naming conflict,
		// so also check that the parent node is not a vertex
		result = append(result, node)
		nodesInfo.node_vars[getKey(node)] = vmaker.getId()
		if query.Name != graphName {
			nodesInfo.parents[getKey(node)] = query
			childList := nodesInfo.children[getKey(query)]
			childList = append(childList, node)
			nodesInfo.children[getKey(query)] = childList
		}

		hasWhere, err := hasWhereCondition(node)
		if err != nil {
			return nil, err
		} else if hasWhere {
			nodesInfo.filtered_node_vars[getKey(node)] = vmaker.getId()
		}

		processPaginationArgs(node, nodesInfo.ordered_node_vars, nodesInfo.limit_node_vars, nodesInfo.offset_node_vars, nodesInfo.ordered_node_args, vmaker)

		fields, err := DFS_Traversal(node,
			nodesInfo,
			schemaInfo,
			vmaker,
			nodeIdGen,
			graphName,
			vars,
			true)
		if err != nil {
			return nil, err
		}
		result = append(result, fields...)
	} else if IsEdge(node, schemaInfo.EdgeMap) && isParentVertex {
		// Check if this is an edge, however there could be a conflict, so check that
		// the parent node is a vertex

		result = append(result, node)
		nodesInfo.edge_accums[getKey(node)] = vmaker.getId()

		if query.Name != graphName {
			nodesInfo.parents[getKey(node)] = query
			childList, ok := nodesInfo.children[getKey(query)]
			if !ok {
				childList = make([]*ast.Field, 0)
			}
			childList = append(childList, node)
			nodesInfo.children[getKey(query)] = childList
		}
		fields, err := DFS_Traversal(node,
			nodesInfo,
			schemaInfo,
			vmaker,
			nodeIdGen,
			graphName,
			vars,
			false)
		if err != nil {
			return nil, err
		}
		result = append(result, fields...)
	} else {
		nodesInfo.requestedFields[node.Name] = true
	}
	return result, nil
}

func processSelectionSet(selSet ast.SelectionSet,
	result []*ast.Field,
	query *ast.Field,
	nodesInfo *NodesInfo,
	schemaInfo *gsql.SchemaInfo,
	vmaker *VariableMaker,
	nodeIdGen *NodeIdGenerator,
	graphName string,
	vars map[string]interface{},
	isParentVertex bool) ([]*ast.Field, error) {

	var err error
	// Extract all the child nodes that are vertex or edge types
	for _, s := range selSet {
		switch node := s.(type) {
		case *ast.Field:
			if result, err = processFieldNode(node, result, query, nodesInfo, schemaInfo, vmaker, nodeIdGen, graphName, vars, isParentVertex); err != nil {
				return nil, err
			}
		default:
			return nil, errors.Errorf("field is of type %T, not supported type in translation", s)
		}
	}
	return result, nil
}

type VariableMaker struct {
	variableId int
}

func (v *VariableMaker) getId() int {
	var res = v.variableId
	v.variableId += 1
	return res
}

type NodeIdGenerator struct {
	nodeId int
}

func (g *NodeIdGenerator) getId() int {
	res := g.nodeId
	g.nodeId += 1
	return res
}

func getKey(node *ast.Field) int {
	return node.Position.Start
}

func DFS_Traversal(query *ast.Field,
	nodesInfo *NodesInfo,
	schemaInfo *gsql.SchemaInfo,
	vmaker *VariableMaker,
	nodeIdGen *NodeIdGenerator,
	graphName string,
	vars map[string]interface{},
	isParentVertex bool) ([]*ast.Field, error) {

	result := make([]*ast.Field, 0)

	newSelectionSet := make(ast.SelectionSet, 0)
	for _, s := range query.SelectionSet {
		newSelectionSet = append(newSelectionSet, copySelection(s, nodeIdGen)...)
	}
	query.SelectionSet = newSelectionSet
	return processSelectionSet(query.SelectionSet, result, query, nodesInfo, schemaInfo, vmaker, nodeIdGen, graphName, vars, isParentVertex)
}

func processDirectives(node *ast.Field, nodesInfo *NodesInfo, vars map[string]interface{}) error {
	nodeKey := getKey(node)
	for _, directive := range node.Directives {
		if directive.Name == "include" || directive.Name == "skip" {
			resDirs := nodesInfo.skipDirs
			if directive.Name == "include" {
				resDirs = nodesInfo.includeDirs
			}

			for _, dirArg := range directive.Arguments {
				if dirArg.Name == "if" {
					if dirArg.Value.Kind == ast.BooleanValue {
						if dirArg.Value.Raw == "true" {
							resDirs[nodeKey] = true
						} else {
							resDirs[nodeKey] = false
						}
					} else if dirArg.Value.Kind == ast.Variable {
						varVal, varExists := vars[getArgVarValue(dirArg.Value.Raw)]

						if !varExists {
							return errors.Errorf("Variable %s was not set for include directive", dirArg.Value.Raw)
						}
						resDirs[nodeKey] = varVal.(bool)
					} else {
						return errors.Errorf("Unexpected directive arg's kind %v", dirArg.Value.Kind)
					}
				} else {
					return errors.Errorf("Unexpected directive arg's name %s", dirArg.Name)
				}
			}
		}
	}

	return nil
}

func validateInput(schema *ast.Schema, query *ast.QueryDocument) error {
	ob := &validator.Events{}
	var err error
	ob.OnValue(func(w *validator.Walker, v *ast.Value) {
		if v.ExpectedType.Name() == "Datetime" && v.Kind != ast.Variable {
			_, err = builtin.ParseDatetime(v.Raw)
			if err != nil {
				err = builtin.NewErrUnacceptableInputRepresentation(v.Raw, builtin.Datetime{})
			}
		}
	})
	validator.Walk(schema, query, ob)
	return err
}

func getGsqlTypeForGraphQlType(graphQlType string, varName string) (string, error) {
	switch graphQlType {
	case "Uint64", "Uint":
		return "UINT", nil
	case "Int64", "Int":
		return "INT", nil
	case "Float":
		return "DOUBLE", nil
	case "String":
		return "STRING", nil
	case "Datetime":
		return "DATETIME", nil
	case "Boolean":
		return "BOOL", nil
	default:
		return "", errors.Errorf("Undefined variable type %v for var %v", graphQlType, varName)
	}
}

func gsqlInputArgs(varDefs ast.VariableDefinitionList) string {
	var queryInputArgs = make([]string, 0)
	for _, arg := range varDefs {
		gsqlType, err := getGsqlTypeForGraphQlType(arg.Type.NamedType, arg.Variable)

		if err != nil {
			fmt.Println(err)
			continue
		}

		queryInputArgs = append(queryInputArgs, fmt.Sprintf("%v %v", gsqlType, arg.Variable))

	}

	return strings.Join(queryInputArgs, ", ")
}

func getArgValue(arg *ast.Argument) string {
	if arg.Value.Kind == ast.Variable {
		return VariableCommonSuffix + arg.Value.Raw
	} else {
		return arg.Value.Raw
	}
}

func getArgVarValue(name string) string {
	return VariableCommonSuffix + name
}
