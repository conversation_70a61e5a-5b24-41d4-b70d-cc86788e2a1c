package executor

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql"
	"github.com/vektah/gqlparser/v2/ast"
)

type Param struct {
	Name       string
	Value      string
	VertexType string
}

type MutationTranslationResult struct {
	Query  string
	Params []Param
}

func TranslateMutationQuery(queryDoc *ast.QueryDocument,
	operation *ast.OperationDefinition,
	schema *ast.Schema,
	schemaInfo gsql.SchemaInfo) (
	query1 *MutationTranslationResult,
	query2 *MutationTranslationResult,
	mutationObject string,
	mutationNamedType string,
	err error) {

	graphNode := operation.SelectionSet[0]
	switch graphNode.(type) {
	case *ast.Field:
		graphName := graphNode.(*ast.Field).Name
		mutation := graphNode.(*ast.Field).SelectionSet[0]
		if strings.HasPrefix(mutation.(*ast.Field).Name, "insert") {
			return TranslateInsertion(mutation.(*ast.Field), schema, graphName, schemaInfo)
		} else if strings.HasPrefix(mutation.(*ast.Field).Name, "delete") {
			return TranslateDelete(mutation.(*ast.Field), schema, graphName, schemaInfo)
		} else {
			return nil, nil, "", "", errors.Errorf("Mutation not implemented")
		}

	default:
		return nil, nil, "", "", errors.Errorf("Illegal selection set item")
	}
}

// tigergraph expects parameters for vertices without unexpected quotes and commas
// to avoid comma issue we need enclose value in qoutes
// to avoid quote issue we need replace it with \"
func FormatParameter(param string) string {
	val := param
	val = strings.ReplaceAll(val, "\"", "\\\"")
	if strings.Contains(val, ",") {
		val = fmt.Sprintf("\"%s\"", val)
	}

	return val
}
