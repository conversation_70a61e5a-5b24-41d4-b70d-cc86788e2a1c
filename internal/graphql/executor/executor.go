// GraphQL Execution Engine for TigerGraph's GSQL language

package executor

import (
	"context"
	"fmt"
	"net/url"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql"
	"github.com/tigergraph/graphql/gsql/builtin"
	"github.com/tigergraph/graphql/gsql/client"
	"github.com/tigergraph/graphql/log"
	"github.com/vektah/gqlparser/v2"
	"github.com/vektah/gqlparser/v2/ast"
	"github.com/vektah/gqlparser/v2/gqlerror"
	"github.com/vektah/gqlparser/v2/validator"
)

type Params struct {
	Query         string                 `json:"query"`
	OperationName string                 `json:"operationName"`
	Variables     map[string]interface{} `json:"variables"`
}
type Result struct {
	Data   interface{}   `json:"data"`
	Errors []interface{} `json:"errors"`
}

type gsqlRunner interface {
	RunInterpretedQuery(ctx context.Context, query string, arguments url.Values) (*client.InterpretedQueryResult, error)
	RunInterpretedQueryWithParams(ctx context.Context, query string, queryParams []string) (*client.InterpretedQueryResult, error)
}

// resolver is either a map with the correct type layout or a resolver function that returns such a map
// todo: currently resolver is not used at all.
// in the future, we might want to support a resolver semantics so I have it here for future compatibility.
// subject to change.
func Exec(ctx context.Context, schema *ast.Schema, schemaInfo gsql.SchemaInfo, params Params, graphName string, gsqlRunner gsqlRunner) Result {
	if params.OperationName == IntrospectionOperation {
		return Result{
			Data: Data{
				Schema: ToClientSchema(schema),
			},
		}
	}

	// Document Preprocessing
	document, errlist := gqlparser.LoadQuery(schema, params.Query)
	if len(errlist) != 0 {
		result := Result{}
		result.Errors = errListToSlice(errlist)
		return result
	}

	operation := document.Operations.ForName(params.OperationName)
	if operation == nil {
		result := Result{
			Errors: []interface{}{
				"operation " + params.OperationName + " is not found",
			},
		}
		return result
	}
	{

		_, validatorError := validator.VariableValues(schema, operation, params.Variables)
		if validatorError != nil {
			result := Result{}
			result.Errors = []interface{}{validatorError}
			return result
		}

		overrideVariableNames(&params, operation)
	}

	// Translation & Execution
	var results []map[string]interface{}
	{
		for _, selection := range operation.SelectionSet {
			if operation.Operation == "mutation" {
				for len(selection.(*ast.Field).SelectionSet) > 0 {
					if len(operation.VariableDefinitions) > 0 {
						result := Result{}
						result.Errors = []interface{}{"TigerGraph GraphQL Service doesn't support parameterized mutation"}
						return result
					}
					gsqlQuery1, gsqlQuery2, mutationObject, mutationNamedType, err := TranslateMutationQuery(document, operation, schema, schemaInfo)

					if err != nil {
						result := Result{}
						result.Errors = []interface{}{err.Error()}
						return result
					}
					var args = make(url.Values, 0)
					// generating url arguments for vertices/edges. GSQL server requires to specify these args as ?v0[0]=<val>&v0[0].type=<vertex_type>&v1[0]=<val>&v1[0].type=<vertex_type>
					for _, val := range gsqlQuery1.Params {
						key := fmt.Sprintf("%s[0]", val.Name)
						args.Add(key, val.Value)
						args.Add(key+".type", val.VertexType)
					}
					gsqlResult, err := gsqlRunner.RunInterpretedQuery(ctx, gsqlQuery1.Query, args)
					if err != nil {
						result := Result{}
						result.Errors = []interface{}{err.Error()}
						return result
					}

					if gsqlQuery2 != nil {
						var args = make(url.Values, 0)
						// generating url arguments for vertices/edges. GSQL server requires to specify these args as ?v0[0]=<val>&v0[0].type=<vertex_type>&v1[0]=<val>&v1[0].type=<vertex_type>
						for _, val := range gsqlQuery2.Params {
							key := fmt.Sprintf("%s[0]", val.Name)
							args.Add(key, val.Value)
							args.Add(key+".type", val.VertexType)
						}
						gsqlResult, err = gsqlRunner.RunInterpretedQuery(ctx, gsqlQuery2.Query, args)
						if err != nil {
							result := Result{}
							result.Errors = []interface{}{err.Error()}
							return result
						}
					}

					lastQuery := gsqlQuery1
					if gsqlQuery2 != nil {
						lastQuery = gsqlQuery2
					}

					finalResult, err := stitchMutationResults(gsqlResult.Results.([]interface{}), lastQuery, graphName, mutationObject, mutationNamedType, selection, schema, params.Variables, schemaInfo)
					if err != nil {
						result := Result{}
						result.Errors = []interface{}{err.Error()}
						return result
					}
					results = append(results, finalResult)
				}

			} else if operation.Operation == "query" {

				gsqlQuery, err := TranslateQuery(document, operation, selection, &schemaInfo, schema, graphName, params.Variables)
				if err != nil {
					result := Result{}
					result.Errors = []interface{}{err.Error()}
					return result
				}

				gsqlQuery.schemaInfo = schemaInfo

				gsqlArgs, err := generateGsqlArgs(operation, params)
				if err != nil {
					result := Result{}
					result.Errors = []interface{}{err}
					return result
				}

				gsqlResult, err := gsqlRunner.RunInterpretedQuery(ctx, gsqlQuery.Query, gsqlArgs)
				if err != nil {
					log.Infof("%+v", err)
					cause := errors.Cause(err)
					msg := "encounterred error when executing queries, possibly due to high server load"
					if qErr, ok := cause.(*client.QueryError); ok {
						log.Info(qErr.Query)
						msg = qErr.Error()
					}
					result := Result{}
					result.Errors = []interface{}{msg}
					return result
				}

				finalResult := stitchResults(gsqlResult.Results.([]interface{}), gsqlQuery, graphName)
				results = append(results, finalResult)
			}
		}
	}

	// Merge Data
	data := make(map[string]interface{})
	for _, result := range results {
		for k, v := range result {
			_, ok := data[k]
			if ok {
				log.Errorf("logic confliction: %v already exists in %+v", k, data)
			}
			data[k] = v
		}
	}
	return Result{Data: data}
}

func errListToSlice(errList gqlerror.List) []interface{} {
	errs := make([]interface{}, len(errList))
	for i := range errList {
		errs[i] = errList[i]
	}
	return errs
}

func generateGsqlArgs(operation *ast.OperationDefinition, params Params) (url.Values, error) {
	gsqlArgs := make(url.Values)

	for _, arg := range operation.VariableDefinitions {
		argVal, ok := params.Variables[arg.Variable]
		if !ok {
			return gsqlArgs, errors.Errorf("Fatal error. Service was not able to find value for variable %v", arg.Variable)
		}
		switch arg.Type.NamedType {
		case "Uint64", "Uint", "Int64", "Int", "Float":
			gsqlArgs.Add(arg.Variable, fmt.Sprintf("%v", argVal.(float64)))
		case "String", "Datetime":
			if arg.Type.NamedType == "Datetime" {
				_, err := builtin.ParseDatetime(argVal.(string))
				if err != nil {
					err = builtin.NewErrUnacceptableInputRepresentation(argVal.(string), builtin.Datetime{})
					return gsqlArgs, err
				}
			}
			gsqlArgs.Add(arg.Variable, argVal.(string))
		case "Boolean":
			gsqlArgs.Add(arg.Variable, fmt.Sprintf("%v", argVal.(bool)))
		default:
			fmt.Printf("Undefined variable type %s for arg %s", arg.Type.NamedType, arg.Variable)
			continue
		}

	}

	return gsqlArgs, nil
}

/*
We need to ovveride variable names to avoid collisions when user specified variable name which is gsql's reserved key word
For example: LIMIT, OFFSET

Convention for variable names is the following : _var_<variableName>

In translation developer must use 2 functions to get values for arguments:

For ast.Argument: getArgValue (function checks is it variable or not then returns correct value)
for ast.Variable: getArgVarValue

*/
func overrideVariableNames(params *Params, operation *ast.OperationDefinition) {
	var overridenVariables = make(map[string]interface{})
	for paramKey, paramVal := range params.Variables {
		overridenVariables[VariableCommonSuffix+paramKey] = paramVal
	}

	params.Variables = overridenVariables

	var overridenVarDefs = make(ast.VariableDefinitionList, 0)
	for _, varVal := range operation.VariableDefinitions {
		varVal.Variable = VariableCommonSuffix + varVal.Variable
		overridenVarDefs = append(overridenVarDefs, varVal)
	}

	operation.VariableDefinitions = overridenVarDefs
}
