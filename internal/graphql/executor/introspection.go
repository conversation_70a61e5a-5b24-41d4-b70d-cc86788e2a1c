package executor

import (
	"sort"

	"github.com/vektah/gqlparser/v2/ast"
)

// This file defines a set of JSON structures that are required
// by a GraphQL client to make introspection work.
const IntrospectionOperation = "IntrospectionQuery"

type Kind string

const (
	KindList   Kind = "LIST"
	KindScalar Kind = "SCALAR"
	KindObject Kind = "OBJECT"
)

type Data struct {
	Schema Schema `json:"__schema"`
}

type OperationType struct {
	Name string `json:"name"`
}

type Schema struct {
	QueryType        *OperationType `json:"queryType"`
	MutationType     *OperationType `json:"mutationType"`
	SubscriptionType *OperationType `json:"subscriptionType"`
	Types            Types          `json:"types"`
	Directives       []Directive    `json:"directives"`
}

type Directive struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Locations   []string   `json:"locations"`
	Args        []Argument `json:"args"`
}

type Argument struct {
	Name         string       `json:"name"`
	Description  string       `json:"description"`
	Type         ArgumentType `json:"type"`
	DefaultValue interface{}  `json:"defaultValue"`
}

type ArgumentType struct {
	Kind   string      `json:"kind"`
	Name   interface{} `json:"name"`
	OfType *OfType     `json:"ofType"`
}

type OfType struct {
	Kind   string  `json:"kind"`
	Name   string  `json:"name"`
	OfType *OfType `json:"ofType"`
}

type Type struct {
	Kind          ast.DefinitionKind `json:"kind"`
	Name          string             `json:"name"`
	Description   string             `json:"description"`
	Fields        []Field            `json:"fields"`
	InputFields   []InputField       `json:"inputFields"`
	Interfaces    []string           `json:"interfaces"`
	EnumValues    []EnumValue        `json:"enumValues"`
	PossibleTypes interface{}        `json:"possibleTypes"`
}

type Types []Type

var _ sort.Interface = Types{}

func (t Types) Len() int {
	return len(t)
}
func (t Types) Less(i, j int) bool {
	return t[i].Name < t[j].Name
}
func (t Types) Swap(i, j int) {
	t[i], t[j] = t[j], t[i]
}

type InputField struct {
	Name         string      `json:"name"`
	Description  string      `json:"description"`
	Type         FieldType   `json:"type"`
	DefaultValue interface{} `json:"defaultValue"`
}

type Field struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Args        []Argument `json:"args"`
	Type        FieldType  `json:"type"`
	// IsDeprecated bool       `json:"isDeprecated"`
	// DeprecationReason string     `json:"deprecationReason"`
}

type EnumValue struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	// IsDeprecated bool   `json:"isDeprecated"`
	// DeprecationReason string `json:"deprecationReason"`
}

type FieldType struct {
	Kind   Kind       `json:"kind"`
	Name   string     `json:"name"`
	OfType *FieldType `json:"ofType"`
}

// ToClientSchema builds a client schema for introspection from a ast.Schema
func ToClientSchema(schema *ast.Schema) Schema {
	_schema := Schema{}
	if schema.Query != nil {
		_schema.QueryType = &OperationType{
			Name: schema.Query.Name,
		}
	}
	if schema.Mutation != nil {
		_schema.MutationType = &OperationType{
			Name: schema.Mutation.Name,
		}
	}
	if schema.Subscription != nil {
		_schema.SubscriptionType = &OperationType{
			Name: schema.Subscription.Name,
		}
	}
	_schema.Types = TypesFromSchema(schema)

	for _, dir := range schema.Directives {
		if dir.Name == "include" || dir.Name == "skip" { // supported directives

			var locs []string
			var args []Argument
			for _, loc := range dir.Locations {
				locs = append(locs, string(loc))
			}

			for _, arg := range dir.Arguments {
				args = append(args, Argument{
					Name:        arg.Name,
					Description: arg.Description,
					Type: ArgumentType{
						Name: arg.Type.Name(),
						Kind: arg.Type.NamedType,
					},
					DefaultValue: arg.DefaultValue,
				})
			}
			_schema.Directives = append(_schema.Directives, Directive{
				Name:        dir.Name,
				Description: dir.Description,
				Locations:   locs,
				Args:        args,
			})
		}
	}
	return _schema
}

func TypesFromSchema(schema *ast.Schema) Types {
	var types Types
	for _, t := range schema.Types {
		ty := Type{}
		ty.Kind = t.Kind
		ty.Name = t.Name
		ty.Description = t.Description
		if t.IsInputType() {
			ty.InputFields = fieldsToInputFields(t.Fields)
		} else {
			ty.Fields = fieldsToFields(t.Fields)
		}

		if t.Interfaces == nil {
			ty.Interfaces = []string{}
		} else {
			ty.Interfaces = t.Interfaces
		}
		for _, enumValue := range t.EnumValues {
			ty.EnumValues = append(ty.EnumValues, EnumValue{
				Name:        enumValue.Name,
				Description: enumValue.Description,
			})
		}
		types = append(types, ty)
	}
	sort.Sort(types) // make sure clients to have a stable & orderred schema build
	return types
}

func fieldsToFields(fields ast.FieldList) []Field {
	var result []Field
	for _, field := range fields {
		if field.Name == "__schema" ||
			field.Name == "__type" ||
			field.Name == "" {
			continue
		}
		clientField := Field{
			Name:        field.Name,
			Description: field.Description,
			Type: FieldType{
				Name: field.Type.Name(),
			},
			Args: astArgumentsToIntrospectionArgs(field.Arguments),
		}
		if field.Type.Elem != nil { // is a list
			clientField.Type = FieldType{
				Kind: KindList,
				OfType: &FieldType{
					Name: field.Type.Name(),
				},
			}
		}
		result = append(result, clientField)
	}
	return result
}

func astArgumentsToIntrospectionArgs(args ast.ArgumentDefinitionList) []Argument {
	var arguments = []Argument{}
	for _, arg := range args {
		kind := KindScalar
		ofType := (*OfType)(nil)
		if arg.Type.Elem != nil {
			kind = KindList
			ofType = &OfType{
				Name: arg.Type.Elem.Name(),
			}
		}

		arguments = append(arguments, Argument{
			Name:        arg.Name,
			Description: arg.Description,
			Type: ArgumentType{
				Name:   arg.Type.Name(),
				Kind:   string(kind),
				OfType: ofType,
			},
			// DefaultValue: arg.DefaultValue.Raw,
		})
	}
	return arguments
}

func fieldsToInputFields(fields ast.FieldList) []InputField {
	var result []InputField
	for _, field := range fields {
		if field.Name == "__schema" ||
			field.Name == "__type" ||
			field.Name == "" {
			continue
		}
		clientField := InputField{
			Name:        field.Name,
			Description: field.Description,
			Type: FieldType{
				Name: field.Type.Name(),
				Kind: "", // todo: get the Kind
			},
		}
		if field.Type.Elem != nil {
			clientField.Type = FieldType{
				Kind: KindList,
				OfType: &FieldType{
					Name: field.Type.Name(),
				},
			}
		}
		result = append(result, clientField)
	}
	return result
}
