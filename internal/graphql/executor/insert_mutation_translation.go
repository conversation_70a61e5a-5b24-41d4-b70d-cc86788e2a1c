package executor

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/graphql/gsql"
	"github.com/vektah/gqlparser/v2/ast"
)

func TranslateInsertion(mutation *ast.Field, schema *ast.Schema, graphName string, schemaInfo gsql.SchemaInfo) (
	*MutationTranslationResult,
	*MutationTranslationResult,
	string,
	string,
	error) {

	var query1string = &strings.Builder{}
	query1string.WriteString(fmt.Sprintf("INTERPRET QUERY() FOR GRAPH %s \n{\n", graphName))

	var query2paramString = &strings.Builder{}
	var query2params = make([]Param, 0)
	var query2body = &strings.Builder{}
	var paramCounter = 0

	objectName := mutation.Name[7:]
	namedType := ""

	if mutation.Definition.Type.NamedType == "" {
		tmp := mutation.Definition.Type.Elem.NamedType
		namedType = tmp[9 : len(tmp)-len("_output")]
	}

	if schemaInfo.VertexMap[objectName] {
		objects := mutation.Arguments[0]
		// Iterate over all the objects that need to be inserted

		objectList := make([]*ast.ChildValue, 0)
		if objects.Value.Kind == ast.ObjectValue {
			objectList = append(objectList, &ast.ChildValue{Name: "objects", Value: objects.Value})
		} else {
			for _, o := range objects.Value.Children {
				objectList = append(objectList, o)
			}
		}

		for i, o := range objectList {
			// For each object in the list, determine the key of the object and its attributes
			// Generate an INSERT statement for each object with the correct separation of keys and attributes
			// At the same time we generate the mutation output query

			query1string.WriteString(fmt.Sprintf("INSERT INTO %s (", objectName))

			allkeys := schemaInfo.GetPrimaryKeysForVertex(objectName)

			keyNames := make([]string, 0)
			keyValues := make([]string, 0)
			rawKeyValues := make([]string, 0)

			attrNames := make([]string, 0)
			attrValues := make([]string, 0)

			// Iterate over all children and check whether they are
			// keys or attributes
			for _, f := range o.Value.Children {
				attr := findAttr(schemaInfo.AllAttributeMap[objectName], f.Name)
				if allkeys[f.Name] {
					keyNames = append(keyNames, f.Name)
					keyValues = append(keyValues, convertAttrValue(f.Value.String(), attr))
					rawKeyValues = append(rawKeyValues, f.Value.Raw)
				} else {
					attrNames = append(attrNames, f.Name)
					attrValues = append(attrValues, convertAttrValue(f.Value.String(), attr))
				}
			}

			// Check that all keys have been specified in the mutation
			for k1 := range allkeys {
				foundKey := false
				for _, k2 := range keyNames {
					if k1 == k2 {
						foundKey = true
					}
				}
				if !foundKey {
					return nil, nil, "", "", errors.Errorf(fmt.Sprintf("'%s' key must be included in the mutation", k1))
				}
			}

			query2paramString.WriteString(fmt.Sprintf("vertex<%s> v%d", objectName, paramCounter))
			query2body.WriteString(fmt.Sprintf("v%d", paramCounter))
			if i != len(objectList)-1 {
				query2paramString.WriteString(",")
				query2body.WriteString(",")
			}

			// If the key is a PrimaryID, we just add it to the beginning
			// of the INSERT INTO statement
			// At the same time add a parameter to query2 parameters
			if len(keyNames) == 1 {
				query1string.WriteString("PRIMARY_ID")
				query2params = append(query2params, Param{Name: fmt.Sprintf("v%d", i),
					Value:      rawKeyValues[0],
					VertexType: objectName})
				// Otherwise, its a composite key, we need to make a composite key for the parameters
			} else {
				compKeyString := &strings.Builder{}
				for i, k := range keyNames {
					query1string.WriteString(k)
					compKeyString.WriteString(FormatParameter(rawKeyValues[i]))
					if i != len(keyNames)-1 {
						query1string.WriteString(",")
						compKeyString.WriteString(",")
					}
				}
				query2params = append(query2params, Param{Name: fmt.Sprintf("v%d", i),
					Value:      compKeyString.String(),
					VertexType: objectName})
			}

			// Add all attributes
			if len(attrNames) != 0 {
				query1string.WriteString(",")
			}

			for i, a := range attrNames {
				query1string.WriteString(a)
				if i != len(attrNames)-1 {
					query1string.WriteString(",")
				}
			}

			// Now add all the values to the INSERT INTO statement
			query1string.WriteString(") VALUES (")
			keysAndAttrs := append(keyValues, attrValues...)
			for i, a := range keysAndAttrs {
				query1string.WriteString(a)
				if i != len(keysAndAttrs)-1 {
					query1string.WriteString(",")
				}
			}

			query1string.WriteString(");\n")
			paramCounter++
		}

		query1string.WriteString("}")

		// Compose the second query from the pieces collected above
		var query2string = &strings.Builder{}
		query2string.WriteString(fmt.Sprintf("INTERPRET QUERY(%s) FOR GRAPH %s {\n", query2paramString.String(), graphName))
		query2string.WriteString(fmt.Sprintf("vertex_set_0 = {%s};\n", query2body.String()))
		query2string.WriteString("print vertex_set_0;\n}")

		var query1 = MutationTranslationResult{Query: query1string.String(),
			Params: make([]Param, 0),
		}

		var query2 = MutationTranslationResult{Query: query2string.String(),
			Params: query2params,
		}
		return &query1, &query2, objectName, namedType, nil

	} else {
		edgeName := schemaInfo.EdgeMap[namedType].Name

		// Iterate over all edge objects
		objects := mutation.Arguments[0]

		objectList := make([]*ast.ChildValue, 0)
		if objects.Value.Kind == ast.ObjectValue {
			objectList = append(objectList, &ast.ChildValue{Name: "objects", Value: objects.Value})
		} else {
			for _, o := range objects.Value.Children {
				objectList = append(objectList, o)
			}
		}

		fromVertexName := schemaInfo.EdgeMap[namedType].FromVertexName
		toVertexName := schemaInfo.EdgeMap[namedType].ToVertexName
		fromIsWildcard := schemaInfo.EdgeMap[namedType].FromIsWildcard
		toIsWildcard := schemaInfo.EdgeMap[namedType].ToIsWildcard

		// Check if we need to flip the from and to vertices (an undirected edge has two entries in
		// our schema, and one of them is flipped)
		flipped := schemaInfo.EdgeMap[namedType].Flipped
		if flipped {
			fromVertexName = schemaInfo.EdgeMap[namedType].ToVertexName
			toVertexName = schemaInfo.EdgeMap[namedType].FromVertexName
		}

		for i, o := range objectList {

			// Generate an insert statement of the first query
			query1string.WriteString(fmt.Sprintf("INSERT INTO %s ( FROM, TO ", edgeName))

			fromString := &strings.Builder{}
			fromKeyString := &strings.Builder{}
			toString := &strings.Builder{}
			toKeyString := &strings.Builder{}
			attrString := &strings.Builder{}

			// Iterate over the fields of the object
			for _, f := range o.Value.Children {
				if f.Name == "from" {
					// Iterate over the keys of the from vertex
					origFromVertexName := schemaInfo.EdgeMap[namedType].FromVertexName
					keys := schemaInfo.PrimaryKeysByVertices[origFromVertexName]

					if len(keys) != 1 {
						fromString.WriteString("(")
					}
					for j, k := range keys {
						k_i, err := getObject(k, f)
						if err != nil {
							return nil, nil, "", "", err
						}
						attr := findAttr(schemaInfo.AllAttributeMap[origFromVertexName], k)
						if fromIsWildcard {
							fromString.WriteString(fmt.Sprintf("%s %s", convertAttrValue(k_i.Value.String(), attr), origFromVertexName))
						} else {
							fromString.WriteString(convertAttrValue(k_i.Value.String(), attr))
						}
						fromKeyString.WriteString(FormatParameter(k_i.Value.Raw))
						if j != len(keys)-1 {
							fromString.WriteString(",")
							fromKeyString.WriteString(",")
						}
					}
					if len(keys) != 1 {
						fromString.WriteString(")")
					}
				} else if f.Name == "to" {
					// Iterate over the keys of the from vertex
					origToVertexName := schemaInfo.EdgeMap[namedType].ToVertexName
					keys := schemaInfo.PrimaryKeysByVertices[origToVertexName]

					if len(keys) != 1 {
						toString.WriteString("(")
					}
					for j, k := range keys {
						k_i, err := getObject(k, f)
						if err != nil {
							return nil, nil, "", "", err
						}
						attr := findAttr(schemaInfo.AllAttributeMap[origToVertexName], k)
						if toIsWildcard {
							toString.WriteString(fmt.Sprintf("%s %s", convertAttrValue(k_i.Value.String(), attr), origToVertexName))
						} else {
							toString.WriteString(convertAttrValue(k_i.Value.String(), attr))
						}
						toKeyString.WriteString(FormatParameter(k_i.Value.Raw))
						if j != len(keys)-1 {
							toString.WriteString(",")
							toKeyString.WriteString(",")
						}
					}
					if len(keys) != 1 {
						toString.WriteString(")")
					}
					// Finally, this must be an attribute, process it as such
				} else {
					query1string.WriteString(fmt.Sprintf(", %s", f.Name))
					attr := findAttr(schemaInfo.AttributeMap[edgeName], f.Name)
					attrString.WriteString(fmt.Sprintf(",%s", convertAttrValue(f.Value.String(), attr)))
				}
			}

			// Finalize insert query
			// If the edge is flipped, we reverse the order of from and to vertices
			if !flipped {
				query1string.WriteString(fmt.Sprintf(") VALUES (%s, %s %s);\n", fromString.String(), toString.String(), attrString.String()))
			} else {
				query1string.WriteString(fmt.Sprintf(") VALUES (%s, %s %s);\n", toString.String(), fromString.String(), attrString.String()))
			}
			// Create parameters and body for mutation response query
			query2paramString.WriteString(fmt.Sprintf("vertex<%s> s%d,", fromVertexName, paramCounter))
			query2paramString.WriteString(fmt.Sprintf("vertex<%s> t%d", toVertexName, paramCounter))
			query2body.WriteString(fmt.Sprintf("(s == s%d AND t == t%d)", paramCounter, paramCounter))

			if i != len(objectList)-1 {
				query2paramString.WriteString(",")
				query2body.WriteString(" OR ")
			}

			// If the edge is flipped, also flip the key strings
			if flipped {
				tmpString := fromKeyString
				fromKeyString = toKeyString
				toKeyString = tmpString
			}

			query2params = append(query2params, Param{Name: fmt.Sprintf("s%d", paramCounter),
				Value:      fromKeyString.String(),
				VertexType: fromVertexName})

			query2params = append(query2params, Param{Name: fmt.Sprintf("t%d", paramCounter),
				Value:      toKeyString.String(),
				VertexType: toVertexName})

			paramCounter++
		}
		query1string.WriteString("}\n")

		// Create the mutation response query
		var query2string = &strings.Builder{}
		query2string.WriteString(fmt.Sprintf("INTERPRET QUERY(%s) FOR GRAPH %s SYNTAX V2 {\n", query2paramString.String(), graphName))
		query2string.WriteString("SetAccum<EDGE> @@edges_0;\n")
		query2string.WriteString(fmt.Sprintf("vertex_set_1 = {%s.*};\n", fromVertexName))
		var dirEdgeMarker = &strings.Builder{}
		if schemaInfo.EdgeMap[namedType].IsDirectional {
			dirEdgeMarker.WriteString(">")
		}
		query2string.WriteString(fmt.Sprintf("L = SELECT s FROM vertex_set_1:s - (%s%s:e) - %s:t\n", edgeName, dirEdgeMarker, toVertexName))
		query2string.WriteString(fmt.Sprintf("WHERE %s\nACCUM @@edges_0 += e;\nprint @@edges_0;\n}", query2body.String()))

		var query1 = MutationTranslationResult{Query: query1string.String(), Params: make([]Param, 0)}
		var query2 = MutationTranslationResult{Query: query2string.String(), Params: query2params}

		return &query1, &query2, edgeName, namedType, nil
	}
}

func convertAttrValue(attrValue string, attr gsql.Attribute) string {
	if attr.AttributeType.Name == "Datetime" {
		return fmt.Sprintf("to_datetime(%s)", attrValue)
	} else {
		return attrValue
	}
}

func findAttr(attrs []gsql.Attribute, attrName string) gsql.Attribute {
	for _, a := range attrs {
		if a.AttributeName == attrName {
			return a
		}
	}
	panic("Attribute not found")
}

func getObject(childName string, obj *ast.ChildValue) (*ast.ChildValue, error) {
	for _, f := range obj.Value.Children {
		if f.Name == childName {
			return f, nil
		}
	}
	return nil, errors.Errorf(fmt.Sprintf("'%s' parameter of the edge must be specified in insert", childName))
}
