package web_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/tigergraph/graphql/executor"
	"github.com/tigergraph/graphql/web"
)

const serverURL = "http://localhost"
const contentType = "application/json"

type Credentials struct {
	GsqlServer string `json:"gsqlServer"`
	GraphName  string `json:"graphName"`
	Username   string `json:"username"`
	Password   string `json:"password"`
	Port       string `json:"port"`
}

func TestMain(m *testing.M) {
	flag.Parse()
	if testing.Short() {
		// don't run integration tests for -short flag
		fmt.Println("Skipped integration tests. '-short' flag was provided")
		os.Exit(0)
	}

	var rc = m.Run()

	os.Exit(rc)
}

const testData = "testdata"
const testQueriesPath = "queries"
const testResultsPath = "results"
const testMutationsPath = "mutations"
const testMutationResultsPath = "mutation_results"
const credentialFileName = "credentials.json"

const mutationPrepareSuffix = ".prepare"
const mutationCheckSuffix = ".check"

/***
This test allows to run chain of graphQL queries.
It supports multiple graphs.
If developer will want to add new graph, then developer will need to create new folder under ./testdata/ folder with credentials.json file inside.
Note: developer must avoid colisions in ports
Developer must put new queries into the ./testdata/<tg_instance_graph_name>/queries folder.
Developer must put expected result of query into the ./testdata/<tg_instance_graph_name>/results folder.
Developer is able to create subfolders for keeping some tests logically separatelly.
If query has variables, then developer must put file near the query file with the same name and with json extension
Naming convention:
	queries: q1.graphql, q2.graphql, q3.graphQL, ..., qN.graphQL
	variables: q1.json, q2.json, q2.json, ..., qN.json
	results: q1.json, q2.json, q3.json, ..., qN.json

IMPORTANT:
1. query's file name and result's file name must be equal (like: q1.graphql and q1.json)
2. query's file path and result's file path must be equal (like: /testdata/queries/dummy/dummy.graphql and /testdata/results/dummy/dummy.json)
3. result file must contain result in raw format
4. query's file name and variables's file name must be equeal and in the same folder


Mutation addition:
If you want to test mutations then you need to create additional folders on the same level as queries and results folders: mutations, mutation_results
Mutations folder must contain 3 files:
 - *.check file - validation script in graphql
 - *.graphql - exact mutation script
 - *.prepare - preparation script (it should delete data firstly, also it supports few graphql queries which must be separated by ';' for cases when for example you need to load some inital data)
***/

func TestIntegration(t *testing.T) {
	var graphs []string
	err := filepath.Walk(testData+"/", func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			if _, err := os.Stat(path + "/" + credentialFileName); !errors.Is(err, os.ErrNotExist) {
				graphs = append(graphs, path)
			}
		}

		return nil
	})

	require.NoError(t, err, "Failed to load graph folders for integration tests")

	for _, g := range graphs {
		t.Run("[graph]:["+g+"]", func(t *testing.T) {
			testQueries(t, g)
		})
	}
}

func testQueries(t *testing.T, graphFolder string) {

	credentialContent, err := ioutil.ReadFile(graphFolder + "/" + credentialFileName)

	require.NoError(t, err, "Failed to read the file %v", credentialFileName)

	credential := Credentials{}
	err = json.Unmarshal([]byte(credentialContent), &credential)

	require.NoError(t, err, "Failed to read the credential file %v", credentialFileName)

	runWebServer(credential)

	var files []string
	if _, errExist := os.Stat(graphFolder + "/" + testQueriesPath); errExist == nil {
		err = filepath.Walk(graphFolder+"/"+testQueriesPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			if !info.IsDir() {
				if filepath.Ext(path) == ".graphql" {
					files = append(files, path)
				}
			}
			return nil
		})

		require.NoError(t, err, "Failed to load query files for integration tests")
	}

	for _, f := range files {
		t.Run("[query]:["+f+"]", func(t *testing.T) {
			testQuery(t, f, credential, true)
		})
	}

	var mutationFiles []string

	if _, errExist := os.Stat(graphFolder + "/" + testMutationsPath); errExist == nil {
		filepath.Walk(graphFolder+"/"+testMutationsPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			if !info.IsDir() {
				if filepath.Ext(path) == ".graphql" {
					mutationFiles = append(mutationFiles, path)
				}
			}

			return nil
		})
	}

	for _, f := range mutationFiles {
		t.Run("[mutation]:["+f+"]", func(t *testing.T) {
			testMutation(t, f, credential)
		})
	}
}

func testMutation(t *testing.T, mutationFileName string, credentials Credentials) {
	// preparation
	prepareScript := fileNameWithoutExtension(mutationFileName) + mutationPrepareSuffix
	b, err := ioutil.ReadFile(prepareScript)

	require.NoError(t, err, "Failed to read the file %v", prepareScript)

	queries := strings.Split(string(b), ";")

	for _, q := range queries {
		_, err := sendQuery(convertQuery(q), credentials)
		require.NoError(t, err, "Failed to send query '%s' to graphql service ", q)
	}

	// execution
	testQuery(t, mutationFileName, credentials, false)

	// validation
	//testQuery(t, fileNameWithoutExtension(mutationFileName)+mutationCheckSuffix, credentials, true)
	validateScript := fileNameWithoutExtension(mutationFileName) + mutationCheckSuffix
	query := getQuery(t, validateScript)

	res, err := sendQuery(query, credentials)

	require.NoError(t, err, "Failed to send query '%s' to graphql service ", validateScript)

	actual, _ := ioutil.ReadAll(res.Body)

	resultFileName := strings.Replace(validateScript, testMutationsPath, testMutationResultsPath, 1) + ".json"
	expected, err := ioutil.ReadFile(resultFileName)

	require.NoError(t, err, "Failed to read the file %v", resultFileName)

	check := areJsonEqualTmp(actual, expected)

	if !check {
		fmt.Println("Expected: ", string(expected))
		fmt.Println("Actual: ", string(actual))
	}
	require.Truef(t, check, "Validation for query %v is not correct", validateScript)
}

func testQuery(t *testing.T, queryFileName string, credentials Credentials, isQuery bool) {
	query := getQuery(t, queryFileName)

	res, err := sendQuery(query, credentials)

	require.NoError(t, err, "Failed to send query '%s' to graphql service ", queryFileName)

	actual, _ := ioutil.ReadAll(res.Body)
	expected := getExpectedResult(t, queryFileName, isQuery)

	/***
	Temporary solution for comparing json results.
	The reason for it is that TigerGraphDB could return List of elements in random order.
	Once we will add ORDER BY support we must start to use require.JSONeq(...) function and add to all tests ORDER BY.
	***/
	check := areJsonEqualTmp(actual, expected)

	if !check {
		fmt.Println("Expected: ", string(expected))
		fmt.Println("Actual: ", string(actual))
	}
	require.Truef(t, check, "Result for query %v is not correct", queryFileName)
	//require.JSONEq(t, string(expected), string(actual), "Result for query %v is not correct", queryFileName)
}

func getExpectedResult(t *testing.T, queryFileName string, isQuery bool) []byte {
	queryPath := testQueriesPath
	resultPath := testResultsPath
	if !isQuery {
		queryPath = testMutationsPath
		resultPath = testMutationResultsPath
	}
	resultFileName := fileNameWithoutExtension(strings.Replace(queryFileName, queryPath, resultPath, 1)) + ".json"
	expected, err := ioutil.ReadFile(resultFileName)

	require.NoError(t, err, "Failed to read the file %v", resultFileName)

	return expected
}

func getQueryParams(t *testing.T, queryFileName string) []byte {
	paramsFileName := fileNameWithoutExtension(queryFileName) + ".json"
	expected, err := ioutil.ReadFile(paramsFileName)

	if err == nil {
		return expected
	}

	return nil
}

func getQuery(t *testing.T, queryFileName string) []byte {
	b, err := ioutil.ReadFile(queryFileName)

	require.NoError(t, err, "Failed to read the file %v", queryFileName)

	params := getQueryParams(t, queryFileName)
	if params != nil {
		return convertQueryWithParams(string(b), params)
	} else {
		return convertQuery(string(b))
	}
}

func sendQuery(postData []byte, credential Credentials) (*http.Response, error) {
	return http.Post(fmt.Sprintf("%v:%v", serverURL, credential.Port), contentType, bytes.NewBuffer(postData))
}

func convertQueryWithParams(query string, params []byte) []byte {
	var paramStruct executor.Params
	paramStruct.Query = query
	paramStruct.Variables = make(map[string]interface{})
	json.Unmarshal(params, &paramStruct.Variables)

	jsonValue, _ := json.Marshal(paramStruct)

	return jsonValue
}

func convertQuery(query string) []byte {
	var paramStruct executor.Params
	paramStruct.Query = query

	jsonValue, _ := json.Marshal(paramStruct)

	return jsonValue
}

func runWebServer(credentials Credentials) {

	var gqlConn = web.NewGraphQLServiceProperties(credentials.GsqlServer, credentials.GraphName, credentials.Username, credentials.Password, credentials.Port)

	go web.RunCobra(&gqlConn, nil)

	for {
		var res = pingWebServer(credentials)

		if res {
			break
		} else {
			time.Sleep(1 * time.Second)
		}
	}
}

func pingWebServer(credentials Credentials) bool {
	var res, _ = http.Get(fmt.Sprintf("%s:%v/ping", serverURL, credentials.Port))

	if res != nil && res.StatusCode == 200 {
		return true
	}

	return false
}

func fileNameWithoutExtension(fileName string) string {
	if pos := strings.LastIndexByte(fileName, '.'); pos != -1 {
		return fileName[:pos]
	}
	return fileName
}

func areJsonEqualTmp(actual []byte, expected []byte) bool {
	return len(actual) == len(expected)
}
