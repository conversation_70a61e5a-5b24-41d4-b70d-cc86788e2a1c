package web

import (
	"context"
	_ "embed"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"

	"github.com/spf13/cobra"
	"github.com/tigergraph/graphql/executor"
	"github.com/tigergraph/graphql/gsql"
	"github.com/tigergraph/graphql/gsql/client"
	"github.com/tigergraph/graphql/log"
	"go.uber.org/zap/zapcore"
)

//go:embed graphiql.html
var graphiqlHTML []byte

type GraphQLServiceProperties struct {
	gsqlServer string
	graphName  string
	username   string
	password   string
	port       string
}

func NewGraphQLServiceProperties(gsqlServer string, graphName string, username string, password string, port string) GraphQLServiceProperties {
	return GraphQLServiceProperties{gsqlServer: gsqlServer, graphName: graphName, username: username, password: password, port: port}
}

func overrideOsArgsIfNeeded(props *GraphQLServiceProperties, cmd *cobra.Command) {
	if props != nil {
		cmd.SetArgs([]string{"-g", props.graphName, "-s", props.gsqlServer, "-w", props.username,
			"-p", props.port, "-u", props.password})
	}
}

func RunCobra(props *GraphQLServiceProperties, logWriter zapcore.WriteSyncer) error {
	var gsqlServer = ""
	var graphName = ""
	var username = ""
	var password = ""
	var port = ""
	var period = 10 // default, can be override by CLI
	var gsqlSecret = ""
	var skipSSL = false

	var rootCmd = &cobra.Command{
		Use:   "",
		Short: "Run GraphQL Service for TigerGraph",
		Run: func(cmd *cobra.Command, args []string) {
			schemaSyncer := NewSchemaSyncer(logWriter)

			if gsqlSecret != "" {
				username = "__GSQL__secret"
				password = gsqlSecret
			}

			mux := http.NewServeMux()
			mux.Handle("/", &Handler{
				SchemaSyncer: schemaSyncer,
				GraphName:    graphName,
				Host:         gsqlServer,
				User:         username,
				Password:     password,
				SkipSSL:      skipSSL,
			})
			mux.HandleFunc("/ping", pingCobra)

			log.Infof("Start to serve at http://localhost:%s", port)
			errServer := http.ListenAndServe(":"+port, mux)
			if errServer != nil {
				log.Fatal(errServer)
			}
		},
	}

	rootCmd.Flags().StringVarP(&gsqlServer, "gsqlServer", "s", "http://localhost:14240", "The address of GSQL Server")
	rootCmd.Flags().StringVarP(&graphName, "graph", "g", "", "graph name")
	rootCmd.Flags().StringVarP(&username, "username", "u", "", "user name of TigerGraph")
	rootCmd.Flags().StringVarP(&password, "password", "w", "", "password of TigerGraph")
	rootCmd.Flags().StringVarP(&port, "port", "p", "8080", "port to listen")
	rootCmd.Flags().IntVarP(&period, "period", "", 10, "schema synchronization period, seconds")
	rootCmd.Flags().StringVarP(&gsqlSecret, "gsqlSecret", "", "", "If supplied, leave password & username empty\nRead more at\nhttps://docs.tigergraph.com/gui/current/admin-portal/management/user-management#_my_profile")
	overrideOsArgsIfNeeded(props, rootCmd)
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}

	return nil
}

func pingCobra(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Headers", "*")
	w.WriteHeader(200)
}

type Handler struct {
	SchemaSyncer SchemaSyncer
	GraphName    string
	Host         string
	User         string
	Password     string
	SkipSSL      bool
}

func (h *Handler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	log.Infow("incoming request", "url", r.URL)
	var params executor.Params

	errMsg := func(err error) string {
		var msg string
		if result, e := json.Marshal(executor.Result{Errors: []interface{}{err.Error()}}); e == nil {
			msg = string(result)
		} else {
			msg = err.Error()
		}
		return msg
	}

	if r.Method == http.MethodOptions {
		return
	}

	if r.Method == http.MethodGet {
		w.Write(graphiqlHTML)
		return
	}
	if err := json.NewDecoder(r.Body).Decode(&params); err != nil {
		w.Write([]byte(errMsg(err)))
		return
	}
	var authorizationToken string
	if r.Header.Get("Authorization") != "" {
		authorizationToken = r.Header.Get("Authorization")
	} else if h.User != "" && h.Password != "" {
		authorizationToken = "Basic " + base64.RawStdEncoding.EncodeToString([]byte(h.User+":"+h.Password))
	} else if cookie, err := r.Cookie("TigerGraphApp"); err == nil {
		authorizationToken = fmt.Sprintf("Bearer %s", cookie.Value)
	} else {
		w.Write([]byte(errMsg(errors.New("authentication failed."))))
		return
	}

	gsqlClient := client.Client{
		Host:               h.Host,
		AuthorizationToken: authorizationToken,
		SkipSSL:            h.SkipSSL,
	}

	gsqlResponse, err := h.SchemaSyncer.Schema(h.Host, authorizationToken, h.GraphName, h.SkipSSL)
	if err != nil {
		log.Error(err)

		w.Write([]byte(errMsg(err)))
		return
	}
	log.Info("load schema", h.GraphName, "for user", h.User)

	// To GraphQL Schema
	schemaInfo := gsql.SchemaInfo{}
	gqlSchema, err := gsqlResponse.Results.ToGraphQLSchema(h.GraphName, &schemaInfo)
	if err != nil {
		log.Error(err)
		w.Write([]byte(errMsg(err)))
		return
	}

	gsqlResponse.Results.GenerateMapping(&schemaInfo)

	ctx := context.WithValue(r.Context(), "GSQL-TIMEOUT", r.Header.Get("GSQL-TIMEOUT"))
	result := executor.Exec(ctx, gqlSchema, schemaInfo, params, h.GraphName, gsqlClient)
	if len(result.Errors) > 0 {
		for _, err := range result.Errors {
			log.Infof("%+v", err)
		}
	}
	responseJSON, err := json.Marshal(result)
	if err != nil {
		w.Write([]byte(errMsg(err)))
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write(responseJSON)
}

type SchemaSyncer interface {
	Schema(host, authorizationToken, graphName string, skipSSL bool) (*client.GsqlSchemaResponse, error)
}

// schemaSyncer is a concurrent construct that syncs the schema with GSQL once per 10 seconds.
// It provides a method to get the current schema.
type schemaSyncer struct{}

func (s *schemaSyncer) Schema(host, authorizationToken, graphName string, skipSSL bool) (*client.GsqlSchemaResponse, error) {
	gsqlResponse, err := client.GetSchema(context.Background(), skipSSL, host, authorizationToken, graphName)
	return gsqlResponse, err
}

func NewSchemaSyncer(logWriter zapcore.WriteSyncer) *schemaSyncer {
	log.Init(logWriter)
	return &schemaSyncer{}
}
