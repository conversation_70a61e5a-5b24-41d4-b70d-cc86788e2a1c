package expr_parser_test

import (
	"testing"

	"github.com/tigergraph/graphql/expr_parser/e_parser"
	"github.com/tigergraph/graphql/gsql"

	"github.com/stretchr/testify/require"
)

func TestExpression(t *testing.T) {
	res, err := e_parser.ParseExpression("to_datetime(\"15-12\") > datetime_sub(now(),interval 1 month)")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("(abs(-15) % (1+2)) / .08 < 14 or not cos(-1) >= 4")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("(\"foo\" not like \"boo\") is not null")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("15 between .5 and 16.8")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("sqrt(1) + pow(2,3) + acos(10) + asin(2) + atan(1) + atan2(2,1) + ceil(4.8) + cos(1) + cosh(2) + exp(156) + floor(1.9) + fmod(1,2) + ldexp(3,4) + log(1) + log10(10) + sin(2) + sinh(3) + tan(5) + tanh(10)")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("to_string(1.98)")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("float_to_int(12.4) + str_to_int(\"1.28\")")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("lower(\"ABC\") + upper(\"cde\")")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("datetime_to_epoch(to_datetime(\"15-12\"))")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("datetime_format(to_datetime(\"15-12\"))")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("year(epoch_to_datetime(1)) + month(epoch_to_datetime(1)) + day(epoch_to_datetime(1)) + hour(epoch_to_datetime(1)) + minute(epoch_to_datetime(1)) + second(epoch_to_datetime(1))")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)

	res, err = e_parser.ParseExpression("datetime_diff(datetime_add(epoch_to_datetime(1),interval 1 year), epoch_to_datetime(1))")
	require.Nil(t, err)
	_, err = res.TypeCheck(make([]gsql.Attribute, 0))
	require.Nil(t, err)
}

func TestExceptions(t *testing.T) {
	_, err := e_parser.ParseExpression("12+")
	require.NotNil(t, err)
	require.EqualError(t, err, "Unexpected <EOF>")

	_, err = e_parser.ParseExpression("foo(a,b,c")
	require.NotNil(t, err)
	require.EqualError(t, err, "Expected ), found <EOF>")

	e, err := e_parser.ParseExpression("12+\"24\"")
	require.Nil(t, err)
	_, err = e.TypeCheck(make([]gsql.Attribute, 0))
	require.EqualError(t, err, "Cannot find the matching signature for the function/operator '+' with argument list (Int,String)")

	e, err = e_parser.ParseExpression("0 between \"24\" and 36")
	require.Nil(t, err)
	_, err = e.TypeCheck(make([]gsql.Attribute, 0))
	require.EqualError(t, err, "Illegal datatypes in between expression: Int between String and Int")

	e, err = e_parser.ParseExpression("\"abc\" like \"def\" escape 0")
	require.Nil(t, err)
	_, err = e.TypeCheck(make([]gsql.Attribute, 0))
	require.EqualError(t, err, "Illegal datatypes in like expression, all arguments must be strings")

}
