package e_parser

import (
	"strconv"

	"github.com/tigergraph/graphql/expr_parser/e_ast"
	"github.com/tigergraph/graphql/expr_parser/e_error"
	"github.com/tigergraph/graphql/expr_parser/e_lexer"
)

type parser struct {
	lexer e_lexer.Lexer
	err   *e_error.Error

	peeked    bool
	peekToken e_lexer.Token
	peekError *e_error.Error

	prev e_lexer.Token
}

func (p *parser) peekPos() *e_ast.Position {
	if p.err != nil {
		return nil
	}

	peek := p.peek()
	return &peek.Pos
}

func (p *parser) peek() e_lexer.Token {
	if p.err != nil {
		return p.prev
	}

	if !p.peeked {
		p.peekToken, p.peekError = p.lexer.ReadToken()
		p.peeked = true
	}

	return p.peekToken
}

func (p *parser) error(tok e_lexer.Token, format string, args ...interface{}) {
	if p.err != nil {
		return
	}
	p.err = e_error.ErrorLocf(tok.Pos.Line, tok.Pos.Column, format, args...)
}

func (p *parser) next() e_lexer.Token {
	if p.err != nil {
		return p.prev
	}
	if p.peeked {
		p.peeked = false
		p.prev, p.err = p.peekToken, p.peekError
	} else {
		p.prev, p.err = p.lexer.ReadToken()
	}
	return p.prev
}

func (p *parser) expectKeyword(value string) e_lexer.Token {
	tok := p.peek()
	if tok.Kind == e_lexer.Name && tok.Value == value {
		return p.next()
	}

	p.error(tok, "Expected %s, found %s", strconv.Quote(value), tok.String())
	return tok
}

func (p *parser) expect(kind e_lexer.Type) e_lexer.Token {
	tok := p.peek()
	if tok.Kind == kind {
		return p.next()
	}

	p.error(tok, "Expected %s, found %s", kind, tok.Kind.String())
	return tok
}

func (p *parser) skip(kind e_lexer.Type) bool {
	if p.err != nil {
		return false
	}

	tok := p.peek()

	if tok.Kind != kind {
		return false
	}
	p.next()
	return true
}

func (p *parser) unexpectedError() {
	p.unexpectedToken(p.peek())
}

func (p *parser) unexpectedToken(tok e_lexer.Token) {
	p.error(tok, "Unexpected %s", tok.String())
}

func (p *parser) many(start e_lexer.Type, end e_lexer.Type, cb func()) {
	hasDef := p.skip(start)
	if !hasDef {
		return
	}

	for p.peek().Kind != end && p.err == nil {
		cb()
	}
	p.next()
}

func (p *parser) some(start e_lexer.Type, end e_lexer.Type, cb func()) {
	hasDef := p.skip(start)
	if !hasDef {
		return
	}

	called := false
	for p.peek().Kind != end && p.err == nil {
		called = true
		cb()
	}

	if !called {
		p.error(p.peek(), "expected at least one definition, found %s", p.peek().Kind.String())
		return
	}

	p.next()
}
