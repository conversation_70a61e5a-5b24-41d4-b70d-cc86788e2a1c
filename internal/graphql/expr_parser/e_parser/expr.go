package e_parser

import (
	"github.com/tigergraph/graphql/expr_parser/e_ast"
	"github.com/tigergraph/graphql/expr_parser/e_lexer"

	"strings"
)

// This is a recursive descent parser for GSQL expression language
// Since the parser is LL(1), we modify the grammar of GSQL appropriately
// to get rid of left recursion and lookahead. The grammar is thus defined
// as given below:

// To keep the grammar LL(1) we don't split the conditions and expressions
// The grammar is a little permissive - the parsing result has to be cheched
// for semantic errors.
//
// Expression = Expression_1 ( 'OR' Expression_1 )*
// Expression_1 = Expression_2 ( 'AND' Expression_2 )*
// Expression_2 = Expression_3 | 'NOT' Expression_3
// Expression_3 = Expression_4 ( comparison_op Expression_4)*
// Expression_4 = Expression_5 ( '|' Expression_5)*
// Expression_5 = Expression_6 ( '&' Expression_6)*
// Expression_6 = Expression_7 ( shift_op Expression_7)*
// Expression_7 = '-' Expression_8 | Expression_8 ( add_op Expression_8)*
// Expression_8 = Expression_9 ( mult_op Expression_9)*
// Expression_9 = Expression_10 ( 'IS' ('NOT')? 'NULL')?
// Expression_10 = Expression_11 ( 'BETWEEN' Expression_4 'AND' Expression_4 )?
// Expression_11 = Expression_12 ( ('NOT')? 'LIKE' Expression ('ESCAPE' string)?)?
// Expression_12 = '(' Expression ')' |
//					Constant |
//					'TRUE'   |
//					'FALSE'  |
//					Name ( '(' (ArgList)? ')' )?
// ArgList = Expression (',' (Expression | ('INTERVAL' Expression TimeUnit )))*

var comparison_ops = map[string]bool{"<": true, ">": true, "<=": true, ">=": true, "==": true, "!=": true}
var time_units = map[string]bool{"year": true, "month": true, "day": true, "hour": true, "minute": true, "second": true}

func ParseExpression(Source string) (e_ast.Expr, error) {
	p := parser{
		lexer: e_lexer.New(Source),
	}

	res := p.parseExpression()
	if p.err == nil {
		if p.peek().Kind != e_lexer.EOF {
			p.unexpectedToken(p.peek())
		}
	}

	return res, p.err
}

func (p *parser) parseExpression() e_ast.Expr {
	expr_1 := p.parseExpression_1()
	if p.err != nil {
		return nil
	}

	var final_expr = expr_1

	for p.peek().Kind == e_lexer.Operator && p.peek().Value == "or" {
		pos := p.peekPos()
		p.next()
		expr_2 := p.parseExpression_1()
		if p.err != nil {
			return nil
		}
		final_expr = &e_ast.BinaryOpExpr{
			Op:       "or",
			LeftArg:  final_expr,
			RightArg: expr_2,
			Position: pos,
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}
	return final_expr
}

func (p *parser) parseExpression_1() e_ast.Expr {
	expr_1 := p.parseExpression_2()
	if p.err != nil {
		return nil
	}
	var final_expr = expr_1

	for p.peek().Kind == e_lexer.Operator && p.peek().Value == "and" {
		pos := p.peekPos()
		p.next()
		expr_2 := p.parseExpression_2()
		if p.err != nil {
			return nil
		}
		final_expr = &e_ast.BinaryOpExpr{
			Op:       "and",
			LeftArg:  final_expr,
			RightArg: expr_2,
			Position: pos,
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}
	return final_expr
}

func (p *parser) parseExpression_2() e_ast.Expr {

	if p.peek().Kind == e_lexer.Operator && p.peek().Value == "not" {
		pos := p.peekPos()
		p.next()
		not_expr := &e_ast.UnaryOpExpr{
			Op:       "not",
			Arg:      p.parseExpression_3(),
			Position: pos,
		}
		return not_expr
	}
	return p.parseExpression_3()
}

func (p *parser) parseExpression_3() e_ast.Expr {
	expr_1 := p.parseExpression_4()
	if p.err != nil {
		return nil
	}
	var final_expr = expr_1

	for p.peek().Kind == e_lexer.Operator && comparison_ops[p.peek().Value] {
		pos := p.peekPos()
		token := p.next()
		expr_2 := p.parseExpression_4()
		if p.err != nil {
			return nil
		}

		final_expr = &e_ast.BinaryOpExpr{
			Op:       token.Value,
			LeftArg:  final_expr,
			RightArg: expr_2,
			Position: pos,
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}
	return final_expr
}

func (p *parser) parseExpression_4() e_ast.Expr {
	expr_1 := p.parseExpression_5()
	if p.err != nil {
		return nil
	}
	var final_expr = expr_1

	for p.peek().Kind == e_lexer.Operator && p.peek().Value == "|" {
		pos := p.peekPos()
		p.next()
		expr_2 := p.parseExpression_5()
		if p.err != nil {
			return nil
		}
		final_expr = &e_ast.BinaryOpExpr{
			Op:       "|",
			LeftArg:  final_expr,
			RightArg: expr_2,
			Position: pos,
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}
	return final_expr
}

func (p *parser) parseExpression_5() e_ast.Expr {
	expr_1 := p.parseExpression_6()
	if p.err != nil {
		return nil
	}
	var final_expr = expr_1

	for p.peek().Kind == e_lexer.Operator && p.peek().Value == "&" {
		pos := p.peekPos()
		p.next()
		expr_2 := p.parseExpression_6()
		if p.err != nil {
			return nil
		}
		final_expr = &e_ast.BinaryOpExpr{
			Op:       "&",
			LeftArg:  final_expr,
			RightArg: expr_2,
			Position: pos,
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}
	return final_expr
}

func (p *parser) parseExpression_6() e_ast.Expr {
	expr_1 := p.parseExpression_7()
	if p.err != nil {
		return nil
	}
	var final_expr = expr_1

	for p.peek().Kind == e_lexer.Operator && (p.peek().Value == "<<" || p.peek().Value == ">>") {
		pos := p.peekPos()
		token := p.next()
		expr_2 := p.parseExpression_7()
		if p.err != nil {
			return nil
		}
		final_expr = &e_ast.BinaryOpExpr{
			Op:       token.Value,
			LeftArg:  final_expr,
			RightArg: expr_2,
			Position: pos,
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}
	return final_expr
}

func (p *parser) parseExpression_7() e_ast.Expr {
	if p.peek().Kind == e_lexer.Operator && p.peek().Value == "-" {
		pos := p.peekPos()
		p.next()
		expr_1 := p.parseExpression_8()
		if p.err != nil {
			return nil
		}
		return &e_ast.UnaryOpExpr{
			Op:       "-",
			Arg:      expr_1,
			Position: pos,
		}
	}

	expr_1 := p.parseExpression_8()
	if p.err != nil {
		return nil
	}
	var final_expr = expr_1

	for p.peek().Kind == e_lexer.Operator && (p.peek().Value == "+" || p.peek().Value == "-") {
		pos := p.peekPos()
		token := p.next()
		expr_2 := p.parseExpression_8()
		if p.err != nil {
			return nil
		}
		final_expr = &e_ast.BinaryOpExpr{
			Op:       token.Value,
			LeftArg:  final_expr,
			RightArg: expr_2,
			Position: pos,
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}
	return final_expr
}

func (p *parser) parseExpression_8() e_ast.Expr {
	expr_1 := p.parseExpression_9()
	if p.err != nil {
		return nil
	}
	var final_expr = expr_1

	for p.peek().Kind == e_lexer.Operator && (p.peek().Value == "*" || p.peek().Value == "/" || p.peek().Value == "%") {
		pos := p.peekPos()
		token := p.next()
		expr_2 := p.parseExpression_9()
		if p.err != nil {
			return nil
		}
		final_expr = &e_ast.BinaryOpExpr{
			Op:       token.Value,
			LeftArg:  final_expr,
			RightArg: expr_2,
			Position: pos,
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}
	return final_expr
}

func (p *parser) parseExpression_9() e_ast.Expr {
	expr_1 := p.parseExpression_10()
	if p.err != nil {
		return expr_1
	}

	if p.peek().Kind == e_lexer.Keyword && p.peek().Value == "is" {
		var is_not_null = false
		pos := p.peekPos()
		p.next()
		if p.peek().Kind == e_lexer.Operator && p.peek().Value == "not" {
			p.next()
			is_not_null = true
		}
		if p.peek().Kind == e_lexer.Keyword && p.peek().Value == "null" {
			p.next()
			return &e_ast.IsNotNull{
				Arg:       expr_1,
				IsNotNull: is_not_null,
				Position:  pos,
			}
		} else if p.peekError != nil {
			p.err = p.peekError
			return nil
		} else {
			p.unexpectedError()
		}
	} else if p.peekError != nil {
		p.err = p.peekError
		return nil
	}

	return expr_1
}

func (p *parser) parseExpression_10() e_ast.Expr {
	expr_1 := p.parseExpression_11()
	if p.err != nil {
		return nil
	}

	if p.peek().Kind == e_lexer.Keyword && p.peek().Value == "between" {
		pos := p.peekPos()
		p.next()
		from_expr := p.parseExpression_4()
		if p.err != nil {
			return nil
		}

		if p.peek().Kind == e_lexer.Operator && p.peek().Value == "and" {
			p.next()
		} else if p.peekError != nil {
			p.err = p.peekError
			return nil
		} else {
			p.unexpectedError()
			return nil
		}

		to_expr := p.parseExpression_4()
		if p.err != nil {
			return nil
		}

		return &e_ast.Between{
			Arg:      expr_1,
			From:     from_expr,
			To:       to_expr,
			Position: pos,
		}
	} else if p.peekError != nil {
		p.err = p.peekError
		return nil
	}

	return expr_1
}

func (p *parser) parseExpression_11() e_ast.Expr {
	expr_1 := p.parseExpression_12()
	if p.err != nil {
		return expr_1
	}
	var not_like = false

	pos := p.peekPos()
	if p.peek().Kind == e_lexer.Operator && p.peek().Value == "not" {
		not_like = true
		p.next()
	}

	if p.peek().Kind == e_lexer.Name && strings.ToLower(p.peek().Value) == "like" {
		p.next()
		like_expr := p.parseExpression()
		if p.err != nil {
			return nil
		}
		var esc_expr e_ast.Expr = nil
		if p.peek().Kind == e_lexer.Keyword && p.peek().Value == "escape" {
			p.next()
			esc_expr = p.parseExpression()
			if p.err != nil {
				return nil
			}
		}
		return &e_ast.Like{
			Arg:        expr_1,
			LikeExpr:   like_expr,
			EscapeExpr: esc_expr,
			Not:        not_like,
			Position:   pos,
		}
	} else if p.peekError != nil {
		p.err = p.peekError
		return nil
	} else if not_like {
		p.unexpectedError()
		return nil
	}

	return expr_1
}

func (p *parser) parseExpression_12() e_ast.Expr {
	if p.peek().Kind == e_lexer.ParenL {
		pos := p.peekPos()
		p.next()
		expr := p.parseExpression()
		if p.err != nil {
			return expr
		}
		p.expect(e_lexer.ParenR)
		return &e_ast.ParenExpr{
			Arg:      expr,
			Position: pos,
		}
	} else if p.peek().Kind == e_lexer.Int || p.peek().Kind == e_lexer.Float || p.peek().Kind == e_lexer.String {
		pos := p.peekPos()
		token := p.next()
		return e_ast.ConstantExpr{
			RawValue: token.Value,
			Type:     token.Kind.Name(),
			Position: pos,
		}
	} else if p.peek().Kind == e_lexer.Keyword && (p.peek().Value == "true" || p.peek().Value == "false") {
		pos := p.peekPos()
		token := p.next()
		return e_ast.ConstantExpr{
			RawValue: token.Value,
			Type:     "Boolean",
			Position: pos,
		}
	} else if p.peek().Kind == e_lexer.Name {
		name := p.next()
		pos := p.peekPos()
		if p.peek().Kind == e_lexer.ParenL {
			p.next()
			if p.peek().Kind == e_lexer.ParenR {
				p.next()
				return &e_ast.FunctionCallExpr{
					Function: name.Value,
					Args:     make([]e_ast.Expr, 0),
					Position: pos,
				}
			} else {
				args := p.parseArgs()
				p.expect(e_lexer.ParenR)
				return &e_ast.FunctionCallExpr{
					Function: name.Value,
					Args:     args,
					Position: pos,
				}
			}
		} else {
			return &e_ast.FieldReferenceExpr{
				Field:    name.Value,
				Position: pos,
			}
		}

	} else if p.peekError != nil {
		p.err = p.peekError
		return nil
	} else {
		p.unexpectedError()
	}
	return nil
}

func (p *parser) parseArgs() []e_ast.Expr {
	res := make([]e_ast.Expr, 0)
	expr := p.parseExpression()
	if p.err != nil {
		return res
	}
	res = append(res, expr)

	for p.peek().Kind == e_lexer.Comma {
		p.next()
		if p.peek().Kind == e_lexer.Keyword && p.peek().Value == "interval" {
			pos := p.peekPos()
			p.next()
			expr := p.parseExpression()
			if p.err != nil {
				return nil
			}
			if p.peek().Kind == e_lexer.Name && time_units[strings.ToLower(p.peek().Value)] {
				unit := p.peek().Value
				p.next()
				res = append(res,
					&e_ast.DatetimeInterval{
						IntervalType: unit,
						Value:        expr,
						Position:     pos,
					})
			} else {
				p.unexpectedError()
			}
		} else {
			expr := p.parseExpression()
			if p.err != nil {
				return nil
			}
			res = append(res, expr)
		}
	}
	if p.peekError != nil {
		p.err = p.peekError
		return nil
	}

	return res
}
