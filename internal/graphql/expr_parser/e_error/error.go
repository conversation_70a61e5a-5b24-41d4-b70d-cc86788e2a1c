package e_error

import (
	"fmt"
)

type Error struct {
	err       error      `json:"-"`
	Message   string     `json:"message"`
	Locations []Location `json:"locations,omitempty"`
	Rule      string     `json:"-"`
}

func (e Error) Error() string {
	return e.Message
}

type Location struct {
	Line   int `json:"line,omitempty"`
	Column int `json:"column,omitempty"`
}

func ErrorLocf(line int, col int, message string, args ...interface{}) *Error {
	return &Error{
		Message: fmt.Sprintf(message, args...),
		Locations: []Location{
			{Line: line, Column: col},
		},
	}
}
