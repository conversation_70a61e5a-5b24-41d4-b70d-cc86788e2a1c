package e_lexer

import (
	"strconv"

	"github.com/tigergraph/graphql/expr_parser/e_ast"
)

const (
	Invalid Type = iota
	EOF
	Operator
	ParenL
	ParenR
	Comma
	Name
	Keyword
	Int
	Float
	String
)

func (t Type) Name() string {
	switch t {
	case Invalid:
		return "Invalid"
	case EOF:
		return "EOF"
	case Operator:
		return "Operator"
	case ParenL:
		return "ParenL"
	case ParenR:
		return "ParenR"
	case Comma:
		return "Comma"
	case Name:
		return "Name"
	case Keyword:
		return "Keyword"
	case Int:
		return "Int"
	case Float:
		return "Float"
	case String:
		return "String"
	}
	return "Unknown " + strconv.Itoa(int(t))
}

func (t Type) String() string {
	switch t {
	case Invalid:
		return "<Invalid>"
	case EOF:
		return "<EOF>"
	case Operator:
		return "Operator"
	case ParenL:
		return "("
	case ParenR:
		return ")"
	case Comma:
		return ","
	case Name:
		return "Name"
	case Keyword:
		return "Keyword"
	case Int:
		return "Int"
	case Float:
		return "Float"
	case String:
		return "String"
	}
	return "Unknown " + strconv.Itoa(int(t))
}

// Kind represents a type of token. The types are predefined as constants.
type Type int

type Token struct {
	Kind  Type           // The token type.
	Value string         // The literal value consumed.
	Pos   e_ast.Position // The file and line this token was read from
}

func (t Token) String() string {
	if t.Value != "" {
		return t.Kind.String() + " " + strconv.Quote(t.Value)
	}
	return t.Kind.String()
}
