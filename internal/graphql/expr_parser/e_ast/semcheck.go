package e_ast

import (
	"github.com/tigergraph/graphql/expr_parser/e_error"
	"github.com/tigergraph/graphql/gsql"
)

type Datatype int

const (
	Invalid Datatype = iota
	Int
	Float
	String
	Boolean
	Datetime
	Interval
)

func (t Datatype) Name() string {
	switch t {
	case Invalid:
		return "Invalid"
	case Int:
		return "Int"
	case Float:
		return "Float"
	case String:
		return "String"
	case Boolean:
		return "Boolean"
	case Datetime:
		return "Datetime"
	case Interval:
		return "Interval"
	}
	return "Unknown"
}

type FunctionSignature struct {
	Args       []Datatype
	ReturnType Datatype
}

var signatures = map[string][]FunctionSignature{
	// Operators

	"+": {
		FunctionSignature{Args: []Datatype{Int, Int}, ReturnType: Int},
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Float},
		FunctionSignature{Args: []Datatype{String, String}, ReturnType: String}},

	"-": {
		FunctionSignature{Args: []Datatype{Int}, ReturnType: Int},
		FunctionSignature{Args: []Datatype{Float}, ReturnType: Float},
		FunctionSignature{Args: []Datatype{Int, Int}, ReturnType: Int},
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Float}},

	"*": {
		FunctionSignature{Args: []Datatype{Int, Int}, ReturnType: Int},
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Float}},

	"/": {FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Float}},

	"%": {FunctionSignature{Args: []Datatype{Int, Int}, ReturnType: Int}},

	"<<": {FunctionSignature{Args: []Datatype{Int, Int}, ReturnType: Int}},

	">>": {FunctionSignature{Args: []Datatype{Int, Int}, ReturnType: Int}},

	"&": {FunctionSignature{Args: []Datatype{Int, Int}, ReturnType: Int}},

	"|": {FunctionSignature{Args: []Datatype{Int, Int}, ReturnType: Int}},

	"==": {
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{String, String}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{Datetime, Datetime}, ReturnType: Boolean}},

	"!=": {
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{String, String}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{Datetime, Datetime}, ReturnType: Boolean}},

	"<": {
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{String, String}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{Datetime, Datetime}, ReturnType: Boolean}},

	">": {
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{String, String}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{Datetime, Datetime}, ReturnType: Boolean}},

	"<=": {
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{String, String}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{Datetime, Datetime}, ReturnType: Boolean}},

	">=": {
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{String, String}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{Datetime, Datetime}, ReturnType: Boolean}},

	"not": {FunctionSignature{Args: []Datatype{Boolean}, ReturnType: Boolean}},

	"and": {FunctionSignature{Args: []Datatype{Boolean, Boolean}, ReturnType: Boolean}},

	"or": {FunctionSignature{Args: []Datatype{Boolean, Boolean}, ReturnType: Boolean}},

	// Between and like. We just model them as functions for convenience
	"#between": {
		FunctionSignature{Args: []Datatype{Float, Float, Float}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{String, String, String}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{Datetime, Datetime, Datetime}, ReturnType: Boolean}},

	"#like": {
		FunctionSignature{Args: []Datatype{String, String}, ReturnType: Boolean},
		FunctionSignature{Args: []Datatype{String, String, String}, ReturnType: Boolean}},

	// Numeric functions
	"abs": {FunctionSignature{Args: []Datatype{Int}, ReturnType: Int},
		FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"sqrt": {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"pow": {FunctionSignature{Args: []Datatype{Int}, ReturnType: Int},
		FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Float}},
	"acos":         {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"asin":         {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"atan":         {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"atan2":        {FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Float}},
	"ceil":         {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"cos":          {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"cosh":         {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"exp":          {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"floor":        {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"fmod":         {FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Float}},
	"ldexp":        {FunctionSignature{Args: []Datatype{Float, Float}, ReturnType: Float}},
	"log":          {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"log10":        {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"sin":          {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"sinh":         {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"tan":          {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"tanh":         {FunctionSignature{Args: []Datatype{Float}, ReturnType: Float}},
	"to_string":    {FunctionSignature{Args: []Datatype{Float}, ReturnType: String}},
	"float_to_int": {FunctionSignature{Args: []Datatype{Float}, ReturnType: Int}},
	"str_to_int":   {FunctionSignature{Args: []Datatype{String}, ReturnType: Int}},

	// String functions
	"lower": {FunctionSignature{Args: []Datatype{String}, ReturnType: String}},
	"upper": {FunctionSignature{Args: []Datatype{String}, ReturnType: String}},

	// Datetime functions
	"to_datetime":       {FunctionSignature{Args: []Datatype{String}, ReturnType: Datetime}},
	"epoch_to_datetime": {FunctionSignature{Args: []Datatype{Int}, ReturnType: Datetime}},
	"datetime_to_epoch": {FunctionSignature{Args: []Datatype{Datetime}, ReturnType: Int}},
	"datetime_format": {
		FunctionSignature{Args: []Datatype{Datetime}, ReturnType: String},
		FunctionSignature{Args: []Datatype{Datetime, String}, ReturnType: String}},
	"now":           {FunctionSignature{Args: []Datatype{}, ReturnType: Datetime}},
	"year":          {FunctionSignature{Args: []Datatype{Datetime}, ReturnType: Int}},
	"month":         {FunctionSignature{Args: []Datatype{Datetime}, ReturnType: Int}},
	"day":           {FunctionSignature{Args: []Datatype{Datetime}, ReturnType: Int}},
	"hour":          {FunctionSignature{Args: []Datatype{Datetime}, ReturnType: Int}},
	"minute":        {FunctionSignature{Args: []Datatype{Datetime}, ReturnType: Int}},
	"second":        {FunctionSignature{Args: []Datatype{Datetime}, ReturnType: Int}},
	"datetime_add":  {FunctionSignature{Args: []Datatype{Datetime, Interval}, ReturnType: Datetime}},
	"datetime_sub":  {FunctionSignature{Args: []Datatype{Datetime, Interval}, ReturnType: Datetime}},
	"datetime_diff": {FunctionSignature{Args: []Datatype{Datetime, Datetime}, ReturnType: Int}},
}

// Main type checking routine, we want to find the function signature that matches the arguments

func findFunctionSignature(e Expr, fname string, args []Datatype) (*FunctionSignature, error) {
	signList, ok := signatures[fname]
	if !ok {
		return nil, e_error.ErrorLocf(e.Pos().Line, e.Pos().Column, "Unknown function '%s'", fname)
	}

	// Iterate over all the signatures for this function/operator
	for _, sign := range signList {
		if len(args) != len(sign.Args) {
			continue
		}

		// We want all the arguments to match
		var allArgsMatch = true
		for i, arg := range sign.Args {
			// If the argument does not match the signature, check if it can be coerced to the signature type
			if args[i] != arg {
				if args[i] == Int && arg == Float {
					continue
				} else {
					allArgsMatch = false
					break
				}
			}
		}

		if allArgsMatch {
			return &sign, nil
		}
	}

	argStr := ""
	for i, arg := range args {
		if i != 0 {
			argStr += ","
		}
		argStr += arg.Name()
	}

	return nil, e_error.ErrorLocf(
		e.Pos().Line,
		e.Pos().Column,
		"Cannot find the matching signature for the function/operator '%s' with argument list (%s)", fname, argStr)
}

func (e UnaryOpExpr) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	argtype, err := e.Arg.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}
	sign, err := findFunctionSignature(e, e.Op, []Datatype{argtype})
	if err != nil {
		return Invalid, err
	}
	return sign.ReturnType, nil
}

func (e BinaryOpExpr) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	leftArgType, err := e.LeftArg.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}
	rightArgType, err := e.RightArg.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}

	sign, err := findFunctionSignature(e, e.Op, []Datatype{leftArgType, rightArgType})
	if err != nil {
		return Invalid, err
	}

	return sign.ReturnType, nil
}

func (e FunctionCallExpr) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	argTypes := make([]Datatype, 0)
	for _, arg_expr := range e.Args {
		argType, err := arg_expr.TypeCheck(attributeList)
		if err != nil {
			return Invalid, err
		}

		argTypes = append(argTypes, argType)
	}

	sign, err := findFunctionSignature(e, e.Function, argTypes)
	if err != nil {
		return Invalid, err
	}

	return sign.ReturnType, nil
}

func (e ConstantExpr) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	switch e.Type {
	case "String":
		return String, nil

	case "Int":
		return Int, nil

	case "Float":
		return Float, nil

	case "Boolean":
		return Boolean, nil

	default:
		return Invalid, e_error.ErrorLocf(e.Pos().Line, e.Pos().Column, "Unknown type encountered '%s'", e.Type)
	}
}

func (e FieldReferenceExpr) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	for _, a := range attributeList {
		if a.AttributeName == e.Field {
			switch a.AttributeType.Name {
			case "Uint64", "Int64":
				return Int, nil
			case "Float":
				return Float, nil
			case "String":
				return String, nil
			case "Datetime":
				return Datetime, nil
			case "Boolean":
				return Boolean, nil
			}
		}
	}
	return Invalid, e_error.ErrorLocf(e.Pos().Line, e.Pos().Column, "Invalid attribute reference '%s'", e.Field)
}

func (e DatetimeInterval) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	return Interval, nil
}

func (e IsNotNull) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	_, err := e.Arg.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}
	return Boolean, nil
}

func (e Between) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	argType, err := e.Arg.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}
	fromType, err := e.From.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}
	toType, err := e.To.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}
	sign, err := findFunctionSignature(e, "#between", []Datatype{argType, fromType, toType})
	if err != nil {
		return Invalid, e_error.ErrorLocf(
			e.Pos().Line,
			e.Pos().Column,
			"Illegal datatypes in between expression: %s between %s and %s", argType.Name(), fromType.Name(), toType.Name())
	}

	return sign.ReturnType, nil
}

func (e Like) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	argType, err := e.Arg.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}
	likeType, err := e.LikeExpr.TypeCheck(attributeList)
	if err != nil {
		return Invalid, err
	}
	var sign *FunctionSignature = nil
	if e.EscapeExpr != nil {
		escType, esc_err := e.EscapeExpr.TypeCheck(attributeList)
		if esc_err != nil {
			return Invalid, esc_err
		}
		sign, err = findFunctionSignature(e, "#like", []Datatype{argType, likeType, escType})
	} else {
		sign, err = findFunctionSignature(e, "#like", []Datatype{argType, likeType})
	}
	if err != nil {
		return Invalid, e_error.ErrorLocf(
			e.Pos().Line,
			e.Pos().Column,
			"Illegal datatypes in like expression, all arguments must be strings")
	}

	return sign.ReturnType, nil
}

func (e ParenExpr) TypeCheck(attributeList []gsql.Attribute) (Datatype, error) {
	return e.Arg.TypeCheck(attributeList)
}
