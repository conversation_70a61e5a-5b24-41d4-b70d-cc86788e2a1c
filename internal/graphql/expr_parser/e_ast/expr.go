package e_ast

import (
	"strings"

	"github.com/tigergraph/graphql/gsql"
)

type Expr interface {
	ToString(tupleVar string) string
	Pos() *Position
	TypeCheck(attributeList []gsql.Attribute) (Datatype, error)
}

type Position struct {
	Start  int // The starting position, in runes, of this token in the input.
	End    int // The end position, in runes, of this token in the input.
	Line   int // The line number at the start of this item.
	Column int // The column number at the start of this item.
	Src    string
}

type UnaryOpExpr struct {
	Op       string
	Arg      Expr
	Position *Position
}

func (e UnaryOpExpr) ToString(tupleVar string) string {
	return e.Op + " " + e.Arg.ToString(tupleVar)
}

func (e UnaryOpExpr) Pos() *Position {
	return e.Position
}

type BinaryOpExpr struct {
	Op       string
	LeftArg  Expr
	RightArg Expr
	Position *Position
}

func (e BinaryOpExpr) ToString(tupleVar string) string {
	return e.LeftArg.ToString(tupleVar) + " " + e.Op + " " + e.RightArg.ToString(tupleVar)
}

func (e BinaryOpExpr) Pos() *Position {
	return e.Position
}

type FunctionCallExpr struct {
	Function string
	Args     []Expr
	Position *Position
}

func (e FunctionCallExpr) ToString(tupleVar string) string {
	res := e.Function + "("
	for i, arg := range e.Args {
		if i != 0 {
			res += ","
		}
		res += " " + arg.ToString(tupleVar)
	}
	res += " )"
	return res
}

func (e FunctionCallExpr) Pos() *Position {
	return e.Position
}

type ConstantExpr struct {
	RawValue string
	Type     string
	Position *Position
}

func (e ConstantExpr) ToString(tupleVar string) string {
	if e.Type == "String" {
		return "\"" + SanitizeString(e.RawValue) + "\""
	} else {
		return e.RawValue
	}
}

func (e ConstantExpr) Pos() *Position {
	return e.Position
}

type FieldReferenceExpr struct {
	Field    string
	Position *Position
}

func (e FieldReferenceExpr) ToString(tupleVar string) string {
	return tupleVar + "." + e.Field
}

func (e FieldReferenceExpr) Pos() *Position {
	return e.Position
}

type DatetimeInterval struct {
	IntervalType string
	Value        Expr
	Position     *Position
}

func (e DatetimeInterval) ToString(tupleVar string) string {
	return "interval" + " " + e.Value.ToString(tupleVar) + " " + e.IntervalType
}

func (e DatetimeInterval) Pos() *Position {
	return e.Position
}

type IsNotNull struct {
	Arg       Expr
	IsNotNull bool
	Position  *Position
}

func (e IsNotNull) ToString(tupleVar string) string {
	res := e.Arg.ToString(tupleVar)
	res += " is "
	if e.IsNotNull {
		res += "not "
	}
	res += "null"
	return res
}

func (e IsNotNull) Pos() *Position {
	return e.Position
}

type Between struct {
	Arg      Expr
	From     Expr
	To       Expr
	Position *Position
}

func (e Between) ToString(tupleVar string) string {
	return e.Arg.ToString(tupleVar) + " between " + e.From.ToString(tupleVar) + " and " + e.To.ToString(tupleVar)
}

func (e Between) Pos() *Position {
	return e.Position
}

type Like struct {
	Arg        Expr
	LikeExpr   Expr
	EscapeExpr Expr
	Not        bool
	Position   *Position
}

func (e Like) ToString(tupleVar string) string {
	res := e.Arg.ToString(tupleVar)
	if e.Not {
		res += " not"
	}
	res += " like " + e.LikeExpr.ToString(tupleVar)
	if e.EscapeExpr != nil {
		res += " escape " + e.EscapeExpr.ToString(tupleVar)
	}
	return res
}

func (e Like) Pos() *Position {
	return e.Position
}

type ParenExpr struct {
	Arg      Expr
	Position *Position
}

func (e ParenExpr) ToString(tupleVar string) string {
	return "( " + e.Arg.ToString(tupleVar) + " )"
}

func (e ParenExpr) Pos() *Position {
	return e.Position
}

func SanitizeString(input string) string {
	result := strings.Builder{}
	for _, c := range input {
		if c == '%' {
			result.WriteByte('%')
		}
		result.WriteRune(c)
	}
	return result.String()
}
