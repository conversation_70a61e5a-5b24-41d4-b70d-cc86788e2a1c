#!/bin/bash

function tigergraph_version() {
    local tg_version=$(sudo -u tigergraph /home/<USER>/tigergraph/app/cmd/gadmin version | grep 'TigerGraph version: [0-9.]*' | awk '{print $NF}')
    echo "$tg_version"
}

function gui_build_num() {
    local build_num=$(curl -sS http://localhost:14242/api/version | jq -r .results.build_num)
    echo $build_num
}

function tg_solution_id() {
    local config_file="/home/<USER>/user_conf.yml"
    local solutionid=$(sudo -u ubuntu grep "solutionid:" $config_file | awk '{print $2}')
    echo $solutionid
}

function check_gui_version() {
    #  Parse the deployment.json file
    echo ENVIRONMENT:$ENVIRONMENT
    wget -O deployment.json https://tigergraph-build-artifacts.s3-us-west-1.amazonaws.com/tigergraph/gus/release/cloud-deployment/deployment_$ENVIRONMENT.json
    if [ $? -ne 0 ]; then
        echo "can not download deployment.json"
        exit 2
    fi
    local deployment=$(cat deployment.json)
    local solution_id=$(tg_solution_id)
    local tg_version=$(tigergraph_version)
    local gui_build_num=$(gui_build_num)
    local solution_keys=$(echo $deployment | jq -r '.solution | keys')

    echo solution:$solution_id
    echo tg_version:$tg_version

    if [[ -n "$solution_id" ]] && [[ $solution_keys == *"$solution_id"* ]]; then
        local release=$(echo $deployment | jq -r ".solution.\"$solution_id\"")
    else
        local release=$(echo $deployment | jq -r ".default.\"$tg_version\"")
    fi

    echo release:$release
    if [[ -z "$release" ]] || [[ $release == "null" ]]; then
        echo "can not find associated release"
        exit 2
    fi

    target_version=$(echo $deployment | jq ".release.\"$release\"")
    target_buildnum=$(echo $target_version | jq -r ".build_num")

    echo target_buildnum:$target_buildnum
    if [[ -z "$target_buildnum" ]] || [[ $target_buildnum == "null" ]]; then
        echo "can not find target buildnum"
        exit 2
    fi

    echo current_buildnum:$gui_build_num
    if [[ -z "$gui_build_num" ]] || [[ $gui_build_num == "null" ]]; then
        echo "can not find current buildnum"
        exit 2
    fi

    if [[ "$gui_build_num" == "$target_buildnum" ]]; then
        return
    else
        return 1
    fi
}

function upgrade_gui() {
    pre_check
    echo "upgrade GUI to $(echo $1 | jq -r ".build_num") "
    local url=$(echo $1 | jq -r ".download_url")
    local target_sum=$(echo $1 | jq -r ".sha256sum")
    sudo -u tigergraph curl -o /home/<USER>/tg_app_guid $url
    local file_sum=$(sha256sum /home/<USER>/tg_app_guid | awk '{print $1}')
    echo $file_sum
    if [[ $target_sum != $file_sum ]]; then
        echo "file integrity checksum failed for tg_app_guid"
        exit 3
    fi
    local tg_version=$(tigergraph_version)
    if [[ -z "$tg_version" ]]; then
        echo "can not find tigergraph version"
        exit 2
    fi
    # clear old backups
    sudo -u tigergraph sh -c "rm -f /home/<USER>/tigergraph/app/${tg_version}/bin/gui/tg_app_guid-backup-*"
    # run upgrade
    sudo -u tigergraph /home/<USER>/tigergraph/app/cmd/gadmin upgrade gui /home/<USER>/tg_app_guid -y
    # clear temp tg_app_guid
    sudo -u tigergraph rm -f /home/<USER>/tg_app_guid
}

function check_and_upgrade_gui() {
    check_gui_version
    if [ $? -eq 1 ]; then
        upgrade_gui "$target_version"
    fi
}

function pre_check() {
    echo "running pre_check for GUI version: $(date "+%Y-%m-%d %H:%M:%S")"
    curl -sS http://localhost:14242/api/version >/home/<USER>/gui_version.json
    if [ $? -ne 0 ]; then
        echo "can not connect to GUI"
        exit 2
    fi
    local pre_check_error=$(cat /home/<USER>/gui_version.json | jq '.error')
    if [[ "$pre_check_error" != "false" ]]; then
        echo "pre_check for GUI version failed: $(cat /home/<USER>/gui_version.json | jq '.message')"
        exit 2
    fi
}

function main() {
    pre_check
    case $1 in
    # sub operations
    check_gui_version)
        check_gui_version
        ;;
    check_and_upgrade_gui)
        check_and_upgrade_gui
        ;;
    *)
        check_and_upgrade_gui
        ;;
    esac
}

main $@
