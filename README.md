# Application Server

Application Server is the backend server for GraphStudio and Admin Portal.

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites
You only need to run either one
> If you haven't configured GitHub access to retrieve internal Go modules run `make access-github`.

> git config --local \\
url."https://{username}:{access_token}@github.com/tigergraph".insteadOf \\
"https://github.com/tigergraph"
--
to config `go get` to access private github repos. 

### Development server

Run `./scripts/air.sh` for a dev server. The server will be listening at `:14242`.
The server will automatically reload if you change any of the source files.

## API document

Run `make start-swagger` to start a Swagger server. Navigate to `http://localhost:14808/`.
To stop the Swagger server, run `make stop-swagger`.

## Build
Run `make build` to build the project. The build artifacts will be stored in the `release/` directory.

## Running the tests

### Running unit tests

Run `make test` to execute the unit tests.

### Coding style tests

Run `make lint` will do a code style check.
