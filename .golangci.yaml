# https://golangci-lint.run/usage/configuration/
run:
  # Timeout for analysis, e.g. 30s, 5m, default is 1m.
  timeout: 5m
  # Include test files or not, default is true.
  tests: true
  skip-dirs-use-default: true

linters-settings:
  exhaustive:
    # Indicate that switch statements are to be considered exhaustive if a
    # 'default' case is present, even if all enum members aren't listed in the
    # switch.
    default-signifies-exhaustive: true
  # funlen:
  #   lines: 128
  goconst:
    # Minimal length of string constant, 3 by default.
    min-len: 2
    # Minimal occurrences count to trigger, 3 by default.
    min-occurrences: 3
  gocyclo:
    # Minimal code complexity to report, 30 by default (but we recommend 10-20).
    min-complexity: 30
  # goimports:
  #   # Put imports beginning with prefix after 3rd-party packages;
  #   # it's a comma-separated list of prefixes.
  #   local-prefixes: github.com/tigergraph/cqrs,github.com/tigergraph/gus
  # golint:
  #   # Minimal confidence for issues, default is 0.8.
  #   min-confidence: 0
  gomnd:
    settings:
      mnd:
        # The list of enabled checks, see https://github.com/tommy-muehle/go-mnd/#checks for description.
        checks: argument,case,condition,return
  govet:
    # Report about shadowed variables.
    check-shadowing: true
    copylocks: false
  # lll:
  #   # Max line length, lines longer will be reported. Default is 120.
  #   # '\t' is counted as 1 character by default, and can be changed with the tab-width option.
  #   line-length: 140
  misspell:
    # Correct spellings using locale preferences for US or UK.
    # Default is to use a neutral variety of English.
    # Setting locale to US will correct the British spelling of 'colour' to 'color'.
    locale: US
  nolintlint:
    # Enable to ensure that nolint directives are all used. Default is true.
    allow-unused: false
    # Disable to ensure that nolint directives don't have a leading space. Default is true.
    allow-leading-space: false
    # Enable to require an explanation of nonzero length after each nolint directive. Default is false.
    require-explanation: true
    # Enable to require nolint directives to mention the specific linter being suppressed. Default is false.
    require-specific: true

linters:
  disable-all: true
  enable:
    # - bodyclose
    # - deadcode
    # - depguard
    - dogsled
    # - dupl
    # - errcheck
    - exhaustive
    # - funlen
    - gochecknoinits
    - goconst
    # - gocritic
    - gocyclo
    - gofmt
    - goimports
    # - golint
    # - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    # - govet
    - ineffassign
    # - lll
    - misspell
    - nakedret
    # - noctx
    - nolintlint
    # - rowserrcheck
    # - exportloopref
    - copyloopvar
    - staticcheck
    # - structcheck
    # - stylecheck
    - typecheck
    # - unconvert
    - unparam
    - unused
    # - varcheck
    # - whitespace

issues:
  exclude-rules:
    - linters:
        - gosec
      # these exclusion rules are for current failures in the code base for gosec which are
      # excluded for future PRs which include:
      # G110: Potential DoS vulnerability via decompression bomb
      text: "G110"
