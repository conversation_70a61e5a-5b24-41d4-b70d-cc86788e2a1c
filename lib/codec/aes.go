package codec

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
)

// PKCSPad adds random padding to text.
// https://en.wikipedia.org/wiki/Padding_(cryptography)#PKCS#5_and_PKCS#7
func PKCSPad(text []byte, blockSize int) ([]byte, error) {
	bytesToPad := blockSize - len(text)%blockSize
	padding := make([]byte, bytesToPad)

	if _, err := rand.Read(padding); err != nil {
		return nil, err
	}
	// Puts the padded size to the last block.
	padding[len(padding)-1] = byte(bytesToPad)

	return append(text, padding...), nil
}

// PKCSTrim removes padding from text.
// https://en.wikipedia.org/wiki/Padding_(cryptography)#PKCS#5_and_PKCS#7
func PKCSTrim(text []byte) ([]byte, error) {
	paddedSize := text[len(text)-1]
	if int(paddedSize) > len(text) {
		return nil, fmt.Errorf("padding size is larger than original text size")
	}
	return text[:len(text)-int(paddedSize)], nil
}

// AESCBCEncrypt encrypts a text in CBC mode.
// https://en.wikipedia.org/wiki/Block_cipher_mode_of_operation#CBC
func AESCBCEncrypt(secret, text string) (string, error) {
	c, err := getCipher(secret)
	if err != nil {
		return "", err
	}

	iv := make([]byte, aes.BlockSize)
	if _, err = rand.Read(iv); err != nil {
		return "", err
	}

	encrypter := cipher.NewCBCEncrypter(c, iv)
	data, err := PKCSPad([]byte(text), encrypter.BlockSize())
	if err != nil {
		return "", err
	}

	data = append(iv, data...)
	encrypter.CryptBlocks(data, data)

	return hex.EncodeToString(data), nil
}

// AESCBCDecrypt decrypts a text in CBC mode.
// https://en.wikipedia.org/wiki/Block_cipher_mode_of_operation#CBC
func AESCBCDecrypt(secret, cipherText string) (string, error) {
	c, err := getCipher(secret)
	if err != nil {
		return "", err
	}

	cipherBytes, err := hex.DecodeString(cipherText)
	if err != nil {
		return "", err
	}

	if len(cipherBytes) < aes.BlockSize {
		return "", fmt.Errorf("text size should be more than %v bytes blocks", aes.BlockSize)
	}
	iv := cipherBytes[:aes.BlockSize]

	data := make([]byte, len(cipherBytes)-aes.BlockSize)
	decrypter := cipher.NewCBCDecrypter(c, iv)
	decrypter.CryptBlocks(data, cipherBytes[aes.BlockSize:])

	bytes, err := PKCSTrim(data)
	if err != nil {
		return "", err
	}

	return string(bytes), nil
}

// getCipher creates and returns a new cipher block with secret.
func getCipher(secret string) (cipher.Block, error) {
	key := sha256.Sum256([]byte(secret))
	return aes.NewCipher(key[:])
}
