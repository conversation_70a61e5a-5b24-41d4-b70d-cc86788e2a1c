package status

import (
	"context"
	"fmt"
	"math"
	"os"
	"sync"
	"time"

	tgCfg "github.com/tigergraph/cqrs/tutopia/common/config"
	"github.com/tigergraph/cqrs/tutopia/common/event"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/util/net/kafka"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/config"
)

var outputProducer *event.EventProducer
var srvDesc *service.ServiceDescriptor
var configOnce *sync.Once = &sync.Once{}

const (
	// retry forever
	status_report_retry_cnt      = math.MaxInt32
	status_report_retry_interval = 3 * time.Second
	kafka_retry_interval         = 3 * time.Second
	default_span_id              = "GUIServiceStatusSelfReport"
)

func Init(cfg *config.Config) error {
	var err error

	configOnce.Do(func() {
		err = initConfig(cfg.GetReplica(), cfg.GetConfig())
	})

	return err
}

func initConfig(replica int, cfg *tgCfg.Config) error {
	sd, err := service.NewServiceDescriptor(service.GUI, service.PartitionAll, replica)
	if err != nil {
		log.Errorf("error when create gui service dscriptor with replica: %d, error: %v", replica, err)
		return fmt.Errorf("error when create gui service dscriptor with replica: %d, error: %v", replica, err)
	}
	srvDesc = &sd

	evtCfg, err := event.NewEventConfig(cfg, sd.GetServiceId(), true)
	if err != nil {
		log.Errorf("error when get event config: %v", err)
		return fmt.Errorf("error when get event config: %v", err)
	}

	kafkaProducer, err := kafka.NewProducer(evtCfg.KafkaConfig, "EventOutputProducer", evtCfg.KafkaOutputTopic)
	if err != nil {
		log.Errorf("error when create kafka producer: %v", err)
		return fmt.Errorf("error when create kafka producer: %v", err)
	}

	kafkaProducer.AsyncStartWithRetry(context.TODO(), kafka_retry_interval)
	outputProducer = event.NewEventProducer(kafkaProducer, *srvDesc)
	return nil
}

func AsyncReportServiceStatus(serviceStatus pb.ServiceStatus) {
	//nolint:gosec // #nosec G115: integer overflow conversion int -> int32
	status, err := event.NewStatus(*srvDesc, pb.ProcessState_StateUnchanged, serviceStatus, nil, int32(os.Getpid()))
	if err != nil {
		log.Errorf("Generate new service status failed, error is: ", err)
	}

	go func() {
		target, err := service.NewServiceDescriptor(service.INFORMANT, service.PartitionAll,
			service.ReplicaAll)
		if err != nil {
			log.Errorf("Generate target service descriptor failed, error is: ", err)
			return
		}
		err = outputProducer.ProduceToManyWithRetry(context.Background(), []service.ServiceDescriptor{target},
			default_span_id, status, status_report_retry_cnt, status_report_retry_interval)
		if err != nil {
			log.Errorf("Produce service status event to kafka failed, error is: ", err)
		}
	}()
}
