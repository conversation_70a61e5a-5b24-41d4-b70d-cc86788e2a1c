package json

import (
	"fmt"
	"testing"
)

func TestBigInterger(t *testing.T) {
	m := make(map[string]interface{})
	m["a"] = 1234567890123456789
	m["b"] = 1234.5678

	bs, _ := Marshal(m)
	fmt.Println(string(bs))

	var m2 map[string]interface{}
	Unmarshal(bs, &m2)

	bs2, _ := Marshal(m2)
	fmt.Println(string(bs2))

	if string(bs2) != string(bs) {
		t.E<PERSON>r("big interger is not correctly unmarshalled")
	}

}
