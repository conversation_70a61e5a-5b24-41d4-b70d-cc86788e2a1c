package fs

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"github.com/pkg/errors"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"

	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/tg"
)

const (
	GRPC_TRANSFER_TIMEOUT = 5 * time.Minute
)

type FileInfo struct {
	Name       string `json:"name"`
	Path       string `json:"path"`
	Size       int64  `json:"size"`
	IsDir      bool   `json:"isDir"`
	ModTime    int64  `json:"modTime"`
	SourcePath string `json:"sourcePath"`
}

type SearchResult struct {
	Line       string `json:"line"`
	Path       string `json:"path"`
	Offset     int64  `json:"offset"`
	LineNumber int64  `json:"lineNumber"`
}

type TGFileSystem struct {
	cfg *config.Config
}

func NewTGFilesystem(cfg *config.Config) *TGFileSystem {
	return &TGFileSystem{cfg: cfg}
}

// List returns first level files and directories under a path of a server.
// If the path points to a file, it returns the file.
// If the path points to a directory, it returns first level files and directories under the path.
func (fs *TGFileSystem) List(path, hostID string) ([]FileInfo, error) {
	cntlrClient, err := tg.ControllerClient(fs.cfg)
	if err != nil {
		return nil, err
	}

	descs, err := fs.cfg.GetServDescFromHostID(hostID)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.ListFileRequest{
		Meta: &pb.FileMeta{
			FilePath: path,
			Sd:       descs[0].ServiceDescriptor,
		},
	}
	resp, err := cntlrClient.ListFile(ctx, req)
	if err != nil {
		return nil, rpc.Error(err, tgServ.CONTROLLER, cntlrClient.ListFile)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return nil, tgErr.NewErrFromPbError(resp.GetError())
	}

	// If input file path is a symbolic link
	if len(resp.Responses) == 1 && len(resp.Responses[0].Infos) == 1 {
		info := resp.Responses[0].Infos[0]
		if info.Path == req.Meta.FilePath && info.SourcePath != "" {
			dir, _ := filepath.Split(req.Meta.FilePath)
			sourceDir, base := filepath.Split(info.SourcePath)
			if sourceDir == "" { // source is at the same dir of symbolic link
				info.SourcePath = filepath.Join(dir, base)
			}
			return fs.List(info.SourcePath, hostID)
		}
	}

	filesInfo := make([]FileInfo, len(resp.GetResponses()[0].Infos))
	for i, info := range resp.GetResponses()[0].Infos {
		filesInfo[i] = FileInfo{
			Name:       info.GetName(),
			Path:       info.GetPath(),
			Size:       info.GetSize(),
			IsDir:      info.GetIsDir(),
			ModTime:    info.GetModTime() / int64(time.Millisecond),
			SourcePath: info.GetSourcePath(),
		}
	}
	return filesInfo, nil
}

// Search returns the search results of a pattern under a path.
func (fs *TGFileSystem) Search(path, filter, pattern string, limit int32, hostID string) ([]SearchResult, error) {
	cntlrClient, err := tg.ControllerClient(fs.cfg)
	if err != nil {
		return nil, err
	}

	descs, err := fs.cfg.GetServDescFromHostID(hostID)
	if err != nil {
		return nil, err
	}
	if len(descs) == 0 {
		return nil, errors.Errorf("host ID %s does not exist", hostID)
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.SearchFileRequest{
		Location: &pb.SearchFileLocation{
			Meta: &pb.FileMeta{
				FilePath: path,
				Sd:       descs[0].ServiceDescriptor,
			},
			FileFilter: filter,
			Recursive:  true,
			Offset:     0,
		},
		Pattern: pattern,
		Limit:   limit,
	}
	resp, err := cntlrClient.SearchFile(ctx, req)
	if err != nil {
		return nil, rpc.Error(err, tgServ.CONTROLLER, cntlrClient.SearchFile)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return nil, tgErr.NewErrFromPbError(resp.GetError())
	}

	searchResults := make([]SearchResult, len(resp.GetResponses()[0].Results))
	for i, result := range resp.GetResponses()[0].Results {
		searchResults[i] = SearchResult{
			Line:       string(result.GetLine()),
			Path:       result.GetPath(),
			Offset:     result.GetOffset(),
			LineNumber: result.GetLineNumber(),
		}
	}
	return searchResults, nil
}

// Read reads content of a file from a server from an offset with the given length.
func (fs *TGFileSystem) Read(cntlrClient pb.ControllerClient, path string, offset, length int64, hostID string) ([]byte, error) {
	descs, err := fs.cfg.GetServDescFromHostID(hostID)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.ReadFileRequest{
		Meta: &pb.FileMeta{
			FilePath: path,
			Sd:       descs[0].ServiceDescriptor,
		},
		Offset: offset,
		Length: length,
	}

	resp, err := cntlrClient.ReadFile(ctx, req)
	if err != nil {
		return nil, rpc.Error(err, tgServ.CONTROLLER, cntlrClient.ReadFile)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return nil, tgErr.NewErrFromPbError(resp.GetError())
	}

	return resp.GetResponses()[0].Data, nil
}

// CopyFile copies a file from a server to other servers.
type FileType string

const (
	FileTypeFile   FileType = "File"
	FileTypeFolder FileType = "Folder"
)

func (fs *TGFileSystem) CopyFile(src, srcHostID, dst string, dstHostIDs []string, fileType FileType) error {
	cntlrClient, err := tg.ControllerClient(fs.cfg)
	if err != nil {
		return err
	}

	srcDescs, err := fs.cfg.GetServDescFromHostID(srcHostID)
	if err != nil {
		return err
	}

	dstDescs := make([]tgServ.ServiceDescriptor, len(dstHostIDs))
	for i, hostID := range dstHostIDs {
		var descs []tgServ.ServiceDescriptor
		descs, err = fs.cfg.GetServDescFromHostID(hostID)
		if err != nil {
			return err
		}
		dstDescs[i] = descs[0]
	}
	dstFileMeta := make([]*pb.FileMeta, len(dstDescs))
	for i, desc := range dstDescs {
		dstFileMeta[i] = &pb.FileMeta{
			FilePath: dst,
			Sd:       desc.ServiceDescriptor,
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), GRPC_TRANSFER_TIMEOUT)
	defer cancel()
	var permission string = "0600"
	if fileType == FileTypeFolder {
		permission = ""
	}
	req := &pb.TransferFileRequest{
		SrcFileMeta: &pb.FileMeta{
			FilePath: src,
			Sd:       srcDescs[0].ServiceDescriptor,
		},
		TgtFileMetas: dstFileMeta,
		Permission:   permission,
		SpanId:       genSpanID("file-transfer"),
	}
	resp, err := cntlrClient.TransferFile(ctx, req)
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.TransferFile)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

// Remove removes path and any children it contains from a server.
func (fs *TGFileSystem) Remove(path string, hostIDs []string) error {
	cntlrClient, err := tg.ControllerClient(fs.cfg)
	if err != nil {
		return err
	}

	metas := make([]*pb.FileMeta, 0, len(hostIDs))
	for _, hostID := range hostIDs {
		descs, err := fs.cfg.GetServDescFromHostID(hostID)
		if err != nil {
			return err
		}
		meta := &pb.FileMeta{
			FilePath: path,
			Sd:       descs[0].ServiceDescriptor,
		}
		metas = append(metas, meta)
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.DeleteFileRequest{
		Metas:  metas,
		SpanId: genSpanID("file-delete"),
	}
	resp, err := cntlrClient.DeleteFile(ctx, req)
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.DeleteFile)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

func genSpanID(action string) string {
	return fmt.Sprintf("[%v]@%v:%s", tgServ.GUI, time.Now().UnixNano(), action)
}
