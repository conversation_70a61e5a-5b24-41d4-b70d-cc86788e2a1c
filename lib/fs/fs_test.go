package fs

import (
	"os"
	"path"
	"reflect"
	"testing"
	"time"

	"github.com/djherbis/times"
	"github.com/stretchr/testify/require"
)

func TestDecompress(t *testing.T) {
	f, _ := os.Open("testdata/data.tar")
	err := Decompress(f, "testdata/temp")
	require.NoError(t, err)

	f, _ = os.Open("testdata/data.tar.gz")
	err = Decompress(f, "testdata/temp")
	require.NoError(t, err)
}

func TestRemoveOldFiles(t *testing.T) {
	type args struct {
		path string
		ts   time.Time
	}
	tests := []struct {
		name    string
		args    args
		remove  bool
		wantErr bool
	}{
		{
			"remove old files",
			args{
				"testdata/temp/removeoldfiles",
				time.Now().Add(time.Hour),
			},
			true,
			false,
		},
		{
			"do not remove files",
			args{
				"testdata/temp/donotremovefiles",
				time.Now().Add(-time.Hour),
			},
			false,
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			os.MkdirAll(tt.args.path, os.ModePerm)
			os.Create(path.Join(tt.args.path, "test"))

			if err := RemoveOldFiles(tt.args.path, tt.args.ts); (err != nil) != tt.wantErr {
				t.Errorf("RemoveOldFiles() error = %v, wantErr %v", err, tt.wantErr)
			}

			_, testFileStatErr := os.Stat(path.Join(tt.args.path, "test"))
			if tt.remove {
				require.ErrorIs(t, testFileStatErr, os.ErrNotExist)
			} else {
				require.NoError(t, testFileStatErr)
			}

			os.RemoveAll(tt.args.path)
		})
	}
}

func Test_getChangeOrModTime(t *testing.T) {
	type args struct {
		timespec times.Timespec
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			"changetime",
			args{
				&fackeTimeSpec{
					changeTime:    time.Date(2023, time.April, 1, 0, 0, 0, 0, time.Local),
					modTime:       time.Date(2023, time.April, 2, 0, 0, 0, 0, time.Local),
					hasChangeTime: true,
				},
			},
			time.Date(2023, time.April, 1, 0, 0, 0, 0, time.Local),
		},
		{
			"modtime",
			args{
				&fackeTimeSpec{
					changeTime:    time.Date(2023, time.April, 1, 0, 0, 0, 0, time.Local),
					modTime:       time.Date(2023, time.April, 2, 0, 0, 0, 0, time.Local),
					hasChangeTime: false,
				},
			},
			time.Date(2023, time.April, 2, 0, 0, 0, 0, time.Local),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getChangeOrModTime(tt.args.timespec); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getChangeOrModTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

type fackeTimeSpec struct {
	modTime       time.Time
	accessTime    time.Time
	changeTime    time.Time
	birthTime     time.Time
	hasChangeTime bool
	hasBirthTime  bool
}

func (ft *fackeTimeSpec) ModTime() time.Time {
	return ft.modTime
}

func (ft *fackeTimeSpec) AccessTime() time.Time {
	return ft.accessTime
}

func (ft *fackeTimeSpec) BirthTime() time.Time {
	return ft.birthTime
}

func (ft *fackeTimeSpec) ChangeTime() time.Time {
	return ft.changeTime
}

func (ft *fackeTimeSpec) HasChangeTime() bool {
	return ft.hasChangeTime
}

func (ft *fackeTimeSpec) HasBirthTime() bool {
	return ft.hasBirthTime
}

type Timespec interface {
	ModTime() time.Time
	AccessTime() time.Time
	ChangeTime() time.Time
	BirthTime() time.Time
	HasChangeTime() bool
	HasBirthTime() bool
}
