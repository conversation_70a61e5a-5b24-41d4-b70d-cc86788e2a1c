package fs

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"time"

	"github.com/djherbis/times"
	gdw "github.com/karrick/godirwalk"
	"github.com/pkg/errors"
)

// Exist checks if the file or directory exists.
func Exist(path string) (bool, error) {
	if _, err := os.Stat(path); err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// SaveUploadedFile saves the uploaded multipart file to disk.
// If the file does not exist, SaveUploadedFile creates it with permissions perm;
// otherwise SaveUploadedFile replaces the file.
func SaveUploadedFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	out, err := os.OpenFile(dst, os.O_CREATE|os.O_RDWR|os.O_TRUNC, 0600)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

// CombineFiles combines multiple files into one.
// If the file does not exist, CombineFiles creates it with permissions perm;
// otherwise CombineFiles replaces the file.
func CombineFiles(src []string, dst string) error {
	out, err := os.OpenFile(dst, os.O_CREATE|os.O_RDWR|os.O_TRUNC, 0600)
	if err != nil {
		return err
	}
	defer out.Close()

	for _, s := range src {
		in, err := os.Open(s)
		if err != nil {
			return err
		}

		_, err = io.Copy(out, in)
		in.Close()
		if err != nil {
			return err
		}
	}

	return nil
}

// RemoveOldFiles recursively removes old files under a path.
// If an error occurs in a filesystem node, skip this node and
// continue walking the filesystem hierarchy with the remaining nodes.
func RemoveOldFiles(path string, ts time.Time) error {
	return gdw.Walk(path, &gdw.Options{
		Callback: func(p string, de *gdw.Dirent) error {
			if de.IsRegular() {
				timespec, err := times.Stat(p)
				if err != nil {
					return err
				}

				if getChangeOrModTime(timespec).Before(ts) {
					return os.Remove(p)
				}
			}
			return nil
		},
		ErrorCallback: func(p string, err error) gdw.ErrorAction {
			return gdw.SkipNode
		},
		Unsorted:          true,
		AllowNonDirectory: true,
	})
}

// Compress compresses a file or a directory.
func Compress(src string, dst io.Writer) error {
	info, err := os.Stat(src)
	if err != nil {
		return err
	}

	if info.IsDir() {
		return compressDir(src, dst)
	}
	return compressFile(src, dst)
}

// compressFile compresses a file using gzip.
func compressFile(src string, dst io.Writer) error {
	gzw := gzip.NewWriter(dst)
	defer gzw.Close()

	file, err := os.Open(src)
	if err != nil {
		return err
	}
	defer file.Close()

	if _, err := io.Copy(gzw, file); err != nil {
		return err
	}
	return nil
}

// compressDir archives a directory using tar then compresses it using gzip.
func compressDir(src string, dst io.Writer) error {
	gzw := gzip.NewWriter(dst)
	defer gzw.Close()
	tw := tar.NewWriter(gzw)
	defer tw.Close()

	return gdw.Walk(src, &gdw.Options{
		Callback: func(p string, de *gdw.Dirent) error {
			info, err := os.Stat(p)
			if err != nil {
				return err
			}

			header, err := tar.FileInfoHeader(info, p)
			if err != nil {
				return err
			}

			// Convert absolute path to relative path to remove redundant directories.
			relPath := p
			if filepath.IsAbs(p) {
				relPath, err = filepath.Rel(src, p)
				if err != nil {
					return err
				}
			}
			header.Name = filepath.ToSlash(relPath)

			if err := tw.WriteHeader(header); err != nil {
				return err
			}

			if !info.IsDir() {
				file, err := os.Open(p)
				if err != nil {
					return err
				}
				defer file.Close()

				if _, err := io.Copy(tw, file); err != nil {
					return err
				}
			}
			return nil
		},
		Unsorted:          true,
		AllowNonDirectory: false,
	})
}

// Decompress decompresses a tarball.
func Decompress(src io.Reader, dst string) error {
	tr, err := convertToTarReader(src)
	if err != nil {
		return err
	}

	for {
		header, err := tr.Next()
		switch {
		case err == io.EOF:
			return nil
		case err != nil:
			return errors.WithStack(err)
		case header == nil:
			continue
		}
		//nolint:gosec // traversal is done iteratively one by one, so this is safe.
		target := filepath.Join(dst, header.Name)

		switch header.Typeflag {
		case tar.TypeDir:
			if err := os.MkdirAll(target, 0755); err != nil {
				return errors.WithStack(err)
			}

		case tar.TypeReg:
			//nolint:gosec // #nosec G115: integer overflow conversion int64 -> uint32
			file, err := os.OpenFile(target, os.O_CREATE|os.O_RDWR, os.FileMode(header.Mode))
			if err != nil {
				return errors.WithStack(err)
			}
			defer file.Close()

			if _, err := io.Copy(file, tr); err != nil {
				return errors.WithStack(err)
			}
		}
	}
}

func convertToTarReader(src io.Reader) (*tar.Reader, error) {
	data, err := io.ReadAll(src)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	gzr, err := gzip.NewReader(bytes.NewReader(data))
	if err == gzip.ErrHeader {
		// assuming that this src data has already be decompressed and is a .tar only
		return tar.NewReader(bytes.NewReader(data)), nil
	}
	if err != nil {
		return nil, errors.WithStack(err)
	}
	defer gzr.Close()
	tr := tar.NewReader(gzr)
	return tr, nil
}

func getChangeOrModTime(timespec times.Timespec) time.Time {
	if timespec.HasChangeTime() {
		return timespec.ChangeTime()
	} else {
		return timespec.ModTime()
	}
}
