package http

import (
	"crypto/tls"
	"net/http"
	"net/url"
	"time"

	"github.com/hashicorp/go-cleanhttp"
	"github.com/hashicorp/go-retryablehttp"

	"github.com/tigergraph/gotools/log"
)

// RetryableClient is the wrapper for retryable HTTP client.
type RetryableClient struct {
	client *retryablehttp.Client
}

type ClientConfigs struct {
	PooledTransport    bool
	Timeout            time.Duration
	RetryMax           int
	RetryWaitMin       time.Duration
	RetryWaitMax       time.Duration
	InsecureSkipVerify bool
}

func NewClient(config *ClientConfigs) *RetryableClient {
	var transport *http.Transport
	if config.PooledTransport {
		transport = cleanhttp.DefaultPooledTransport()
	} else {
		transport = cleanhttp.DefaultTransport()
	}
	//nolint:gosec // this is only used to talk to internal HTTPS servers.
	transport.TLSClientConfig = &tls.Config{InsecureSkipVerify: config.InsecureSkipVerify}
	httpClient := &http.Client{Transport: transport, Timeout: config.Timeout}

	return &RetryableClient{
		&retryablehttp.Client{
			HTTPClient:   httpClient,
			Logger:       &httpLogger{},
			RetryMax:     config.RetryMax,
			RetryWaitMin: config.RetryWaitMin,
			RetryWaitMax: config.RetryWaitMax,
			Backoff:      retryablehttp.DefaultBackoff,
			CheckRetry:   retryablehttp.DefaultRetryPolicy,
		},
	}
}

// Do sends an HTTP request and returns an HTTP response, following
// policy (such as redirects, cookies, auth) as configured on the
// client.
func (c *RetryableClient) Do(req *http.Request) (*http.Response, error) {
	wrappedReq, err := retryablehttp.FromRequest(req)
	if err != nil {
		return nil, err
	}
	return c.client.Do(wrappedReq)
}

// Get issues a GET to the specified URL.
func (c *RetryableClient) Get(url string) (*http.Response, error) {
	return c.client.Get(url)
}

// Head issues a HEAD to the specified URL.
func (c *RetryableClient) Head(url string) (*http.Response, error) {
	return c.client.Head(url)
}

// Post issues a POST to the specified URL.
func (c *RetryableClient) Post(url, bodyType string, body interface{}) (*http.Response, error) {
	return c.client.Post(url, bodyType, body)
}

// PostForm issues a POST to the specified URL, with data's keys and
// values URL-encoded as the request body.
func (c *RetryableClient) PostForm(url string, data url.Values) (*http.Response, error) {
	return c.client.PostForm(url, data)
}

type httpLogger struct{}

// Enforce logger to implement LeveledLogger.
var _ retryablehttp.LeveledLogger = (*httpLogger)(nil)

func (l *httpLogger) Debug(msg string, args ...interface{}) {
	log.Debug(msg, args)
}

func (l *httpLogger) Info(msg string, args ...interface{}) {
	log.Info(msg, args)
}

func (l *httpLogger) Warn(msg string, args ...interface{}) {
	log.Warn(msg, args)
}

func (l *httpLogger) Error(msg string, args ...interface{}) {
	log.Error(msg, args)
}
