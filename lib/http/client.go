package http

import (
	"bytes"
	"context"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"time"

	"github.com/hashicorp/go-cleanhttp"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/tigergraph/gotools/log"
)

var (
	// A regular expression to match the error returned by net/http when the
	// configured number of redirects is exhausted. This error isn't typed
	// specifically so we resort to matching on the error string.
	redirectsErrorRe = regexp.MustCompile(`stopped after \d+ redirects\z`)

	// A regular expression to match the error returned by net/http when the
	// scheme specified in the URL is invalid. This error isn't typed
	// specifically so we resort to matching on the error string.
	schemeErrorRe = regexp.MustCompile(`unsupported protocol scheme`)
)

// RetryableClient is the wrapper for retryable HTTP client.
type RetryableClient struct {
	client *retryablehttp.Client
}

type ClientConfigs struct {
	PooledTransport    bool
	Timeout            time.Duration
	RetryMax           int
	RetryWaitMin       time.Duration
	RetryWaitMax       time.Duration
	InsecureSkipVerify bool
	CheckRetry         retryablehttp.CheckRetry
}

func NewClient(config *ClientConfigs) *RetryableClient {
	var transport *http.Transport
	if config.PooledTransport {
		transport = cleanhttp.DefaultPooledTransport()
	} else {
		transport = cleanhttp.DefaultTransport()
	}
	//nolint:gosec // this is only used to talk to internal HTTPS servers.
	transport.TLSClientConfig = &tls.Config{InsecureSkipVerify: config.InsecureSkipVerify}
	httpClient := &http.Client{Transport: transport, Timeout: config.Timeout}
	if config.CheckRetry == nil {
		config.CheckRetry = retryablehttp.DefaultRetryPolicy
	}
	return &RetryableClient{
		&retryablehttp.Client{
			HTTPClient:   httpClient,
			Logger:       &httpLogger{},
			RetryMax:     config.RetryMax,
			RetryWaitMin: config.RetryWaitMin,
			RetryWaitMax: config.RetryWaitMax,
			Backoff:      retryablehttp.DefaultBackoff,
			CheckRetry:   config.CheckRetry,
		},
	}
}

// baseReqGSQLServerRetryPolicy provides a retry policy for gsql server.
// if the response from gsql server is a json, it can be dertemined as
// already proccessed by gsgl server, in this case, even the response code is
// 50x, we still don't need retry it.
func baseReqGSQLServerRetryPolicy(resp *http.Response, err error) (bool, error) {
	if err != nil {
		if v, ok := err.(*url.Error); ok {
			// Don't retry if the error was due to too many redirects.
			if redirectsErrorRe.MatchString(v.Error()) {
				return false, v
			}

			// Don't retry if the error was due to an invalid protocol scheme.
			if schemeErrorRe.MatchString(v.Error()) {
				return false, v
			}

			// Don't retry if the error was due to TLS cert verification failure.
			if _, ok := v.Err.(x509.UnknownAuthorityError); ok {
				return false, v
			}
		}

		// The error is likely recoverable so retry.
		return true, nil
	}

	// 429 Too Many Requests is recoverable. Sometimes the server puts
	// a Retry-After response header to indicate when the server is
	// available to start processing request from client.
	if resp.StatusCode == http.StatusTooManyRequests {
		return true, nil
	}

	if resp.StatusCode == 0 {
		return true, fmt.Errorf("unexpected HTTP status %s", resp.Status)
	}

	// Check the response code. We retry on 500-range responses to allow
	// the server time to recover, as 500's are typically not permanent
	// errors and may relate to outages on the server side. This will catch
	// invalid response codes as well, like 0 and 999.
	if resp.StatusCode >= 500 && resp.StatusCode != 501 {
		// first clone the response
		clonedResp, err := cloneResponse(resp)
		if err != nil {
			return true, err
		}
		// check the result body is json
		// if is json, doesn't need retry
		responseJson := make(map[string]interface{})
		body, err := io.ReadAll(clonedResp.Body)
		if err != nil {
			return true, fmt.Errorf("unexpected HTTP status %s", resp.Status)
		}
		if err := json.Unmarshal(body, &responseJson); err != nil {
			return true, fmt.Errorf("unexpected HTTP status %s, it isn't a json", resp.Status)
		}
		clonedResp.Body.Close()
		// is json, doesn't need retry
		return false, nil
	}

	return false, nil
}

func cloneResponse(resp *http.Response) (*http.Response, error) {
	// Read the response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	// Restore the original response body so it can be read again
	resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	// Create a new response with the same properties
	clonedResp := new(http.Response)
	*clonedResp = *resp
	// Set the cloned response body
	clonedResp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	return clonedResp, nil
}

// DefaultReqGSQLServerRetryPolicy provides a default callback for Client.CheckRetry, which
// will retry on connection errors and server errors.
func DefaultReqGSQLServerRetryPolicy(ctx context.Context, resp *http.Response, err error) (bool, error) {
	// do not retry on context.Canceled or context.DeadlineExceeded
	if ctx.Err() != nil {
		return false, ctx.Err()
	}

	// don't propagate other errors
	shouldRetry, _ := baseReqGSQLServerRetryPolicy(resp, err)
	return shouldRetry, nil
}

// Do sends an HTTP request and returns an HTTP response, following
// policy (such as redirects, cookies, auth) as configured on the
// client.
func (c *RetryableClient) Do(req *http.Request) (*http.Response, error) {
	wrappedReq, err := retryablehttp.FromRequest(req)
	if err != nil {
		return nil, err
	}
	return c.client.Do(wrappedReq)
}

// Get issues a GET to the specified URL.
func (c *RetryableClient) Get(url string) (*http.Response, error) {
	return c.client.Get(url)
}

// Head issues a HEAD to the specified URL.
func (c *RetryableClient) Head(url string) (*http.Response, error) {
	return c.client.Head(url)
}

// Post issues a POST to the specified URL.
func (c *RetryableClient) Post(url, bodyType string, body interface{}) (*http.Response, error) {
	return c.client.Post(url, bodyType, body)
}

// PostForm issues a POST to the specified URL, with data's keys and
// values URL-encoded as the request body.
func (c *RetryableClient) PostForm(url string, data url.Values) (*http.Response, error) {
	return c.client.PostForm(url, data)
}

type httpLogger struct{}

// Enforce logger to implement LeveledLogger.
var _ retryablehttp.LeveledLogger = (*httpLogger)(nil)

func (l *httpLogger) Debug(msg string, args ...interface{}) {
	log.Debug(msg, args)
}

func (l *httpLogger) Info(msg string, args ...interface{}) {
	log.Info(msg, args)
}

func (l *httpLogger) Warn(msg string, args ...interface{}) {
	log.Warn(msg, args)
}

func (l *httpLogger) Error(msg string, args ...interface{}) {
	log.Error(msg, args)
}
