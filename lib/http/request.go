package http

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/dao/model"
)

func SetFromGraphStudio(req *http.Request) error {
	cookieStr := req.Header.Get("GSQL-Cookie")
	cookie := make(model.GSQLCookies)
	if cookieStr != "" {
		if err := json.Unmarshal([]byte(cookieStr), &cookie); err != nil {
			return err
		}
	}
	cookie["fromGraphStudio"] = true
	cookieBytes, err := json.Marshal(cookie)
	if err != nil {
		return err
	}
	req.Header.Set("GSQL-Cookie", string(cookieBytes))
	return nil
}

func SetForwardedForFromContext(c context.Context, req *http.Request) {
	if gc, ok := c.(*gin.Context); ok {
		if gc.GetHeader("X-Forwarded-For") != "" {
			req.Header.Set("X-Forwarded-For", gc.<PERSON>Header("X-Forwarded-For"))
		}
	}
}
