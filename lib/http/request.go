package http

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/dao/model"
)

func SetFromGraphStudio(req *http.Request) error {
	cookieStr := req.Header.Get("GSQL-Cookie")
	cookie := make(model.GSQLCookies)
	if cookieStr != "" {
		if err := json.Unmarshal([]byte(cookieStr), &cookie); err != nil {
			return err
		}
	}
	cookie["fromGraphStudio"] = true
	cookieBytes, err := json.Marshal(cookie)
	if err != nil {
		return err
	}
	req.Header.Set("GSQL-Cookie", string(cookieBytes))
	return nil
}

func SetForwardedForFromContext(c context.Context, req *http.Request) {
	if gc, ok := c.(*gin.Context); ok {
		if gc.GetHeader("X-Forwarded-For") != "" {
			req.Header.Set("X-Forwarded-For", gc.GetHeader("X-Forwarded-For"))
		}
	}
}

// Set Credentials for GSQL server
func SetCredentials(req *http.Request, creds *model.UserCredentials) {

	if creds == nil {
		return
	}

	switch creds.AuthType {
	case model.AuthTokenAuthType:
		req.Header.Set("Authorization", "Bearer "+creds.AuthToken)
	case model.GsqlTokenAuthType:
		req.Header.Set("Authorization", "Bearer "+creds.GsqlToken)
	case model.KerberosAuthType:
		req.Header.Set("Authorization", "Negotiate "+creds.Password)
	default:
		req.SetBasicAuth(creds.Username, creds.Password)
	}
}
