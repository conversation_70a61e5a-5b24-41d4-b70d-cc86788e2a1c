package jwt

import (
	"time"

	"github.com/google/uuid"
	"github.com/tigergraph/gus/lib/codec"
)

type StandardClaims struct {
	ExpiresAt int64 `json:"exp,omitempty"`
	IssuedAt  int64 `json:"iat,omitempty"`
}

type Claims struct {
	ID           uuid.UUID `json:"id"` // client id
	SessionID    uuid.UUID `json:"sessionId"`
	Username     string    `json:"username"`
	RealUsername string    `json:"realUsername"`
	Password     string    `json:"password"`
	Revoked      bool      `json:"revoked"`
	StandardClaims
}

func (c Claims) EncryptPassword(authToken string) (*Claims, error) {
	encryptedPwd, err := codec.AESCBCEncrypt(authToken, c.Password)
	if err != nil {
		return nil, err
	}
	c.Password = encryptedPwd
	return &c, nil
}

func New(
	id, sessionID uuid.UUID,
	username, password, realUsername string,
	duration time.Duration,
) Claims {

	issuedTime := time.Now()
	expiredTime := issuedTime.Add(duration)
	claims := Claims{
		ID:           id,
		SessionID:    sessionID,
		Username:     username,
		RealUsername: realUsername,
		Password:     password,
		StandardClaims: StandardClaims{
			IssuedAt:  issuedTime.Unix(),
			ExpiresAt: expiredTime.Unix(),
		},
	}
	return claims
}
