package tg

import (
	"fmt"
	"sync"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	tgGrpc "github.com/tigergraph/cqrs/util/net/grpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/resolver"

	"github.com/tigergraph/gus/lib/config"
)

const (
	TG_SCHEME = "tg"
)

var (
	conn     *grpc.ClientConn
	cntlrMux = &sync.Mutex{}
)

type tgResolveBuilder struct {
	cfg *config.Config
}

func (*tgResolveBuilder) Scheme() string {
	return TG_SCHEME
}

func (r *tgResolveBuilder) Build(target resolver.Target, cc resolver.ClientConn, opts resolver.BuildOptions) (resolver.Resolver, error) {

	hostnames, err := r.cfg.GetAllControllerHostnames()
	if err != nil {
		return nil, err
	}

	port := r.cfg.GetControllerPort()

	addresses := make([]string, len(hostnames))
	for i, hostname := range hostnames {
		addresses[i] = fmt.Sprintf("%v:%v", hostname, port)
	}

	state := resolver.State{
		Addresses: make([]resolver.Address, len(addresses)),
	}

	for i, addr := range addresses {
		state.Addresses[i] = resolver.Address{
			Addr: addr,
		}
	}

	cc.UpdateState(state)

	return &nopResolver{}, nil
}

type nopResolver struct{}

func (*nopResolver) Start(target resolver.Target) error {
	return nil
}

func (*nopResolver) ResolveNow(resolver.ResolveNowOptions) {}

func (*nopResolver) Close() {}

// ControllerClient is the client for communication with Controller.
// The client attempts to connect to any of the Controller instances.
func ControllerClient(cfg *config.Config) (pb.ControllerClient, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config is nil")
	}
	if conn != nil {
		return pb.NewControllerClient(conn), nil
	}
	cntlrMux.Lock()
	defer cntlrMux.Unlock()

	var err error
	options := tgGrpc.DialOptions()
	options = append(options, grpc.WithResolvers(&tgResolveBuilder{cfg: cfg}))
	// Use round robin load balancing, to fix https://graphsql.atlassian.net/browse/APPS-2525
	options = append(options, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))

	conn, err = grpc.Dial(fmt.Sprintf("%s:///", TG_SCHEME), options...)
	if err != nil {
		return nil, err
	}

	return pb.NewControllerClient(conn), nil

}
