package tg

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
)

func RequestGSQLClient(ctx context.Context, cfg *config.Config, graphName, username, password, command string) ([]byte, error) {

	request := model.GSQLCommandRequest{
		Command: command,
		Cookies: model.GSQLCookies{
			"fromGraphStudio": true,
			"graph":           graphName,
		},
	}

	resp, err := RequestGSQLFileHandler(ctx, username, password, "", cfg, request)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(resp.Status)
	}

	defer resp.Body.Close()

	return ParseGSQLStream(resp.Body)
}

func RequestGSQLClientByAuthToken(ctx context.Context, cfg *config.Config, graphName, authToken, command string) (result []byte, err error) {
	request := model.GSQLCommandRequest{
		Command: command,
		Cookies: model.GSQLCookies{
			"fromGraphStudio": true,
			"graph":           graphName,
		},
	}

	resp, err := RequestGSQLFileHandler(ctx, "", "", authToken, cfg, request)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(resp.Status)
	}

	defer resp.Body.Close()

	return ParseGSQLStream(resp.Body)
}

func ParseGSQLStream(r io.Reader) (output []byte, err error) {
	output = make([]byte, 0)
	scanner := bufio.NewScanner(r)
	for scanner.Scan() {
		line := scanner.Text()
		// ignore output startswith __GSQL__
		// e.g. __GSQL__INTERACT__  __GSQL__COOKIES__
		if !strings.HasPrefix(line, "__GSQL__") {
			output = append(output, []byte(line)...)
		}
		if strings.HasPrefix(line, "__GSQL__RETURN__CODE__") {
			words := strings.Split(line, ",")
			if len(words) != 2 {
				return output, fmt.Errorf("invalid GSQL response: %s", line)
			}
			_, v := words[0], words[1]
			if v != "0" {
				return output, fmt.Errorf("GSQL exited with code %s", v)
			}
		}
	}
	return output, nil
}
