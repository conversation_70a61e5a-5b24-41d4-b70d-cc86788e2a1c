package tg

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
)

const (
	minimumSendingInterval     = time.Millisecond * 100
	progressBarCompletePattern = "\\[=*\\s*\\]\\s100%[^l]*"
)

func RequestGSQLClient(ctx context.Context, cfg *config.Config, graphName string, creds *model.UserCredentials, command string) ([]byte, error) {

	request := model.GSQLCommandRequest{
		Command: command,
		Cookies: model.GSQLCookies{
			"fromGraphStudio": true,
		},
	}

	if graphName != "" {
		request.Cookies["graph"] = graphName
	}

	resp, err := RequestGSQLFileHandler(ctx, creds, cfg, request)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, errors.New(resp.Status)
		}
		var response map[string]interface{}
		if err := json.Unmarshal(body, &response); err != nil {
			return nil, errors.New(resp.Status)
		}
		if response["message"] != nil {
			return []byte(response["message"].(string)), errors.New(resp.Status)
		}
		return nil, errors.New(string(body))
	}

	return ParseGSQLStream(resp.Body)
}

func RequestGSQLClientByAuthToken(ctx context.Context, cfg *config.Config, graphName, authToken, command string) (result []byte, err error) {
	request := model.GSQLCommandRequest{
		Command: command,
		Cookies: model.GSQLCookies{
			"fromGraphStudio": true,
		},
	}

	if graphName != "" {
		request.Cookies["graph"] = graphName
	}

	resp, err := RequestGSQLFileHandler(ctx, &model.UserCredentials{AuthToken: authToken, AuthType: model.AuthTokenAuthType}, cfg, request)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(resp.Status)
	}

	defer resp.Body.Close()

	return ParseGSQLStream(resp.Body)
}

func ParseGSQLStream(r io.Reader) (output []byte, err error) {
	output = make([]byte, 0)
	scanner := bufio.NewScanner(r)
	for scanner.Scan() {
		line := scanner.Text()
		// ignore output startswith __GSQL__
		// e.g. __GSQL__INTERACT__  __GSQL__COOKIES__
		if !strings.HasPrefix(line, "__GSQL__") {
			output = append(output, []byte(line)...)
		}
		if strings.HasPrefix(line, "__GSQL__RETURN__CODE__") {
			words := strings.Split(line, ",")
			if len(words) != 2 {
				return output, fmt.Errorf("invalid GSQL response: %s", line)
			}
			_, v := words[0], words[1]
			if v != "0" {
				return output, fmt.Errorf("GSQL exited with code %s", v)
			}
		}
	}
	return output, nil
}

// StreamGSQLResponseToSSE streams the GSQL response body to the client via SSE.
// It handles progress bar, interactive command, and __GSQL__RETURN__CODE__ logic.
// If an error is returned, the caller should send an SSE error event; otherwise, send a done event.
func StreamGSQLResponseToSSE(
	c *gin.Context,
	req *model.GSQLCommandRequest,
	resp *http.Response,
) error {
	reader := bufio.NewReader(resp.Body)
	var lastSentTime time.Time
	buffer := ""
	newLine := make([]byte, 0)

	for {
		b, err := reader.ReadByte()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Warnf("Failed to decode GSQL response: %+v", err)
			return err
		}
		switch b {
		case '\r':
			// Handle special case for progress bar
			newLine = []byte("\r" + string(newLine))
		case '\n':
			newLine = append(newLine, b)
		default:
			newLine = append(newLine, b)
			continue
		}

		re := regexp.MustCompile(progressBarCompletePattern)
		if re.Match(newLine) {
			newLine = []byte("\r" + string(newLine))
		}

		var cmdError error
		if bytes.HasPrefix(newLine, []byte("__GSQL__INTERACT__")) {
			log.Infof("unsupport command: %+v", newLine)
			// in the future, it may need abort session
			// once the session is aborted, the sessionId shall not be reused
			delete(req.Cookies, "sessionId")
			newCookieStr, _ := json.Marshal(req.Cookies)
			c.SSEvent("message", model.Response{Results: fmt.Sprintf("__GSQL__COOKIES__,%s\n", newCookieStr)})
			cmdError = fmt.Errorf("interactive commands are not supported so far")
		}
		// Check for __GSQL__RETURN__CODE__ and error
		cmdStartIdx := bytes.Index(newLine, []byte("__GSQL__RETURN__CODE__"))
		if cmdStartIdx > -1 {
			successIndex := bytes.Index(newLine, []byte("__GSQL__RETURN__CODE__,0"))
			if successIndex == -1 {
				cmdError = fmt.Errorf("gsql command execution failed")
			}
		}

		buffer += string(newLine)
		newLine = make([]byte, 0)

		// If error occurred or enough time has passed since the last send, send the buffer
		if time.Since(lastSentTime) > minimumSendingInterval || cmdError != nil {
			c.SSEvent("message", model.Response{Results: buffer})
			c.Writer.Flush()
			lastSentTime = time.Now()
			buffer = ""
		}

		if cmdError != nil {
			return cmdError
		}
	}

	// Send any remaining buffer after the loop
	if buffer != "" {
		c.SSEvent("message", model.Response{Results: buffer})
		c.Writer.Flush()
	}

	return nil
}
