package tg

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
)

type GSQLHTTPClientV2 struct {
	httpClient *h.RetryableClient
	urlPrefix  string
}

func NewGSQLHTTPClientV2(cfg *config.Config) *GSQLHTTPClientV2 {
	return &GSQLHTTPClientV2{
		httpClient: h.NewClient(&h.ClientConfigs{
			Timeout:            cfg.GetHTTPRequestTimeout(),
			RetryMax:           cfg.GetHTTPRequestRetryMax(),
			RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
			RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
			InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
			CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
		}),
		urlPrefix: fmt.Sprintf(
			"%s://%s:%d",
			cfg.GetNginxProtocol(),
			cfg.GetGSQLServerHostname(),
			cfg.GetNginxPort(),
		),
	}
}

// Request sends a request to GSQL server and returns its parsed response.
func (s *GSQLHTTPClientV2) Request(c *gin.Context, creds *model.UserCredentials, request *model.Request, response interface{}) error {

	httpRequest, err := http.NewRequestWithContext(
		context.Background(),
		request.Method,
		s.urlPrefix+request.Url,
		bytes.NewReader(request.Body),
	)
	if err != nil {
		log.Errorf(c, "Failed to prepare request to GSQL server: %v", err)
		return err
	}

	if request.Method == http.MethodGet {
		httpRequest.URL.RawQuery = request.QueryParameters.Encode()
	}

	for key, value := range request.Header {
		httpRequest.Header.Set(key, value)
	}

	h.SetCredentials(httpRequest, creds)
	h.SetFromGraphStudio(httpRequest)
	h.SetForwardedForFromContext(c, httpRequest)
	// TODO: Log the GSQL request.

	httpResponse, err := s.httpClient.Do(httpRequest)
	if err != nil {
		log.Errorf(c, "Failed to get response from GSQL server: %v", err)
		return err
	}
	defer httpResponse.Body.Close()

	responseBody, err := io.ReadAll(httpResponse.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from GSQL server: %v", err)
		return err
	}

	if err := json.Unmarshal(responseBody, response); err != nil {
		log.Errorf(c, "Failed to parse response from GSQL server: %v", err)
		// TODO: Handle the unmarshal error.
		return err
	}

	return nil
}
