package tg

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func Test_parseGSQLStream(t *testing.T) {
	t.Run("gsql exit with 0", func(t *testing.T) {
		input := strings.NewReader("__GSQL__RETURN__CODE__,0\nhello,world")
		output, err := ParseGSQLStream(input)
		require.NoError(t, err)
		require.Equal(t, string(output), "hello,world")
	})
	t.Run("gsql exit with 1", func(t *testing.T) {
		input := strings.NewReader("__GSQL__RETURN__CODE__,1\nhello,world")
		output, err := ParseGSQLStream(input)
		require.ErrorContains(t, err, "GSQL exited with code 1")
		require.Empty(t, output)
	})
	t.Run("invalid __GSQL__RETURN__CODE__", func(t *testing.T) {
		input := strings.NewReader("__GSQL__RETURN__CODE__\nhello,world")
		output, err := ParseGSQLStream(input)
		require.ErrorContains(t, err, "invalid GSQL response: __GSQL__RETURN__CODE__")
		require.Empty(t, output)
	})
	t.Run("__GSQL__CLEAN__LINE__", func(t *testing.T) {
		input := strings.NewReader("__GSQL__CLEAN__LINE__\nhello,world")
		output, err := ParseGSQLStream(input)
		require.NoError(t, err)
		require.Equal(t, string(output), "hello,world")
	})
	t.Run("__GSQL__COOKIES__", func(t *testing.T) {
		input := strings.NewReader("__GSQL__COOKIES__,aaa=bbb\nhello,world")
		output, err := ParseGSQLStream(input)
		require.NoError(t, err)
		require.Equal(t, string(output), "hello,world")
	})
}
