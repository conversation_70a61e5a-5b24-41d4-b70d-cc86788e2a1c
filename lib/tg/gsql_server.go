package tg

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
)

func RequestGSQLFileHandler(
	ctx context.Context,
	creds *model.UserCredentials,
	cfg *config.Config,
	request model.GSQLCommandRequest,
) (*http.Response, error) {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		// remove gsqlserver prefix
		"/gsql/v1/statements",
	)

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, requestURL,
		strings.NewReader(request.Command))

	if creds == nil {
		return nil, errors.New("no credentials provided")
	}

	h.SetCredentials(req, creds)
	// Always add fromGraphStudio cookie to the request.
	if request.Cookies == nil {
		request.Cookies = make(model.GSQLCookies)
	}
	request.Cookies["fromGraphStudio"] = true

	cookieStr, _ := json.Marshal(request.Cookies)
	req.Header.Set("GSQL-Cookie", string(cookieStr))
	h.SetForwardedForFromContext(ctx, req)

	req.Header.Set("Content-Type", "text/plain")

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server requests.
	})
	return client.Do(req)
}
