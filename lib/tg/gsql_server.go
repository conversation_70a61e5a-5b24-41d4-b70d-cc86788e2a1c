package tg

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
)

func RequestGSQLFileHandler(
	ctx context.Context,
	username string,
	password string,
	authToken string,
	cfg *config.Config,
	request model.GSQLCommandRequest,
) (*http.Response, error) {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		"/gsqlserver",
		"/gsql/file",
	)

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, requestURL,
		strings.NewReader(url.QueryEscape(request.Command)))

	if username != "" && password != "" {
		req.Set<PERSON>(username, password)
	} else {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", authToken))
	}

	// Always add fromGraphStudio cookie to the request.
	if request.Cookies == nil {
		request.Cookies = make(model.GSQLCookies)
	}
	request.Cookies["fromGraphStudio"] = true

	cookieStr, _ := json.Marshal(request.Cookies)
	req.Header.Set("GSQL-Cookie", string(cookieStr))
	h.SetForwardedForFromContext(ctx, req)

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server requests.
	})
	return client.Do(req)
}

func AbortClientSession(
	username string,
	password string,
	cfg *config.Config,
	cookie model.GSQLCookies,
) error {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		"/gsqlserver",
		"/gsql/abortclientsession",
	)

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, requestURL,
		strings.NewReader("abortclientsession"))

	if username != "" && password != "" {
		req.SetBasicAuth(username, password)
	}

	// Always add fromGraphStudio cookie to the request.
	if cookie == nil {
		cookie = make(model.GSQLCookies)
	}
	cookie["fromGraphStudio"] = true

	cookieStr, _ := json.Marshal(cookie)
	req.Header.Set("GSQL-Cookie", string(cookieStr))

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server requests.
	})

	response, err := client.Do(req)
	if err != nil {
		return err
	}

	if response.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to abort client session, status code: %d", response.StatusCode)
	}

	return nil
}
