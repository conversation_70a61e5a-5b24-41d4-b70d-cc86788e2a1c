package runner

import (
	"context"
	"fmt"
	"sync"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
)

type State int

const (
	Running State = iota
	Stopping
	Stopped
)

func (s State) String() string {
	return [...]string{"Running", "Stopping", "Stopped"}[s]
}

// Runner is the controller for long running instance.
type Runner struct {
	Instance interfaces.Runnable

	state     State
	mux       *sync.Mutex
	exitError error

	// Channel to receive signal when the Runner is stopped.
	stop chan struct{}

	// Context of the running instance.
	ctx    context.Context
	cancel context.CancelFunc
}

func New(i interfaces.Runnable) Runner {
	return Runner{
		Instance: i,
		state:    Stopped,
		mux:      &sync.Mutex{},
	}
}

// AsyncStart asynchronously starts the Runner in a goroutine.
// The Runner can only be started in Stopped state.
// If it is already running, returns immediately.
func (r *Runner) AsyncStart() error {
	r.checkPanic()
	r.mux.Lock()
	defer r.mux.Unlock()

	if r.state == Running {
		log.Info("The runner is already started")
		return nil
	}
	if r.state != Stopped {
		return fmt.Errorf(
			"the runner can only be started in %s state, current state is %s",
			Stopped.String(),
			r.state.String(),
		)
	}

	if err := r.Instance.SetUp(); err != nil {
		r.Instance.TearDown()
		return err
	}

	r.stop = make(chan struct{})
	r.ctx, r.cancel = context.WithCancel(context.Background())

	cancelSignal := make(chan struct{})
	cancelDone := make(chan struct{})

	// Goroutine to handle when the context is canceled.
	go r.contextHandler(cancelSignal, cancelDone)
	// Goroutine to run the instance.
	go r.runHandler(cancelSignal, cancelDone)

	r.state = Running
	return nil
}

// contextHandler stops when either the context is canceled or Run finishes.
func (r *Runner) contextHandler(cancelSignal, cancelDone chan struct{}) {
	r.mux.Lock()
	ctx := r.ctx
	r.mux.Unlock()

	select {
	case <-ctx.Done():
	case <-cancelSignal:
	}
	close(cancelDone)
}

// runHandler stops when Run finishes.
func (r *Runner) runHandler(cancelSignal, cancelDone chan struct{}) {
	r.mux.Lock()
	ctx := r.ctx
	r.mux.Unlock()

	err := r.Instance.Run(ctx)
	close(cancelSignal)
	<-cancelDone

	r.mux.Lock()
	defer r.mux.Unlock()

	r.exitError = err
	r.Instance.TearDown()
	r.state = Stopped
	close(r.stop)
}

// AsyncStop asynchronously stops the Runner by canceling the context.
// The Runner can only be stopped in Running state.
// If it is stopping or already stopped, returns immediately.
func (r *Runner) AsyncStop() {
	r.checkPanic()
	r.mux.Lock()
	defer r.mux.Unlock()

	if r.state != Running {
		log.Infof(
			"The runner can only be stopped in %s state, current state is %s",
			Running.String(),
			r.state.String(),
		)
		return
	}

	r.cancel()
	r.state = Stopping
}

// Wait waits until the Runner stops.
// If the Runner is already stopped, returns immediately.
func (r *Runner) Wait() error {
	r.checkPanic()
	r.mux.Lock()

	if r.state == Stopped {
		r.mux.Unlock()
		return r.GetExitError()
	}

	stop := r.stop
	r.mux.Unlock()
	<-stop

	return r.GetExitError()
}

// Start starts the runner asynchronously and waits for it to finish.
func (r *Runner) Start() error {
	if err := r.AsyncStart(); err != nil {
		return err
	}
	return r.Wait()
}

// Stop stops the runner asynchronously and waits for it to finish.
func (r *Runner) Stop() error {
	r.AsyncStop()
	return r.Wait()
}

// GetState returns the current state of the Runner.
func (r *Runner) GetState() State {
	r.checkPanic()
	r.mux.Lock()
	defer r.mux.Unlock()
	return r.state
}

// GetExitError returns the exit error.
func (r *Runner) GetExitError() error {
	r.checkPanic()
	r.mux.Lock()
	defer r.mux.Unlock()
	return r.exitError
}

// checkPanic checks if the Runnable instance is initialized or not.
func (r *Runner) checkPanic() {
	if r.Instance == nil {
		log.Panic("Runnable instance is not initialized")
	}
}
