package sync

import (
	"fmt"
	"time"

	"github.com/tigergraph/cqrs/util/net/etcd"
	tgSync "github.com/tigergraph/cqrs/util/sync"
)

const keyPrefix = "/lock/gui"

// KeyLocker is the wrapper for Etcd key locker.
// It is a distributed lock, use this if concurrency across multiple servers is required.
type KeyLocker struct {
	locker tgSync.KeyLocker
}

// Enforce locker to implement KeyLocker.
var _ tgSync.KeyLocker = (*KeyLocker)(nil)

func NewKeyLocker() (*KeyLocker, error) {
	locker, err := etcd.NewKeyLocker()
	if err != nil {
		return nil, err
	}
	return &KeyLocker{locker: locker}, nil
}

// Lock locks a key.
// Key should not start with "/", for example: "a/b/c".
func (l *KeyLocker) Lock(key string) error {
	return l.locker.Lock(genLockKey(key))
}

func (l *KeyLocker) TryLock(key string, timeout time.Duration) error {
	return l.locker.TryLock(genLockKey(key), timeout)
}

func (l *KeyLocker) TryUnlock(key string, timeout time.Duration) error {
	return l.locker.TryUnlock(genLockKey(key), timeout)
}

func (l *KeyLocker) TryLockAll(keys []string, timeout time.Duration) error {
	return l.locker.TryLockAll(keys, timeout)
}

func (l *KeyLocker) TryUnlockAll(keys []string, timeout time.Duration) error {
	return l.locker.TryUnlockAll(keys, timeout)
}

// LockAll locks a list of keys.
// Key should not start with "/", for example: "a/b/c".
func (l *KeyLocker) LockAll(keys []string) error {
	keysWithPrefix := make([]string, len(keys))
	for i, key := range keys {
		keysWithPrefix[i] = genLockKey(key)
	}
	return l.locker.LockAll(keysWithPrefix)
}

// Unlock unlocks a key.
// Key should not start with "/", for example: "a/b/c".
func (l *KeyLocker) Unlock(key string) error {
	return l.locker.Unlock(genLockKey(key))
}

// UnlockAll unlocks a list of keys.
// Key should not start with "/", for example: "a/b/c".
func (l *KeyLocker) UnlockAll(keys []string) error {
	keysWithPrefix := make([]string, len(keys))
	for i, key := range keys {
		keysWithPrefix[i] = genLockKey(key)
	}
	return l.locker.UnlockAll(keysWithPrefix)
}

func genLockKey(key string) string {
	return fmt.Sprintf("%s/%s", keyPrefix, key)
}
