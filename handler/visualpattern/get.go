package visualpattern

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// Get returns a visual pattern for a graph.
func Get(c *gin.Context) {
	graphName := c.Param("graphName")
	patternName := c.Param("patternName")

	daoManager := dao.GetManager(c)
	pattern, err := daoManager.GetVisualPattern(graphName, patternName)
	if err != nil {
		if errors.Is(err, db.ErrNotFound) {
			mw.Abort(
				c,
				http.StatusNotFound,
				fmt.Sprintf("Visual pattern '%s' for graph '%s' cannot be found.", patternName, graphName),
			)
		} else {
			log.Errorf(c, "Failed to get visual pattern %s for graph %s: %v", patternName, graphName, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to get visual pattern '%s' for graph '%s'.", patternName, graphName),
			)
		}
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", pattern)
}
