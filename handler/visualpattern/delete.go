package visualpattern

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Delete deletes a visual pattern from a graph.
func Delete(c *gin.Context) {
	graphName := c.Param("graphName")
	patternName := c.Param("patternName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteVisualPattern(graphName, patternName); err != nil {
		log.Errorf(c, "Failed to delete visual pattern %s from graph %s: %v", patternName, graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete visual pattern '%s' from graph '%s'.", patternName, graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted visual pattern '%s' from graph '%s'.", patternName, graphName),
	)
}
