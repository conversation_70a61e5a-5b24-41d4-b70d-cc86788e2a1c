package visualpattern

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Upsert upserts a visual pattern for a graph.
func Upsert(c *gin.Context) {
	graphName := c.Param("graphName")
	patternName := c.Param("patternName")

	var pattern interface{}
	if err := c.ShouldBindJSON(&pattern); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	daoManager := dao.GetManager(c)
	err := daoManager.UpsertVisualPattern(graphName, patternName, pattern)
	if err != nil {
		log.Errorf(c, "Failed to upsert visual pattern %s for graph %s: %v", patternName, graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to upsert visual pattern '%s' for graph '%s'.", patternName, graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully upserted visual pattern '%s' for graph '%s'.", patternName, graphName),
	)
}
