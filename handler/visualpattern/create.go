package visualpattern

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// Create creates a visual pattern for a graph.
func Create(c *gin.Context) {
	graphName := c.Param("graphName")
	patternName := c.Param("patternName")

	var pattern interface{}
	if err := c.ShouldBindJSON(&pattern); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.CreateVisualPattern(graphName, patternName, pattern); err != nil {
		if err == db.ErrAlreadyExist {
			mw.Abort(
				c,
				http.StatusConflict,
				fmt.Sprintf("Visual pattern '%s' already exists on graph '%s'.", patternName, graphName),
			)
		} else {
			log.Errorf(c, "Failed to create visual pattern %s for graph %s: %v", patternName, graphName, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to create visual pattern '%s' for graph '%s'.", patternName, graphName),
			)
		}
		return
	}

	mw.Reply(
		c,
		http.StatusCreated,
		fmt.Sprintf("Successfully created visual pattern '%s' for graph '%s'.", patternName, graphName),
	)
}
