package visualpattern

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// DeleteAll deletes all visual patterns from a graph.
func DeleteAll(c *gin.Context) {
	graphName := c.Param("graphName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteAllVisualPatterns(graphName); err != nil {
		log.Errorf(c, "Failed to delete all visual patterns from graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete all visual patterns from graph '%s'.", graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted all visual patterns from graph '%s'.", graphName),
	)
}
