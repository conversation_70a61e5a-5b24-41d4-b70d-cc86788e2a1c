package healthcheck

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	mw "github.com/tigergraph/gus/middleware"
)

func ListHealthCheckScripts(ctx *gin.Context, ctrl pb.ControllerClient) {
	resp, err := ctrl.ListHealthCheckScripts(ctx, &pb.ListHealthCheckScriptsRequest{})

	if err != nil {
		log.Errorf(ctx, "Failed to list health check scripts: %v", err)
		mw.Abort(ctx, http.StatusInternalServerError, err.Error())
		return
	}
	if resp.Error != nil {
		log.Errorf(ctx, "Failed to list health check scripts: %v", resp.Error)
		mw.Abort(ctx, http.StatusInternalServerError, resp.Error.Message)
		return
	}

	mw.ReplyWithResult(ctx, http.StatusOK, "", resp.Scripts)
}
