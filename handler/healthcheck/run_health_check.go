package healthcheck

import (
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	tgSync "github.com/tigergraph/gus/lib/sync"
)

type RunHealthCheckParam struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
type HealthCheckItem struct {
	ModuleName string                `json:"module_name" binding:"required"`
	Params     []RunHealthCheckParam `json:"params" binding:"required"`
}
type RunHealthCheckRequest struct {
	Items []HealthCheckItem `json:"items" binding:"required"`
}

type SSEMessage struct {
	Type    string `json:"type"`
	Payload any    `json:"payload"`
}

type HealthCheckEvent struct {
	Type string `json:"type"` // "status" | "recover"
	Data any    `json:"data"`
}

type HealthCheckStatus struct {
	ScriptName string                `json:"script_name"`
	Status     string                `json:"status"` // 'running', 'success', 'failed'
	Report     *pb.HealthCheckReport `json:"report,omitempty"`
	Message    string                `json:"message,omitempty"`
}

const (
	ReportWaitTimeout  = 1 * time.Hour
	ReportWaitInterval = 5 * time.Second
)

func RunHealthCheckScript(ctx *gin.Context, ctrl pb.ControllerClient, ifm pb.InformantClient) {
	var req RunHealthCheckRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		log.Errorf(ctx, "Invalid payload: %v", err)
		ctx.SSEvent("error", "Invalid payload.")
		return
	}

	locker, err := lockForHealthCheck()
	if err != nil {
		log.Errorf(ctx, "Failed to lock for health check: %v", err)
		ctx.SSEvent("error", "Failed to lock for health check.")
		return
	}
	defer locker.Unlock(dao.RunningHealthCheckPrefix)

	var mutex = &sync.Mutex{}

	runningHealthChecks := make([]model.RunningHealthCheck, len(req.Items))
	expiration := time.Now().Add(ReportWaitTimeout)
	for i, item := range req.Items {
		runningHealthChecks[i] = model.RunningHealthCheck{Expiration: expiration, ScriptName: item.ModuleName}
	}
	daoManager := dao.GetManager(ctx)
	daoManager.SaveRunningHealthChecks(runningHealthChecks)

	var wg sync.WaitGroup
	messages := make(chan SSEMessage)

	runHealthCheck := func(item HealthCheckItem, index int, message chan<- SSEMessage) {
		defer wg.Done()

		params := make([]*pb.HealthCheckModuleParam, 0)
		for _, p := range item.Params {
			params = append(params, &pb.HealthCheckModuleParam{
				Name:  p.Name,
				Value: p.Value,
			})
		}
		spanId := util.GetSimpleSpanId(util.SUPPORT_HEALTHCHECK_KEY)
		_, err := ctrl.RunHealthCheck(ctx, &pb.RunHealthCheckRequest{
			ModuleName: item.ModuleName,
			Params:     params,
			SpanId:     spanId,
		})

		if err != nil {
			log.Errorf(ctx, "Failed to run health check script: %v", err)
			message <- SSEMessage{Type: "message", Payload: HealthCheckEvent{Type: "status", Data: HealthCheckStatus{ScriptName: item.ModuleName, Status: "Failed", Message: err.Error()}}}
			return
		}

		mutex.Lock()
		// only successfully started health check will be saved and recovered
		runningHealthChecks[index].SpanID = spanId
		daoManager.SaveRunningHealthChecks(runningHealthChecks)
		mutex.Unlock()

		message <- SSEMessage{Type: "message", Payload: HealthCheckEvent{Type: "status", Data: HealthCheckStatus{ScriptName: item.ModuleName, Status: "Running"}}}

		// start polling from ifm
		maxRetry := int(ReportWaitTimeout.Seconds()) / int(ReportWaitInterval.Seconds())
		var resp *pb.GetHealthCheckReportResponse
		for i := 0; i < maxRetry; i++ {
			resp, err = ifm.GetHealthCheckReport(ctx, &pb.GetHealthCheckReportRequest{ReportId: spanId})
			if err != nil {
				log.Errorf(ctx, "Failed to get health check report: %v", err)
			} else if resp.Error != nil || resp.Report != nil {
				break
			}
			time.Sleep(ReportWaitInterval)
		}
		if resp == nil {
			log.Errorf(ctx, "Failed to get health check report: timeout")
			message <- SSEMessage{Type: "message", Payload: HealthCheckEvent{Type: "status", Data: HealthCheckStatus{ScriptName: item.ModuleName, Status: "Failed", Message: "timeout"}}}
		} else if resp.Error != nil {
			log.Errorf(ctx, "Failed to get health check report: %v", resp.Error)
		} else {
			message <- SSEMessage{Type: "message", Payload: HealthCheckEvent{Type: "status", Data: HealthCheckStatus{ScriptName: item.ModuleName, Status: "Completed", Report: resp.Report}}}
		}
	}

	for i := range req.Items {
		wg.Add(1)
		go runHealthCheck(req.Items[i], i, messages)
	}

	go func() {
		wg.Wait()
		close(messages)
	}()

	for message := range messages {
		ctx.SSEvent(message.Type, message.Payload)
		ctx.Writer.Flush()
	}

	daoManager.DeleteRunningHealthChecks()
}

func lockForHealthCheck() (*tgSync.KeyLocker, error) {
	locker, err := tgSync.NewKeyLocker()
	if err != nil {
		return nil, err
	}
	return locker, locker.TryLock(dao.RunningHealthCheckPrefix, time.Minute)
}
