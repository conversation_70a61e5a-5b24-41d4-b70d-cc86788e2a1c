package healthcheck

import (
	"errors"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/service/db"
)

func RecoverRunningHealthCheck(ctx *gin.Context, ctrl pb.ControllerClient, ifm pb.InformantClient) {
	daoManager := dao.GetManager(ctx)
	recoveredHCs, err := daoManager.GetRunningHealthChecks()
	if err != nil && !errors.Is(err, db.ErrNotFound) {
		ctx.SSEvent("error", "Failed to get running health checks.")
		log.Errorf(ctx, "Failed to get running health checks: %v", err)
		return
	}

	runningHCs := make([]model.RunningHealthCheck, 0)
	runningHCNames := make([]string, 0)
	for _, hc := range recoveredHCs {
		if hc.SpanID != "" {
			runningHCs = append(runningHCs, hc)
			runningHCNames = append(runningHCNames, hc.ScriptName)
		}
	}

	if errors.Is(err, db.ErrNotFound) || len(runningHCs) == 0 || runningHCs[0].Expiration.Before(time.Now()) {
		daoManager.DeleteRunningHealthChecks()
		ctx.SSEvent("message", HealthCheckEvent{Type: "recover-list", Data: []string{}})
		return
	}

	ctx.SSEvent("message", HealthCheckEvent{Type: "recover-list", Data: runningHCNames})
	ctx.Writer.Flush()

	var wg sync.WaitGroup
	messages := make(chan SSEMessage)

	pollingFromIFM := func(hc model.RunningHealthCheck, messages chan<- SSEMessage) {
		defer wg.Done()

		maxRetry := int(ReportWaitTimeout.Seconds()) / int(ReportWaitInterval.Seconds())
		var resp *pb.GetHealthCheckReportResponse
		for i := 0; i < maxRetry; i++ {
			resp, err = ifm.GetHealthCheckReport(ctx, &pb.GetHealthCheckReportRequest{ReportId: hc.SpanID})
			if err != nil {
				log.Errorf(ctx, "Failed to get health check report: %v", err)
			} else if resp.Error != nil || resp.Report != nil {
				break
			}
			time.Sleep(ReportWaitInterval)
		}

		if resp == nil {
			log.Errorf(ctx, "Failed to get health check report: timeout")
			messages <- SSEMessage{Type: "message", Payload: HealthCheckEvent{Type: "status", Data: HealthCheckStatus{ScriptName: hc.ScriptName, Status: "Failed", Message: "timeout"}}}
		} else if resp.Error != nil {
			log.Errorf(ctx, "Failed to get health check report: %v", resp.Error)
		} else {
			messages <- SSEMessage{Type: "message", Payload: HealthCheckEvent{Type: "status", Data: HealthCheckStatus{ScriptName: hc.ScriptName, Status: "Completed", Report: resp.Report}}}
		}
	}

	for i := range runningHCs {
		wg.Add(1)
		go pollingFromIFM(runningHCs[i], messages)
	}

	go func() {
		wg.Wait()
		close(messages)
	}()

	for message := range messages {
		ctx.SSEvent(message.Type, message.Payload)
		ctx.Writer.Flush()
	}

	daoManager.DeleteRunningHealthChecks()
}
