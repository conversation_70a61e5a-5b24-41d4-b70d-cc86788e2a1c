package data

import (
	"fmt"
	"net/http"
	"path/filepath"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

// GetChunk checks if an uploaded chunk exists for a file.
func GetChunk(checkAndCreateCategoryDirFunc interfaces.CheckAndCreateCategoryDirFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		cat := c.Param("category")
		if !checkCategory(c, cat, checkAndCreateCategoryDirFunc) {
			return
		}

		var info FileChunkInfo
		if err := c.ShouldBindQuery(&info); err != nil {
			log.Warnf(c, "Invalid query: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Invalid query.")
			return
		}

		filename := filepath.Base(info.Filename)
		chunkDirPath := getChunkDirPath(mw.GetConfig(c), info.Identifier)
		chunkPath := getChunkPath(chunkDirPath, filename, info.ChunkNumber)
		exist, err := fs.Exist(chunkPath)
		if err != nil {
			log.Errorf(c, "Failed to check path %s: %v", chunkPath, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf(
					"Failed to check for chunk %d existence for file '%s' in category '%s'.",
					info.ChunkNumber,
					filename,
					cat,
				),
			)
			return
		}
		if !exist {
			mw.Abort(
				c,
				http.StatusNotFound,
				fmt.Sprintf(
					"Chunk %d for file '%s' in category '%s' cannot be found.",
					info.ChunkNumber,
					filename,
					cat,
				),
			)
			return
		}

		mw.Reply(
			c,
			http.StatusOK,
			fmt.Sprintf(
				"Chunk %d exists for file '%s' in category '%s'.",
				info.ChunkNumber,
				filename,
				cat,
			),
		)
	}
}
