package data

import (
	"fmt"
	"mime/multipart"
	"net/http"
	"net/url"
	"path"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

type validateFunc func(*multipart.FileHeader) error

// Upload uploads a file to a path in a category.
func Upload(checkAndCreateCategoryDirFunc interfaces.CheckAndCreateCategoryDirFunc) gin.HandlerFunc {

	userIconValidatorFunc := func(header *multipart.FileHeader) error {
		file, err := header.Open()
		if err != nil {
			return err
		}
		defer file.Close()

		buff := make([]byte, 512) // docs tell that it take only first 512 bytes into consideration
		if _, err := file.Read(buff); err != nil {
			return err
		}
		contentType := http.DetectContentType(buff)
		re := regexp.MustCompile("image/.*")
		if !re.Match([]byte(contentType)) {
			return fmt.Errorf("worng content-type:%s", contentType)
		}
		if !strings.HasSuffix(header.Filename, ".png") {
			return fmt.Errorf("invalid file extension: %s", header.Filename)
		}
		return nil
	}

	emptyValidatorFunc := func(header *multipart.FileHeader) error {
		return nil
	}

	getValidatorFunc := func(cat string) validateFunc {
		switch cat {
		case GetCategory().UserIcon:
			return userIconValidatorFunc
		default:
			return emptyValidatorFunc
		}
	}

	return func(c *gin.Context) {
		cat := c.Param("category")
		if !checkCategory(c, cat, checkAndCreateCategoryDirFunc) {
			return
		}

		header, err := c.FormFile("file")
		if err != nil {
			log.Warnf(c, "Invalid payload: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
			return
		}

		filePath := c.Query("path")
		// APPS-2082 for loading data, need to check the user folder
		if cat == GetCategory().LoadingData {
			filePath = path.Join(filePath, url.PathEscape(mw.GetRealUsername(c)))
		}
		filename := filepath.Base(header.Filename)
		if !checkPath(c, cat, filePath, filename) {
			return
		}

		p, err := getPathUnderDir(getCategoryPath(mw.GetConfig(c), cat), filePath)
		if err != nil {
			log.Error(c, err)
			mw.Abort(
				c,
				http.StatusBadRequest,
				err.Error(),
			)
			return
		}

		if err = getValidatorFunc(cat)(header); err != nil {
			log.Errorf(c, "File '%s' in category %s validate err:%v.", header.Filename, cat, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf(
					"File %s in category %s is invalid.",
					header.Filename,
					cat,
				),
			)
			return
		}

		cfg := mw.GetConfig(c)

		p = path.Join(p, filename)
		if err := fs.SaveUploadedFile(header, p); err != nil {
			log.Errorf(c, "Failed to save uploaded file to %s: %v", p, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf(
					"Failed to upload file '%s' to path '/%s' in category '%s'.",
					filename,
					filePath,
					cat,
				),
			)
			return
		}
		CopyFileToAllServers(c, cfg, p, fs.FileTypeFile)

		mw.Reply(
			c,
			http.StatusCreated,
			fmt.Sprintf(
				"Successfully uploaded file '%s' to path '/%s' in category '%s'.",
				filename,
				filePath,
				cat,
			),
		)
	}
}
