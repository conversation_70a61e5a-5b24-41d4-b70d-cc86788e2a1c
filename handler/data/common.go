package data

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

const UploadedChunksTempDir = "uploaded_chunks"

type CategoryList struct {
	UserIcon    string
	LoadingData string
	UpgradeFile string
	Solution    string
}

// struct literal is not const, use a Getter to prevent potential mutation
var category = CategoryList{
	UserIcon:    "user_icons",
	LoadingData: "loading_data",
	UpgradeFile: "upgrade_file",
	Solution:    "solution",
}

func GetCategory() CategoryList {
	return category
}

type FileChunkBasicInfo struct {
	Filename     string `form:"resumableFilename" binding:"required"`
	Identifier   string `form:"resumableIdentifier" binding:"required"`
	Type         string `form:"resumableType"`
	RelativePath string `form:"resumableRelativePath"`
}

type FileChunkInfo struct {
	FileChunkBasicInfo

	ChunkNumber      int `form:"resumableChunkNumber" binding:"required"`
	ChunkSize        int `form:"resumableChunkSize" binding:"required"`
	CurrentChunkSize int `form:"resumableCurrentChunkSize" binding:"required"`
	TotalChunks      int `form:"resumableTotalChunks" binding:"required"`
	TotalSize        int `form:"resumableTotalSize" binding:"required"`
}

// checkCategory checks if the category exists.
// If the category exists, it attempts to create the directory for this category
// in case the directory doesn't exist.
func checkCategory(c *gin.Context, cat string, checkAndCreateCategoryDir interfaces.CheckAndCreateCategoryDirFunc) bool {
	cfg := mw.GetConfig(c)
	ok, err := checkAndCreateCategoryDir(cfg, cat, mw.GetRealUsername(c))
	if ok {
		return ok
	}
	// though substring matching is a poor way to check errors,
	// it's ok here because we know the implementation of
	// CheckAndCreateCategoryDir
	// should not do this if it's a method call of an interface
	if strings.Contains(err.Error(), "Failed to create category") {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
	} else {
		mw.Abort(c, http.StatusBadRequest, err.Error())
	}
	return false
}

func CheckAndCreateCategoryDir(config *config.Config, cat string, userName string) (bool, error) {
	switch cat {
	case
		GetCategory().UserIcon,
		GetCategory().UpgradeFile:
		if err := os.MkdirAll(getCategoryPath(config, cat), 0700); err != nil {
			log.Errorf("Failed to create category %s: %v", cat, err)
			return false, errors.Errorf("Failed to create category '%s'.", cat)
		}
		return true, nil
	case
		// APPS-2082: For loading data category, we need to ensure that it creates the loading data folder,
		// as well as the folder under the user's name.
		GetCategory().LoadingData,
		GetCategory().Solution:
		if err := os.MkdirAll(path.Join(getCategoryPath(config, cat), url.PathEscape(userName)), 0700); err != nil {
			log.Errorf("Failed to create category %s: %v", cat, err)
			return false, errors.Errorf("Failed to create category '%s'.", cat)
		}
		return true, nil
	default:
		return false, errors.Errorf("Category '%s' does not exist.", cat)
	}
}

// checkPath first checks if the file path exists.
// Then it checks if the file exists if the request method is POST.
func checkPath(c *gin.Context, cat, filePath, filename string) bool {
	p := path.Join(getCategoryPath(mw.GetConfig(c), cat), filePath)

	exist, err := fs.Exist(p)
	if err != nil {
		log.Errorf(c, "Failed to check path %s: %v", p, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to check for path '/%s' existence in category '%s'.", filePath, cat),
		)
		return false
	}
	if !exist {
		mw.Abort(
			c,
			http.StatusNotFound,
			fmt.Sprintf("Path '/%s' cannot be found in category '%s'.", filePath, cat),
		)
		return false
	}

	if c.Request.Method == http.MethodPost {
		p = path.Join(p, filename)
		exist, err := fs.Exist(p)
		if err != nil {
			log.Errorf(c, "Failed to check path %s: %v", p, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf(
					"Failed to check for file '%s' existence under path '/%s' in category '%s'.",
					filename,
					filePath,
					cat,
				),
			)
			return false
		}
		if exist {
			mw.Abort(
				c,
				http.StatusConflict,
				fmt.Sprintf(
					"File '%s' already exists under path '/%s' in category '%s'.",
					filename,
					filePath,
					cat,
				),
			)
			return false
		}
	}

	return true
}

func getCategoryPath(cfg *config.Config, cat string) string {
	return path.Join(cfg.GetDataDirPath(), cat)
}

func getChunkDirPath(cfg *config.Config, id string) string {
	return path.Join(cfg.GetTempDirPath(), UploadedChunksTempDir, id)
}

func getChunkPath(chunkDir, filename string, chunkNumber int) string {
	chunkName := fmt.Sprintf("%s_part_%010d", filename, chunkNumber)
	return path.Join(chunkDir, chunkName)
}

func CopyFileToAllServers(ctx context.Context, cfg *config.Config, filePath string, fileType fs.FileType) {
	dstHostIDs, err := cfg.GetAllGUIHostIDs()
	if err != nil {
		log.Errorf(ctx, "Failed to get all GUI host IDs: %v", err)
		return
	}
	if len(dstHostIDs) == 1 {
		return
	}

	srcHostID, err := cfg.GetHostID()
	if err != nil {
		log.Errorf(ctx, "Failed to get local host ID: %v", err)
		return
	}

	tgFS := fs.NewTGFilesystem(cfg)
	if err := tgFS.CopyFile(filePath, srcHostID, filePath, dstHostIDs, fileType); err != nil {
		log.Errorf(ctx, "Failed to copy file %s to all GUI servers: %v", filePath, err)
	}
}

func RemoveDirFromAllServers(ctx context.Context, cfg *config.Config, filePath string) error {
	dstHostIDs, err := cfg.GetAllGUIHostIDs()
	if err != nil {
		log.Errorf(ctx, "Failed to get all GUI host IDs: %v", err)
		return err
	}

	tgFS := fs.NewTGFilesystem(cfg)
	if err := tgFS.Remove(filePath, dstHostIDs); err != nil {
		log.Errorf(ctx, "Failed to remove dir %s from all GUI servers: %v", filePath, err)
		return err
	}
	return nil
}

func getPathUnderDir(dir string, relativePath string) (string, error) {
	relativePath2, err := filepath.Abs(path.Join(dir, relativePath))
	if err != nil {
		return "", errors.WithStack(err)
	}
	dirPath, err := filepath.Abs(dir)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if !strings.HasPrefix(relativePath2, dirPath) {
		return "", errors.Errorf("'%v' is not under directory '%v'", relativePath, dir)
	}
	return relativePath2, nil
}

// To categorize a FileInfo array into separate arrays of files and folders.
func getFilesAndDirectories(files []fs.FileInfo) ([]fs.FileInfo, []fs.FileInfo) {
	outputFiles := make([]fs.FileInfo, 0)
	outputDirectories := make([]fs.FileInfo, 0)

	for _, file := range files {
		if !file.IsDir {
			outputFiles = append(outputFiles, file)
		} else {
			outputDirectories = append(outputDirectories, file)
		}
	}

	return outputFiles, outputDirectories
}
