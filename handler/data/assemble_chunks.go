package data

import (
	"fmt"
	"net/http"
	"net/url"
	"path"
	"path/filepath"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

// AssembleChunks assembles uploaded chunks for a file.
func AssembleChunks(checkAndCreateCategoryDirFunc interfaces.CheckAndCreateCategoryDirFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		cat := c.Param("category")
		if !checkCategory(c, cat, checkAndCreateCategoryDirFunc) {
			return
		}

		var info FileChunkBasicInfo
		if err := c.ShouldBindQuery(&info); err != nil {
			log.Warnf(c, "Invalid query: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Invalid query.")
			return
		}

		filePath := c.Query("path")
		// APPS-2082 for loading data, need to check the user folder
		if cat == GetCategory().LoadingData || cat == GetCategory().Solution {
			filePath = path.Join(filePath, url.PathEscape(mw.GetRealUsername(c)))
		}
		filename := filepath.Base(info.Filename)
		if !checkPath(c, cat, filePath, filename) {
			return
		}

		cfg := mw.GetConfig(c)
		chunkDirPath := getChunkDirPath(cfg, info.Identifier)
		chunksInfo, err := getChunksInfo(c, cat, filename, chunkDirPath)
		if err != nil {
			return
		}
		if len(chunksInfo) == 0 {
			mw.Abort(c, http.StatusBadRequest, "No chunks to assemble.")
			return
		}

		if err := assembleChunks(c, cat, filePath, filename, chunksInfo); err != nil {
			return
		}
		RemoveDirFromAllServers(c, cfg, chunkDirPath)

		mw.Reply(
			c,
			http.StatusCreated,
			fmt.Sprintf(
				"Successfully assembled file '%s' to path '/%s' in category '%s'.",
				filename,
				filePath,
				cat,
			),
		)
	}
}

func getChunksInfo(c *gin.Context, cat, filename, chunkDirPath string) ([]fs.FileInfo, error) {
	cfg := mw.GetConfig(c)
	hostID, err := cfg.GetHostID()
	if err != nil {
		log.Errorf(c, "Failed to get local host ID: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get local host ID.")
		return nil, err
	}

	tgFS := fs.NewTGFilesystem(cfg)
	chunksInfo, err := tgFS.List(chunkDirPath, hostID)
	if err != nil {
		log.Errorf(c, "Failed to list chunks under path %s: %v", chunkDirPath, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to list chunks for file '%s' in category '%s'.", filename, cat),
		)
		return nil, err
	}

	return chunksInfo, nil
}

func assembleChunks(c *gin.Context, cat, filePath, filename string, chunksInfo []fs.FileInfo) error {
	cfg := mw.GetConfig(c)
	p := path.Join(getCategoryPath(cfg, cat), filePath, filename)

	if err := fs.CombineFiles(getChunkPaths(chunksInfo, filename), p); err != nil {
		log.Errorf(c, "Failed to combine file %s: %v", filename, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to assemble file '%s' to path '/%s' in category '%s'.",
				filename,
				filePath,
				cat,
			),
		)
		return err
	}
	CopyFileToAllServers(c, cfg, p, fs.FileTypeFile)

	return nil
}

func getChunkPaths(chunksInfo []fs.FileInfo, filename string) []string {
	var chunkPaths []string
	for _, fileInfo := range chunksInfo {
		if strings.HasPrefix(fileInfo.Name, filename) {
			chunkPaths = append(chunkPaths, fileInfo.Path)
		}
	}
	sort.Strings(chunkPaths)
	return chunkPaths
}
