package data

import (
	"fmt"
	"net/http"
	"net/url"
	"path"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

// List retrieves first level files and directories under a path in a category.
func List(checkAndCreateCategoryDirFunc interfaces.CheckAndCreateCategoryDirFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		cat := c.Param("category")
		if !checkCategory(c, cat, checkAndCreateCategoryDirFunc) {
			return
		}

		cfg := mw.GetConfig(c)
		hostID, err := cfg.GetHostID()
		if err != nil {
			log.Errorf(c, "Failed to get local host ID: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to get local host ID.")
			return
		}

		tgFS := fs.NewTGFilesystem(cfg)
		filePath := c.Query("path")
		p, err := getPathUnderDir(getCategoryPath(mw.GetConfig(c), cat), filePath)
		if err != nil {
			log.Error(c, err)
			mw.Abort(
				c,
				http.StatusBadRequest,
				err.Error(),
			)
			return
		}

		filesInfo, err := tgFS.List(p, hostID)
		if err != nil {
			log.Errorf(c, "Failed to list files and directories under path %s: %v", p, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf(
					"Failed to list files and directories under path '/%s' in category '%s'.",
					filePath,
					cat,
				),
			)
			return
		}

		// APPS-2082: Only perform this check if we are listing loading_data files.
		if cat == GetCategory().LoadingData {
			// These files are uploaded before APPS-2082. This is done to maintain compatibility with older versions.
			files, directories := getFilesAndDirectories(filesInfo)

			// Append all subfolder files if the user is a superuser.
			if mw.IsSuperUser(c) {
				for _, directory := range directories {
					if directory.IsDir {
						subFiles, err := tgFS.List(directory.Path, hostID)
						if err != nil {
							log.Errorf(c, "Failed to list files and directories under path %s: %v", directory.Path, err)
							mw.Abort(
								c,
								http.StatusInternalServerError,
								fmt.Sprintf(
									"Failed to list files and directories under path '/%s' in category '%s'.",
									directory.Path,
									cat,
								),
							)
							return
						}

						// Update subfolder information in the FileInfo to facilitate easier retrieval by the frontend.
						for n := range subFiles {
							subFiles[n].Name = path.Join(directory.Name, subFiles[n].Name)
						}
						files = append(files, subFiles...)
					}
				}
			} else {
				// Only list files belonging to the current user.
				userFilePath := path.Join(p, url.PathEscape(mw.GetRealUsername(c)))
				if _, err := fs.Exist(userFilePath); err == nil {
					subFiles, err := tgFS.List(userFilePath, hostID)
					if err != nil {
						log.Errorf(c, "Failed to list files and directories under path %s: %v", userFilePath, err)
						mw.Abort(
							c,
							http.StatusInternalServerError,
							fmt.Sprintf(
								"Failed to list files and directories under path '/%s' in category '%s'.",
								userFilePath,
								cat,
							),
						)
						return
					}

					// Update subfolder information in the FileInfo to facilitate easier retrieval by the frontend.
					for n := range subFiles {
						subFiles[n].Name = path.Join(url.PathEscape(mw.GetRealUsername(c)), subFiles[n].Name)
					}
					files = append(files, subFiles...)
				}
			}
			mw.ReplyWithResult(c, http.StatusOK, "", files)
		} else {
			mw.ReplyWithResult(c, http.StatusOK, "", filesInfo)
		}
	}
}
