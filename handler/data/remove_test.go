package data

import (
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestRelativePath(t *testing.T) {
	t.Run("negative", func(t *testing.T) {
		_, err := getPathUnderDir("", "../")
		require.EqualError(t, err, "'../' is not under directory ''")
	})
	t.Run("negative", func(t *testing.T) {
		_, err := getPathUnderDir("", "..")
		require.EqualError(t, err, "'..' is not under directory ''")
	})
	t.Run("positive", func(t *testing.T) {
		path, err := getPathUnderDir("", "~")
		abs, _ := filepath.Abs("./~")
		require.Equal(t, abs, path)
		require.NoError(t, err)
	})
	t.Run("positive", func(t *testing.T) {
		path, err := getPathUnderDir("", "./")
		abs, _ := filepath.Abs("./")
		require.Equal(t, abs, path)
		require.NoError(t, err)
	})
}
