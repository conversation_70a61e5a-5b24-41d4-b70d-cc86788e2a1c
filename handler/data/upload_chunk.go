package data

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

// UploadChunk uploads a chunk for a file.
func UploadChunk(checkAndCreateCategoryDirFunc interfaces.CheckAndCreateCategoryDirFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		cat := c.Param("category")
		if !checkCategory(c, cat, checkAndCreateCategoryDirFunc) {
			return
		}

		var info FileChunkInfo
		if err := c.ShouldBind(&info); err != nil {
			log.Warnf(c, "Invalid payload: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
			return
		}

		header, err := c.FormFile("file")
		if err != nil {
			log.Warnf(c, "Invalid payload: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
			return
		}

		filename := filepath.Base(info.Filename)
		chunkDirPath := getChunkDirPath(mw.GetConfig(c), info.Identifier)
		if err := os.MkdirAll(chunkDirPath, 0700); err != nil {
			log.Errorf(c, "Failed to create directory %s: %v", chunkDirPath, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to create directory to store chunks for file '%s' in category '%s'.", filename, cat),
			)
			return
		}

		chunkPath := getChunkPath(chunkDirPath, filename, info.ChunkNumber)
		if err := fs.SaveUploadedFile(header, chunkPath); err != nil {
			log.Errorf(c, "Failed to save uploaded file to %s: %v", chunkPath, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf(
					"Failed to upload chunk %d for file '%s' in category '%s'.",
					info.ChunkNumber,
					filename,
					cat,
				),
			)
			return
		}
		CopyFileToAllServers(c, mw.GetConfig(c), chunkPath, fs.FileTypeFile)

		mw.Reply(
			c,
			http.StatusCreated,
			fmt.Sprintf(
				"Successfully uploaded chunk %d for file '%s' in category '%s'.",
				info.ChunkNumber,
				filename,
				cat,
			),
		)
	}
}
