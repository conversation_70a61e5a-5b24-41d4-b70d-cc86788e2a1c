package data

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

// Remove removes all files and directories under a path in a category.
func Remove(checkAndCreateCategoryDirFunc interfaces.CheckAndCreateCategoryDirFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		cat := c.Param("category")
		if !checkCategory(c, cat, checkAndCreateCategoryDirFunc) {
			return
		}

		filePath := c.Query("path")
		{
			cfg := mw.GetConfig(c)
			p, err := getPathUnderDir(getCategoryPath(cfg, cat), filePath)
			if err != nil {
				log.Error(c, err)
				mw.Abort(
					c,
					http.StatusBadRequest,
					err.Error(),
				)
				return
			}
			err = RemoveDirFromAllServers(c, cfg, p)
			if err != nil {
				log.Errorf(c, "Failed to remove path %s: %v", p, err)
				mw.Abort(
					c,
					http.StatusInternalServerError,
					fmt.Sprintf("Failed to remove path '/%s' in category '%s'.", filePath, cat),
				)
				return
			}
		}
		mw.Reply(
			c,
			http.StatusOK,
			fmt.Sprintf("Successfully removed path '/%s' in category '%s'.", filePath, cat),
		)
	}
}
