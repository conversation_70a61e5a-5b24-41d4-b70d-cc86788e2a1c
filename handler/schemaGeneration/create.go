package schemaGeneration

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	. "github.com/tigergraph/graphql/gsql"
	mw "github.com/tigergraph/gus/middleware"
)

type SampleData struct {
	FileName string     `json:"fileName"`
	Header   []string   `json:"header"`
	Data     [][]string `json:"data"`
}

type SchemaGeneration struct {
	SampleData    []SampleData
	ReservedWords []string
}

const (
	DefaultType = "STRING"
)

var (
	PotentialSuffixes = []string{"id", "number", "num", "no"}
)

func trimSuffix(str, suffix string) string {
	if strings.HasSuffix(strings.ToLower(str), strings.ToLower(suffix)) {
		return str[:len(str)-len(suffix)]
	}
	return str
}

func findPrimaryIDsFromCSVHeader(header []string) map[int]string {
	primaryIDColumns := make(map[int]string)

	for i, columnName := range header {
		for _, suffix := range PotentialSuffixes {
			lowerName := strings.ToLower(columnName)
			trimmedName := trimSuffix(lowerName, suffix)
			if trimmedName != lowerName {
				trimmedName = trimSuffix(trimmedName, "_")
				primaryIDColumns[i] = trimmedName
				break
			}
		}
	}

	return primaryIDColumns
}

// countDistinctValues counts the number of distinct values for each column of sample data.
func countDistinctValues(sampleData SampleData, primaryIDColumns map[int]string) map[int]int {
	distinctCounts := make(map[int]map[string]bool)

	// Initialize distinctCounts map
	for index := range sampleData.Header {
		distinctCounts[index] = make(map[string]bool)
	}

	// Count distinct values for each column
	for _, row := range sampleData.Data {
		for columnIndex, value := range row {
			if _, ok := primaryIDColumns[columnIndex]; ok || len(primaryIDColumns) == 0 {
				distinctCounts[columnIndex][value] = true
			}
		}
	}

	// Count the number of distinct values for each column
	counts := make(map[int]int)
	for columnIndex, distinctValues := range distinctCounts {
		counts[columnIndex] = len(distinctValues)
	}

	return counts
}

// maxDistinctColumn returns the column with the maximum number of distinct values.
func maxDistinctColumnIndex(distinctCounts map[int]int) (int, int) {
	maxColumnIndex := 0
	maxCount := 0

	// Find the column with the maximum distinct count
	for columnIndex, count := range distinctCounts {
		if count > maxCount || count == maxCount && columnIndex < maxColumnIndex {
			maxColumnIndex = columnIndex
			maxCount = count
		}
	}

	return maxColumnIndex, maxCount
}

func buildName(name string, index int, reservedWords map[string]bool, isTypeName bool) string {
	lowerName := strings.ToLower(name)
	// Make type name first letter upper case
	if reservedWords[lowerName] {
		lowerName += "_"
	}
	if isTypeName {
		lowerName = strings.ToUpper(lowerName[:1]) + lowerName[1:]
	}

	pattern := `[^a-zA-Z_]`
	re := regexp.MustCompile(pattern)
	lowerName = re.ReplaceAllString(lowerName, "")
	if len(lowerName) == 0 {
		if isTypeName {
			return "TypeName"
		} else {
			return fmt.Sprintf("column_%d", index+1)
		}
	}

	return lowerName
}

func buildVertexType(
	typeName string,
	primaryIDName string,
	index int,
	reservedWords map[string]bool,
	attributeType string,
	typeNameCount map[string]int,
) VertexType {
	newTypeName := buildName(typeName, index, reservedWords, true)
	newPrimaryIDName := buildName(primaryIDName, index, reservedWords, false)
	typeNameCount[newPrimaryIDName] += 1
	typeNameCount[newTypeName] += 1
	if typeNameCount[newTypeName] > 1 {
		newTypeName = fmt.Sprintf("%s%d", newTypeName, typeNameCount[newTypeName])
	}
	if attributeType == "DOUBLE" {
		attributeType = DefaultType
	}
	vertex := VertexType{
		Name: newTypeName,
		PrimaryID: Attribute{
			AttributeName: newPrimaryIDName,
			AttributeType: AttributeType{
				Name:          attributeType,
				ValueTypeName: DefaultType,
			},
			IsPrimaryKey:         false,
			PrimaryIDAsAttribute: true,
		},
		Attributes: []Attribute{},
		IsLocal:    true,
		Config: struct {
			Taggable             bool   `json:"TAGGABLE"`
			Stats                string `json:"STATS"`
			PrimaryIDAsAttribute bool   `json:"PRIMARY_ID_AS_ATTRIBUTE"`
		}{
			Taggable:             false,
			Stats:                "OUTDEGREE_BY_EDGETYPE",
			PrimaryIDAsAttribute: true,
		},
	}
	return vertex
}

func buildAttribute(
	attributeName string,
	index int,
	reservedWords map[string]bool,
	attributeType string,
	typeNameCount map[string]int,
) Attribute {
	newAttributeName := buildName(attributeName, index, reservedWords, false)
	typeNameCount[newAttributeName] += 1
	if typeNameCount[newAttributeName] > 1 {
		newAttributeName = fmt.Sprintf("%s%d", newAttributeName, typeNameCount[newAttributeName])
	}
	attr := Attribute{
		AttributeName: newAttributeName,
		AttributeType: AttributeType{
			Name:          attributeType,
			ValueTypeName: DefaultType,
		},
		IsPrimaryKey: false,
	}
	return attr
}

func detectDataType(data string) string {
	// Try parsing as integer
	if match, _ := regexp.MatchString(`^-?\d+$`, data); match {
		return "INT"
	}

	// Try parsing as double
	if match, _ := regexp.MatchString(`^-?\d+(\.\d+)?$`, data); match {
		return "DOUBLE"
	}

	// Try parsing as boolean
	if strings.ToLower(data) == "true" || strings.ToLower(data) == "false" {
		return "BOOL"
	}

	// Try parsing as datetime
	if match, _ := regexp.MatchString(`^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$`, data); match {
		return "DATETIME"
	}

	// Otherwise, consider it a string
	return DefaultType
}

func detectColumnDataTypes(sampleData SampleData) map[string]string {
	header := sampleData.Header
	data := sampleData.Data
	numColumns := len(data[0])
	columnTypes := make(map[string]string)

	// Check if all rows in a column have the same data type
	for i := 0; i < numColumns; i++ {
		dataTypeSet := make(map[string]bool)
		for _, row := range data {
			dataType := detectDataType(row[i])
			dataTypeSet[dataType] = true
		}

		if len(dataTypeSet) == 1 {
			for k := range dataTypeSet {
				columnTypes[header[i]] = k
			}
		} else {
			columnTypes[header[i]] = DefaultType
		}
	}

	return columnTypes
}

func genVerticesAndEdgesFromSampleData(
	sampleData SampleData,
	primaryIDColumns map[int]string,
	mappings map[string][]int,
	reservedWords map[string]bool,
) ([]VertexType, []EdgeType) {
	var primaryTypeName string
	var primaryIDName string
	var primaryTypeIndex int
	var vertices []VertexType
	var edges []EdgeType
	typeNameCount := make(map[string]int)

	columnDataTypes := detectColumnDataTypes(sampleData)
	distinctCounts := countDistinctValues(sampleData, primaryIDColumns)
	primaryTypeIndex, _ = maxDistinctColumnIndex(distinctCounts)
	primaryTypeName = primaryIDColumns[primaryTypeIndex]
	primaryIDName = sampleData.Header[primaryTypeIndex]

	if len(primaryIDColumns) == 0 || len(primaryTypeName) == 0 {
		strs := strings.Split(sampleData.FileName, "/")
		fileName := strs[len(strs)-1]
		primaryTypeName = strings.Split(fileName, ".")[0]
	}

	primaryVertex := buildVertexType(primaryTypeName, primaryIDName, primaryTypeIndex, reservedWords, columnDataTypes[primaryIDName], typeNameCount)
	mappings[primaryVertex.Name] = append(mappings[primaryVertex.Name], primaryTypeIndex)

	for columnIndex, columnName := range sampleData.Header {
		if columnIndex == primaryTypeIndex {
			continue
		}
		if _, ok := primaryIDColumns[columnIndex]; !ok {
			primaryVertex.Attributes = append(primaryVertex.Attributes, buildAttribute(columnName, columnIndex, reservedWords, columnDataTypes[columnName], typeNameCount))
			mappings[primaryVertex.Name] = append(mappings[primaryVertex.Name], columnIndex)
		} else {
			typeName := primaryIDColumns[columnIndex]
			if len(typeName) == 0 {
				typeName = columnName
			}
			vertex := buildVertexType(typeName, columnName, columnIndex, reservedWords, columnDataTypes[columnName], typeNameCount)
			vertices = append(vertices, vertex)
			mappings[vertex.Name] = append(mappings[vertex.Name], columnIndex)
			edge := buildEdgeType(primaryVertex, vertex)
			edges = append(edges, edge)
			mappings[edge.Name] = append(mappings[edge.Name], primaryTypeIndex)
			mappings[edge.Name] = append(mappings[edge.Name], columnIndex)
		}
	}
	vertices = append([]VertexType{primaryVertex}, vertices...)
	return vertices, edges
}

func buildEdgeType(v1 VertexType, v2 VertexType) EdgeType {
	edgeName := fmt.Sprintf("%s_to_%s", trimSuffix(v1.Name, "_"), trimSuffix(v2.Name, "_"))
	edge := EdgeType{
		Name:               edgeName,
		FromVertexTypeName: v1.Name,
		ToVertexTypeName:   v2.Name,
		IsDirected:         false,
		Attributes:         []Attribute{},
		EdgePairs: []EdgePair{
			{
				From: v1.Name,
				To:   v2.Name,
			},
		},
		IsLocal: true,
	}
	return edge
}

func genEdgesFromVertices(
	vertices []VertexType,
	typeNameCount map[string]int,
	uf *UnionFind,
) []EdgeType {
	var edges []EdgeType
	for i := 1; i < len(vertices); i++ {
		if !uf.Connected(vertices[0].Name, vertices[i].Name) {
			edge := buildEdgeType(vertices[0], vertices[i])
			if typeNameCount[edge.Name] == 0 {
				typeNameCount[edge.Name] += 1
				edges = append(edges, edge)
				uf.Union(vertices[0].Name, vertices[i].Name)
			}
		}
	}
	return edges
}

func removePotentialSuffixes(name string) string {
	for _, suffix := range PotentialSuffixes {
		name = trimSuffix(name, suffix)
	}
	return trimSuffix(name, "_")
}

func matchVertexAttributesWithPrimaryID(v1 VertexType, v2 VertexType) int {
	for i, attr := range v1.Attributes {
		// Check if the attribute type and name match the primary ID of v2
		if attr.AttributeType.Name == v2.PrimaryID.AttributeType.Name &&
			removePotentialSuffixes(attr.AttributeName) == removePotentialSuffixes(v2.PrimaryID.AttributeName) {
			return i
		}
	}
	return -1
}

func genEdgesFromMappings(
	fileMappings map[string]map[string][]int,
	vertices []VertexType,
	typeNameCount map[string]int,
	uf *UnionFind,
) []EdgeType {
	var edges []EdgeType
	for key := range fileMappings {
		for i := 0; i < len(vertices); i++ {
			if _, ok := fileMappings[key][vertices[i].Name]; !ok {
				continue
			}
			for j := 0; j < len(vertices); j++ {
				// Existing edge
				if _, ok := fileMappings[key][vertices[j].Name]; ok {
					continue
				}
				primaryIDIndex := matchVertexAttributesWithPrimaryID(vertices[i], vertices[j])
				if primaryIDIndex == -1 {
					continue
				}
				edge := buildEdgeType(vertices[i], vertices[j])
				if typeNameCount[edge.Name] == 0 {
					typeNameCount[edge.Name] += 1
					edges = append(edges, edge)
					uf.Union(vertices[i].Name, vertices[j].Name)
					fileMappings[key][edge.Name] = append(fileMappings[key][edge.Name], fileMappings[key][vertices[i].Name][0])
					fileMappings[key][edge.Name] = append(fileMappings[key][edge.Name], fileMappings[key][vertices[i].Name][primaryIDIndex+1])
				}
			}

		}
	}
	return edges
}

func mergeVerticesAndEdges(
	vertices []VertexType,
	newVertices []VertexType,
	edges []EdgeType,
	newEdges []EdgeType,
	mappings map[string][]int,
	typeNameCount map[string]int,
	uf *UnionFind,
) ([]VertexType, []EdgeType) {
	for i := range newVertices {
		found := false
		for j := range vertices {
			// Try to remove duplicate vertices
			if vertices[j].Name == newVertices[i].Name {
				found = true
				// If attributes are the same order
				isSameOrder := true
				for k := 0; k < len(vertices[j].Attributes) && k < len(newVertices[i].Attributes); k++ {
					if vertices[j].Attributes[k].AttributeName != newVertices[i].Attributes[k].AttributeName ||
						vertices[j].Attributes[k].AttributeType.Name != newVertices[i].Attributes[k].AttributeType.Name {
						isSameOrder = false
						break
					}
				}
				if isSameOrder {
					if len(vertices[j].Attributes) < len(newVertices[i].Attributes) {
						vertices[j].Attributes = newVertices[i].Attributes
					}
				} else {
					typeNameCount[newVertices[i].Name] += 1
					newVertices[i].Name = fmt.Sprintf("%s%d", newVertices[i].Name, typeNameCount[newVertices[i].Name])
					mappings[newVertices[i].Name] = mappings[vertices[j].Name]
					for l := range newEdges {
						if newEdges[l].FromVertexTypeName == vertices[j].Name {
							newEdges[l].FromVertexTypeName = newVertices[i].Name
							newEdges[l].EdgePairs[0].From = newVertices[i].Name
						}
						if newEdges[l].ToVertexTypeName == vertices[j].Name {
							newEdges[l].ToVertexTypeName = newVertices[i].Name
							newEdges[l].EdgePairs[0].To = newVertices[i].Name
						}
					}
					delete(mappings, vertices[j].Name)
					vertices = append(vertices, newVertices[i])
					uf.AddElement(newVertices[i].Name)
				}
			}
		}
		if !found {
			typeNameCount[newVertices[i].Name] += 1
			vertices = append(vertices, newVertices[i])
			uf.AddElement(newVertices[i].Name)
		}
	}
	// Remove duplicate edges
	for i := range newEdges {
		if typeNameCount[newEdges[i].Name] == 0 {
			typeNameCount[newEdges[i].Name] += 1
			edges = append(edges, newEdges[i])
			uf.Union(newEdges[i].FromVertexTypeName, newEdges[i].ToVertexTypeName)
		}
	}
	return vertices, edges
}

func Create(c *gin.Context) {
	var schemaGeneration SchemaGeneration
	if err := c.ShouldBindJSON(&schemaGeneration); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}
	sampleDataArray := schemaGeneration.SampleData
	reservedWords := make(map[string]bool)
	for _, element := range schemaGeneration.ReservedWords {
		reservedWords[strings.ToLower(element)] = true
	}

	verticesRes := []VertexType{}
	edgesRes := []EdgeType{}
	primaryVertices := []VertexType{}
	mappings := make(map[string]map[string][]int)
	typeNameCount := make(map[string]int)
	uf := NewUnionFind()

	for _, sampleData := range sampleDataArray {
		primaryIDColumns := findPrimaryIDsFromCSVHeader(sampleData.Header)

		// Create
		for i := len(sampleData.Header); i < len(sampleData.Data[0]); i++ {
			sampleData.Header = append(sampleData.Header, fmt.Sprintf("column_%d", i+1))
		}

		mappings[sampleData.FileName] = make(map[string][]int)
		vertices, edges := genVerticesAndEdgesFromSampleData(sampleData, primaryIDColumns, mappings[sampleData.FileName], reservedWords)
		verticesRes, edgesRes = mergeVerticesAndEdges(verticesRes, vertices, edgesRes, edges, mappings[sampleData.FileName], typeNameCount, uf)
		if len(vertices) > 0 {
			primaryVertices = append(primaryVertices, vertices[0])
		}

		if len(primaryIDColumns) > 0 {
			log.Info(c, "Primary ID column indices and names:")
			for index, name := range primaryIDColumns {
				log.Info(c, "Index: ", index, "Name: ", name)
			}
		} else {
			log.Info(c, "No primary ID columns found in the CSV header")
		}
	}
	edgesRes = append(edgesRes, genEdgesFromMappings(mappings, verticesRes, typeNameCount, uf)...)
	edgesRes = append(edgesRes, genEdgesFromVertices(primaryVertices, typeNameCount, uf)...)

	schema := Schema{
		VertexTypes: verticesRes,
		EdgeTypes:   edgesRes,
	}
	log.Info(c, "Generated schema", schema)

	resp := struct {
		Schema   Schema                      `json:"schema"`
		Mappings map[string]map[string][]int `json:"mappings"`
	}{
		Schema:   schema,
		Mappings: mappings,
	}

	mw.ReplyWithResult(c, http.StatusOK, "", resp)
}

// UnionFind to find connected vertices
type UnionFind struct {
	parent map[string]string
	rank   map[string]int
}

func NewUnionFind() *UnionFind {
	return &UnionFind{
		parent: make(map[string]string),
		rank:   make(map[string]int),
	}
}

func (uf *UnionFind) Find(x string) string {
	if uf.parent[x] != x {
		uf.parent[x] = uf.Find(uf.parent[x]) // Path compression
	}
	return uf.parent[x]
}

func (uf *UnionFind) Union(x, y string) {
	rootX := uf.Find(x)
	rootY := uf.Find(y)

	if rootX != rootY {
		if uf.rank[rootX] > uf.rank[rootY] {
			uf.parent[rootY] = rootX
		} else if uf.rank[rootX] < uf.rank[rootY] {
			uf.parent[rootX] = rootY
		} else {
			uf.parent[rootY] = rootX
			uf.rank[rootX]++
		}
	}
}

func (uf *UnionFind) Connected(x, y string) bool {
	return uf.Find(x) == uf.Find(y)
}

func (uf *UnionFind) AddElement(x string) {
	if _, exists := uf.parent[x]; !exists {
		uf.parent[x] = x
		uf.rank[x] = 0
	}
}
