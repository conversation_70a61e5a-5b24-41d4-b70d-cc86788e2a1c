package health

import (
	"context"
	"net/http"
	"os/exec"
	"strings"

	"github.com/tigergraph/gotools/log"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gus/build"
	mw "github.com/tigergraph/gus/middleware"
)

// Ping returns if the server is online.
func Ping(c *gin.Context) {
	mw.Reply(c, http.StatusOK, "pong")
}

// Version info
func Version(c *gin.Context, controllerClient pb.ControllerClient) {
	response, err := controllerClient.GetRepoInfo(c, &pb.GetRepoInfoRequest{})
	if err != nil {
		log.Errorf(c, "Failed to get repo info: %v", err)
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	// Run gadmin version command
	cmdCtx, cancel := context.WithTimeout(c, mw.GetConfig(c).GetHTTPRequestTimeout())
	defer cancel()

	cmd := exec.CommandContext(cmdCtx, "gadmin", "version")
	log.Infof(c, "Executing command: %s", strings.Join(cmd.Args, " "))
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Errorf(c, "Failed to get gadmin version: %v: %s", err, output)
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	// Check if it's community edition from first line
	isCommunity := false
	if len(output) > 0 {
		firstLine := strings.Split(string(output), "\n")[0]
		isCommunity = strings.Contains(firstLine, "Community Edition")
	}

	cfg := mw.GetConfig(c)
	resp := struct {
		BuildTime            string `json:"build_time"`
		GitCommit            string `json:"git_commit"`
		BuildNum             string `json:"build_num"`
		TigerGraphVersion    string `json:"tigergraph_version"`
		IsGraphStudioEnabled bool   `json:"is_graphstudio_enabled"`
		IsAdminPortalEnabled bool   `json:"is_adminportal_enabled"`
		IsInsightsEnabled    bool   `json:"is_insights_enabled"`
		IsGraphQLEnabled     bool   `json:"is_graphql_enabled"`
		IsGSQLShellEnabled   bool   `json:"is_gsqlshell_enabled"`
		IsCommunityEdition   bool   `json:"is_community_edition"`
	}{
		BuildTime:            build.BuildTime,
		GitCommit:            build.GitCommit,
		BuildNum:             build.BuildNum,
		TigerGraphVersion:    response.GetGAVersion(),
		IsGraphStudioEnabled: cfg.GetEnableGraphStudio(),
		IsAdminPortalEnabled: cfg.GetEnableAdminPortal(),
		IsInsightsEnabled:    cfg.GetEnableInsights(),
		IsGraphQLEnabled:     cfg.GetEnableGraphQL(),
		IsGSQLShellEnabled:   cfg.GetEnableGSQLShell(),
		IsCommunityEdition:   isCommunity,
	}
	mw.ReplyWithResult(c, http.StatusOK, "", resp)
}
