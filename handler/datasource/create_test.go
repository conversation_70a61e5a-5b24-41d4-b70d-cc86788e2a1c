package datasource

import (
	"testing"

	"github.com/tigergraph/gus/dao/model"
)

func TestGenDataSoureCode(t *testing.T) {
	type args struct {
		ds     interface{}
		dsType string
	}

	tests := []struct {
		args args
		want string
	}{
		{
			args: args{
				ds: &model.AmazonS3DataSource{
					AccessKey: "accessKey",
					SecretKey: "secretKey",
				},
				dsType: "s3",
			},
			want: `CREATE DATA_SOURCE ds = """{"access.key":"accessKey","secret.key":"secretKey","type":"s3"}""" FOR GRAPH test_graph`,
		},
		{
			args: args{
				ds: &model.GoogleCloudStorageDataSource{
					ProjectId:    "projectId",
					PrivateKeyId: "privateKeyId",
					PrivateKey:   "privateKey",
					ClientEmail:  "clientEmail",
				},
				dsType: "gcs",
			},
			want: `CREATE DATA_SOURCE ds = """{"client_email":"clientEmail","private_key":"privateKey","private_key_id":"privateKeyId","project_id":"projectId","type":"gcs"}""" FOR GRAPH test_graph`,
		},
		{
			args: args{
				ds: &model.AzureBlobStorageDataSource{
					AccountKey: "accountKey",
				},
				dsType: "abs",
			},
			want: `CREATE DATA_SOURCE ds = """{"account.key":"accountKey","type":"abs"}""" FOR GRAPH test_graph`,
		},
	}

	for _, test := range tests {
		code, err := generateDataSourceCode(test.args.ds, "test_graph", "ds", test.args.dsType)
		if err != nil {
			t.Errorf("generateDataSourceCode() error = %v", err)
		}
		if code != test.want {
			t.Errorf("generateDataSourceCode() got = %v want = %v", code, test.want)
		}
	}
}
