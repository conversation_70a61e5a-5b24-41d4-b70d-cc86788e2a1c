package datasource

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// GetDataset returns dataset of a data source of a given type for a graph.
func GetDataset(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")
	dataSourceName := c.Param("dataSourceName")
	dataSourceUri := c.Query("dataSourceUri")

	daoManager := dao.GetManager(c)
	dataset, err := daoManager.GetDataSourceDataset(graphName, dataSourceType, dataSourceName, dataSourceUri)
	if err != nil {
		log.Errorf(c,
			"Failed to get the dataset of the data source for graph %s: %v",
			graphName, err,
		)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to get the dataset of the data source for graph '%s'.",
				graphName,
			),
		)
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "", dataset)
}
