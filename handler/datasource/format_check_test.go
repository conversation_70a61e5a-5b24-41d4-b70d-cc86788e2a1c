package datasource

import (
	"strings"
	"testing"

	"github.com/tigergraph/gus/dao/model"
)

func TestFormatTest(t *testing.T) {
	tests := []struct {
		name string
		args model.CSVFormatRequest
		want model.CSVFormatResponse
	}{
		{
			name: "comma, newline, header, no quote",
			args: model.CSVFormatRequest{
				Data: "name,blocked,location,hasPhone\\nScott,no,Zembla,111\\nMike,no,Zembla,222",
			},
			want: model.CSVFormatResponse{
				Separator: ",",
				Eol:       "\\n",
				Header:    true,
				Quote:     "none",
			},
		},
		{
			name: "|, newline, no header, no quote",
			args: model.CSVFormatRequest{
				Data: "1|Customer#*********|IVhzIApeRb ot,c,E|15|25-************|711.56|BUILDING|to the even, regular platelets. regular, ironic epitaphs nag e\\n2|Customer#*********|XSTf4,NCwDVaWNe6tEgvwfmRchLXak|13|23-************|121.65|AUTOMOBILE|l accounts. blithely ironic theodolites integrate boldly: caref",
			},
			want: model.CSVFormatResponse{
				Separator: "|",
				Eol:       "\\n",
				Header:    false,
				Quote:     "none",
			},
		},
		{
			name: "|, newline, header, no quote",
			args: model.CSVFormatRequest{
				Data: "mediumId|accountId|createTime|location\\n4398046511620|*****************|2020-08-17 07:30:47.471|Vietnam -> Bắc_Giang\\n8796093022411|*****************|2020-08-23 03:46:50.668|India -> Barasat",
			},
			want: model.CSVFormatResponse{
				Separator: "|",
				Eol:       "\\n",
				Header:    true,
				Quote:     "none",
			},
		},
		{
			name: "|, newline, header, no quote",
			args: model.CSVFormatRequest{
				Data: "mediumId|accountId|createTime|location\\n4398046511620|*****************|2020-08-17 07:30:47.471|Vietnam -> Bắc_Giang\\n8796093022411|*****************|2020-08-23 03:46:50.668|India -> Barasat",
			},
			want: model.CSVFormatResponse{
				Separator: "|",
				Eol:       "\\n",
				Header:    true,
				Quote:     "none",
			},
		},
		{
			name: "\\t, newline, header, no quote",
			args: model.CSVFormatRequest{
				Data: "name	gender	age	state\\nTom	male	40	ca\\nDan	male	34	ny",
			},
			want: model.CSVFormatResponse{
				Separator: "\t",
				Eol:       "\\n",
				Header:    true,
				Quote:     "none",
			},
		},
		{
			name: "\u001D, \u001C, header, no quote",
			args: model.CSVFormatRequest{
				Data: "primary_id\u001DNumber\u001DRevision\u001DName\u001DStatus\u001DDateReleased\u001DDateCreated\u001D\u001Cz_KAO8R$JxCHID\u001D1079121\u001DA\u001DCN Q4 Document Updates- BOM and Data Standards\u001DElaborating\u001D1970-01-01 00:00:00\u001D2022-12-05 11:00:00\u001D\u001CzxOAi0ZaJxCHID\u001D1054786\u001DA\u001DICA PRMD and RMR\u001DComplete\u001D2021-10-05 14:16:00\u001D2021-08-18 15:55:00\u001D\u001CzxKAh1niJxCHID\u001D1050463\u001DA\u001DInitial Release for Engineering Study Report, EDGEPORT (MPN EP-USB-8) Functionality Test for GNR and DEHYB\u001DComplete\u001D2022-10-17 09:40:00\u001D2021-05-26 22:01:00\u001D",
			},
			want: model.CSVFormatResponse{
				Separator: "\u001D",
				Eol:       "\u001C",
				Header:    true,
				Quote:     "none",
			},
		},
		{
			name: "comma, newline, header, single quote",
			args: model.CSVFormatRequest{
				Data: "'name','age','school','onboard_time','members'\\n'1','326','MfnrUNttXi','15/7/2014 03:38:26','KzCn9dUYCo'\\n'100000','12','NQ1fI7JduU','10/3/2020 14:41:06','lcbtT5DqHl'",
			},
			want: model.CSVFormatResponse{
				Separator: ",",
				Eol:       "\\n",
				Header:    true,
				Quote:     "'",
			},
		},
		{
			name: "comma, newline, header, double quote",
			args: model.CSVFormatRequest{
				Data: "\"name\",\"age\",\"school\",\"onboard_time\",\"members\"\\n\"1\",\"326\",\"MfnrUNttXi\",\"15/7/2014 03:38:26\",\"KzCn9dUYCo\"\\n\"100000\",\"12\",\"NQ1fI7JduU\",\"10/3/2020 14:41:06\",\"lcbtT5DqHl\"",
			},
			want: model.CSVFormatResponse{
				Separator: ",",
				Eol:       "\\n",
				Header:    true,
				Quote:     "\"",
			},
		},
		{
			name: "|, newline, header, double quote",
			args: model.CSVFormatRequest{
				Data: "\"name\"|\"age\"|\"school\"|\"onboard_time\"|\"members\"\\n\"1\"|\"326\"|\"MfnrUNttXi\"|\"15/7/2014 03:38:26\"|\"KzCn9dUYCo\"\\n\"100000\"|\"12\"|\"NQ1fI7JduU\"|\"10/3/2020 14:41:06\"|\"lcbtT5DqHl\"",
			},
			want: model.CSVFormatResponse{
				Separator: "|",
				Eol:       "\\n",
				Header:    true,
				Quote:     "\"",
			},
		},
		{
			name: "invalid eol",
			args: model.CSVFormatRequest{
				Data: "name,blocked,location,hasPhone\u0001Scott,no,Zembla,111\u0001Mike,no,Zembla,222",
			},
			want: model.CSVFormatResponse{
				Separator: "\u0001", // invalid eol will be treated as special separator
				Eol:       "\\n",
				Header:    false,
				Quote:     "none",
			},
		},
		{
			name: "escaped characters",
			args: model.CSVFormatRequest{
				Data: "name,blocked,location,hasPhone\\nScott\\,Larry,no,Zembla,111\\,111\\nMike,no,Zembla,222",
			},
			want: model.CSVFormatResponse{
				Separator: ",",
				Eol:       "\\n",
				Header:    true,
				Quote:     "none",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data := tt.args.Data
			eol, err := dsEOLCheck(data)
			if err != nil || eol != tt.want.Eol {
				t.Errorf("dsEOLCheck() = '%v', want '%v'", eol, tt.want.Eol)
			}
			lines := strings.Split(data, eol)
			separator := dsSeparatorCheck(lines)
			header := dsHeaderCheck(lines)
			quote := dsQuoteCheck(lines)
			if separator != tt.want.Separator || header != tt.want.Header || quote != tt.want.Quote {
				t.Errorf("dsSeparatorCheck() = '%v', want '%v'", separator, tt.want.Separator)
				t.Errorf("dsHeaderCheck() = '%v', want '%v'", header, tt.want.Header)
				t.Errorf("dsQuoteCheck() = '%v', want '%v'", quote, tt.want.Quote)
			}
		})
	}
}
