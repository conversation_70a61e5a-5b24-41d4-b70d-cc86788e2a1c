package datasource

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"text/template"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	h "github.com/tigergraph/gus/lib/http"
	tgJSON "github.com/tigergraph/gus/lib/json"
	mw "github.com/tigergraph/gus/middleware"
)

type GSQLS3DataSourceConfig struct {
	Type      string `json:"type" binding:"required"`
	AccessKey string `json:"access.key" binding:"required"`
	SecretKey string `json:"secret.key" binding:"required"`
}

type GSQLGCSDataSourceConfig struct {
	Type         string `json:"type" binding:"required"`
	ClientEmail  string `json:"client.email" binding:"required"`
	PrivateKeyId string `json:"private.key.id" binding:"required"`
	PrivateKey   string `json:"private.key" binding:"required"`
	ProjectId    string `json:"project.id" binding:"required"`
}

type GSQLABSDataSourceConfig struct {
	Type       string `json:"type" binding:"required"`
	AccountKey string `json:"account.key" binding:"required"`
}

type GSQLSnowflakeDataSourceConfig struct {
	Type     string `json:"type" binding:"required"`
	Username string `json:"connection.user" binding:"required"`
	Password string `json:"connection.password" binding:"required"`
	Url      string `json:"connection.url" binding:"required"`
}

type GSQLCreateDataSourcePayload struct {
	Name   string      `json:"name" binding:"required"`
	Config interface{} `json:"config" binding:"required"`
}

// Create creates data source of a given name and type for a graph.
func Create(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")
	dataSourceName := c.Param("dataSourceName")

	dataSource, err := dsCredentialFormatCheck(c, dataSourceType)
	if err != nil {
		return
	}

	if err := authenticate(c, dataSource, dataSourceType); err != nil {
		log.Errorf(c, "Failed to authenticate data source: %s", dataSourceName)
		mw.Abort(c, http.StatusInternalServerError, "Failed to authenticate data source: "+dataSourceName)
		return
	}

	// construct payload
	payload := GSQLCreateDataSourcePayload{Name: dataSourceName}
	switch dataSourceType {
	case model.AmazonS3:
		s3DataSource := dataSource.(model.AmazonS3DataSource)
		payload.Config = GSQLS3DataSourceConfig{
			Type:      "S3",
			AccessKey: s3DataSource.AccessKey,
			SecretKey: s3DataSource.SecretKey,
		}
	case model.GoogleCloudStorage:
		gcsDataSource := dataSource.(googleCloudStorageCredentials)
		payload.Config = GSQLGCSDataSourceConfig{
			Type:         "GCS",
			ClientEmail:  gcsDataSource.ClientEmail,
			PrivateKeyId: gcsDataSource.PrivateKeyId,
			PrivateKey:   gcsDataSource.PrivateKey,
			ProjectId:    gcsDataSource.ProjectId,
		}
	case model.AzureBlobStorage:
		absDataSource := dataSource.(model.AzureBlobStorageDataSource)
		payload.Config = GSQLABSDataSourceConfig{
			Type:       "ABS",
			AccountKey: absDataSource.AccountKey,
		}
	case model.Snowflake:
		snowflakeDataSource := dataSource.(model.SnowflakeDataSource)
		payload.Config = GSQLSnowflakeDataSourceConfig{
			Type:     "SNOWFLAKE",
			Username: snowflakeDataSource.Username,
			Password: snowflakeDataSource.Password,
			Url:      snowflakeDataSource.Url,
		}
	default:
		mw.Abort(c, http.StatusInternalServerError, "Data source type is not supported.")
	}

	// send request to GSQL servert
	_, err = requestGSQLServer(c, http.MethodPost, "/gsql/v1/data-sources?graph="+graphName, payload)
	if err != nil {
		log.Errorf(c, "Failed to create data source: %s, %v", dataSourceName, err)
		if strings.Contains(err.Error(), "Semantic Check Fails") {
			mw.Abort(c, http.StatusConflict, err.Error())
			return
		}
		mw.Abort(c, http.StatusInternalServerError, "Failed to create data source: "+dataSourceName)
		return
	}

	mw.Reply(
		c,
		http.StatusCreated,
		fmt.Sprintf(
			"Successfully created data source '%s' of type '%s' for graph '%s'.",
			dataSourceName, dataSourceType, graphName,
		),
	)
}

// UpsertUri upserts a dataset of data source of a given name and type for a graph.
func UpsertDataset(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")
	dataSourceName := c.Param("dataSourceName")
	dataSourceUri := c.Query("dataSourceUri")

	var dataset model.DataSourceDataset
	if err := c.ShouldBindJSON(&dataset); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.UpsertDataSourceDataset(graphName, dataSourceType, dataSourceName, dataSourceUri, dataset); err != nil {
		log.Errorf(c,
			"Failed to upsert the dataset '%s' of data source %s of type '%s' for graph %s: %v",
			dataSourceUri, dataSourceName, dataSourceType, graphName, err,
		)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to create dataset '%s' of data source '%s' of type '%s' for graph %s.",
				dataSourceUri, dataSourceName, dataSourceType, graphName,
			),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusCreated,
		fmt.Sprintf(
			"Successfully created dataset '%s' of data source '%s' of type '%s' for graph '%s'.",
			dataSourceUri, dataSourceName, dataSourceType, graphName,
		),
	)
}

func authenticate(c *gin.Context, reqBody interface{}, dataSourceType string) error {
	switch dataSourceType {
	case model.AmazonS3:
		s3Credentials := GSQLS3Credentials{
			Config: reqBody.(model.AmazonS3DataSource),
			Type:   "s3",
		}
		_, err := requestGSQLServer(c, http.MethodPost, gsqlS3AuthUrl, s3Credentials)

		return err
	case model.GoogleCloudStorage:
		return authenticateToGCS(c, nil)
	default:
		return nil
	}
}

// TODO: Implement the GCS authentication.
func authenticateToGCS(c *gin.Context, jsonData []byte) error {
	// client, err := storage.NewClient(
	// 	context.Background(),
	// 	option.WithCredentialsJSON(jsonData),
	// )
	// if err != nil {
	// 	return err
	// }
	// defer client.Close()

	return nil
}

func requestGSQLServer(c *gin.Context, requestMethod string, url string, reqBody interface{}) (*model.Response, error) {
	var cfg = mw.GetConfig(c)
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		url,
	)

	reqBodyData, _ := tgJSON.Marshal(reqBody)
	req, _ := http.NewRequestWithContext(context.Background(), requestMethod, requestURL, bytes.NewBuffer(reqBodyData))
	req.Header.Set("Content-Type", "application/json")
	creds := mw.GetUserCredentials(c)
	h.SetCredentials(req, creds)
	h.SetFromGraphStudio(req)
	// Add X-Forwarded-For
	h.SetForwardedForFromContext(c, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(c, "Failed to get response from GSQL server: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from GSQL server: %v", err)
		return nil, err
	}

	res := &model.Response{}
	if err = json.Unmarshal(body, res); err != nil {
		log.Errorf(c, "Failed to parse response from GSQL server: %v", err)
		return nil, err
	}

	message := cast.ToString(res.Message)
	if res.Error {
		log.Errorf(c, "%s", message)
		return res, fmt.Errorf("%s", message)
	}

	return res, nil
}

func CreateGSQLDataSource(gsqlClient interfaces.RequestGSQLClient, c *gin.Context, dataSource interface{}, graphName, dataSourceName, dataSourceType string) error {
	code, err := generateDataSourceCode(dataSource, graphName, dataSourceName, dataSourceType)
	if err != nil {
		return err
	}

	output, err := gsqlClient(
		c,
		mw.GetConfig(c),
		graphName,
		mw.GetUserCredentials(c),
		code,
	)
	msg := strings.TrimSpace(string(output))
	if err != nil {
		return errors.New(msg)
	}
	return nil
}

func generateDataSourceCode(dataSource interface{}, graphName, dataSourceName, dataSourceType string) (string, error) {
	const createDataSourceTmpl = `CREATE DATA_SOURCE {{.Name}} = """{{.DsJsonConfig}}""" FOR GRAPH {{.GraphName}}`

	tmpl, err := template.New("createDataSource").Parse(createDataSourceTmpl)
	if err != nil {
		return "", err
	}

	var b strings.Builder
	data := make(map[string]interface{})
	data["Type"] = dataSourceType
	data["Name"] = dataSourceName
	data["GraphName"] = graphName

	dsConfig := make(map[string]interface{})
	dsConfig["type"] = dataSourceType
	switch dataSource := dataSource.(type) {
	case *model.AmazonS3DataSource:
		dsConfig["access.key"] = dataSource.AccessKey
		dsConfig["secret.key"] = dataSource.SecretKey
	case *model.GoogleCloudStorageDataSource:
		dsConfig["project_id"] = dataSource.ProjectId
		dsConfig["private_key_id"] = dataSource.PrivateKeyId
		dsConfig["private_key"] = dataSource.PrivateKey
		dsConfig["client_email"] = dataSource.ClientEmail
	case *model.AzureBlobStorageDataSource:
		dsConfig["account.key"] = dataSource.AccountKey
	default:
		return "", errors.New("invalid data source")
	}

	dsConfigBytes, err := json.Marshal(dsConfig)
	if err != nil {
		return "", err
	}

	data["DsJsonConfig"] = string(dsConfigBytes)

	if err := tmpl.Execute(&b, data); err != nil {
		return "", err
	}

	return b.String(), nil
}
