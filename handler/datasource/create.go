package datasource

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	h "github.com/tigergraph/gus/lib/http"
	tgJSON "github.com/tigergraph/gus/lib/json"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// Create creates data source of a given name and type for a graph.
func Create(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")
	dataSourceName := c.Param("dataSourceName")

	dataSource, err := dsCredentialFormatCheck(c, dataSourceType)
	if err != nil {
		return
	}

	if err := authenticate(c, dataSource, dataSourceType); err != nil {
		log.Errorf(c, "Failed to authenticate data source: %s", dataSourceName)
		mw.Abort(c, http.StatusInternalServerError, "Failed to authenticate data source: "+dataSourceName)
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.CreateDataSource(graphName, dataSourceType, dataSourceName, dataSource); err != nil {
		if err == db.ErrAlreadyExist {
			mw.Abort(
				c,
				http.StatusConflict,
				fmt.Sprintf(
					"Data source name '%s' of type '%s' already exists on graph '%s'.",
					dataSourceName, dataSourceType, graphName,
				),
			)
		} else {
			log.Errorf(c,
				"Failed to create data source %s of type '%s' for graph %s: %v",
				dataSourceName, dataSourceType, graphName, err,
			)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf(
					"Failed to create data source '%s' of type '%s' for graph %s.",
					dataSourceName, dataSourceType, graphName,
				),
			)
		}
		return
	}

	mw.Reply(
		c,
		http.StatusCreated,
		fmt.Sprintf(
			"Successfully created data source '%s' of type '%s' for graph '%s'.",
			dataSourceName, dataSourceType, graphName,
		),
	)
}

func authenticate(c *gin.Context, reqBody interface{}, dataSourceType string) error {
	switch dataSourceType {
	case model.AmazonS3:
		s3Credentials := GSQLS3Credentials{
			Config: reqBody.(model.AmazonS3DataSource),
			Type:   "s3",
		}
		_, err := requestGSQLServer(c, http.MethodPost, gsqlS3AuthUrl, s3Credentials)

		return err
	case model.GoogleCloudStorage:
		return authenticateToGCS(c, nil)
	default:
		return nil
	}
}

// TODO: Implement the GCS authentication.
func authenticateToGCS(c *gin.Context, jsonData []byte) error {
	// client, err := storage.NewClient(
	// 	context.Background(),
	// 	option.WithCredentialsJSON(jsonData),
	// )
	// if err != nil {
	// 	return err
	// }
	// defer client.Close()

	return nil
}

func requestGSQLServer(c *gin.Context, requestMethod string, url string, reqBody interface{}) (*model.Response, error) {
	var cfg = mw.GetConfig(c)
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		url,
	)

	reqBodyData, _ := tgJSON.Marshal(reqBody)
	req, _ := http.NewRequestWithContext(context.Background(), requestMethod, requestURL, bytes.NewBuffer(reqBodyData))
	req.SetBasicAuth(mw.GetUsername(c), mw.GetPassword(c))
	h.SetFromGraphStudio(req)
	// Add X-Forwarded-For
	h.SetForwardedForFromContext(c, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(c, "Failed to get response from GSQL server: %v", err)
		return nil, err
	}
	if resp.StatusCode >= 300 {
		log.Errorf(c, "Failed with status code: %v, and status: %s.", resp.StatusCode, resp.Status)
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from GSQL server: %v", err)
		return nil, err
	}

	res := &model.Response{}
	if err = json.Unmarshal(body, res); err != nil {
		log.Errorf(c, "Failed to parse response from GSQL server: %v", err)
		return nil, err
	}

	message := cast.ToString(res.Message)
	if res.Error && (resp.StatusCode < http.StatusBadRequest) {
		log.Errorf(c, "%s", message)
		return res, fmt.Errorf("%s", message)
	}

	return res, nil
}
