package datasource

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Delete deletes a data source of a given name and type from a graph.
func Delete(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")
	dataSourceName := c.Param("dataSourceName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteDataSource(graphName, dataSourceType, dataSourceName); err != nil {
		log.Errorf(c,
			"Failed to delete data source  %s from graph %s: %v",
			dataSourceName, graphName, err,
		)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to delete data source '%s' from graph '%s'.",
				dataSourceName, graphName,
			),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf(
			"Successfully deleted data source '%s' from graph '%s'.",
			dataSourceName, graphName,
		),
	)
}
