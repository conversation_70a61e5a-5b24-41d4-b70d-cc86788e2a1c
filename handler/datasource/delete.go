package datasource

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Delete deletes a data source of a given name and type from a graph.
func Delete(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceName := c.Param("dataSourceName")

	// call gsql server to delete data source
	requestURL := fmt.Sprintf(
		"%s/%s?graph=%s",
		"/gsql/v1/data-sources",
		dataSourceName,
		graphName,
	)

	_, err := requestGSQLServer(c, http.MethodDelete, requestURL, nil)
	if err != nil {
		log.Errorf(c, "Failed to delete data source '%s' from graph '%s': %v", dataSourceName, graphName, err)
		if strings.Contains(err.<PERSON>rror(), "not found") {
			mw.Abort(c, http.StatusNotFound, fmt.Sprintf("Data source '%s' not found in graph '%s'.", dataSourceName, graphName))
			return
		}
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to delete data source '%s' from graph '%s'.",
				dataSourceName, graphName,
			),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf(
			"Successfully deleted data source '%s' from graph '%s'.",
			dataSourceName, graphName,
		),
	)
}

// Delete deletes a dataset of a data source a given name and type from a graph.
func DeleteDataset(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")
	dataSourceName := c.Param("dataSourceName")
	dataSourceUri := c.Query("dataSourceUri")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteDataSourceDataset(graphName, dataSourceType, dataSourceName, dataSourceUri); err != nil {
		log.Errorf(c,
			"Failed to delete the dataset %s of data source  %s from graph %s: %v",
			dataSourceUri, dataSourceName, graphName, err,
		)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to delete the dataset %s of data source '%s' from graph '%s'.",
				dataSourceUri, dataSourceName, graphName,
			),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf(
			"Successfully deleted the dataset '%s' of data source '%s' from graph '%s'.",
			dataSourceUri, dataSourceName, graphName,
		),
	)
}
