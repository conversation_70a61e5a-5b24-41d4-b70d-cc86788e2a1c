package datasource

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// GetAllNames returns names of all data sources of a given type for a graph.
func GetAllNames(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")

	daoManager := dao.GetManager(c)
	dataSourceNames, err := daoManager.GetAllDataSourceNames(graphName, dataSourceType)
	if err != nil {
		log.Errorf(c,
			"Failed to get the names of all data sources for graph %s: %v",
			graphName, err,
		)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to get the names of all data sources for graph '%s'.",
				graphName,
			),
		)
		return
	}
	mw.<PERSON><PERSON><PERSON>ithResult(c, http.StatusOK, "", dataSourceNames)
}
