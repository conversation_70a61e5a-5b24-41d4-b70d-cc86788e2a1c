package datasource

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// GetAllNames returns names of all data sources of a given type for a graph.
func GetAllNames(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")

	res, err := requestGSQLServer(c, http.MethodGet, "/gsql/v1/data-sources?graph="+graphName, nil)
	if err == nil && res.Error {
		err = errors.New(res.Message)
	}
	if err != nil {
		log.Errorf(c,
			"Failed to get the names of all data sources for graph %s: %v",
			graphName, err,
		)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to get the names of all data sources for graph '%s'.",
				graphName,
			),
		)
		return
	}

	dataSourceNames := make([]string, 0)
	for _, dataSource := range res.Results.([]interface{}) {
		if dataSource, ok := dataSource.(map[string]interface{}); ok {
			if dataSource["type"] == strings.ToUpper(dataSourceType) {
				dataSourceNames = append(dataSourceNames, dataSource["name"].(string))
			}
		}
	}

	mw.ReplyWithResult(c, http.StatusOK, "", dataSourceNames)
}

// GetDataSourceTypeUris returns uris of a given type for a graph.
func GetDataSourceTypeUris(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")

	daoManager := dao.GetManager(c)
	dataSourceTypeUris, err := daoManager.GetDataSourceTypeUris(graphName, dataSourceType)
	if err != nil {
		log.Errorf(c,
			"Failed to get the uris of the data type for graph %s: %v",
			graphName, err,
		)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to get the uris of the data type for graph '%s'.",
				graphName,
			),
		)
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "", dataSourceTypeUris)
}

// GetDataSourceUris returns uris of a data source of a given type for a graph.
func GetDataSourceUris(c *gin.Context) {
	graphName := c.Param("graphName")
	dataSourceType := c.Param("dataSourceType")
	dataSourceName := c.Param("dataSourceName")

	daoManager := dao.GetManager(c)
	dataSourceUris, err := daoManager.GetDataSourceUris(graphName, dataSourceType, dataSourceName)
	if err != nil {
		log.Errorf(c,
			"Failed to get the uris of the data source for graph %s: %v",
			graphName, err,
		)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf(
				"Failed to get the uris of the data source for graph '%s'.",
				graphName,
			),
		)
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "", dataSourceUris)
}
