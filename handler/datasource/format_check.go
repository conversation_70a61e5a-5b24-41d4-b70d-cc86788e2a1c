package datasource

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	// "cloud.google.com/go/storage"
	// "google.golang.org/api/option"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

const (
	gsqlS3AuthUrl = "/gsqlserver/gsql/check/ds-credentials"
)

type googleCloudStorageCredentials struct {
	model.GoogleCloudStorageDataSource

	Type                    string `json:"type" binding:"required"`
	ClientId                string `json:"client_id" binding:"required"`
	AuthURI                 string `json:"auth_uri" binding:"required"`
	TokenURI                string `json:"token_uri" binding:"required"`
	AuthProviderX509CertURL string `json:"auth_provider_x509_cert_url" binding:"required"`
	ClientX509CertURL       string `json:"client_x509_cert_url" binding:"required"`
}

type GSQLS3Credentials struct {
	Config model.AmazonS3DataSource `json:"config" binding:"required"`
	Type   string                   `json:"type" binding:"required"`
}

func FormatCheck(c *gin.Context) {
	dataSourceType := c.Param("dataSourceType")
	_, err := dsCredentialFormatCheck(c, dataSourceType)
	if err != nil {
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf(
			"Data source credentials of type '%s' has the correct format.",
			dataSourceType,
		),
	)
}

func dsCredentialFormatCheck(c *gin.Context, dataSourceType string) (interface{}, error) {
	var err error
	var dataSource interface{}

	switch dataSourceType {
	case model.AmazonS3:
		s3DataSource := model.AmazonS3DataSource{}
		err = c.ShouldBindJSON(&s3DataSource)
		dataSource = s3DataSource
	case model.GoogleCloudStorage:
		gcsCredentials := googleCloudStorageCredentials{}
		err = c.ShouldBindJSON(&gcsCredentials)
		dataSource = gcsCredentials
	case model.AzureBlobStorage:
		absDataSource := model.AzureBlobStorageDataSource{}
		err = c.ShouldBindJSON(&absDataSource)
		dataSource = absDataSource
	default:
		// TODO: Support more data source types in the future.
		log.Infof(c, "Data source with type %s has not been supported.", dataSourceType)
		mw.Abort(
			c,
			http.StatusBadRequest,
			fmt.Sprintf(
				"Data source with type %s has not been supported.",
				dataSourceType,
			),
		)
	}

	if err != nil {
		log.Errorf(
			c,
			"Invalid data source credentials format: %v for data source of type %s",
			err,
			dataSourceType,
		)
		mw.Abort(
			c,
			http.StatusBadRequest,
			fmt.Sprintf(
				"Invalid data source credentials format for data source of type: %s",
				dataSourceType,
			),
		)
		return nil, err
	}

	return dataSource, nil
}
