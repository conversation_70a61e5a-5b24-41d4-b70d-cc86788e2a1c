package datasource

import (
	"bytes"
	"fmt"
	"net/http"
	"strings"
	"unicode"

	"github.com/gin-gonic/gin"

	// "cloud.google.com/go/storage"
	// "google.golang.org/api/option"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

const (
	gsqlS3AuthUrl = "/gsql/v1/internal/check/ds-credentials"
	sampleSize    = 200
)

type googleCloudStorageCredentials struct {
	model.GoogleCloudStorageDataSource

	Type                    string `json:"type" binding:"required"`
	ClientId                string `json:"client_id" binding:"required"`
	AuthURI                 string `json:"auth_uri" binding:"required"`
	TokenURI                string `json:"token_uri" binding:"required"`
	AuthProviderX509CertURL string `json:"auth_provider_x509_cert_url" binding:"required"`
	ClientX509CertURL       string `json:"client_x509_cert_url" binding:"required"`
}

type GSQLS3Credentials struct {
	Config model.AmazonS3DataSource `json:"config" binding:"required"`
	Type   string                   `json:"type" binding:"required"`
}

func FormatCheck(c *gin.Context) {
	dataSourceType := c.Param("dataSourceType")
	_, err := dsCredentialFormatCheck(c, dataSourceType)
	if err != nil {
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf(
			"Data source credentials of type '%s' has the correct format.",
			dataSourceType,
		),
	)
}

func dsCredentialFormatCheck(c *gin.Context, dataSourceType string) (interface{}, error) {
	var err error
	var dataSource interface{}

	switch dataSourceType {
	case model.AmazonS3:
		s3DataSource := model.AmazonS3DataSource{}
		err = c.ShouldBindJSON(&s3DataSource)
		dataSource = s3DataSource
	case model.GoogleCloudStorage:
		gcsCredentials := googleCloudStorageCredentials{}
		err = c.ShouldBindJSON(&gcsCredentials)
		dataSource = gcsCredentials
	case model.AzureBlobStorage:
		absDataSource := model.AzureBlobStorageDataSource{}
		err = c.ShouldBindJSON(&absDataSource)
		dataSource = absDataSource
	case model.Snowflake:
		snowflakeDataSource := model.SnowflakeDataSource{}
		err = c.ShouldBindJSON(&snowflakeDataSource)
		dataSource = snowflakeDataSource
	default:
		// TODO: Support more data source types in the future.
		log.Infof(c, "Data source with type %s has not been supported.", dataSourceType)
		mw.Abort(
			c,
			http.StatusBadRequest,
			fmt.Sprintf(
				"Data source with type %s has not been supported.",
				dataSourceType,
			),
		)
	}

	if err != nil {
		log.Errorf(
			c,
			"Invalid data source credentials format: %v for data source of type %s",
			err,
			dataSourceType,
		)
		mw.Abort(
			c,
			http.StatusBadRequest,
			fmt.Sprintf(
				"Invalid data source credentials format for data source of type: %s",
				dataSourceType,
			),
		)
		return nil, err
	}

	return dataSource, nil
}

func CSVFormatCheck(c *gin.Context) {
	csvFormatRequest := model.CSVFormatRequest{}
	if err := c.ShouldBindJSON(&csvFormatRequest); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}
	data := csvFormatRequest.Data
	eol, err := dsEOLCheck(data)
	if err != nil {
		return
	}
	lines := strings.Split(data, eol)
	separator := dsSeparatorCheck(lines)
	header := dsHeaderCheck(lines)
	quote := dsQuoteCheck(lines)
	res := model.CSVFormatResponse{
		Eol:       eol,
		Separator: separator,
		Header:    header,
		Quote:     quote,
	}
	mw.ReplyWithResult(c, http.StatusOK, "", res)
}

func dsEOLCheck(data string) (string, error) {
	reader := strings.NewReader(data)
	KB := 1024
	buf := make([]byte, 128*KB)
	_, err := reader.Read(buf)
	if err != nil {
		return "", err
	}

	if bytes.Contains(buf, []byte{'\r', '\n'}) {
		return "\\r\\n", nil
	}
	if bytes.Contains(buf, []byte{'\r'}) {
		return "\\r", nil
	}
	// Special character: File Separator
	if strings.Contains(data, "\u001C") {
		return "\u001C", nil
	}

	return "\\n", nil
}

func dsSeparatorCheck(lines []string) string {
	// Unicode special separators
	specialSeparators := []string{"\u0001", "\u0002", "\u0003", "\u0004", "\u0005", "\u0006", "\u0007", "\u0008", "\u0009", "\u000A", "\u000B", "\u000C", "\u000D", "\u000E", "\u000F", "\u0010", "\u0011", "\u0012", "\u0013", "\u0014", "\u0015", "\u0016", "\u0017", "\u0018", "\u0019", "\u001A", "\u001B", "\u001C", "\u001D", "\u001E", "\u001F"}
	for _, separator := range specialSeparators {
		for i := 0; i < len(lines) && i < sampleSize; i++ {
			if strings.Contains(lines[i], separator) {
				return separator
			}
		}
	}

	// Sniffer will skip single and double quotes
	separators := []rune{',', '\t', '|', '/', '\\', '^', '~', '?', '!', '@', '#', '$', '%', '&', '*', '-', '_', '+', '=', '`', '<', '>', ':', ';', '.', ' '}
	var likelihoodMap = map[string]float64{}

	// Find the separator with its likelihood
	for _, separator := range separators {
		likelihood := calculateSeparatorLikelihood(lines, string(separator))
		if likelihood == 0 {
			continue
		} else if likelihood > 0.9 {
			return string(separator)
		}
		likelihoodMap[string(separator)] = likelihood
	}

	// Find the separator with the highest likelihood
	max := 0.0
	res := ","
	for separator, likelihood := range likelihoodMap {
		if likelihood > max {
			max = likelihood
			res = separator
		}
	}
	return res
}

func calculateSeparatorLikelihood(lines []string, separator string) float64 {
	total := 0
	count := 0
	firstValidLine := ""

	// Count each row contains the same number of separators
	for _, line := range lines {
		// Remove the escape character before the separator
		line = strings.ReplaceAll(line, "\\"+separator, "")
		if len(line) == 0 {
			continue
		}
		if len(firstValidLine) == 0 {
			firstValidLine = line
		}
		total += 1
		if total > sampleSize {
			break
		}
		if strings.Count(line, separator) == strings.Count(firstValidLine, separator) && strings.Count(line, separator) > 0 {
			count += 1
		}
	}
	if total == 0 {
		return 0
	}
	return float64(count) / float64(total)
}

func dsHeaderCheck(lines []string) bool {
	if len(lines) == 0 {
		return false
	} else {
		// Find the first row that contains characters
		var header string
		for _, line := range lines {
			if len(line) > 0 {
				header = line
				break
			}
		}
		for _, char := range header {
			// If the first row contains any numeric characters, it is not a header row.
			if unicode.IsDigit(char) {
				return false
			}
		}
	}
	return true
}

func dsQuoteCheck(lines []string) string {
	quotes := []string{"'", "\""}
	for _, quote := range quotes {
		for _, line := range lines {
			if len(line) == 0 {
				continue
			}
			// check each line starts and ends with the same quote
			if strings.HasPrefix(line, quote) && strings.HasSuffix(line, quote) {
				return quote
			}
		}
	}
	return "none"
}
