package graphstyle

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// GetGlobal returns global graph style.
func GetGlobal(c *gin.Context) {
	daoManager := dao.GetManager(c)
	style, err := daoManager.GetGraphStyle("global")
	if err != nil {
		if errors.Is(err, db.ErrNotFound) {
			mw.Abort(
				c,
				http.StatusNotFound,
				"Global graph style cannot be found.",
			)
		} else {
			log.Errorf(c, "Failed to get global graph style: %v", err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				"Failed to get global graph style.",
			)
		}
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", style)
}
