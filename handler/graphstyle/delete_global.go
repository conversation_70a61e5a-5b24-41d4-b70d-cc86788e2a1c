package graphstyle

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// DeleteGlobal deletes global graph style.
func DeleteGlobal(c *gin.Context) {
	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteGraphStyle("global"); err != nil {
		log.Errorf(c, "Failed to delete global graph style: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			"Failed to delete global graph style.",
		)
		return
	}

	mw.Reply(c, http.StatusOK, "Successfully deleted global graph style.")
}
