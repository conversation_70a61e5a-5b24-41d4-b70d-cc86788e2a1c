package graphstyle

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
)

var StyleErrorPrefix string = "StyleError"

func requestGraphSchema(
	ctx context.Context,
	cfg *config.Config,
	creds *model.UserCredentials,
	graphname string,
) (model.Schema, error) {
	schema := model.Schema{}
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s/%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		"/gsql/v1/schema/graphs",
		graphname,
	)

	req, _ := http.NewRequestWithContext(context.Background(), "GET", requestURL, nil)

	h.SetCredentials(req, creds)
	h.SetFromGraphStudio(req)
	h.SetForwardedForFromContext(ctx, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server requests.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})
	resp, err := client.Do(req)

	if err != nil {
		return schema, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return schema, err
	}

	if err = json.Unmarshal(body, &schema); err != nil {
		return schema, err
	}
	return schema, nil
}
