package graphstyle

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Delete deletes the graph style for a graph.
func Delete(c *gin.Context) {
	graphName := c.Param("graphName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteGraphStyle(graphName); err != nil {
		log.Errorf(c, "Failed to delete local graph style for graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete local graph style for graph '%s'.", graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted local graph style for graph '%s'.", graphName),
	)
}
