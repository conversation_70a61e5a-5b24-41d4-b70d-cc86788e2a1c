package graphstyle

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

// UpserGlobal upserts global graph style.
func UpserGlobal(c *gin.Context) {
	var gStyle = model.GraphStyle{}
	if err := c.ShouldBindJSON(&gStyle); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	schema, err := requestGraphSchema(c, mw.GetConfig(c), mw.GetUserCredentials(c), "global")
	if err != nil {
		log.Warnf("Failed to request the schema: %+v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to request the schema.")
		return
	}

	daoManager := dao.GetManager(c)
	err = daoManager.UpsertGraphStyle("global", gStyle, schema)
	if err != nil {
		if strings.HasPrefix(err.Error(), StyleErrorPrefix) {
			log.Warnf("%+v", err)
			mw.Abort(c, http.StatusBadRequest, err.Error())
			return
		} else {
			log.Errorf(c, "Failed to upsert global graph style: %+v", err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to upsert global graph style: %s", err),
			)
			return
		}
	}

	mw.Reply(c, http.StatusOK, "Successfully upserted global graph style.")
}
