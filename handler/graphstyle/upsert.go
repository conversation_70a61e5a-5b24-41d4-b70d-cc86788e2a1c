package graphstyle

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

// Upsert upserts the graph style for a graph.
func Upsert(c *gin.Context) {
	graphName := c.Param("graphName")

	var gStyle = model.GraphStyle{}
	if err := c.ShouldBindJSON(&gStyle); err != nil {
		log.Warnf(c, "Invalid payload: %+v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	schema, err := requestGraphSchema(c, mw.GetConfig(c), mw.GetUsername(c), mw.GetPassword(c), graphName)
	if err != nil {
		log.Warnf("Failed to request the schema: %+v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to request the schema.")
		return
	}

	daoManager := dao.GetManager(c)
	err = daoManager.UpsertGraphStyle(graphName, gStyle, schema)
	if err != nil {
		if strings.HasPrefix(err.Error(), StyleErrorPrefix) {
			log.Warnf("%+v", err)
			mw.Abort(c, http.StatusBadRequest, err.Error())
			return
		} else {
			log.Warnf(c, "Failed to upsert local graph style for graph %s: %+v", graphName, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to upsert local graph style for graph '%s'.", graphName),
			)
			return
		}
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully upserted local graph style for graph '%s'.", graphName),
	)
}
