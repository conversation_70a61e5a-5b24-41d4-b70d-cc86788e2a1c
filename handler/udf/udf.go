package udf

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

func GetUDF(c *gin.Context, udfService interfaces.UDFService) {
	filename := c.Param("filename")
	creds := mw.GetUserCredentials(c)

	udf, err := udfService.GetUDF(
		c,
		creds,
		filename,
	)
	if err != nil {
		log.Warnf(c, "Failed to get UDF %s: %+v", filename, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to get UDF '%s'.", filename),
		)
		return
	}

	response := struct {
		UDF string `json:"udf"`
	}{
		UDF: udf,
	}

	mw.ReplyWithResult(c, http.StatusOK, "", response)
}

func SetUDF(c *gin.Context, udfService interfaces.UDFService) {
	filename := c.Param("filename")
	creds := mw.GetUserCredentials(c)

	payload := struct {
		UDF string `json:"udf" binding:"required"`
	}{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	err := udfService.SetUDF(
		c,
		creds,
		filename,
		payload.UDF,
	)
	if err != nil {
		log.Warnf(c, "Failed to set UDF %s: %+v", filename, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to set UDF '%s', error: %v", filename, err),
		)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", nil)
}
