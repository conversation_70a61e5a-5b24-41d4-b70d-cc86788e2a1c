package log

import (
	"fmt"
	"math"
	"net/http"
	"path"
	"sync"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

const defaultSearchLimit = 1000

type SearchInfo struct {
	HostIDs    []string `form:"hostID"`
	Components []string `form:"component"`
	Pattern    string   `form:"pattern" binding:"required"`
	Limit      int32    `form:"limit"`
}

// Search returns the search results of a pattern among a list of components.
func Search(c *gin.Context) {
	var info SearchInfo
	if err := c.ShouldBindQuery(&info); err != nil {
		log.Warnf(c, "Invalid query: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid query.")
		return
	}

	if info.Limit == 0 {
		info.Limit = defaultSearchLimit
	}
	if info.Limit < 0 || info.Limit > defaultSearchLimit {
		mw.Abort(
			c,
			http.StatusBadRequest,
			fmt.Sprintf("The maximum number of search results should be between 1 and %d.", defaultSearchLimit),
		)
		return
	}

	cfg := mw.GetConfig(c)
	hostIDs := info.HostIDs
	if hostIDs == nil {
		hostIDs = cfg.GetAllHostIDs()
	}
	cmpts := info.Components
	if cmpts == nil {
		var err error
		cmpts, err = listCmptsUnderLogRoot(c, cfg)
		if err != nil {
			return
		}
	}
	limit := int32(math.Round(float64(info.Limit) / float64(len(hostIDs))))
	results := runSearch(cfg, hostIDs, cmpts, info.Pattern, limit)

	mw.ReplyWithResult(c, http.StatusOK, "", results)
}

func listCmptsUnderLogRoot(c *gin.Context, cfg *config.Config) ([]string, error) {
	hostID, err := cfg.GetHostID()
	if err != nil {
		log.Errorf(c, "Failed to get local host ID: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get local host ID.")
		return nil, err
	}

	tgFS := fs.NewTGFilesystem(cfg)
	logRootPath := cfg.GetLogRootDirPath()
	filesInfo, err := tgFS.List(logRootPath, hostID)
	if err != nil {
		log.Errorf(c, "Failed to list components under path %s: %v", logRootPath, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to list components under path '/%s'.", logRootPath),
		)
		return nil, err
	}

	cmpt := make([]string, len(filesInfo))
	for i, fileInfo := range filesInfo {
		cmpt[i] = fileInfo.Name
	}
	return cmpt, nil
}

func runSearch(
	cfg *config.Config,
	hostIDs, cmpts []string,
	pattern string,
	limit int32,
) map[string][]fs.SearchResult {
	mux := &sync.Mutex{}
	tgFS := fs.NewTGFilesystem(cfg)
	results := make(map[string][]fs.SearchResult)

	var wg sync.WaitGroup
	wg.Add(len(hostIDs) * len(cmpts))
	for _, hostID := range hostIDs {
		results[hostID] = make([]fs.SearchResult, 0)
		for _, cmpt := range cmpts {
			go func(hostID, cmpt string) {
				defer wg.Done()

				p := path.Join(cfg.GetLogRootDirPath(), cmpt)
				result, err := tgFS.Search(p, "", pattern, limit, hostID)
				if err != nil {
					log.Errorf(
						"Failed to search for pattern %s under path %s in host %s: %v",
						pattern,
						p,
						hostID,
						err,
					)
					return
				}

				mux.Lock()
				defer mux.Unlock()
				results[hostID] = append(results[hostID], result...)
			}(hostID, cmpt)
		}
	}

	wg.Wait()
	return results
}
