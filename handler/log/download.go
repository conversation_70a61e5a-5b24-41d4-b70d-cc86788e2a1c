package log

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

const LogTempDir = "logs"

// Download returns the compressed file or directory.
func Download(c *gin.Context) {
	cfg := mw.GetConfig(c)
	localHostID, err := cfg.GetHostID()
	if err != nil {
		log.Errorf(c, "Failed to get local host ID: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get local host ID.")
		return
	}
	hostID := c.DefaultQuery("hostID", localHostID)

	tempDir := path.Join(cfg.GetTempDirPath(), LogTempDir)
	if err = os.MkdirAll(tempDir, 0700); err != nil {
		log.Errorf(c, "Failed to create directory %s: %v", tempDir, err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to create temporary directory for downloading log.")
		return
	}
	if tempDir, err = os.MkdirTemp(tempDir, "log_*"); err != nil {
		log.Errorf(c, "Failed to create directory %s: %v", tempDir, err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to create temporary directory for downloading log.")
		return
	}
	defer os.RemoveAll(tempDir)

	tgFS := fs.NewTGFilesystem(cfg)
	filePath := c.Query("path")
	src := path.Join(cfg.GetLogRootDirPath(), filePath)
	dst := path.Join(tempDir, filepath.Base(filePath))
	// check prefix to avoid path traversals
	if !strings.HasPrefix(src, cfg.GetLogRootDirPath()) {
		mw.Abort(
			c,
			http.StatusBadRequest,
			fmt.Sprintf("Path:'%s' is invalid", filePath),
		)
		return
	}
	if err := tgFS.CopyFile(src, hostID, dst, []string{localHostID}, fs.FileTypeFile); err != nil {
		log.Errorf(c, "Failed to copy %s to %s: %v", src, dst, err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to copy log to local server.")
		return
	}

	filename := fmt.Sprintf("%s.gz", filepath.Base(filePath))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))

	c.Stream(func(w io.Writer) bool {
		if err := fs.Compress(dst, w); err != nil {
			log.Errorf(c, "Failed to compress file with path %s: %v", dst, err)
		}
		return false
	})
}
