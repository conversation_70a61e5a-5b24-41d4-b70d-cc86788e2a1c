package log

import (
	"fmt"
	"net/http"
	"path"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

// List retrieves first level files and directories under a path.
func List(c *gin.Context) {
	cfg := mw.GetConfig(c)
	localHostID, err := cfg.GetHostID()
	if err != nil {
		log.Errorf(c, "Failed to get local host ID: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get local host ID.")
		return
	}
	hostID := c.<PERSON>("hostID", localHostID)

	tgFS := fs.NewTGFilesystem(cfg)
	filePath := c.Query("path")
	p := path.Join(cfg.GetLogRootDirPath(), filePath)
	abs, err := filepath.Abs(p)
	if err != nil {
		log.Error(c, err)
	}
	if !strings.HasPrefix(abs, cfg.GetLogRootDirPath()) {
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Path '%s' is not inside %s", filePath, cfg.GetLogRootDirPath()),
		)
		return
	}
	filesInfo, err := tgFS.List(p, hostID)
	if err != nil {
		log.Errorf(c, "Failed to list files and directories under path %s: %v", p, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to list files and directories under path '/%s'.", filePath),
		)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", filesInfo)
}
