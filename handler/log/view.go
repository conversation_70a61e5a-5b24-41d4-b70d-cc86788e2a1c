package log

import (
	"fmt"
	"net/http"
	"path"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

const defaultMaxReadLength = 32 << 20

type FileInfo struct {
	Path   string `form:"path" binding:"required"`
	Offset int64  `form:"offset"`
	Length int64  `form:"length"`
}

// View returns the content of a file.
func View(c *gin.Context, cfg *config.Config, tgFS interfaces.TGFileSystem, cntlrClient pb.ControllerClient) {
	var info FileInfo
	if err := c.ShouldBindQuery(&info); err != nil {
		log.Warnf(c, "Invalid query: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid query.")
		return
	}
	if info.Offset < 0 {
		mw.Abort(c, http.StatusBadRequest, "Offset should not be negative.")
		return
	}
	if info.Length == 0 {
		info.Length = defaultMaxReadLength
	}
	if info.Length < 1 || info.Length > defaultMaxReadLength {
		mw.Abort(
			c,
			http.StatusBadRequest,
			"Bytes to read should be between 1Byte and 32MB.",
		)
		return
	}

	localHostID, err := cfg.GetHostID()
	if err != nil {
		log.Errorf(c, "Failed to get local host ID: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get local host ID.")
		return
	}
	hostID := c.DefaultQuery("hostID", localHostID)

	p := path.Join(cfg.GetLogRootDirPath(), info.Path)

	// check prefix to avoid path traversals
	if !strings.HasPrefix(p, cfg.GetLogRootDirPath()) {
		mw.Abort(
			c,
			http.StatusBadRequest,
			fmt.Sprintf("Path:'%s' is invalid", info.Path),
		)
		return
	}
	bytes, err := tgFS.Read(cntlrClient, p, info.Offset, info.Length, hostID)
	if err != nil {
		log.Errorf(c, "Failed to read file with path %s: %v", p, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to read file with path '/%s'.", info.Path),
		)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", string(bytes))
}
