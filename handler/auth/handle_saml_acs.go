package auth

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/codec"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/jwt"
	mw "github.com/tigergraph/gus/middleware"
)

func HandleSAMLACS(
	UpsertSAMLResponse interfaces.UpsertSAMLResponse,
	cfg *config.Config,
	gsqlAuthenticator interfaces.GSQLAuthenticator,
	authenticationService interfaces.AuthenticationService,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		samlResp, exist := c.GetPostForm("SAMLResponse")
		if !exist {
			samlResp, exist = c.GetQuery("SAMLResponse")
			if !exist {
				mw.Abort(c, http.StatusBadRequest, "SAML response doesn't exist.")
				return
			}

		}

		relayState, exist2 := c.GetPostForm("RelayState")
		if !exist2 {
			relayState, _ = c.GetQuery("RelayState")
		}

		locationBytes, err := base64.StdEncoding.DecodeString(relayState)
		if err != nil {
			mw.Abort(c, http.StatusBadRequest, err.Error())
			return
		}
		location := "/"
		if len(locationBytes) > 0 {
			location = string(locationBytes)
		}

		hash := fmt.Sprintf("%x", sha256.Sum256([]byte(samlResp)))
		if err := UpsertSAMLResponse(hash, samlResp); err != nil {
			log.Errorf(c, "Failed to upsert SAML response with hash %s: %v", hash, err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to upsert SAML response.")
			return
		}

		creds := &model.UserCredentials{
			Username: cfg.GetSSOBuiltinUser(),
			Password: samlResp,
		}

		info, err := gsqlAuthenticator(c, cfg, creds, true)
		if err != nil {
			log.Errorf(c, "Failed to authenticate with GSQL: %v", err)
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		encryptedPwd, err := codec.AESCBCEncrypt(cfg.GetAuthToken(), hash)
		if err != nil {
			log.Errorf(c, "Failed to encrypt password: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to encrypt password.")
			return
		}

		claims := jwt.New(
			uuid.New(),
			mw.GetReqID(c),
			creds.Username,
			encryptedPwd,
			info.Name,
			time.Duration(cfg.GetCookieDuration())*time.Second,
		)

		sessionToken, err := authenticationService.Register(c, claims)
		if err != nil {
			log.Error(c, err)
			mw.Abort(c, http.StatusInternalServerError, "failed to login")
			return
		}
		mw.SetCookie(c, sessionToken, cfg.GetCookieSameSite())
		c.Header("Location", location)
		mw.ReplyWithResult(c, http.StatusFound, "", info)
	}
}
