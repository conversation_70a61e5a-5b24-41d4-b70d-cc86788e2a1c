package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// LogOut removes the cookie to unauthorize user for future requests.
func LogOut(auth interfaces.AuthenticationService) gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg := mw.GetConfig(c)
		if mw.GetUsername(c) == cfg.GetSSOBuiltinUser() {
			hash := mw.GetPassword(c)
			daoManager := dao.GetManager(c)
			if err := daoManager.DeleteSAMLResponse(hash); err != nil {
				log.Errorf(c, "Failed to delete SAML response with hash %s: %v", hash, err)
			}
		}

		cookie, err := c.<PERSON>(mw.CookieName)
		if err != nil {
			log.Info(c, err)
			mw.Reply(c, http.StatusOK, "Successfully logged out.")
			return
		}
		if err := auth.Revoke(cookie); err != nil {
			log.Info(c, err)
			mw.Reply(c, http.StatusInternalServerError, "failed to log out.")
			return
		}

		mw.DeleteCookie(c)
		mw.Reply(c, http.StatusOK, "Successfully logged out.")
	}
}
