package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// LogOut removes the cookie to unauthorize user for future requests.
func LogOut(c *gin.Context) {

	cfg := mw.GetConfig(c)
	if mw.GetUsername(c) == cfg.GetSSOBuiltinUser() {
		hash := mw.GetUserCredentials(c).Password
		daoManager := dao.GetManager(c)
		if hash != "" {
			if err := daoManager.DeleteSAMLResponse(hash); err != nil {
				log.Errorf(c, "Failed to delete SAML response with hash %s: %v", hash, err)
			}
		}
	}

	mw.DeleteCookie(c)
	mw.Reply(c, http.StatusOK, "Successfully logged out.")

}
