package auth

import (
	"encoding/base64"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/codec"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/jwt"
	mw "github.com/tigergraph/gus/middleware"
)

func HandleOIDCCallback(
	cfg *config.Config,
	gsqlAuthenticator interfaces.GSQLAuthenticator,
	authenticationService interfaces.AuthenticationService,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add authorization code flow support: https://graphsql.atlassian.net/wiki/spaces/TGDesignDocs/pages/3148480627/Design+for+OIDC+SSO+with+authorization+code+flow
		code := c.PostForm("code")
		idToken := c.PostForm("id_token")
		state := c.PostForm("state")

		if code == "" {
			code = c.Query("code")
		}
		if idToken == "" {
			idToken = c.Query("id_token")
		}
		if state == "" {
			state = c.Query("state")
		}

		log.Debugf(c, "code=%v, id_token=%v, state=%v", code, idToken, state)

		if code == "" && idToken == "" {
			mw.Abort(c, http.StatusBadRequest, "code or id_token is missing in OIDC response")
			return
		}

		if state == "" {
			mw.Abort(c, http.StatusBadRequest, "state is missing in OIDC response")
			return
		}

		stateBytes, err := base64.StdEncoding.DecodeString(state)
		if err != nil {
			log.Errorf(c, "Failed to decode state: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Failed to decode state")
			return
		}

		creds := &model.UserCredentials{
			Username: cfg.GetOIDCBuiltinUser(),
			Password: code,
		}
		// GSQL handles id_token as first priority
		if idToken != "" {
			creds.Password = idToken
		}

		info, err := gsqlAuthenticator(c, cfg, creds, true)
		if err != nil {
			log.Errorf("Failed to authenticate with GSQL: %v", err)
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		encryptedPwd, err := codec.AESCBCEncrypt(cfg.GetAuthToken(), creds.Password)
		if err != nil {
			log.Errorf("Failed to encrypt password: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to encrypt password.")
			return
		}

		claims := jwt.New(
			uuid.New(),
			mw.GetReqID(c),
			creds.Username,
			encryptedPwd,
			info.Name,
			time.Duration(cfg.GetCookieDuration())*time.Second,
		)

		sessionToken, err := authenticationService.Register(c, claims)
		if err != nil {
			log.Error("Failed to register session: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "failed to login")
			return
		}

		mw.SetCookie(c, sessionToken, cfg.GetCookieSameSite())
		c.Header("Location", string(stateBytes))
		mw.ReplyWithResult(c, http.StatusFound, "", info)
	}
}
