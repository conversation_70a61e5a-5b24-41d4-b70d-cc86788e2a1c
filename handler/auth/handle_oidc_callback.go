package auth

import (
	"encoding/base64"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

func HandleOIDCCallback(
	cfg *config.Config,
	gsqlAuthenticator interfaces.GSQLAuthenticator,
	requestNewGsqlToken interfaces.RequestNewGsqlToken,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add authorization code flow support: https://graphsql.atlassian.net/wiki/spaces/TGDesignDocs/pages/3148480627/Design+for+OIDC+SSO+with+authorization+code+flow
		code := c.PostForm("code")
		idToken := c.PostForm("id_token")
		state := c.PostForm("state")

		if code == "" {
			code = c.Query("code")
		}
		if idToken == "" {
			idToken = c.Query("id_token")
		}
		if state == "" {
			state = c.Query("state")
		}

		log.Debugf(c, "code=%v, id_token=%v, state=%v", code, idToken, state)

		if code == "" && idToken == "" {
			mw.Abort(c, http.StatusBadRequest, "code or id_token is missing in OIDC response")
			return
		}

		if state == "" {
			mw.Abort(c, http.StatusBadRequest, "state is missing in OIDC response")
			return
		}

		stateBytes, err := base64.StdEncoding.DecodeString(state)
		if err != nil {
			log.Errorf(c, "Failed to decode state: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Failed to decode state")
			return
		}

		creds := &model.UserCredentials{
			Username: cfg.GetOIDCBuiltinUser(),
			Password: code,
		}
		// GSQL handles id_token as first priority
		if idToken != "" {
			creds.Password = idToken
		}

		info, err := gsqlAuthenticator(c, cfg, creds, true)
		if err != nil {
			log.Errorf("Failed to authenticate with GSQL: %v", err)
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		token, err := requestNewGsqlToken(c, creds.Username, creds.Password, "", int64(cfg.GetCookieDuration()))
		if err != nil {
			log.Warnf(c, "Failed to request new GSQL token: %v", err)
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		mw.SetCookie(c, token, cfg.GetCookieSameSite())
		c.Header("Location", string(stateBytes))
		mw.ReplyWithResult(c, http.StatusFound, "", info)
	}
}
