package auth

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/codec"
	"github.com/tigergraph/gus/lib/jwt"
	mw "github.com/tigergraph/gus/middleware"
)

func Login<PERSON>andler(
	GSQLAuthenticator interfaces.GSQLAuthenticator,
	authenticationService interfaces.AuthenticationService,
	securityChecker interfaces.SecurityChecker,
) func(c *gin.Context) {
	// LogIn authenticates the user and sets a cookie for subsequent verification.
	return func(c *gin.Context) {
		var creds model.UserCredentials
		if err := c.ShouldBindJSON(&creds); err != nil {
			log.Warnf(c, "Invalid payload: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
			return
		}

		cfg := mw.GetConfig(c)
		info, err := GSQLAuthenticator(c, cfg, &creds, true)
		if err != nil {
			log.Warnf(c, "Failed to authenticate with GSQL: %v", err)
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}
		loginResponse := &struct {
			*model.UserInfo
			SecurityRecommendations []string `json:"securityRecommendations"`
		}{
			UserInfo: info,
		}

		encryptedPwd, err := codec.AESCBCEncrypt(cfg.GetAuthToken(), creds.Password)
		if err != nil {
			log.Errorf(c, "Failed to encrypt password: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to encrypt password.")
			return
		}

		token := jwt.New(
			uuid.New(),
			mw.GetReqID(c),
			creds.Username,
			encryptedPwd,
			info.Name,
			time.Duration(cfg.GetCookieDuration())*time.Second,
		)
		if err != nil {
			log.Errorf(c, "Failed to generate JWT: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to generate JWT.")
			return
		}

		// stateful authentication & session management refactor,
		sessionToken, err := authenticationService.Register(c, token)
		if err != nil {
			log.Warnf("%v %+v\n", mw.GetReqID(c), err)
			mw.Abort(c, http.StatusInternalServerError, "failed to login")
			return
		}
		mw.SetCookie(c, sessionToken, cfg.GetCookieSameSite())

		// get security recommendations from gsql
		// ignore error and don't block login
		gsqlLoginResponse, _ := securityChecker(c, cfg, &creds)
		var securityRecommendations []string
		if gsqlLoginResponse != nil {
			securityRecommendations = getSecurityRecommendationList(gsqlLoginResponse.SecurityRecommendations)
		}
		loginResponse.SecurityRecommendations = securityRecommendations

		mw.ReplyWithResult(c, http.StatusOK, "", loginResponse)
	}
}

const CHANGE_DEFAULT_TG_PASSWORD = 0x1

func getSecurityRecommendationList(securityRecommendations int) []string {
	checkSecurityRecommendation := func(recommendation int) bool {
		return securityRecommendations&recommendation == recommendation
	}

	var recommendations []string
	if checkSecurityRecommendation(CHANGE_DEFAULT_TG_PASSWORD) {
		recommendations = append(recommendations, "Change default tigergraph user's password")
	}
	return recommendations
}
