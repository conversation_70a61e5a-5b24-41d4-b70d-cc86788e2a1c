package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

func LoginHandler(
	gsqlAuthenticator interfaces.GSQLAuthenticator,
	requestNewGsqlToken interfaces.RequestNewGsqlToken,
) func(c *gin.Context) {
	// LogIn authenticates the user and sets a cookie for subsequent verification.
	return func(c *gin.Context) {
		var creds model.UserCredentials
		if err := c.ShouldBindJSON(&creds); err != nil {
			log.Warnf(c, "Invalid payload: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
			return
		}

		cfg := mw.GetConfig(c)

		loginResponse, err := gsqlAuthenticator(c, cfg, &creds, true)
		if err != nil {
			log.Warnf(c, "Failed to authenticate with GSQL: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "GSQL authentication failed.")
			return
		}

		token, err := requestNewGsqlToken(c, creds.Username, creds.Password, "", int64(cfg.GetCookieDuration()))
		if err != nil {
			log.Warnf(c, "Failed to request new GSQL token: %v", err)
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		mw.SetCookie(c, token, cfg.GetCookieSameSite())
		mw.ReplyWithResult(c, http.StatusOK, "", loginResponse)
	}
}
