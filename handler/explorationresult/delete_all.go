package explorationresult

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// DeleteAll deletes all exploration results from a graph.
func DeleteAll(c *gin.Context) {
	graphName := c.Param("graphName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteAllExplorationResults(graphName); err != nil {
		log.Errorf(c, "Failed to delete all exploration results from graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete all exploration results from graph '%s'.", graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted all exploration results from graph '%s'.", graphName),
	)
}
