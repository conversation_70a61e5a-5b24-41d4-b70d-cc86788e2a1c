package explorationresult

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// Get returns an exploration result for a graph.
func Get(c *gin.Context) {
	graphName := c.Param("graphName")
	eName := c.<PERSON>m("explorationName")

	daoManager := dao.GetManager(c)
	exploration, err := daoManager.GetExplorationResult(graphName, eName)
	if err != nil {
		if errors.Is(err, db.ErrNotFound) {
			mw.Abort(
				c,
				http.StatusNotFound,
				fmt.Sprintf(
					"Exploration result '%s' for graph '%s' cannot be found.",
					eName,
					graphName,
				),
			)
		} else {
			log.Errorf(c, "Failed to get exploration result %s for graph %s: %v", eName, graphName, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to get exploration result '%s' for graph '%s'.", eName, graphName),
			)
		}
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", exploration)
}
