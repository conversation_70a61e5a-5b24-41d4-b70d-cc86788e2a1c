package explorationresult

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// Create creates an exploration result for a graph.
func Create(c *gin.Context) {
	graphName := c.Param("graphName")
	eName := c.Param("explorationName")

	var exploration model.ExplorationResult
	if err := c.ShouldBindJSON(&exploration); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.CreateExplorationResult(graphName, eName, exploration); err != nil {
		if err == db.ErrAlreadyExist {
			mw.Abort(
				c,
				http.StatusConflict,
				fmt.Sprintf("Exploration result '%s' already exists on graph '%s'.", eName, graphName),
			)
		} else {
			log.Errorf(c, "Failed to create exploration result %s for graph %s: %v", eName, graphName, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to create exploration result '%s' for graph '%s'.", eName, graphName),
			)
		}
		return
	}

	mw.Reply(
		c,
		http.StatusCreated,
		fmt.Sprintf("Successfully created exploration result '%s' for graph '%s'.", eName, graphName),
	)
}
