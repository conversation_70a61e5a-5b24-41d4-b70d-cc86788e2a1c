package explorationresult

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

// Upsert upserts an exploration result for a graph.
func Upsert(c *gin.Context) {
	graphName := c.Param("graphName")
	eName := c.Param("explorationName")

	var exploration model.ExplorationResult
	if err := c.ShouldBindJSON(&exploration); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	daoManager := dao.GetManager(c)
	err := daoManager.UpsertExplorationResult(graphName, eName, exploration)
	if err != nil {
		log.Errorf(c, "Failed to upsert exploration result %s for graph %s: %v", eName, graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to upsert exploration result '%s' for graph '%s'.", eName, graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf(
			"Successfully upserted exploration result '%s' for graph '%s'.",
			eName,
			graphName,
		),
	)
}
