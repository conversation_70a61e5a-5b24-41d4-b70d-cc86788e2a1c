package explorationresult

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// GetAll returns all exploration results for a graph.
func GetAll(c *gin.Context) {
	graphName := c.Param("graphName")

	daoManager := dao.GetManager(c)
	explorations, err := daoManager.GetAllExplorationResults(graphName)
	if err != nil {
		log.Errorf(c, "Failed to get all exploration results for graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to get all exploration results for graph '%s'.", graphName),
		)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", explorations)
}
