package explorationresult

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Delete deletes an exploration result from a graph.
func Delete(c *gin.Context) {
	graphName := c.Param("graphName")
	eName := c.Param("explorationName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteExplorationResult(graphName, eName); err != nil {
		log.Errorf(c, "Failed to delete exploration result %s from graph %s: %v", eName, graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete exploration result '%s' from graph '%s'.", eName, graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted exploration result '%s' from graph '%s'.", eName, graphName),
	)
}
