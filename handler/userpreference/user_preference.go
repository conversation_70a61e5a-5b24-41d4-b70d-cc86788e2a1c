package userpreference

import (
	"net/http"

	"encoding/json"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// UpsertUserPreference updates or creates a user's preference
func UpsertUserPreference(c *gin.Context) {
	userID := mw.GetUserInfo(c).Name

	var preference map[string]interface{}
	if err := c.ShouldBindJSON(&preference); err != nil {
		log.Errorf(c, "Failed to bind JSON: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Limit data size to 256KB
	const maxPreferenceSize = 256 * 1024 // 256KB
	jsonBytes, err := json.Marshal(preference)
	if err != nil {
		log.Errorf(c, "Failed to marshal preference: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to process user preference")
		return
	}
	if len(jsonBytes) > maxPreferenceSize {
		log.Errorf(c, "User preference too large: %d bytes", len(jsonBytes))
		mw.Abort(c, http.StatusBadRequest, "User preference data too large (max 256KB)")
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.UpsertUserPreference(userID, preference); err != nil {
		log.Errorf(c, "Failed to upsert user preference: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to upsert user preference")
		return
	}

	mw.Reply(c, http.StatusOK, "Successfully saved user preference")
}

// GetUserPreference retrieves a user's preference
func GetUserPreference(c *gin.Context) {
	userID := mw.GetUserInfo(c).Name

	daoManager := dao.GetManager(c)
	preference, err := daoManager.GetUserPreference(userID)
	if err != nil {
		log.Errorf(c, "Failed to get user preference: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get user preference")
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", preference)
}

// DeleteUserPreference deletes a user's preference
func DeleteUserPreference(c *gin.Context) {
	userID := mw.GetUserInfo(c).Name

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteUserPreference(userID); err != nil {
		log.Errorf(c, "Failed to delete user preference: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to delete user preference")
		return
	}

	mw.Reply(c, http.StatusOK, "Successfully deleted user preference")
}
