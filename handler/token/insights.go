package token

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/graphql"
	mw "github.com/tigergraph/gus/middleware"
)

func Create(c *gin.Context, insightService interfaces.InsightsService, deps graphql.Deps) {
	appId := c.Param("appId")
	queryRoot, err := graphql.NewQuery(deps,
		mw.GetUserInfo(c),
		mw.GetUserCredentials(c),
		mw.GetReqID(c).String(),
	)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	role, err := queryRoot.GetUserRoleForApp(c, appId)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	if role != graphql.Owner {
		mw.Abort(c, http.StatusForbidden, "only owner can create api_token")
		return
	}

	req := &struct {
		model.UserCredentials
	}{}

	if err := c.ShouldBindJSON(req); err != nil {
		mw.Abort(c, http.StatusBadRequest, "invalid payload: "+err.Error())
		return
	}

	apiToken, err := insightService.CreateToken(c, appId, &req.UserCredentials)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "success created api_token", apiToken)
}

func Delete(c *gin.Context, insightService interfaces.InsightsService, deps graphql.Deps) {
	appId := c.Param("appId")
	queryRoot, err := graphql.NewQuery(deps,
		mw.GetUserInfo(c),
		mw.GetUserCredentials(c),
		mw.GetReqID(c).String(),
	)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	role, err := queryRoot.GetUserRoleForApp(c, appId)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	if role != graphql.Owner {
		mw.Abort(c, http.StatusForbidden, "only owner can delete api_token")
		return
	}

	if err := insightService.DeleteToken(c, appId); err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "success deleted api_token", nil)
}

func Get(c *gin.Context, insightService interfaces.InsightsService, deps graphql.Deps) {

	appId := c.Param("appId")
	queryRoot, err := graphql.NewQuery(deps,
		mw.GetUserInfo(c),
		mw.GetUserCredentials(c),
		mw.GetReqID(c).String(),
	)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	role, err := queryRoot.GetUserRoleForApp(c, appId)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	if role != graphql.Owner {
		mw.Abort(c, http.StatusForbidden, "only owner can get api_token")
		return
	}

	apiToken, err := insightService.GetToken(c, appId)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "success get api_token", apiToken)
}
