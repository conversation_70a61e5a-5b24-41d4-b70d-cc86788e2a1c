package config

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"math/big"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	mw "github.com/tigergraph/gus/middleware"
)

type CertificateInfo struct {
	Country          string `json:"country" binding:"required"`
	Province         string `json:"province" binding:"required"`
	Locality         string `json:"locality" binding:"required"`
	Organization     string `json:"organization" binding:"required"`
	OrganizationUnit string `json:"organizationUnit" binding:"required"`
	CommonName       string `json:"commonName" binding:"required"`
}

type CertificateResult struct {
	Certificate string `json:"cert"`
	PrivateKey  string `json:"privateKey"`
}

// GenerateCert generates a self-signed certificate.
func GenerateCert(c *gin.Context) {
	var info CertificateInfo
	if err := c.Should<PERSON>ind<PERSON>(&info); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	priv, err := rsa.GenerateKey(rand.Reader, 4096)
	if err != nil {
		log.Errorf(c, "Failed to generate private key: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to generate private key.")
		return
	}

	serialNumberLimit := new(big.Int).Lsh(big.NewInt(1), 128)
	serialNumber, err := rand.Int(rand.Reader, serialNumberLimit)
	if err != nil {
		log.Errorf(c, "Failed to generate serial number: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to generate serial number.")
		return
	}

	template := generateCertTemplate(&info, serialNumber)
	certBytes, err := x509.CreateCertificate(rand.Reader, template, template, &priv.PublicKey, priv)
	if err != nil {
		log.Errorf(c, "Failed to create certificate: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to create certificate.")
		return
	}

	privBytes, err := x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		log.Errorf(c, "Failed to marshal private key: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to marshal private key.")
		return
	}

	result := &CertificateResult{
		Certificate: string(pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: certBytes})),
		PrivateKey:  string(pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: privBytes})),
	}
	mw.ReplyWithResult(c, http.StatusOK, "", result)
}

func generateCertTemplate(info *CertificateInfo, serialNumber *big.Int) *x509.Certificate {
	notBefore := time.Now()
	notAfter := notBefore.Add(365 * 24 * time.Hour)

	return &x509.Certificate{
		SerialNumber: serialNumber,
		Subject: pkix.Name{
			Country:            []string{info.Country},
			Province:           []string{info.Province},
			Locality:           []string{info.Locality},
			Organization:       []string{info.Organization},
			OrganizationalUnit: []string{info.OrganizationUnit},
			CommonName:         info.CommonName,
		},
		NotBefore: notBefore,
		NotAfter:  notAfter,

		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
	}
}
