package config

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"

	"github.com/tigergraph/gotools/log"
	mw "github.com/tigergraph/gus/middleware"
)

type ResponseHeader struct {
	FieldName  string
	FieldValue string
}

func validateResponseHeaders(values string) error {
	var headers []ResponseHeader
	if err := json.Unmarshal([]byte(values), &headers); err != nil {
		return err
	}

	for _, header := range headers {
		// CORS headers are not allowed
		if strings.HasPrefix(header.FieldName, "Access-Control-") {
			return fmt.Errorf("Access-Control-* headers are not allowed, please configure them through the Application Server (GUI) settings in AdminPortal.")
		}
	}

	return nil
}

// Set sets and applies configs for a list of config keys.
func Set(c *gin.Context, cntlrClient pb.ControllerClient) {
	var configs map[string]string
	if err := c.ShouldBindJSON(&configs); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	if len(configs) == 0 {
		mw.Abort(c, http.StatusBadRequest, "No configurations to apply.")
		return
	}

	if err := setConfigs(cntlrClient, configs); err != nil {
		log.Errorf(c, "Failed to set configs: %v", err)
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}

	if err := applyConfigs(cntlrClient); err != nil {
		log.Errorf(c, "Failed to apply configs: %v", err)
		mw.Abort(c, http.StatusInternalServerError, err.Error())

		if err := discardConfigs(cntlrClient); err != nil {
			log.Errorf(c, "Failed to discard configs: %v", err)
		}
		return
	}

	mw.Reply(c, http.StatusOK, "Successfully set and applied configurations.")
}

func setConfigs(cntlrClient pb.ControllerClient, configs map[string]string) error {

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	var entries []*pb.ConfigEntry
	for k, v := range configs {
		if k == "System.License" {
			req := &pb.SetConfigRequest{
				SpanId:  util.GenSpanId(util.SET_KEY),
				Entries: []*pb.ConfigEntry{{Key: k, Value: v}},
			}
			resp, err := cntlrClient.SetLicenseConfig(ctx, req)
			if err != nil {
				return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.SetLicenseConfig)
			}
			if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
				return tgErr.NewErrFromPbError(resp.GetError())
			}
		} else {
			if k == "Nginx.ResponseHeaders" {
				if err := validateResponseHeaders(v); err != nil {
					return err
				}
			}
			entries = append(entries, &pb.ConfigEntry{Key: k, Value: v})
		}

	}

	req := &pb.SetConfigRequest{
		SpanId:  util.GenSpanId(util.SET_KEY),
		Entries: entries,
	}
	resp, err := cntlrClient.SetConfig(ctx, req)
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.SetConfig)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

func applyConfigs(cntlrClient pb.ControllerClient) error {

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CONFIG_APPLY_TIMEOUT)
	defer cancel()

	resp, err := cntlrClient.ApplyConfig(ctx, &pb.ApplyConfigRequest{
		SpanId:  util.GenSpanId(util.APPLY_KEY),
		Force:   true,
		Initial: false,
	})
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.ApplyConfig)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

func discardConfigs(cntlrClient pb.ControllerClient) error {

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	resp, err := cntlrClient.DiscardChange(ctx, &pb.DiscardChangeRequest{
		SpanId: util.GenSpanId(util.DISCARD_KEY),
	})
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.DiscardChange)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}
