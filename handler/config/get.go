package config

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	tgCfg "github.com/tigergraph/cqrs/tutopia/common/config"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"

	"github.com/tigergraph/gotools/log"
	mw "github.com/tigergraph/gus/middleware"
)

// Get returns the configs for a list of config keys.
func Get(c *gin.Context, cntlrClient pb.ControllerClient) {
	configKeys := c.QueryArray("key")
	if len(configKeys) == 0 {
		mw.Abort(c, http.StatusBadRequest, "No configuration keys.")
		return
	}

	// reject if there are invalid keys
	if key := containInvalidKeys(mw.IsSuperUser(c), configKeys); key != "" {
		mw.Abort(c, http.StatusBadRequest, key+" is an invalid key")
		return
	}

	cfg, err := getConfigs(cntlrClient)
	if err != nil {
		log.Errorf(c, "Failed to get configs: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get configurations.")
		return
	}

	configs := make(map[string]interface{})
	for _, key := range configKeys {
		valueStr, err := cfg.GetEntryAsString(key, key != "System.License")
		if err != nil {
			log.Errorf(c, "Failed to get config value for key %s: %v", key, err)
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		var value interface{}
		if err := json.Unmarshal([]byte(valueStr), &value); err != nil {
			value = valueStr
		}
		configs[key] = value
	}

	mw.ReplyWithResult(c, http.StatusOK, "", configs)
}

func getConfigs(cntlrClient pb.ControllerClient) (*tgCfg.Config, error) {

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	resp, err := cntlrClient.PullConfig(ctx, &pb.PullConfigRequest{Type: pb.ConfigType_AppliedConfig})
	if err != nil {
		return nil, rpc.Error(err, tgServ.CONTROLLER, cntlrClient.PullConfig)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return nil, tgErr.NewErrFromPbError(resp.GetError())
	}

	return tgCfg.NewConfigFromPb(resp.Config)
}

// return the sensitive key for super user
func containSensitiveKeys(keys []string) string {
	sensitiveKeys := map[string]struct{}{
		strings.ToLower("System.AuthToken"):             {},
		strings.ToLower("GSQL.GithubUserAcessToken"):    {},
		strings.ToLower("Nginx.ConfigTemplate"):         {},
		strings.ToLower("System.S3.AWSAccessKeyID"):     {},
		strings.ToLower("System.S3.AWSSecretAccessKey"): {},
		strings.ToLower("System.License"):               {},
	}
	for _, key := range keys {
		lKey := strings.ToLower(key)
		_, ok := sensitiveKeys[lKey]
		if ok {
			return key
		}
	}
	return ""
}

// return key not in the allow list for non superuser
func containNotAllowedKeys(keys []string) string {
	allowedKeys := map[string]struct{}{
		strings.ToLower("GUI.ClientIdleTimeSec"):                 {},
		strings.ToLower("GUI.GraphStatCheckIntervalSec"):         {},
		strings.ToLower("GUI.EnableDarkTheme"):                   {},
		strings.ToLower("RESTPP.Factory.DefaultQueryTimeoutSec"): {},
		strings.ToLower("GPE.QueryLocalMemLimitMB"):              {},
		strings.ToLower("Nginx.Port"):                            {},
		strings.ToLower("System.HostList"):                       {},
		strings.ToLower("System.Backup.Local.Enable"):            {},
		strings.ToLower("GSE.BasicConfig.Nodes"):                 {},
	}
	for _, key := range keys {
		lKey := strings.ToLower(key)
		_, ok := allowedKeys[lKey]
		if !ok {
			return key
		}
	}
	return ""
}

func containInvalidKeys(isSuperUser bool, keys []string) string {
	if isSuperUser {
		return containSensitiveKeys(keys)
	}
	return containNotAllowedKeys(keys)
}
