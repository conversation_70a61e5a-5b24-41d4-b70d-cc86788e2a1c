package config

import "testing"

func TestContainInvalidKeys(t *testing.T) {
	type args struct {
		isSuperUser bool
		keys        []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "invalid keys for super user",
			args: args{
				isSuperUser: true,
				keys:        []string{"System.AuthToken"},
			},
			want: "System.AuthToken",
		},
		{
			name: "valid keys for super user",
			args: args{
				isSuperUser: true,
				keys:        []string{"GUI.EnableDarkTheme"},
			},
			want: "",
		},
		{
			name: "valid keys for non super user",
			args: args{
				isSuperUser: false,
				keys:        []string{"GUI.EnableDarkTheme"},
			},
			want: "",
		},
		{
			name: "invalid keys for non super user",
			args: args{
				isSuperUser: false,
				keys:        []string{"System.AuthToken"},
			},
			want: "System.AuthToken",
		},
		{
			name: "empty keys",
			args: args{
				isSuperUser: true,
				keys:        []string{},
			},
			want: "",
		},
		{
			name: "nil keys",
			args: args{
				isSuperUser: true,
				keys:        nil,
			},
			want: "",
		},
		{
			name: "mixed keys for super user",
			args: args{
				isSuperUser: true,
				keys:        []string{"GUI.EnableDarkTheme", "System.AuthToken"},
			},
			want: "System.AuthToken",
		},
		{
			name: "mixed keys for non super user",
			args: args{
				isSuperUser: false,
				keys:        []string{"GUI.EnableDarkTheme", "System.AuthToken"},
			},
			want: "System.AuthToken",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := containInvalidKeys(tt.args.isSuperUser, tt.args.keys); got != tt.want {
				t.Errorf("containInvalidKeys() = %v, want %v", got, tt.want)
			}
		})
	}
}
