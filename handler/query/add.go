package query

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

type PostQuery struct {
	Name         string `json:"name"`
	ErrorMessage string `json:"errorMessage"`
}

type QueryInfo struct {
	Name     string `json:"name"`
	NeedDrop bool   `json:"needDrop"`
}

// Add adds queries for a graph.
func Add(daoManager interfaces.DaoManager, gsqlClient interfaces.RequestGSQLClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		var (
			queryInfoArray           []QueryInfo
			queryNames               []string
			shouldBeDeleteQueryNames []string
		)

		graphName := c.Param("graphName")
		err := c.ShouldBind(&queryInfoArray)
		if err != nil {
			mw.ReplyWithResult(c, http.StatusBadRequest, "Invalid payload.", nil)
			return
		}

		for _, queryInfo := range queryInfoArray {
			if queryInfo.NeedDrop {
				shouldBeDeleteQueryNames = append(shouldBeDeleteQueryNames, queryInfo.Name)
			}
			queryNames = append(queryNames, queryInfo.Name)
		}

		hasDraft, hasNoDraft := checkQueryDrafts(daoManager, graphName, queryNames)
		if len(hasDraft) == 0 {
			mw.ReplyWithResult(c, http.StatusBadRequest, "No query drafts to be added.", nil)
			return
		}

		// Try to delete the given queries.
		// Deleting a draft query not yet known to GSQL will always fail,
		// so should not abort at this point.
		// Even if there's an unexpected error from GSQL,
		// it's okay not to abort because the query won't be added in the next step anyway.
		_ = deleteQueriesFromGSQL(gsqlClient, c, graphName, shouldBeDeleteQueryNames)

		success, failed := addQueryDraftsWithRetry(daoManager, gsqlClient, c, graphName, queryNames)
		result := map[string][]PostQuery{
			"success": success,
			"failed":  append(hasNoDraft, failed...),
		}

		mw.ReplyWithResult(c, http.StatusOK, "", result)
	}
}

func checkQueryDrafts(
	daoManager interfaces.DaoManager,
	graphName string,
	queryNames []string,
) (hasDraft []PostQuery, hasNoDraft []PostQuery) {
	hasDraft = make([]PostQuery, 0)
	hasNoDraft = make([]PostQuery, 0)

	for _, name := range queryNames {
		if _, err := daoManager.GetQueryDraft(graphName, name); err == nil {
			hasDraft = append(hasDraft, PostQuery{
				Name:         name,
				ErrorMessage: "",
			})
		} else {
			hasNoDraft = append(hasNoDraft, PostQuery{
				Name:         name,
				ErrorMessage: strings.TrimSpace(err.Error()),
			})
		}
	}

	return
}

func addQueryDraftsWithRetry(
	daoManager interfaces.DaoManager,
	gsqlClient interfaces.RequestGSQLClient,
	c *gin.Context,
	graphName string,
	queryNames []string,
) (success, failed []PostQuery) {
	success = make([]PostQuery, 0)
	for len(queryNames) > 0 {
		added, needToAdd := addQueryDraftsToGSQL(daoManager, gsqlClient, c, graphName, queryNames)
		if len(added) == 0 {
			failed = needToAdd
			break
		}

		for _, postQuery := range added {
			success = append(success, postQuery)

			for i, n := range queryNames {
				if n == postQuery.Name {
					queryNames = append(queryNames[:i], queryNames[i+1:]...)
					break
				}
			}
		}
	}

	return
}

func addQueryDraftsToGSQL(
	daoManager interfaces.DaoManager,
	gsqlClient interfaces.RequestGSQLClient,
	c *gin.Context,
	graphName string,
	queryNames []string,
) ([]PostQuery, []PostQuery) {
	added := make([]PostQuery, 0)
	failed := make([]PostQuery, 0)
	for _, name := range queryNames {
		draft, err := daoManager.GetQueryDraft(graphName, name)
		if err != nil {
			log.Errorf(c, "Failed to get query draft %s for graph %s: %v", name, graphName, err)
			continue
		}

		output, err := gsqlClient(
			c,
			mw.GetConfig(c),
			graphName,
			mw.GetUsername(c),
			mw.GetPassword(c),
			draft.Code,
		)
		if err != nil {
			errorMessage := strings.TrimSpace(string(output))
			log.Errorf(c,
				"Failed to add query draft %s for graph %s: %v: %s",
				name,
				graphName,
				err,
				errorMessage,
			)
			failed = append(failed, PostQuery{
				Name:         name,
				ErrorMessage: errorMessage,
			})
			continue
		}
		added = append(added, PostQuery{
			Name:         name,
			ErrorMessage: "",
		})
	}

	return added, failed
}
