package query

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao"
	h "github.com/tigergraph/gus/lib/http"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// Get returns a query's info for a graph.
func Get(c *gin.Context) {
	graphName := c.Param("graphName")
	queryName := c.Param("queryName")

	queryInfo, err := getQueryInfoFromGSQL(c, graphName, queryName)
	if err != nil {
		return
	}

	daoManager := dao.GetManager(c)
	draft, err := daoManager.GetQueryDraft(graphName, queryName)
	if err != nil {
		if errors.Is(err, db.ErrNotFound) {
			if queryInfo == nil {
				mw.Abort(
					c,
					http.StatusNotFound,
					fmt.Sprintf("Query '%s' for graph '%s' cannot be found.", queryName, graphName),
				)
				return
			}
		} else {
			log.Errorf(c, "Failed to get query draft %s for graph %s: %v", queryName, graphName, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to get query draft '%s' for graph '%s'.", queryName, graphName),
			)
			return
		}
	}

	if queryInfo == nil {
		queryInfo = &Info{
			CallerQueries: make([]string, 0),
			Endpoint:      struct{}{},
		}
	}
	if draft != nil {
		queryInfo.Draft = draft.Code
		queryInfo.Syntax = draft.Syntax
	}
	mw.ReplyWithResult(c, http.StatusOK, "", queryInfo)
}

func getQueryInfoFromGSQL(c *gin.Context, graphName, queryName string) (*Info, error) {
	cfg := mw.GetConfig(c)
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		"/gsql/v1/queries/info",
	)

	query := make(url.Values)
	query.Add("graph", graphName)
	query.Add("query", queryName)

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, requestURL, nil)
	req.URL.RawQuery = query.Encode()
	creds := mw.GetUserCredentials(c)
	h.SetCredentials(req, creds)
	h.SetFromGraphStudio(req)
	// Add X-Forwarded-For
	h.SetForwardedForFromContext(c, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})

	var responseError = "Failed to get query's information from GSQL server."
	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(c, "Failed to get response from GSQL server: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			responseError,
		)
		return nil, errors.New(responseError)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from GSQL server: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			responseError,
		)
		return nil, errors.New(responseError)
	}

	type queryResponse struct {
		Error   bool   `json:"error"`
		Message string `json:"message"` // use generic because this can be an object.
		Results []Info `json:"results"`
	}

	res := queryResponse{}
	if err := json.Unmarshal(body, &res); err != nil {
		log.Errorf(c, "Failed to parse response from GSQL server: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			responseError,
		)
		return nil, errors.New(responseError)
	}

	if res.Error {
		mw.Abort(
			c,
			resp.StatusCode,
			responseError,
		)
		return nil, errors.New(res.Message)
	}
	return &res.Results[0], nil
}
