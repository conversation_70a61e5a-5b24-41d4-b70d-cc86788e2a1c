package query

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// DeleteAllQueryDraft deletes all query drafts from a graph.
func DeleteAllQueryDraft(c *gin.Context) {
	graphName := c.Param("graphName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteAllQueryDraft(graphName); err != nil {
		log.Errorf(c, "Failed to delete all query drafts from graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete all query drafts from graph '%s'.", graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted all query drafts from graph '%s'.", graphName),
	)
}
