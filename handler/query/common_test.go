package query

import "testing"

func TestNo<PERSON>atchReplace(t *testing.T) {
	tests := []struct {
		queryContent string
		expected     bool
	}{
		{"CREATE OR REPLACE OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb {RETURN \"cypher works!\"}", true},
		{"CREATE OR REPLACE QUERY cypher() FOR GRAPH ldbc_snb {RETURN \"cypher works!\"}", true},
		{"CREATE OR REPLACE BATCH QUERY cypher() FOR GRAPH ldbc_snb {RETURN \"cypher works!\"}", true},
		{"CREATE OR REPLACE BATCH OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb {RETURN \"cypher works!\"}", true},
		{"CREATE OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb {RETURN \"cypher works!\"}", false},
	}

	for _, test := range tests {
		if result := isCreateOrReplaceQuery(test.queryContent); result != test.expected {
			t.<PERSON>("noMatchReplace(%q) = %t, expected %t", test.queryContent, result, test.expected)
		}
	}
}

func TestRemoveCommentsInCode(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "single-line comment",
			input:    "CREATE OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb { # Write query logic here",
			expected: "CREATE OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb { ",
		},
		{
			name:     "multi-line comment",
			input:    "CREATE OPENCYPHER QUERY cypher(/* Parameters \nhere */) FOR GRAPH ldbc_snb { RETURN \"cypher works!\" }",
			expected: "CREATE OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb { RETURN \"cypher works!\" }",
		},
		{
			name:     "single-line and multi-line comment",
			input:    "CREATE OPENCYPHER QUERY cypher(/* Parameters \nhere */) FOR GRAPH ldbc_snb { # Write query logic here",
			expected: "CREATE OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb { ",
		},
		{
			name:     "no comment",
			input:    "CREATE OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb { RETURN \"cypher works!\" }",
			expected: "CREATE OPENCYPHER QUERY cypher() FOR GRAPH ldbc_snb { RETURN \"cypher works!\" }",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if result := removeCommentsInCode(tc.input); result != tc.expected {
				t.Errorf("removeCommentsInCode(%q) = %q, expected %q", tc.input, result, tc.expected)
			}
		})
	}
}
