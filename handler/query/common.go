package query

type Info struct {
	Name           string      `json:"name"`
	Syntax         string      `json:"syntax"`
	Draft          string      `json:"draft"`
	Code           string      `json:"code"`
	Installed      bool        `json:"installed"`
	Enabled        bool        `json:"enabled"`
	GraphUpdate    bool        `json:"graphUpdate"`
	Installing     bool        `json:"installing"`
	OptimizedLevel int         `json:"optimizedLevel"`
	CallerQueries  []string    `json:"callerQueries"`
	Endpoint       interface{} `json:"endpoint"`
	IsHidden       bool        `json:"isHidden"`
	IsACLSpecified bool        `json:"isACLSpecified"`
}
