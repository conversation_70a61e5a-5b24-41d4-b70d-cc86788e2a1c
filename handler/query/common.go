package query

import (
	"errors"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

type Info struct {
	Name           string      `json:"name"`
	Syntax         string      `json:"syntax"`
	Draft          string      `json:"draft"`
	Code           string      `json:"code"`
	Installed      bool        `json:"installed"`
	Enabled        bool        `json:"enabled"`
	GraphUpdate    bool        `json:"graphUpdate"`
	Installing     bool        `json:"installing"`
	InstallMode    string      `json:"installMode"`
	OptimizedLevel int         `json:"optimizedLevel"`
	CallerQueries  []string    `json:"callerQueries"`
	Endpoint       interface{} `json:"endpoint"`
	IsHidden       bool        `json:"isHidden"`
	IsACLSpecified bool        `json:"isACLSpecified"`
}

func CreateGSQLQuery(gsqlClient interfaces.RequestGSQLClient, c *gin.Context, graphName string, code string) error {
	output, err := gsqlClient(
		c,
		mw.GetConfig(c),
		graphName,
		mw.GetUserCredentials(c),
		code,
	)
	msg := strings.TrimSpace(string(output))
	if err != nil && !strings.Contains(msg, "Saved as draft") {
		return errors.New(msg)
	}
	return nil
}

func isCreateOrReplaceQuery(queryContent string) bool {
	pattern := "^\\s*create\\s+(or\\s+replace)\\s+(?:batch\\s+)?(?:distributed\\s+)?(?:opencypher\\s+)?query\\s+([^\\s]+)\\s*\\("
	re := regexp.MustCompile(pattern)
	return re.MatchString(strings.ToLower(removeCommentsInCode(queryContent)))
}

func removeCommentsInCode(queryContent string) string {
	multiLineCommentPattern := regexp.MustCompile(`/\*(.|\n)*?\*/`)
	singleLineCommentPattern := regexp.MustCompile(`(\/\/|#).*`)
	queryContent = multiLineCommentPattern.ReplaceAllString(queryContent, "")
	queryContent = singleLineCommentPattern.ReplaceAllString(queryContent, "")
	return queryContent
}

func deleteLegacyQueryDraft(c *gin.Context, graphName string, queryName string) error {
	daoManager := dao.GetManager(c)
	return daoManager.DeleteQueryDraft(graphName, queryName)
}
