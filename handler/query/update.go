package query

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

// Update upserts a query draft for a graph.
func Update(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")
	queryName := c.Param("queryName")

	var draft model.QueryDraft
	if err := c.ShouldBindJSON(&draft); err != nil {
		log.Warnf(c, "Invalid payload: %+v", err)
		mw.ReplyWithResult(c, http.StatusBadRequest,
			"Invalid payload.", nil)
		return
	}

	needDrop := !isCreateOrReplaceQuery(draft.Code)
	if needDrop {
		deleteQueriesFromGSQL(gsqlClient, c, graphName, []string{queryName})
	}
	err := CreateGSQLQuery(gsqlClient, c, graphName, draft.Code)
	if err != nil {
		log.Errorf(c, "Failed to create query %s for graph %s: %v", draft.Name, graphName, err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}
	// for old version, there will be a draft saved in etcd
	// need to delete it after draft was add to gsql
	deleteLegacyQueryDraft(c, graphName, queryName)

	mw.ReplyWithResult(c, http.StatusOK, "", fmt.Sprintf(
		"Successfully update query '%s' for graph '%s'.",
		queryName,
		graphName,
	))
}
