package query

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

// Create creates a query draft for a graph.
func Create(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")
	queryName := c.Param("queryName")

	var draft model.QueryDraft
	if err := c.ShouldBindJSON(&draft); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.ReplyWithResult(c, http.StatusBadRequest, "Invalid payload.", nil)
		return
	}

	err := CreateGSQLQuery(gsqlClient, c, graphName, draft.Code)
	if err != nil {
		log.Errorf(c, "Failed to create query %s for graph %s: %v", queryName, graphName, err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", fmt.Sprintf("Successfully created query '%s' for graph '%s'.", queryName, graphName))
}
