package query

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// CreateDraft creates a query draft for a graph.
func CreateDraft(c *gin.Context) {
	graphName := c.Param("graphName")
	queryName := c.Param("queryName")

	var draft model.QueryDraft
	if err := c.ShouldBindJSON(&draft); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.ReplyWithResult(c, http.StatusBadRequest, "Invalid payload.", nil)
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.CreateQueryDraft(graphName, queryName, draft); err != nil {
		if err == db.ErrAlreadyExist {
			errMessage := fmt.Sprintf("Query draft '%s' already exists on graph '%s'.", queryName, graphName)
			mw.ReplyWithResult(c, http.StatusBadRequest, errMessage, nil)
		} else {
			log.Errorf(c, "Failed to create query draft %s for graph %s: %v", queryName, graphName, err)
			errMsg := fmt.Sprintf("Failed to create query draft '%s' for graph '%s'.", queryName, graphName)
			mw.ReplyWithResult(c, http.StatusInternalServerError, errMsg, nil)
		}
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", fmt.Sprintf("Successfully created query draft '%s' for graph '%s'.", queryName, graphName))
}
