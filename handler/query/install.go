package query

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// InstallRequest represents the request body for installing queries
type InstallRequest struct {
	QueryNames []string `json:"queryNames"`
	InstallAll bool     `json:"installAll"`
}

// Install installs queries for a graph.
func Install(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")

	var req InstallRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.ReplyWithResult(c, http.StatusBadRequest, "Invalid payload.", nil)
		return
	}

	queryNames := req.QueryNames
	installAll := req.InstallAll

	var cmd string
	if installAll {
		cmd = "INSTALL QUERY *"
	} else {
		if len(queryNames) == 0 {
			mw.ReplyWithResult(c, http.StatusBadRequest, "No queries to install.", nil)
			return
		}

		// parse queryNames
		// to make sure that
		// only valid identifiers exist, avoid query injection
		// todo: let GSQL team to implement an InstallQuery API
		if err := ParseQueryNames(queryNames); err != nil {
			mw.ReplyWithResult(
				c,
				http.StatusBadRequest,
				err.Error(),
				nil,
			)
			return
		}

		cmd = fmt.Sprintf("INSTALL QUERY %s", strings.Join(queryNames, ", "))
	}

	output, err := gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUsername(c), mw.GetPassword(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Warnf(c, "Failed to install queries for graph %s: %+v: %s", graphName, err, cleanOutput)
		mw.ReplyWithResult(c, http.StatusInternalServerError, cleanOutput, nil)
		return
	}

	success := make([]string, 0)
	failed := make([]string, 0)
	daoManager := dao.GetManager(c)
	for _, queryName := range queryNames {
		if err := daoManager.DeleteQueryDraft(graphName, queryName); err != nil {
			log.Errorf(c, "Failed to delete query draft %s from graph %s: %v", queryName, graphName, err)
			failed = append(failed, queryName)
		} else {
			success = append(success, queryName)
		}
	}

	result := map[string][]string{
		"success":             success,
		"failedToDeleteDraft": failed,
	}
	mw.ReplyWithResult(c, http.StatusOK, "", result)
}

func ParseQueryNames(queryNames []string) error {
	reg := regexp.MustCompile("^[_|a-zA-Z]+[a-zA-Z|_|0-9]*$")
	for _, name := range queryNames {
		// https://docs.tigergraph.com/gsql-ref/current/appendix/complete-formal-syntax#_gsql_query_language_ebnf
		matched := reg.MatchString(name)
		if !matched {
			return errors.Errorf("%v is invalid query name", name)
		}
	}
	return nil
}
