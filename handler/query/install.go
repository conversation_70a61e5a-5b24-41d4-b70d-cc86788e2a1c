package query

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// InstallRequest represents the request body for installing queries
type InstallRequest struct {
	QueryNames []string `json:"queryNames"`
	InstallAll bool     `json:"installAll"`
}

// Install installs queries for a graph.
func Install(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")

	var queryNames []string
	var installAll bool

	// Try to get parameters from request body first (new way)
	var req InstallRequest
	if err := c.ShouldBindJSON(&req); err == nil && (len(req.QueryNames) > 0 || req.InstallAll) {
		// Use request body parameters
		queryNames = req.QueryNames
		installAll = req.InstallAll
	} else {
		// Fall back to query string parameters (old way for backward compatibility)
		queryNames = c.QueryArray("queryName")
		installAll = c.Query("installAll") == "true"
	}

	var cmd string
	if installAll {
		cmd = "INSTALL QUERY *"
	} else {
		if len(queryNames) == 0 {
			mw.ReplyWithResult(c, http.StatusBadRequest, "No queries to install.", nil)
			return
		}
		// parse queryNames
		// to make sure that
		// only valid identifiers exist, avoid query injection
		// todo: let GSQL team to implement an InstallQuery API
		if err := ParseQueryNames(queryNames); err != nil {
			mw.ReplyWithResult(
				c,
				http.StatusBadRequest,
				err.Error(),
				nil,
			)
			return
		}

		cmd = fmt.Sprintf("INSTALL QUERY %s", strings.Join(queryNames, ", "))
	}

	output, err := gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUserCredentials(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Warnf(c, "Failed to install queries for graph %s: %+v: %s", graphName, err, cleanOutput)
		mw.ReplyWithResult(c, http.StatusInternalServerError, cleanOutput, nil)
		return
	}

	// Get latest queries info from GSQL to check which queries are actually installed
	queriesInfo, err := getAllQueriesInfoFromGSQL(c, graphName)
	if err != nil {
		log.Warnf(c, "Failed to get queries info from GSQL after installation: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to verify installation status.", nil)
		return
	}

	// Create a map for quick lookup of installed queries
	installedQueries := make(map[string]bool)
	for _, queryInfo := range queriesInfo {
		if queryInfo.Installed {
			installedQueries[queryInfo.Name] = true
		}
	}

	// Only delete drafts for queries that are confirmed to be installed
	success := make([]string, 0)
	failed := make([]string, 0)
	daoManager := dao.GetManager(c)
	for _, queryName := range queryNames {
		if installedQueries[queryName] {
			// Query is installed, try to delete its draft
			if err := daoManager.DeleteQueryDraft(graphName, queryName); err != nil {
				log.Errorf(c, "Failed to delete query draft %s from graph %s: %v", queryName, graphName, err)
				failed = append(failed, queryName)
			} else {
				success = append(success, queryName)
			}
		} else {
			// Query is not installed, add to failed list
			log.Warnf(c, "Query %s was not installed successfully", queryName)
			failed = append(failed, queryName)
		}
	}

	result := map[string][]string{
		"success":             success,
		"failedToDeleteDraft": failed,
	}
	mw.ReplyWithResult(c, http.StatusOK, "", result)
}

func ParseQueryNames(queryNames []string) error {
	reg := regexp.MustCompile("^[_|a-zA-Z]+[a-zA-Z|_|0-9]*$")
	for _, name := range queryNames {
		// https://docs.tigergraph.com/gsql-ref/current/appendix/complete-formal-syntax#_gsql_query_language_ebnf
		matched := reg.MatchString(name)
		if !matched {
			return errors.Errorf("%v is invalid query name", name)
		}
	}
	return nil
}
