package query

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Install installs queries for a graph.
func Install(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")
	queryNames := c.QueryArray("queryName")
	installAll := c.Query("installAll") == "true"

	var cmd string
	if installAll {
		cmd = "INSTALL QUERY *"
	} else {
		if len(queryNames) == 0 {
			mw.ReplyWithResult(c, http.StatusBadRequest, "No queries to install.", nil)
			return
		}

		// parse queryNames
		// to make sure that
		// only valid identifiers exist, avoid query injection
		// todo: let GSQL team to implement an InstallQuery API
		if err := ParseQueryNames(queryNames); err != nil {
			mw.ReplyWithResult(
				c,
				http.StatusBadRequest,
				err.Error(),
				nil,
			)
			return
		}

		cmd = fmt.Sprintf("INSTALL QUERY %s", strings.Join(queryNames, ", "))
	}

	output, err := gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUsername(c), mw.GetPassword(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Warnf(c, "Failed to install queries for graph %s: %+v: %s", graphName, err, cleanOutput)
		mw.ReplyWithResult(c, http.StatusInternalServerError, cleanOutput, nil)
		return
	}

	installedQueries := parseInstalledQueriesFromOutput(cleanOutput)

	success := make([]string, 0)
	failed := make([]string, 0)
	daoManager := dao.GetManager(c)
	for _, queryName := range installedQueries {
		if err := daoManager.DeleteQueryDraft(graphName, queryName); err != nil {
			log.Errorf(c, "Failed to delete query draft %s from graph %s: %v", queryName, graphName, err)
			failed = append(failed, queryName)
		} else {
			success = append(success, queryName)
		}
	}

	result := map[string][]string{
		"success":             success,
		"failedToDeleteDraft": failed,
	}
	mw.ReplyWithResult(c, http.StatusOK, "", result)
}

func ParseQueryNames(queryNames []string) error {
	reg := regexp.MustCompile("^[_|a-zA-Z]+[a-zA-Z|_|0-9]*$")
	for _, name := range queryNames {
		// https://docs.tigergraph.com/gsql-ref/current/appendix/complete-formal-syntax#_gsql_query_language_ebnf
		matched := reg.MatchString(name)
		if !matched {
			return errors.Errorf("%v is invalid query name", name)
		}
	}
	return nil
}

func parseInstalledQueriesFromOutput(output string) []string {
	pattern := `Installed queries:\s*\[([^\]]+)\]`
	re := regexp.MustCompile(pattern)

	matches := re.FindStringSubmatch(output)
	if len(matches) < 2 {
		return []string{}
	}

	// Extract the query names from the captured group
	queryListStr := strings.TrimSpace(matches[1])
	if queryListStr == "" {
		return []string{}
	}

	// Split by comma and trim whitespace from each query name
	queryNames := strings.Split(queryListStr, ",")
	result := make([]string, 0, len(queryNames))
	for _, name := range queryNames {
		trimmedName := strings.TrimSpace(name)
		if trimmedName != "" {
			result = append(result, trimmedName)
		}
	}

	return result
}
