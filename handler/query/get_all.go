package query

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao"
	h "github.com/tigergraph/gus/lib/http"
	mw "github.com/tigergraph/gus/middleware"
)

// GetAll returns all queries' info for a graph.
func GetAll(c *gin.Context) {
	graphName := c.Param("graphName")

	queriesInfo, err := getAllQueriesInfoFromGSQL(c, graphName)
	if err != nil {
		return
	}

	infoIndex := make(map[string]int)
	for i, queryInfo := range queriesInfo {
		infoIndex[queryInfo.Name] = i
	}

	daoManager := dao.GetManager(c)
	drafts, err := daoManager.GetAllQueryDrafts(graphName)
	if err != nil {
		log.Errorf(c, "Failed to get all query drafts for graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to get all query drafts for graph '%s'.", graphName),
		)
		return
	}

	for _, draft := range drafts {
		if i, ok := infoIndex[draft.Name]; ok {
			queriesInfo[i].Draft = draft.Code
		} else {
			queryInfo := Info{
				Name:          draft.Name,
				Draft:         draft.Code,
				Syntax:        draft.Syntax,
				GraphUpdate:   draft.GraphUpdate,
				CallerQueries: make([]string, 0),
				Endpoint:      struct{}{},
			}
			queriesInfo = append(queriesInfo, queryInfo)
		}
	}

	mw.ReplyWithResult(c, http.StatusOK, "", queriesInfo)
}

func getAllQueriesInfoFromGSQL(c *gin.Context, graphName string) ([]Info, error) {
	cfg := mw.GetConfig(c)
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		"/gsql/v1/queries/info",
	)

	query := make(url.Values)
	query.Add("graph", graphName)
	query.Add("status", "ALL")

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, requestURL, nil)
	req.URL.RawQuery = query.Encode()
	creds := mw.GetUserCredentials(c)
	h.SetCredentials(req, creds)
	h.SetFromGraphStudio(req)
	// Add X-Forwarded-For
	h.SetForwardedForFromContext(c, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})

	var errorMsg = "Failed to get queries' information from GSQL server."
	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(c, "Failed to get response from GSQL server: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			errorMsg,
		)
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from GSQL server: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			errorMsg,
		)
		return nil, err
	}

	type queryResponse struct {
		Results []Info
		Error   bool        `json:"error"`
		Message interface{} `json:"message"`
	}
	res := queryResponse{}
	if err := json.Unmarshal(body, &res); err != nil {
		log.Errorf(c, "Failed to parse response from GSQL server: %v", err)
		return make([]Info, 0), nil
	}
	if res.Error {
		mw.Abort(
			c,
			resp.StatusCode,
			cast.ToString(res.Message),
		)
		return nil, errors.New(cast.ToString(res.Message))
	}
	return res.Results, nil
}
