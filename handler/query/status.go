package query

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"path"
	"sync"

	"github.com/gin-gonic/gin"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	h "github.com/tigergraph/gus/lib/http"
	tgJSON "github.com/tigergraph/gus/lib/json"
	mw "github.com/tigergraph/gus/middleware"
	"go.uber.org/multierr"
)

type Status struct {
	RequestId      string                       `json:"requestid"`
	StartTime      string                       `json:"startTime"`
	ExpirationTime string                       `json:"expirationTime"`
	Url            string                       `json:"url"`
	ElapsedTime    int                          `json:"elapsedTime"`
	Status         string                       `json:"status"`
	Results        interface{}                  `json:"results"`
	ProcessLogs    map[string][]fs.SearchResult `json:"processLogs"`
}

type queryStatus struct {
	Error   bool      `json:"error"`
	Message string    `json:"message"`
	Results []*Status `json:"results"`
}

func QueryStatus(c *gin.Context) {
	cfg := mw.GetConfig(c)

	requestId := c.Request.FormValue("requestid")
	if requestId == "" {
		mw.ReplyWithResult(c, http.StatusBadRequest, "RequestId required.", nil)
		return
	}

	graphName := c.Param("graphName")
	if graphName == "" {
		mw.ReplyWithResult(c, http.StatusBadRequest, "Graph name required.", nil)
		return
	}

	sts, err := queryStatusAndResult(graphName, requestId, c)
	if err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, fmt.Sprintf("Error getting query status:%v.", err), nil)
		return
	}

	// Get query result and logs
	wg := &sync.WaitGroup{}
	mtx := &sync.Mutex{}
	var errors error
	for i := range sts {
		st := sts[i]
		wg.Add(1)
		go func(st *Status) {
			defer wg.Done()
			if err := fillProcessLogs(st, cfg); err != nil {
				mtx.Lock()
				defer mtx.Unlock()
				errors = multierr.Append(errors, err)
				return
			}
		}(st)
	}
	wg.Wait()

	if errors != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, fmt.Sprintf("Failed to get query status:%v.", errors), nil)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", sts)
}

// query status and result
func queryStatusAndResult(graphName, requestId string, c *gin.Context) (sts []*Status, err error) {
	cfg := mw.GetConfig(c)
	hosts, err := cfg.GetAllHostnamesForService(tgServ.RESTPP)
	if err != nil {
		return nil, err
	}

	log.Debugf(c, "queryStatusAndResult all hosts:%v", hosts)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal RESTPP requests.
	})

	qst := queryStatus{}
	mtx := &sync.Mutex{}
	wg := &sync.WaitGroup{}
	success := false
	actualHost := ""
	for i := range hosts {
		restPPHostName := hosts[i]
		wg.Add(1)
		go func(host string) {
			defer wg.Done()
			// Get query status from restpp
			restPPQueryStatusRequestURL := fmt.Sprintf(
				"http://%s:%d%s?requestid=%s&graph_name=%s",
				host,
				cfg.GetConfig().ProtoConf.GetRESTPP().GetNginxPort(),
				"/query_status",
				requestId, graphName,
			)

			queryStatusReq, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, restPPQueryStatusRequestURL, nil)
			h.SetFromGraphStudio(queryStatusReq)
			h.SetForwardedForFromContext(c, queryStatusReq)
			queryStatusResp, err := client.Do(queryStatusReq)

			if err != nil {
				log.Errorf(c, "Failed to get response from RESTPP: %v", err)
				return
			}
			defer queryStatusResp.Body.Close()
			body, err := io.ReadAll(queryStatusResp.Body)
			if err != nil {
				log.Errorf(c, "Failed to read response from RESTPP: %v", err)
				return
			}

			mtx.Lock()
			defer mtx.Unlock()
			if success {
				return
			}
			if err := tgJSON.Unmarshal(body, &qst); err != nil {
				log.Errorf(c, "Failed to parse response from RESTPP: %v", err)
				return
			}

			if qst.Error {
				return
			}
			if len(qst.Results) > 0 {
				success = true
				actualHost = host
			}
		}(restPPHostName)
	}
	wg.Wait()

	var errs error
	for i := range qst.Results {
		st := qst.Results[i]
		wg.Add(1)
		go func(st *Status) {
			defer wg.Done()
			if err := fillQueryResults(st, actualHost, c); err != nil {
				mtx.Lock()
				defer mtx.Unlock()
				errs = multierr.Append(errs, err)
				return
			}
		}(st)
	}

	if errs != nil {
		return nil, errs
	}

	if len(qst.Results) == 0 {
		return nil, fmt.Errorf("no query status found for requestId:%s", requestId)
	}

	return qst.Results, nil
}

func fillQueryResults(st *Status, restPPHostName string, c *gin.Context) error {
	cfg := mw.GetConfig(c)
	if st.Status != "success" {
		return nil
	}

	requestId := st.RequestId
	// Get query status from restpp
	restPPQueryResultRequestURL := fmt.Sprintf(
		"http://%s:%d%s?requestid=%s",
		restPPHostName,
		cfg.GetConfig().ProtoConf.GetRESTPP().GetNginxPort(),
		"/query_result",
		requestId,
	)

	queryResultReq, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, restPPQueryResultRequestURL, nil)
	h.SetFromGraphStudio(queryResultReq)
	h.SetForwardedForFromContext(c, queryResultReq)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal RESTPP requests.
	})

	queryResultResp, err := client.Do(queryResultReq)
	if err != nil {
		return fmt.Errorf("Failed to get query result from RESTPP for requestId:%s : %v", requestId, err)
	}
	defer queryResultResp.Body.Close()

	queryResult := struct {
		Error   bool        `json:"error"`
		Message string      `json:"message"`
		Results interface{} `json:"results"`
	}{}

	body, err := io.ReadAll(queryResultResp.Body)
	if err != nil {
		return fmt.Errorf("Failed to read query result response from RESTPP:%v for requestId:%s", err, requestId)
	}

	if err := tgJSON.Unmarshal(body, &queryResult); err != nil {
		return fmt.Errorf("Failed to parse query result for requestId: %s from RESTPP: %v", requestId, err)
	}

	if queryResult.Error {
		return fmt.Errorf("Query result error for requestId: %s from RESTPP: %s", requestId, queryResult.Message)
	}

	st.Results = queryResult.Results

	return nil
}

func fillProcessLogs(st *Status, cfg *config.Config) error {
	hostIDs := cfg.GetAllHostIDs()
	tgFS := fs.NewTGFilesystem(cfg)
	results := make(map[string][]fs.SearchResult)
	pattern := st.RequestId

	mux := &sync.Mutex{}
	wg := &sync.WaitGroup{}
	var errors error
	for _, hostID := range hostIDs {
		results[hostID] = make([]fs.SearchResult, 0)
		wg.Add(1)
		go func(hostID string) {
			defer wg.Done()
			p := path.Join(cfg.GetLogRootDirPath(), "gpe")
			result, err := tgFS.Search(p, "", pattern, 1000, hostID)
			mux.Lock()
			defer mux.Unlock()
			if err != nil {
				errors = multierr.Append(errors, fmt.Errorf(
					"Failed to search for pattern %s under path %s in host %s: %v",
					pattern,
					p,
					hostID,
					err,
				))
			} else {
				results[hostID] = append(results[hostID], result...)
			}
		}(hostID)
	}
	wg.Wait()

	if errors != nil {
		return errors
	}

	st.ProcessLogs = results
	return nil
}
