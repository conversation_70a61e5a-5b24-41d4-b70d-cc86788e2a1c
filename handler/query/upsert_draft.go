package query

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

// UpsertDraft upserts a query draft for a graph.
func UpsertDraft(c *gin.Context) {
	graphName := c.Param("graphName")
	queryName := c.<PERSON>m("queryName")

	var draft model.QueryDraft
	if err := c.ShouldBindJSON(&draft); err != nil {
		log.Warnf(c, "Invalid payload: %+v", err)
		mw.ReplyWithResult(c, http.StatusBadRequest,
			"Invalid payload.", nil)
		return
	}

	daoManager := dao.GetManager(c)
	err := daoManager.UpsertQueryDraft(graphName, queryName, draft)
	if err != nil {
		log.Warnf(c, "Failed to upsert query draft %s for graph %s: %+v", queryName, graphName, err)
		mw.ReplyWithResult(c, http.StatusInternalServerError,
			fmt.Sprintf("Failed to upsert query draft '%s' for graph '%s'.", queryName, graphName), nil)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", fmt.Sprintf(
		"Successfully upserted query draft '%s' for graph '%s'.",
		queryName,
		graphName,
	))
}
