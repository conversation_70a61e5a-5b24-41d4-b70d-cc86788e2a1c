package query

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// DeleteAll deletes all queries and their drafts from a graph.
func DeleteAll(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")

	cmd := "DROP QUERY ALL"
	output, err := gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUsername(c), mw.GetPassword(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Errorf(c, "Failed to delete all queries from graph %s: %v: %s", graphName, err, cleanOutput)
		mw.Abort(c, http.StatusInternalServerError, cleanOutput)
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteAllQueryDraft(graphName); err != nil {
		log.Errorf(c, "Failed to delete all query drafts from graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete all query drafts from graph '%s'.", graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted all queries from graph '%s'.", graphName),
	)
}
