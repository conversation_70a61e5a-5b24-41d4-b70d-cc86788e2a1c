package query

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// DeleteDraft deletes a query draft from a graph.
func DeleteDraft(c *gin.Context) {
	graphName := c.Param("graphName")
	queryName := c.Param("queryName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteQueryDraft(graphName, queryName); err != nil {
		log.Warnf(c, "Failed to delete query draft %s from graph %s: %+v", queryName, graphName, err)
		mw.ReplyWithResult(c, http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete query draft '%s' from graph '%s'.", queryName, graphName), nil)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK,
		"", fmt.Sprintf("Successfully deleted query draft '%s' from graph '%s'.", queryName, graphName))
}
