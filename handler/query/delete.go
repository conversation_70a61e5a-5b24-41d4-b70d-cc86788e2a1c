package query

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"

	mw "github.com/tigergraph/gus/middleware"
)

// Delete deletes a query and its draft from a graph.
func Delete(gsqlClient interfaces.RequestGSQLClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		graphName := c.Param("graphName")
		queryName := c.Param("queryName")

		if err := deleteQueriesFromGSQL(gsqlClient, c, graphName, []string{queryName}); err != nil {
			mw.ReplyWithResult(c, http.StatusInternalServerError,
				fmt.Sprintf("Failed to delete query '%s' from graph '%s'.", queryName, graphName), nil)
			return
		}

		daoManager := dao.GetManager(c)
		if err := daoManager.DeleteQueryDraft(graphName, queryName); err != nil {
			log.Warnf(c, "Failed to delete query draft %s from graph %s: %+v", queryName, graphName, err)
			mw.ReplyWithResult(c, http.StatusInternalServerError,
				fmt.Sprintf("Failed to delete query draft '%s' from graph '%s'.", queryName, graphName), nil)
			return
		}

		mw.ReplyWithResult(c, http.StatusOK,
			"", fmt.Sprintf("Successfully deleted query '%s' from graph '%s'.", queryName, graphName))
	}
}

func deleteQueriesFromGSQL(gsqlClient interfaces.RequestGSQLClient, c *gin.Context, graphName string, queryNames []string) error {
	var err error
	var output []byte
	for _, queryName := range queryNames {
		cmd := fmt.Sprintf("DROP QUERY %s", queryName)
		output, err = gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUsername(c), mw.GetPassword(c), cmd)
		if err != nil {
			log.Errorf(c,
				"Failed to delete query %s from graph %s: %v: %s",
				queryName,
				graphName,
				err,
				strings.TrimSpace(string(output)),
			)
			continue
		}
	}
	return err
}
