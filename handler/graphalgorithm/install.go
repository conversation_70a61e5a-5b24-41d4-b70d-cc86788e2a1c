package graphalgorithm

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/handler/query"
	mw "github.com/tigergraph/gus/middleware"
)

type PostQuery struct {
	Name          string `json:"name"`
	AlgorithmName string `json:"algorithmName"`
	ErrorMessage  string `json:"errorMessage"`
}

func Install(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	categories, err := loadAllCategory(c)
	if err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to load gsql graph algorithms.", nil)
		return
	}

	graphName := c.Param("graphName")
	queryNames := c.QueryArray("queryName")

	if len(queryNames) == 0 {
		mw.ReplyWithResult(c, http.StatusInternalServerError, "No queries to install.", nil)
		return
	}

	success, successSubQueries, failed := addQueryToGSQL(c, gsqlClient, graphName, queryNames, categories)
	result := map[string][]PostQuery{
		"success": success,
		"failed":  failed,
	}

	if len(success) == 0 {
		mw.ReplyWithResult(c, http.StatusOK, "", result)
		return
	}

	successQueryNames := make([]string, 0)
	for _, postQuery := range successSubQueries {
		successQueryNames = append(successQueryNames, postQuery.AlgorithmName)
	}
	for _, postQuery := range success {
		successQueryNames = append(successQueryNames, postQuery.AlgorithmName)
	}

	// parse successQueryNames
	// to make sure that
	// only valid identifiers exist, avoid query injection
	// todo: let GSQL team to implement an InstallQuery API
	if err := query.ParseQueryNames(successQueryNames); err != nil {
		mw.ReplyWithResult(
			c,
			http.StatusBadRequest,
			err.Error(),
			nil,
		)
		return
	}

	cmd := fmt.Sprintf("INSTALL QUERY %s", strings.Join(successQueryNames, ", "))
	output, err := gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUserCredentials(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Errorf(c, "Failed to install queries for graph %s: %v: %s", graphName, err, cleanOutput)
		mw.ReplyWithResult(c, http.StatusInternalServerError, cleanOutput, "")
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", result)
}

func addQueryToGSQL(c *gin.Context, gsqlClient interfaces.RequestGSQLClient, graphName string, queryNames []string, categories []Category) ([]PostQuery, []PostQuery, []PostQuery) {
	added := make([]PostQuery, 0)
	addedSubQueries := make([]PostQuery, 0)
	failed := make([]PostQuery, 0)
	algorithms := getAllAlgorithms(categories)
	for _, name := range queryNames {
		algorithm, err := getAlgorithmForQuery(name, algorithms)
		if err != nil {
			log.Errorf(c, "Failed to get %s algorithm: %v", name, err)
			failed = append(failed, PostQuery{
				Name:          name,
				AlgorithmName: algorithm.AlgorithmName,
				ErrorMessage:  err.Error(),
			})
			continue
		}

		addedSubQueries = append(addedSubQueries, addSubQueries(c, gsqlClient, graphName, algorithm)...)

		output, err := gsqlClient(
			c,
			mw.GetConfig(c),
			graphName,
			mw.GetUserCredentials(c),
			algorithm.Gsql,
		)
		if err != nil {
			errorMessage := strings.TrimSpace(string(output))
			log.Errorf(c,
				"Failed to add query %s for graph %s: %v: %s",
				name,
				graphName,
				err,
				errorMessage,
			)
			failed = append(failed, PostQuery{
				Name:          name,
				AlgorithmName: algorithm.AlgorithmName,
				ErrorMessage:  errorMessage,
			})
			continue
		}
		added = append(added, PostQuery{
			Name:          name,
			AlgorithmName: algorithm.AlgorithmName,
			ErrorMessage:  "",
		})
	}

	return added, addedSubQueries, failed
}

func getAllAlgorithms(categories []Category) []Algorithm {
	algorithms := make([]Algorithm, 0)

	for _, category := range categories {
		algorithms = getAlgorithmsForCategory(algorithms, category)
	}

	return algorithms
}

func getAlgorithmsForCategory(algorithms []Algorithm, category Category) []Algorithm {
	for _, subCatetory := range category.Subs {
		algorithms = getAlgorithmsForCategory(algorithms, subCatetory)
	}
	algorithms = append(algorithms, category.Algorithms...)
	return algorithms
}

func getAlgorithmForQuery(queryName string, algorithms []Algorithm) (*Algorithm, error) {
	for _, algorithm := range algorithms {
		if algorithm.Name == queryName {
			return &algorithm, nil
		}
	}

	s := fmt.Sprintf("no matching algorithm for %s", queryName)
	return nil, errors.New(s)
}

func addSubQueries(c *gin.Context, gsqlClient interfaces.RequestGSQLClient, graphName string, algorithm *Algorithm) []PostQuery {
	added := make([]PostQuery, 0)
	for _, subQuery := range algorithm.Dependencies.Queries {
		output, err := gsqlClient(
			c,
			mw.GetConfig(c),
			graphName,
			mw.GetUserCredentials(c),
			subQuery.Gsql,
		)
		if err != nil {
			errorMessage := strings.TrimSpace(string(output))
			log.Errorf(c,
				"Failed to add subQuery %s for algorithm %s to graph %s: %v: %s",
				subQuery.Name,
				algorithm.Name,
				graphName,
				err,
				errorMessage,
			)

			// Just ignore sub query related error
			continue
		}

		added = append(added, PostQuery{
			Name:          subQuery.Name,
			AlgorithmName: subQuery.Name,
			ErrorMessage:  "",
		})
	}

	return added
}
