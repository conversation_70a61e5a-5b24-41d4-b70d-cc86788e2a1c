package graphalgorithm

import (
	"os"
	"path/filepath"
	"strings"

	"github.com/bmatcuk/doublestar/v4"
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	mw "github.com/tigergraph/gus/middleware"
	"gopkg.in/yaml.v2"
)

const TGGlobalGSQL = "tg_global_gsql.yml"

// @           : this file
// {x}         : the directory of file x
// &x          : any file which matches pattern x
// **          : any depth of arbitrary directories
// *           : wildcard character

type TGGlobal struct {
	categoryPattern    string
	subCategoryPattern string
	algorithmPattern   string
}

type FileInfo struct {
	parentName string
	path       string
}

type Algorithm struct {
	Name              string `json:"name" yaml:"name"`
	AlgorithmName     string `json:"algorithmName" yaml:"-"`
	Filename          string `json:"-" yaml:"filename"`
	Description       string `json:"description" yaml:"description"`
	SchemaConstraints string `json:"schemaConstraints" yaml:"schema_constraints"`
	Path              string `json:"-"`
	Gsql              string `json:"-"`
	ShowInGraphStudio bool   `json:"-" yaml:"include"`
	Dependencies      struct {
		Queries []struct {
			Name string `json:"name"`
			File string `json:"file" yaml:"file"`
			Gsql string `json:"gsql"`
		} `json:"queries" yaml:"queries"`
	} `json:"-" yaml:"dependencies"`
}

type Category struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Path        string      `json:"-"`
	Subs        []Category  `json:"subs"`
	Algorithms  []Algorithm `json:"algorithms"`
}

func loadDescription(path string) (string, error) {
	yamlFile, err := os.ReadFile(path)
	if err != nil {
		return "", err
	}
	description := struct {
		Description string `yaml:"description"`
	}{}

	err = yaml.Unmarshal(yamlFile, &description)
	if err != nil {
		return "", err
	}

	return description.Description, nil
}

func loadGSQL(path string, file string) (string, error) {
	dir := filepath.Dir(path)
	// remove {@}
	file = strings.ReplaceAll(file, "{@}/", "")
	targetFile := filepath.Join(dir, file)
	gsql, err := os.ReadFile(targetFile)
	if err != nil {
		return "", err
	}

	return string(gsql), nil
}

func getAlgorithmName(fileName string) string {
	return strings.TrimSuffix(filepath.Base(fileName), filepath.Ext(fileName))
}

func loadAlgorithm(path string) (*Algorithm, error) {
	yamlFile, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	algorithm := struct {
		Algorithm Algorithm `yaml:"algorithm"`
	}{}

	err = yaml.Unmarshal(yamlFile, &algorithm)
	if err != nil {
		return nil, err
	}

	gsql, err := loadGSQL(path, algorithm.Algorithm.Filename)
	if err != nil {
		return nil, err
	}

	algorithm.Algorithm.Gsql = gsql
	algorithm.Algorithm.Path = path
	algorithm.Algorithm.AlgorithmName = getAlgorithmName(algorithm.Algorithm.Filename)

	// load sub query
	for i := 0; i < len(algorithm.Algorithm.Dependencies.Queries); i++ {
		query := &algorithm.Algorithm.Dependencies.Queries[i]
		name := filepath.Base(query.File)
		name = strings.TrimSuffix(name, filepath.Ext(name))
		query.Name = name

		gsql, err := loadGSQL(path, query.File)
		if err != nil {
			return nil, err
		}
		query.Gsql = gsql
	}

	return &algorithm.Algorithm, nil
}

func loadGlobalConfig(c *gin.Context) (*TGGlobal, error) {
	config := mw.GetConfig(c)
	yamlFile, err := os.ReadFile(filepath.Join(config.GetGSQLAlgorithmsPath(), TGGlobalGSQL))
	if err != nil {
		return nil, err
	}

	conf := struct {
		AlgorithmNavigation struct {
			Category struct {
				Pattern string `yaml:"pattern"`
			} `yaml:"category"`
			SubCategory struct {
				Pattern string `yaml:"pattern"`
			} `yaml:"sub_category"`
			Algorithm struct {
				Pattern string `yaml:"pattern"`
			} `yaml:"algorithm"`
		} `yaml:"algorithm_navigation"`
	}{}

	err = yaml.Unmarshal(yamlFile, &conf)
	if err != nil {
		return nil, err
	}

	tgGlobal := &TGGlobal{}
	tgGlobal.categoryPattern = conf.AlgorithmNavigation.Category.Pattern
	tgGlobal.subCategoryPattern = conf.AlgorithmNavigation.SubCategory.Pattern
	tgGlobal.algorithmPattern = conf.AlgorithmNavigation.Algorithm.Pattern

	return tgGlobal, nil
}

func preprocessPattern(pattern string, c *gin.Context) string {
	config := mw.GetConfig(c)
	return strings.ReplaceAll(pattern, "{@}", config.GetGSQLAlgorithmsPath())
}

func loadFilesByPattern(categoryPattern string) ([]FileInfo, error) {
	base, pattern := doublestar.SplitPattern(categoryPattern)

	fsys := os.DirFS(base)
	files, err := doublestar.Glob(fsys, pattern)
	if err != nil {
		return nil, err
	}
	fileInfos := make([]FileInfo, len(files))
	for i, file := range files {
		parentName := filepath.Base(filepath.Dir(file))
		fileInfos[i] = FileInfo{
			parentName: parentName,
			path:       filepath.Join(base, file),
		}
	}

	return fileInfos, nil
}

func loadCategoryByPattern(categoryPattern string, c *gin.Context) ([]Category, error) {
	categoryPattern = preprocessPattern(categoryPattern, c)

	fileInfos, err := loadFilesByPattern(categoryPattern)
	if err != nil {
		return nil, err
	}

	categories := make([]Category, len(fileInfos))

	for i, fileInfo := range fileInfos {
		description, err := loadDescription(fileInfo.path)
		if err != nil {
			return nil, err
		}
		categories[i] = Category{
			Name:        fileInfo.parentName,
			Path:        fileInfo.path,
			Description: description,
		}
	}

	return categories, nil
}

func loadAlgorithmsByPattern(algorithmPattern string, c *gin.Context) ([]Algorithm, error) {
	algorithmPattern = preprocessPattern(algorithmPattern, c)

	fileInfos, err := loadFilesByPattern(algorithmPattern)
	if err != nil {
		return nil, err
	}

	algorithms := make([]Algorithm, 0)

	for _, fileInfo := range fileInfos {
		algorithm, err := loadAlgorithm(fileInfo.path)
		if err != nil {
			log.Errorf("Failed to load algorithm %s: %v", fileInfo.path, err)
			return nil, err
		}
		if !algorithm.ShowInGraphStudio {
			continue
		}

		algorithms = append(algorithms, *algorithm)
	}

	return algorithms, nil
}

func loadAllCategory(c *gin.Context) ([]Category, error) {
	conf, err := loadGlobalConfig(c)
	if err != nil {
		log.Errorf("Failed to load gsql algorithm config: %v", err)
		return nil, err
	}

	categories, err := loadCategoryByPattern(conf.categoryPattern, c)
	if err != nil {
		log.Errorf("Failed to load category: %v", err)
		return nil, err
	}

	for i := 0; i < len(categories); i++ {
		category := &categories[i]
		err = loadSubCategory(category, conf.subCategoryPattern, c)
		if err != nil {
			log.Errorf("Failed to load sub category for %s: %v", category.Name, err)
			return nil, err
		}
		err = loadCategoryAlgorithm(category, conf.algorithmPattern, c)
		if err != nil {
			log.Errorf("Failed to load algorithms for %s: %v", category.Name, err)
			return nil, err
		}
	}

	return cleanCategories(categories), nil
}

func getCategoryForPath(category *Category, path string) *Category {
	subDir := filepath.Dir(path)
	subParents := []string{}
	for {
		subParent := filepath.Base(subDir)
		subDir = filepath.Dir(subDir)
		subParents = append(subParents, subParent)

		if subParent == category.Name {
			break
		}
		if subParent == "/" || subParent == "." {
			break
		}
	}

	parentCategory := category
	// len(subParents) - 1 match category.name, so start at len(subParents) - 2
	for i := len(subParents) - 2; i >= 0; i-- {
		subParent := subParents[i]
		for j := 0; j < len(parentCategory.Subs); j++ {
			if parentCategory.Subs[j].Name == subParent {
				parentCategory = &parentCategory.Subs[j]
				break
			}
		}
	}

	return parentCategory
}

func loadSubCategory(category *Category, subCategoryPattern string, c *gin.Context) error {
	categorDir := filepath.Dir(category.Path)

	subCategoryPattern = strings.ReplaceAll(subCategoryPattern, "{&category}", categorDir)

	subs, err := loadCategoryByPattern(subCategoryPattern, c)
	if err != nil {
		return err
	}

	for i := 0; i < len(subs); i++ {
		sub := subs[i]

		parentCategory := getCategoryForPath(category, sub.Path)
		parentCategory.Subs = append(parentCategory.Subs, sub)
	}

	return nil
}

func loadCategoryAlgorithm(category *Category, algorithmPattern string, c *gin.Context) error {
	categoryDir := filepath.Dir(category.Path)

	algorithmPattern = strings.ReplaceAll(algorithmPattern, "{&category}", categoryDir)

	algorithms, err := loadAlgorithmsByPattern(algorithmPattern, c)
	if err != nil {
		return err
	}

	for i := 0; i < len(algorithms); i++ {
		algorithm := algorithms[i]

		parentCategory := getCategoryForPath(category, algorithm.Path)
		parentCategory.Algorithms = append(parentCategory.Algorithms, algorithm)
	}

	return nil
}

// remove empty category and sub category.
func cleanCategories(categories []Category) []Category {
	nonEmptyCategories := make([]Category, 0)
	for i := 0; i < len(categories); i++ {
		category := &categories[i]
		category = cleanCategory(category)
		if category != nil {
			nonEmptyCategories = append(nonEmptyCategories, *category)
		}

	}
	return nonEmptyCategories
}

func cleanCategory(category *Category) *Category {
	nonEmptySubs := make([]Category, 0)

	for i := 0; i < len(category.Subs); i++ {
		subCategory := &category.Subs[i]
		subCategory = cleanCategory(subCategory)
		if subCategory != nil {
			nonEmptySubs = append(nonEmptySubs, *subCategory)
		}
	}

	category.Subs = nonEmptySubs

	if len(category.Algorithms) == 0 && len(category.Subs) == 0 {
		return nil
	}

	return category
}
