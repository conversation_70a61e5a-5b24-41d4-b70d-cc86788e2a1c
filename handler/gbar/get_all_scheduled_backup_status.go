package gbar

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// GetAllScheduledBackupStatus returns all automatic scheduled backup status.
func GetAllScheduledBackupStatus(c *gin.Context) {
	daoManager := dao.GetManager(c)
	status, err := daoManager.GetAllScheduledBackupStatus()
	if err != nil {
		log.Errorf(c, "Failed to get all scheduled backup status: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			"Failed to get all scheduled backup status.",
		)
		return
	}
	log.Infof(c, "%+v", status)
	mw.ReplyWithResult(c, http.StatusOK, "", status)
}
