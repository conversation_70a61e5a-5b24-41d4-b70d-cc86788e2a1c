package gbar

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/graphql"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

// RemoveBackup deletes a backup.
func RemoveBackup(c *gin.Context, t2pClient pb.ControllerClient, cfg *config.Config, daoManager interfaces.DaoManager) {
	backupTag := c.Param("backupTag")
	graphqlResolver := graphql.Query{
		Deps: graphql.Deps{
			T2pClient:  t2pClient,
			Config:     cfg,
			DaoManager: daoManager,
		},
	}
	ch, err := graphqlResolver.RemoveBackupStream(c, struct{ Tags []string }{Tags: []string{backupTag}})
	if err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
	}
	for progress := range ch {
		if progress.Error != nil {
			c.SSEvent("error", progress.Error.Error())
		} else {
			c.SSEvent("message", progress.Progress)
		}
		c.Writer.Flush()
	}
}
