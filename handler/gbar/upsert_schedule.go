package gbar

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"

	"github.com/robfig/cron/v3"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/gbar"
)

// UpsertSchedule upserts automatic backup schedule and sets up cron job.
func UpsertSchedule(c *gin.Context, t2pClient pb.ControllerClient, config *config.Config) {
	daoManager := dao.GetManager(c)

	var schedule model.BackupSchedule
	if err := c.ShouldBindJSON(&schedule); err != nil {
		log.Errorf(c, "Invalid Payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	if err := validateSchedule(schedule); err != nil {
		log.Errorf(c, "invalid schedule: %v", err)
		mw.Abort(c, http.StatusBadRequest, "invalid schedule")
		return
	}

	if err := daoManager.UpsertBackupSchedule(schedule); err != nil {
		log.Errorf(c, "Failed to upsert backup schedule: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			"Failed to upsert backup schedule.",
		)
		return
	}

	gbarService := gbar.GetService(c)
	backupSchedulerEnable, err := gbarService.BackupSchedulerEnable()
	if err != nil {
		log.Errorf(c, "Backup scheduler: %v", err)
		mw.Abort(c, http.StatusForbidden, fmt.Sprintf("Backup scheduler: %v", err))
		return
	}
	if !backupSchedulerEnable {
		log.Error(c, "You should enable backup scheduler first, then modify the policy.")
		mw.Abort(c, http.StatusForbidden, "You should enable backup scheduler first, then modify the policy.")
		return
	}

	if err := gbarService.ScheduleBackup(t2pClient, config); err != nil {
		log.Errorf(c, "Failed to schedule a routine automatic GBAR backup: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			"Failed to schedule a routine automatic GBAR backup.",
		)
		return
	}
	updateCronJobOnAllNodes(c)

	mw.Reply(
		c,
		http.StatusOK,
		"Successfully scheduled a routine automatic GBAR backup.",
	)
}

func validateSchedule(schedule model.BackupSchedule) error {
	specParser := cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow)
	scheduleS := fmt.Sprintf(
		"%s %s %s %s %s",
		schedule.Minutes,
		schedule.Hours,
		schedule.DayOfMonth,
		schedule.Month,
		schedule.DayOfWeek,
	)
	_, err := specParser.Parse(scheduleS)
	return err
}

func updateCronJobOnAllNodes(c *gin.Context) {
	cfg := mw.GetConfig(c)
	hostnames, err := cfg.GetAllGUIHostnames()
	if err != nil {
		log.Errorf(c, "Failed to get all GUI hostnames: %v", err)
		return
	}
	currentHostname := cfg.GetGUIHostname()

	var wg sync.WaitGroup
	wg.Add(len(hostnames) - 1)
	for _, hostname := range hostnames {
		if hostname == currentHostname {
			continue
		}

		go func(hostname string) {
			requestGUI(c, hostname)
			wg.Done()
		}(hostname)
	}

	wg.Wait()
}

func requestGUI(c *gin.Context, hostname string) {
	cfg := mw.GetConfig(c)
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		cfg.GetNginxProtocol(),
		hostname,
		cfg.GetNginxPort(),
		"/internal/gui/api/gbar/schedule/cron",
	)

	req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, requestURL, nil)
	req.Header.Set("X-Tigergraph-No-Redirect", "true")
	// Add X-Forwarded-For
	h.SetForwardedForFromContext(c, req)

	// forward the cookie from the request header
	req.Header.Set("Cookie", c.Request.Header.Get("Cookie"))
	// forward the authorization from the request header
	req.Header.Set("Authorization", c.Request.Header.Get("Authorization"))

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GUI server requests.
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(c, "Failed to get response from %s: %v", hostname, err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from %s: %v", hostname, err)
		return
	}

	res := &model.Response{}
	if err := json.Unmarshal(body, res); err != nil {
		log.Errorf(c, "Failed to parse response from %s: %v", hostname, err)
		return
	}

	if res.Error {
		log.Errorf(c, "Failed to update cron job for %s: %s", hostname, res.Message)
		return
	}
	log.Infof(c, "Successfully updated cron job for %s", hostname)
}
