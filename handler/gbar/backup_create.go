package gbar

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/graphql"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/gbar"
)

// Backup runs GBAR backup to an archive.
func BackUp(c *gin.Context, t2pClient pb.ControllerClient, config *config.Config, daoManager interfaces.DaoManager) {
	backupTag := c.Query("backupTag")
	if strings.Contains(backupTag, graphql.AutoBackupTag) {
		mw.ReplyWithResult(
			c,
			http.StatusBadRequest,
			fmt.Sprintf("Backup tag contains reseverd keyword '%s'.", graphql.AutoBackupTag),
			nil,
		)
		return
	}

	graphqlResolver := graphql.Query{
		Deps: graphql.Deps{
			T2pClient:  t2pClient,
			Config:     config,
			DaoManager: daoManager,
		},
	}
	ch, err := graphqlResolver.CreateBackupStream(c, graphql.CreateBackupArgs{
		BackupCreateRequest: pb.BackupCreateRequest{
			Tag:         backupTag,
			StagingPath: gbar.StagingPath(config),
		},
		IsAutomaticBackup: false,
	})
	defer func() {
		if err := graphqlResolver.Deps.DaoManager.SetGBARInProgress(false); err != nil {
			log.Errorf(c, "Failed to set GBAR in progress to false: %v", err)
		}
	}()

	if err != nil {
		if errors.Is(err, graphql.ErrMaxBackup) {
			mw.ReplyWithResult(c, http.StatusInsufficientStorage, "Maximum number of backups has been reached. Please delete some backups.", nil)
			return
		}

		log.Errorf(c, "Failed to back up with tag %s: %v", backupTag, err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, fmt.Sprintf("Failed to back up with tag '%s'.", backupTag), nil)
		return
	}
	for progress := range ch {
		if progress.Error != nil {
			c.SSEvent("error", progress.Error.Error())
		} else {
			c.SSEvent("message", progress.Progress)
		}
		c.Writer.Flush()
	}
	graphqlResolver.CleanUp(c)
}
