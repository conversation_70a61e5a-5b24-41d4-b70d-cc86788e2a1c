package gbar

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/graphql"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/gbar"
)

// Restore runs GBAR restore from an archive.
func Restore(c *gin.Context, t2pClient pb.ControllerClient, config *config.Config, daoManager interfaces.DaoManager) {
	backupTag := c.Query("backupTag")
	meta := c.Query("meta")

	metadata := ""
	if meta == "true" {
		var req model.MetadataRequest
		if err := c.ShouldBindJ<PERSON>(&req); err != nil {
			log.Warnf(c, "Invalid payload: %v", err)
			mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
			return
		}
		metadata = req.Metadata
	}

	graphqlResolver := graphql.Query{
		Deps: graphql.Deps{
			T2pClient:  t2pClient,
			Config:     config,
			DaoManager: daoManager,
		},
	}
	stream, err := graphqlResolver.RestoreBackupStream(c, graphql.RestoreBackupArgs{BackupRestoreRequest: pb.BackupRestoreRequest{
		Tag:         backupTag,
		StagingPath: gbar.StagingPath(config),
		Metadata:    metadata,
	}})
	defer func() {
		if err := graphqlResolver.Deps.DaoManager.SetGBARInProgress(false); err != nil {
			log.Errorf(c, "Failed to set GBAR in progress to false: %v", err)
		}
	}()

	if err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}
	for progress := range stream {
		if progress.Error != nil {
			c.SSEvent("error", progress.Error.Error())
		} else {
			c.SSEvent("message", progress.Progress)
		}
		c.Writer.Flush()
	}
}
