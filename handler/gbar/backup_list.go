package gbar

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/graphql"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

// ListBackup returns the list of existing backups.
func ListBackup(c *gin.Context, t2pClient pb.ControllerClient, cfg *config.Config, daoManager interfaces.DaoManager) {
	backupTag := c.Query("backupTag")
	meta := c.Query("meta")
	graphqlResolver := graphql.Query{
		Deps: graphql.Deps{
			T2pClient:  t2pClient,
			Config:     cfg,
			DaoManager: daoManager,
		},
	}
	isMeta := meta == "true"
	backups, err := graphqlResolver.ListBackup(c, backupTag, isMeta)
	if err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}
	if isMeta {
		mw.ReplyWithResult(c, http.StatusOK, "", backups.MetaData)
		return
	}
	backupInfos := []model.BackupInfo{}
	for _, backup := range backups.Backups {
		backupInfos = append(backupInfos, model.BackupInfo{
			CreatedAt: backup.Time,
			Name:      backup.Tag,
			SizeBytes: backup.SizeBytes,
		})
	}
	mw.ReplyWithResult(c, http.StatusOK, "", backupInfos)
}
