package gbar

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/gbar"
)

// UpdateCron updates the local cron job for automatic GBAR backup.
func UpdateCron(c *gin.Context, t2pClient pb.ControllerClient, config *config.Config) {
	gbarService := gbar.GetService(c)
	backupSchedulerEnable, err := gbarService.BackupSchedulerEnable()
	if err != nil {
		log.Errorf(c, "Backup scheduler: %v", err)
		mw.Abort(c, http.StatusForbidden, fmt.Sprintf("Backup scheduler: %v", err))
		return
	}
	if !backupSchedulerEnable {
		log.Error(c, "You should enable backup scheduler first, then modify the policy.")
		mw.Abort(c, http.StatusForbidden, "You should enable backup scheduler first, then modify the policy.")
		return
	}

	if err := gbarService.ScheduleBackup(t2pClient, config); err != nil {
		log.Errorf(c, "Failed to update cron job for automatic GBAR backup: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			"Failed to update cron job for automatic GBAR backup.",
		)
	}

	mw.Reply(
		c,
		http.StatusOK,
		"Successfully updated cron job for automatic GBAR backup.",
	)
}
