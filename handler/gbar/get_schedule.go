package gbar

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// GetSchedule returns current automatic backup schedule.
func GetSchedule(c *gin.Context) {
	daoManager := dao.GetManager(c)
	result, err := daoManager.GetBackupSchedule()
	if err != nil {
		log.Errorf(c, "Failed to get automatic backup schedule: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			"Failed to get automatic backup schedule.",
		)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", result)
}
