package query_cache

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/proxy"
	tgSync "github.com/tigergraph/gus/lib/sync"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

const (
	InstalledQueryPrefix = "query-run/installed"
	InterpretQueryPrefix = "query-run/interpret"
)

func getLockKeyForInstalledQuery(graphName, queryName string, queryParams string) string {
	h := sha256.New()
	h.Write([]byte(queryParams))
	id := hex.EncodeToString(h.Sum(nil))
	return strings.Join([]string{InstalledQueryPrefix, graphName, queryName, id}, "/")
}

func getLockKeyForInterpretQuery(query string, queryParams string) string {
	h := sha256.New()
	h.Write([]byte(query))
	h.Write([]byte(queryParams))
	id := hex.EncodeToString(h.Sum(nil))
	return strings.Join([]string{InstalledQueryPrefix, id}, "/")
}

func lockQuery(lockKey string) (*tgSync.KeyLocker, error) {
	locker, err := tgSync.NewKeyLocker()
	if err != nil {
		return nil, err
	}

	// For large graph, we need to wait for a longer time.
	err = locker.TryLock(lockKey, time.Minute*10)
	if err != nil {
		return nil, err
	}

	return locker, nil
}

// general idea
// 1. fetch cache from etcd
// 2. if is not force run query existed, return the cache
// 3. if not existed, lock the query in etcd
// 4. run the query and save the query to etcd
// 5. unlock
func ServerInstalledQuery(c *gin.Context) {
	log.Info(mw.GetReqID(c), "enter")

	graphName := c.Param("graphName")
	queryName := c.Param("queryName")
	isForce := c.Query("__force") == "true"
	body, err := io.ReadAll(c.Request.Body)
	queryParams := string(body)
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	if err != nil {
		log.Errorf(c, "Failed to read response from request: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get response from request.")
		return
	}

	daoManager := dao.GetManager(c)
	if !isForce {
		queryResultCache, err := daoManager.GetInstalledQueryResultCache(graphName, queryName, queryParams)
		if err == nil {
			c.Data(http.StatusOK, "application/json", queryResultCache.Result)
			c.Abort()
			return
		}

		// For all error except ErrNotFound, just return the error.
		if !errors.Is(err, db.ErrNotFound) {
			mw.Abort(c, http.StatusInternalServerError, "Failed to get query result cache.")
			return
		}
	}

	lockKey := getLockKeyForInstalledQuery(graphName, queryName, queryParams)
	locker, err := lockQuery(lockKey)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, "Failed to lock for query")
		return
	}
	defer func() { _ = locker.Unlock(lockKey) }()

	// Run the query.
	bw := &bodyWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
	c.Writer = bw

	startTime := time.Now().Unix()
	path := fmt.Sprintf("/query/%s/%s", graphName, queryName)
	proxy.ServeRESTPP(c, path, graphName)

	statusCode := c.Writer.Status()
	// Save the result to cache.
	if statusCode >= 200 && statusCode < 400 {
		queryResultCache := model.QueryResultCache{
			Result:    bw.body.Bytes(),
			StartTime: startTime,
			EndTime:   time.Now().Unix(),
		}
		daoManager.UpsertInstalledQueryResultCache(graphName, queryName, queryParams, queryResultCache)
	}
}

func ServerInterpretQuery(c *gin.Context) {
	log.Info(mw.GetReqID(c), "enter")

	queryParams := c.Request.URL.RawQuery
	isForce := c.Query("__force") == "true"
	body, err := io.ReadAll(c.Request.Body)
	query := string(body)
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	if err != nil {
		log.Errorf(c, "Failed to read response from request: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get response from request.")
		return
	}

	daoManager := dao.GetManager(c)
	if !isForce {
		queryResultCache, err := daoManager.GetInterpretQueryResultCache(query, queryParams)
		if err == nil {
			c.Data(http.StatusOK, "application/json", queryResultCache.Result)
			c.Abort()
			return
		}

		if !errors.Is(err, db.ErrNotFound) {
			mw.Abort(c, http.StatusInternalServerError, "Failed to get query result cache.")
			return
		}
	}

	lockKey := getLockKeyForInterpretQuery(query, queryParams)
	locker, err := lockQuery(lockKey)
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, "Failed to lock for query")
		return
	}
	defer func() { _ = locker.Unlock(lockKey) }()

	// Run the query.
	bw := &bodyWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
	c.Writer = bw

	startTime := time.Now().Unix()
	c.Params = []gin.Param{
		{
			Key:   "proxyPath",
			Value: "/interpreted_query",
		},
	}
	proxy.ServeGSQLServer(c)

	statusCode := c.Writer.Status()
	// Save the result to cache.
	if statusCode >= 200 && statusCode < 400 {
		queryResultCache := model.QueryResultCache{
			Result:    bw.body.Bytes(),
			StartTime: startTime,
			EndTime:   time.Now().Unix(),
		}
		daoManager.UpsertInterpretQueryResultCache(query, queryParams, queryResultCache)
	}
}

type bodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}
