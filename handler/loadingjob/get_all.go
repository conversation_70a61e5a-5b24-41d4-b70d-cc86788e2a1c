package loadingjob

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

// GetAll returns all loading jobs for a graph.
func GetAll(c *gin.Context, gsqlCLient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")

	cmd := "EXPORT JOB ALL AS JSON"
	output, err := gsqlCLient(c, mw.GetConfig(c), graphName, mw.GetUsername(c), mw.GetPassword(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Errorf(c, "Failed to get all loading jobs for graph %s: %v: %s", graphName, err, cleanOutput)
		mw.Abort(c, http.StatusInternalServerError, cleanOutput)
		return
	}

	var job []interface{}
	if err = json.Unmarshal(output, &job); err != nil {
		log.Errorf(c, "Failed to parse response from GSQL client: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to get all loading jobs for graph '%s'.", graphName),
		)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", job)
}
