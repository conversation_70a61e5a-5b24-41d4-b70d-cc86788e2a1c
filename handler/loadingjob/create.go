package loadingjob

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	tgJSON "github.com/tigergraph/gus/lib/json"
	mw "github.com/tigergraph/gus/middleware"
)

// Create creates a loading job for a graph.
func Create(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")
	jobName := c.Param("jobName")

	var job interface{}
	if err := c.ShouldBindJSON(&job); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	jobStr, _ := tgJSON.Marshal(job)
	cmd := fmt.Sprintf("IMPORT JOB FROM JSON %s", jobStr)
	output, err := gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUserCredentials(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Errorf(c,
			"Failed to create loading job %s for graph %s: %v: %s",
			jobName,
			graphName,
			err,
			cleanOutput,
		)
		mw.Abort(c, http.StatusInternalServerError, cleanOutput)
		return
	}

	mw.Reply(
		c,
		http.StatusCreated,
		fmt.Sprintf("Successfully created loading job '%s' for graph '%s'.", jobName, graphName),
	)
}
