package loadingjob

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

// Stop stops the given loading jobs.
func Stop(c *gin.Context, loadingJobService interfaces.LoadingJobService) {
	graphName := c.Param("graphName")
	jobNames := c.<PERSON>("jobName")

	result := loadingJobService.StopJobs(c, graphName, jobNames)

	mw.ReplyWithResult(c, http.StatusOK, "", result)
}
