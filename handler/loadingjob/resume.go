package loadingjob

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

// Resume resumes the given loading jobs.
func Resume(c *gin.Context, loadingJobService interfaces.LoadingJobService) {
	graphName := c.Param("graphName")
	jobNames := c.Query<PERSON>y("jobName")

	result := loadingJobService.ResumeJobs(c, graphName, jobNames)

	mw.ReplyWithResult(c, http.StatusOK, "", result)
}
