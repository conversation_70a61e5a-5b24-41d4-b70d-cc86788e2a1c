package loadingjob

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

// GetProgress returns the loading progress of the given loading jobs.
func GetProgress(c *gin.Context, loadingJobService interfaces.LoadingJobService) {
	graphName := c.Param("graphName")
	jobNames := c.Query<PERSON>y("jobName")

	result := loadingJobService.GetJobsProgress(c, graphName, jobNames)

	mw.ReplyWithResult(c, http.StatusOK, "", result)
}
