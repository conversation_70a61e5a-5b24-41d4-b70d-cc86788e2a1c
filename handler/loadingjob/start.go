package loadingjob

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	mw "github.com/tigergraph/gus/middleware"
)

// Start starts the given loading jobs.
func Start(c *gin.Context, loadingJobService interfaces.LoadingJobService) {
	graphName := c.Param("graphName")

	var jobsInfo []model.StartJobInfo
	if err := c.ShouldBindJSON(&jobsInfo); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	result := loadingJobService.StartJobs(c, graphName, jobsInfo)

	mw.ReplyWithResult(c, http.StatusOK, "", result)
}
