package loadingjob

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

// Get returns a loading job for a graph.
func Get(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")
	jobName := c.Param("jobName")

	cmd := fmt.Sprintf("EXPORT JOB %s AS JSON", jobName)
	output, err := gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUserCredentials(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Errorf(c,
			"Failed to get loading job %s for graph %s: %v: %s",
			jobName,
			graphName,
			err,
			cleanOutput,
		)
		mw.Abort(c, http.StatusInternalServerError, cleanOutput)
		return
	}

	var job interface{}
	if err = json.Unmarshal(output, &job); err != nil {
		log.Errorf(c, "Failed to parse response from GSQL client: %v", err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to get loading job '%s' for graph '%s'.", jobName, graphName),
		)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", job)
}
