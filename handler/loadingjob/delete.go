package loadingjob

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Delete deletes a loading job from a graph.
func Delete(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	graphName := c.Param("graphName")
	jobName := c.Param("jobName")

	cmd := fmt.Sprintf("DROP JOB %s", jobName)
	output, err := gsqlClient(c, mw.GetConfig(c), graphName, mw.GetUsername(c), mw.GetPassword(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Errorf(c,
			"Failed to delete loading job %s from graph %s: %v: %s",
			jobName,
			graphName,
			err,
			cleanOutput,
		)
		mw.Abort(c, http.StatusInternalServerError, cleanOutput)
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteLoadingJobLog(graphName, jobName); err != nil {
		log.Errorf(c, "Failed to delete log for loading job %s from graph %s: %v", jobName, graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete log for loading job '%s' from graph '%s'.", jobName, graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted loading job '%s' from graph '%s'.", jobName, graphName),
	)
}
