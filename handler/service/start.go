package service

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	"github.com/tigergraph/cqrs/tutopia/common/service"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

// Start starts a list of services.
func Start(c *gin.Context, cfg *config.Config, cntlrClient pb.ControllerClient) {
	timeout := cfg.GetStartServiceDefaultTimeoutMS()
	serviceNames := c.QueryArray("serviceName")
	if len(serviceNames) == 0 {
		mw.Abort(c, http.StatusBadRequest, "No services to start.")
		return
	}

	descs, err := util.ParseServices(serviceNames)
	if err != nil {
		log.Errorf(c, "Failed to parse service names: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Failed to parse service names.")
		return
	}

	if err := startServices(cntlrClient, descs, timeout); err != nil {
		log.Errorf(c, "Failed to start services: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to start services.")
		return
	}

	mw.Reply(c, http.StatusOK, "Successfully started services.")
}

func startServices(cntlrClient pb.ControllerClient, services []service.ServiceDescriptor, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	sds := make([]*pb.ServiceDescriptor, len(services))
	for i, s := range services {
		sds[i] = &pb.ServiceDescriptor{
			ServiceName: s.ServiceName,
			Partition:   s.Partition,
			Replica:     s.Replica,
		}
	}

	resp, err := cntlrClient.StartService(ctx, &pb.StartServiceRequest{
		SpanId:             util.GenSpanId(util.START_KEY),
		ServiceDescriptors: sds,
	})
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.StartService)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}
	return nil

}
