package service

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	"github.com/tigergraph/cqrs/tutopia/common/service"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

// Stop stops a list of services.
func Stop(c *gin.Context, cfg *config.Config, cntlrClient pb.ControllerClient) {
	timeout := cfg.GetStopServiceDefaultTimeoutMS()
	serviceNames := c.Query<PERSON>rray("serviceName")
	if len(serviceNames) == 0 {
		mw.Abort(c, http.StatusBadRequest, "No services to stop.")
		return
	}

	for _, serviceName := range serviceNames {
		// a list of services that shoube not be stopped
		for _, serv := range []string{tgServ.EXECUTOR, tgServ.CONTROLLER, tgServ.GUI} {
			if strings.EqualFold(serviceName, serv) {
				mw.Abort(
					c,
					http.StatusBadRequest,
					fmt.Sprintf("Please use 'gadmin' to stop '%s'.", serv),
				)
				return
			}
		}

		if strings.EqualFold(serviceName, tgServ.GSE) && cfg.GetGSEStopTimeoutMS() > timeout {
			// GSE is a special case, it is configured with a separate value.
			timeout = cfg.GetGSEStopTimeoutMS()
		}
	}

	descs, err := util.ParseServices(serviceNames)
	if err != nil {
		log.Errorf(c, "Failed to parse service names: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Failed to parse service names.")
		return
	}

	if err := stopServices(cntlrClient, descs, timeout); err != nil {
		log.Errorf(c, "Failed to stop services: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to stop services.")
		return
	}

	mw.Reply(c, http.StatusOK, "Successfully stopped services.")
}

func stopServices(cntlrClient pb.ControllerClient, services []service.ServiceDescriptor, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	sds := make([]*pb.ServiceDescriptor, len(services))
	for i, s := range services {
		sds[i] = &pb.ServiceDescriptor{
			ServiceName: s.ServiceName,
			Partition:   s.Partition,
			Replica:     s.Replica,
		}
	}
	resp, err := cntlrClient.StopService(ctx, &pb.StopServiceRequest{
		SpanId:             util.GenSpanId(util.START_KEY),
		ServiceDescriptors: sds,
	})
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.StopService)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}
	return nil
}
