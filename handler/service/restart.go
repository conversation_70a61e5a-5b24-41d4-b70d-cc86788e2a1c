package service

import (
	"net/http"
	"os/exec"
	"strings"
	"syscall"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	tgComServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

// Restart restarts a list of services.
func Restart(c *gin.Context, cfg *config.Config, cntlrClient pb.ControllerClient) {
	startTimeout := cfg.GetStartServiceDefaultTimeoutMS()
	stopTimeout := cfg.GetStopServiceDefaultTimeoutMS()
	serviceNames := c.QueryArray("serviceName")
	if len(serviceNames) == 0 {
		mw.Abort(c, http.StatusBadRequest, "No services to restart.")
		return
	}

	descs, err := util.ParseServices(serviceNames)
	if err != nil {
		log.Errorf(c, "Failed to parse service names: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Failed to parse service names.")
		return
	}

	restartWithChildProc := false
	for _, serviceName := range serviceNames {

		// a list of services that shoube not be restarted with grpc, should be using gadmin
		for _, serv := range []string{tgComServ.EXECUTOR, tgComServ.CONTROLLER, tgComServ.GUI} {
			if strings.EqualFold(serviceName, serv) {
				restartWithChildProc = true
			}
		}
		if strings.EqualFold(serviceName, tgComServ.GSE) && cfg.GetGSEStopTimeoutMS() > stopTimeout {
			// GSE is a special case, it is configured with a separate value.
			stopTimeout = cfg.GetGSEStopTimeoutMS()
		}
	}

	// If the service itself needs to be restarted, use a detached child process.
	if restartWithChildProc {
		gadminBin := cfg.GetGadminBinPath()
		args := append([]string{"restart"}, serviceNames...)
		args = append(args, "-y")

		cmd := exec.Command(gadminBin, args...)
		// Set env to empty and change pgid so that the child process is not tracked by EXE.
		cmd.Env = make([]string, 0)
		cmd.SysProcAttr = &syscall.SysProcAttr{
			Setpgid: true,
		}

		if err := cmd.Start(); err != nil {
			log.Errorf(c, "Failed to restart services: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to restart services.")
			return
		}
	} else {

		if err := stopServices(cntlrClient, descs, stopTimeout); err != nil {
			log.Errorf(c, "Failed to stop services: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to stop services.")
			return
		}

		if err := startServices(cntlrClient, descs, startTimeout); err != nil {
			log.Errorf(c, "Failed to start services: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to start services.")
			return
		}

	}

	mw.Reply(c, http.StatusOK, "Successfully restarted services.")
}
