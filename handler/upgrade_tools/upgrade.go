package upgradetools

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/handler/data"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	"github.com/tigergraph/gus/lib/helper"
	mw "github.com/tigergraph/gus/middleware"
)

// file
// - tools-all-signed-[version].tar.gz
// 	 - tools-all.tar.gz
//		- gst.tar.gz
//		- gap.tat.gz
//		- tools.tar.gz
//		- gshell.tar.gz
//		- graphql.tar.gz
//		- insights.tar.gz
//     	- tools-version.json
//   - tools-all.tar.gz.minisign

// 1. download or upload the file witch includes signed file and signature file into `data/gui/upgrade_file/tools-upgrade`
// 2. unzip file, get signed file and signature file
// 3. verify package then unzip signed file
// 4. get information from version.json
// 5. check support, check version
// 6. replace

type LocalUpgradeProps struct {
	FileName string `form:"fileName" binding:"required"`
}

type VersionInformation struct {
	Major               string `json:"major" binding:"required"`
	Minor               string `json:"minor" binding:"required"`
	Build               string `json:"build" binding:"required"`
	Revision            string `json:"revision" binding:"required"`
	SupportedTGVersions string `json:"supportedTG" binding:"required"`  // split by ,
	PackageNames        string `json:"packageNames" binding:"required"` // split by ,
}

const UNZIPFOLDERNAME = "tools-upgrade"
const VERSIONINFORMATIONFILENAME = "tools-version.json"
const SIGNATUREFILENAME = "tools-all.tar.gz.minisig"
const SIGNEDFILENAME = "tools-all.tar.gz"
const UPGRADEFILENAME = "tools-all-sign.tar.gz"
const VERIFYPUBLICKEY = `untrusted comment: minisign public key 15C6C626860A6B2B
RWQrawqGJsbGFfD7La/qjtuBL/ZpInUZY7A9hEZ4y55djVxBeSUSUXYa`
const LATESTPACKAGEDOWNLOADBASEURL = "https://app-tools-information.s3.us-west-2.amazonaws.com"
const LATESTPACKAGENAME = "tools-all-signed-latest.tar.gz"

func Upgrade(c *gin.Context) {
	cfg := mw.GetConfig(c)
	unzipFolderPath := filepath.Join(cfg.GetDataDirPath(), data.GetCategory().UpgradeFile, UNZIPFOLDERNAME)
	var err error
	defer func() {
		if err != nil {
			log.Errorf("Failed to replace pakcages: %v", err)
			c.SSEvent("error", fmt.Sprintf("Failed to upgrade Tools: %v. Will rollback it!", err))
		}

		if err := data.RemoveDirFromAllServers(c, cfg, unzipFolderPath); err != nil {
			log.Errorf("Failed to remove upgrade folder %v. err: %v", unzipFolderPath, err)
		}
	}()

	// get version information
	var upgradeVersionInformation VersionInformation
	upgradeVersionInformation, err = getVersionInformation(unzipFolderPath)
	if err != nil {
		return
	}
	packageNames := strings.Split(upgradeVersionInformation.PackageNames, ",")

	// unzip
	c.SSEvent("message", "Extracting the signed package of TigerGraph Suite...")
	c.Writer.Flush()
	for _, packageName := range packageNames {
		err = helper.Untar(filepath.Join(unzipFolderPath, fmt.Sprintf("%v.tar.gz", packageName)), unzipFolderPath)
		if err != nil {
			return
		}
	}

	// replace files
	c.SSEvent("message", "Replacing the existing package of TigerGraph Suite with the new one...")
	c.Writer.Flush()
	err = replaceToolsFiles(cfg, unzipFolderPath, packageNames)
	if err != nil {
		return
	}

	c.SSEvent("message", "The upgrade process completed successfully!")
}

func RemoteUpgradeCheck(c *gin.Context, controllerClient pb.ControllerClient) {
	cfg := mw.GetConfig(c)
	unzipFolderPath := filepath.Join(cfg.GetDataDirPath(), data.GetCategory().UpgradeFile, UNZIPFOLDERNAME)
	upgradeFilePath := filepath.Join(unzipFolderPath, UPGRADEFILENAME)
	var err error
	defer func() {
		if err != nil {
			log.Errorf("Failed to upgrade Tools: %v", err)
			mw.Abort(c, http.StatusInternalServerError, fmt.Sprintf("Failed to upgrade Tools: %v. Will rollback it!", err))
			if err := data.RemoveDirFromAllServers(c, cfg, unzipFolderPath); err != nil {
				log.Errorf("Failed to remove upgrade folder %v. err: %v", unzipFolderPath, err)
			}
		}
	}()

	var repoInformation *pb.GetRepoInfoResponse
	repoInformation, err = controllerClient.GetRepoInfo(c, &pb.GetRepoInfoRequest{})
	if err != nil {
		return
	}

	// create unzip folder
	err = createUnzipFolder(cfg, unzipFolderPath)
	if err != nil {
		return
	}

	// download
	var bigVersion string
	bigVersion, err = getBigVersion(repoInformation.GetGAVersion())
	if err != nil {
		return
	}

	err = helper.DownloadFile(
		fmt.Sprintf("%s/%s/%s", LATESTPACKAGEDOWNLOADBASEURL, fmt.Sprintf("%s.x", bigVersion), LATESTPACKAGENAME),
		upgradeFilePath,
	)
	if err != nil {
		return
	}

	err = upgradeCheck(c, repoInformation.GetGAVersion(), unzipFolderPath, upgradeFilePath)
	if err != nil {
		return
	}
	// copy untared files to other nodes since the incoming upgrade request might be routed there
	data.CopyFileToAllServers(c, cfg, unzipFolderPath, fs.FileTypeFolder)
}

func LocalUpgradeCheck(c *gin.Context, controllerClient pb.ControllerClient) {
	cfg := mw.GetConfig(c)
	unzipFolderPath := filepath.Join(cfg.GetDataDirPath(), data.GetCategory().UpgradeFile, UNZIPFOLDERNAME)
	upgradeFilePath := filepath.Join(unzipFolderPath, UPGRADEFILENAME)
	var err error
	defer func() {
		if err != nil {
			log.Errorf("Failed to upgrade Tools: %v", err)
			mw.Abort(c, http.StatusInternalServerError, fmt.Sprintf("Failed to upgrade Tools: %v. Will rollback it!", err))
			if err := data.RemoveDirFromAllServers(c, cfg, unzipFolderPath); err != nil {
				log.Errorf("Failed to remove upgrade folder %v. err: %v", unzipFolderPath, err)
			}
		}
	}()

	var repoInformation *pb.GetRepoInfoResponse
	repoInformation, err = controllerClient.GetRepoInfo(c, &pb.GetRepoInfoRequest{})
	if err != nil {
		return
	}

	// get file name
	props := &LocalUpgradeProps{}
	err = c.ShouldBind(props)
	if err != nil {
		return
	}

	// create unzip folder
	err = createUnzipFolder(cfg, unzipFolderPath)
	if err != nil {
		return
	}

	// move file
	upgradeFileFolder := filepath.Join(cfg.GetDataDirPath(), data.GetCategory().UpgradeFile)
	uploadFile := filepath.Join(upgradeFileFolder, props.FileName)
	err = os.Rename(uploadFile, upgradeFilePath)
	if err != nil {
		return
	}
	err = data.RemoveDirFromAllServers(c, cfg, uploadFile)
	if err != nil {
		return
	}

	err = upgradeCheck(c, repoInformation.GetGAVersion(), unzipFolderPath, upgradeFilePath)
	if err != nil {
		return
	}
	// copy untared files to other nodes since the incoming upgrade request might be routed there
	data.CopyFileToAllServers(c, cfg, unzipFolderPath, fs.FileTypeFolder)
}

func createUnzipFolder(cfg *config.Config, unzipFolderPath string) (err error) {
	// delete old one
	err = os.RemoveAll(unzipFolderPath)
	if err != nil {
		return
	}
	// make new one
	upgradeFilesFolder := filepath.Join(cfg.GetDataDirPath(), data.GetCategory().UpgradeFile)
	if _, err = os.Stat(upgradeFilesFolder); err != nil {
		err = os.Mkdir(upgradeFilesFolder, 0700)
		if err != nil {
			return
		}
	}
	err = os.Mkdir(unzipFolderPath, 0700)
	if err != nil {
		return
	}

	return nil
}

func replaceToolsFiles(cfg *config.Config, unzipFolderPath string, packageNames []string) (err error) {
	tgFS := fs.NewTGFilesystem(cfg)
	currentHostID, err := cfg.GetHostID()
	if err != nil {
		return
	}

	if err != nil {
		return
	}

	for _, packageName := range packageNames {
		srcFilePath := filepath.Join(unzipFolderPath, packageName)
		targetFilePath := filepath.Join(cfg.GetGuiBinPath(), packageName)
		if err = tgFS.CopyFile(srcFilePath, currentHostID, targetFilePath, cfg.GetAllHostIDs(), fs.FileTypeFolder); err != nil {
			return
		}
	}

	// copy tools-version.json
	srcFilePath := filepath.Join(unzipFolderPath, VERSIONINFORMATIONFILENAME)
	targetFilePath := filepath.Join(cfg.GetGuiBinPath(), VERSIONINFORMATIONFILENAME)
	if err = tgFS.CopyFile(srcFilePath, currentHostID, targetFilePath, cfg.GetAllHostIDs(), fs.FileTypeFile); err != nil {
		return
	}
	return
}

func concatVersion(versionInformation VersionInformation) string {
	return fmt.Sprintf("%s.%s.%s.%s", versionInformation.Major, versionInformation.Minor, versionInformation.Build, versionInformation.Revision)
}

func getVersionInformation(targetFolderPath string) (versionInformation VersionInformation, err error) {
	versionInformationFilePath := filepath.Join(targetFolderPath, VERSIONINFORMATIONFILENAME)

	var versionInformationFileEntity *os.File
	versionInformationFileEntity, err = os.Open(versionInformationFilePath)
	if err != nil {
		return
	}
	defer versionInformationFileEntity.Close()

	var versionInformationFileBytes []byte
	versionInformationFileBytes, err = io.ReadAll(versionInformationFileEntity)
	if err != nil {
		return
	}

	err = json.Unmarshal(versionInformationFileBytes, &versionInformation)
	if err != nil {
		return
	}

	return
}

func verifySignedFile(unzipFolderPath string) (err error) {
	signatureFilePath := filepath.Join(unzipFolderPath, SIGNATUREFILENAME)
	signedFilePath := filepath.Join(unzipFolderPath, SIGNEDFILENAME)
	err = helper.VerifyMiniSignPackage(VERIFYPUBLICKEY, signatureFilePath, signedFilePath)
	if err != nil {
		return
	}
	return nil
}

func upgradeCheck(c *gin.Context, currentTGVersion string, srcPath string, dstPath string) (err error) {
	cfg := mw.GetConfig(c)
	signedFilePath := filepath.Join(srcPath, SIGNEDFILENAME)

	c.SSEvent("message", "Extracting the package of TigerGraph Suite...")
	c.Writer.Flush()
	// unzip upgrade file
	err = helper.Untar(dstPath, srcPath)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	c.SSEvent("message", "Verifying the integrity of the package of TigerGraph Suite...")
	c.Writer.Flush()
	// verify
	err = verifySignedFile(srcPath)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	c.SSEvent("message", "Extracting the signed package of TigerGraph Suite...")
	c.Writer.Flush()
	// unzip signed file
	err = helper.Untar(signedFilePath, srcPath)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	// get version information
	var upgradeVersionInformation VersionInformation
	upgradeVersionInformation, err = getVersionInformation(srcPath)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	for _, supportVersion := range strings.Split(upgradeVersionInformation.SupportedTGVersions, ",") {
		var supportBigVersion string
		var currentBigVersion string
		supportBigVersion, err = getBigVersion(supportVersion)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
		currentBigVersion, err = getBigVersion(currentTGVersion)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
		if supportBigVersion == currentBigVersion {
			var currentVersionInformation VersionInformation
			currentVersionInformation, err = getVersionInformation(cfg.GetGuiBinPath())
			if err != nil {
				//nolint:nakedret // err is already defined in return parameters
				return
			}
			c.SSEvent("message", gin.H{
				"current": concatVersion(currentVersionInformation),
				"target":  concatVersion(upgradeVersionInformation),
			})
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}
	err = fmt.Errorf(
		"the current TigerGraph version %v does not support updating tools to %v. Please update tigergraph to version %v and try it again",
		currentTGVersion,
		concatVersion(upgradeVersionInformation),
		upgradeVersionInformation.SupportedTGVersions,
	)

	return err
}

func getBigVersion(version string) (string, error) {
	regex := regexp.MustCompile(`^(\d+\.\d+)\.(\d+|[x])$`)

	matches := regex.FindStringSubmatch(version)
	if len(matches) == 3 {
		bigVersion := matches[1]
		return bigVersion, nil
	} else {
		return "", fmt.Errorf("invalid version")
	}
}
