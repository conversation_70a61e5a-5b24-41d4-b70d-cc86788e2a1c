package system

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"

	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/gstatusgraph"
)

func GetGraphStatus(c *gin.Context) {
	cfg := mw.GetConfig(c)

	isForceString := c.<PERSON>("force", "false")
	isForce, err := strconv.ParseBool(isForceString)
	if err != nil {
		log.Warnf("Failed to parse force parameter: %v", err)
		mw.ReplyWithResult(c, http.StatusBadRequest, "Failed to parse force parameter.", nil)
		return
	}

	graphController := gstatusgraph.New(cfg)
	graphStatus, err := graphController.GetGraphStatus(isForce)
	if err != nil {
		log.Warnf("Failed to get graph status: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to get graph status.", nil)
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "Success", graphStatus)
}

func GetGSEStatus(c *gin.Context) {
	cfg := mw.GetConfig(c)
	graphController := gstatusgraph.New(cfg)
	graphStatus, err := graphController.GetGSEStatus()
	if err != nil {
		log.Warnf("Failed to get GSE status: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to get GSE status.", nil)
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "Success", graphStatus)
}
