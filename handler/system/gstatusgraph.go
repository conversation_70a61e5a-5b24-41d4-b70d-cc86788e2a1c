package system

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/gstatusgraph"
)

func GetGraphStatus(c *gin.Context) {
	cfg := mw.GetConfig(c)
	graphController := gstatusgraph.New(cfg)
	graphStatus, err := graphController.GetGraphStatus()
	if err != nil {
		log.Warnf("Failed to get graph status: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to get graph status.", nil)
		return
	}
	mw.ReplyWithResult(c, http.StatusOK, "Success", graphStatus)
}
