package system

import (
	"bufio"
	"fmt"
	"io"
	ioFS "io/fs"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"syscall"

	"github.com/gin-gonic/gin"
	"github.com/otiai10/copy"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/service/db"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/data"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	tgJSON "github.com/tigergraph/gus/lib/json"
	mw "github.com/tigergraph/gus/middleware"
	apitoken "github.com/tigergraph/gus/service/api_token"
	"github.com/tigergraph/gus/service/gstatusgraph"
)

const ExportTempDir = "exports"

type ExportCheckResult struct {
	IsEnoughSpace bool   `json:"isEnoughSpace"`
	GraphSize     uint64 `json:"graphSize"`
	DiskFreeSpace uint64 `json:"diskFreeSpace"`
}

func ExportCheck(c *gin.Context) {
	cfg := mw.GetConfig(c)
	graphController := gstatusgraph.New(cfg)
	graphStatus, err := graphController.GetGraphStatus()
	if err != nil {
		log.Errorf(c, "Failed to get graph status: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get graph status.")
		return
	}
	diskFreeSpace, err := getDiskFreeSpace(cfg.GetSystemTempDirPath())
	if err != nil {
		log.Errorf(c, "Failed to get disk free space: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get disk free space.")
		return
	}
	log.Debugf(c, "Export debug: graph bytes: %v", graphStatus.TopologyBytes)
	log.Debugf(c, "Export debug: disk free space: %d", diskFreeSpace)
	result := ExportCheckResult{
		IsEnoughSpace: diskFreeSpace > cast.ToUint64(graphStatus.TopologyBytes),
		GraphSize:     cast.ToUint64(graphStatus.TopologyBytes),
		DiskFreeSpace: diskFreeSpace,
	}
	mw.ReplyWithResult(c, http.StatusOK, "", &result)
}

func getDiskFreeSpace(path string) (uint64, error) {
	var stat syscall.Statfs_t
	err := syscall.Statfs(path, &stat)
	if err != nil {
		return 0, err
	}

	//nolint:gosec // #nosec G115: integer overflow conversion int64 -> uint64
	freeSize := stat.Bavail * uint64(stat.Bsize)
	return freeSize, nil
}

// Export exports graph metadata, graph data, kv data and users' icons.
func Export(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	tempDir, err := createExportTempDir(mw.GetConfig(c))
	if err != nil {
		log.Errorf(c, "Failed to create directory %s: %v", tempDir, err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to create temporary directory for export.")
		return
	}
	log.Debugf(c, "Export debug: Created temporary directory for export: %s", tempDir)
	defer os.RemoveAll(tempDir)

	if err = exportGraphData(c, tempDir, gsqlClient); err != nil {
		log.Errorf(c, "Failed to export graph data: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to export graph.")
		return
	}

	guiTempDir := path.Join(tempDir, GUIExportedDir)
	if err = os.MkdirAll(guiTempDir, 0700); err != nil {
		log.Errorf(c, "Failed to create directory %s: %v", guiTempDir, err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to create temporary directory for export.")
		return
	}
	if err = exportGUIData(c, guiTempDir); err != nil {
		return
	}

	filename := fmt.Sprintf("%s.tar.gz", filepath.Base(tempDir))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))

	c.Stream(func(w io.Writer) bool {
		if err := fs.Compress(tempDir, w); err != nil {
			log.Errorf(c, "Failed to compress file with path %s: %v", tempDir, err)
		}
		return false
	})
}

func createExportTempDir(cfg *config.Config) (string, error) {
	tempDir := path.Join(cfg.GetTempDirPath(), ExportTempDir)
	if err := os.MkdirAll(tempDir, 0700); err != nil {
		return "", err
	}
	return os.MkdirTemp(tempDir, "export_*")
}

func exportGraphData(c *gin.Context, tempDir string, gsqlClient interfaces.RequestGSQLClient) error {
	isExportData := cast.ToBool(c.Query("isExportData"))
	exportGraphnames := c.Query("exportGraphNames")
	if exportGraphnames == "" {
		exportGraphnames = "ALL"
	}
	exportOpts := []string{"-T"}
	if isExportData {
		exportOpts = append(exportOpts, "-D")
	}

	graphTempDir := path.Join(tempDir, GraphExportedDir)
	if err := os.MkdirAll(graphTempDir, 0700); err != nil {
		return errors.Errorf("Failed to create directory %s: %v", graphTempDir, err)
	}
	log.Debugf(c, "Export debug: Created graph temporary directory for export: %s", graphTempDir)

	cmd := fmt.Sprintf("EXPORT GRAPH %s %s TO \"%s\"", exportGraphnames, strings.Join(exportOpts, " "), graphTempDir)
	output, err := gsqlClient(c, mw.GetConfig(c), "", mw.GetUsername(c), mw.GetPassword(c), cmd)
	if err != nil {
		log.Errorf(c, "Failed to export graph: %v: %s", err, strings.TrimSpace(string(output)))
		return err
	}

	// a merging buffer to merge all exported graph data from other GSQL nodes
	mergingTempDir := path.Join(tempDir, "merging")
	log.Debugf(c, "Export debug: merging temp dir: %s", mergingTempDir)
	if err = os.MkdirAll(mergingTempDir, 0700); err != nil {
		return err
	}
	defer os.RemoveAll(mergingTempDir)

	cfg := mw.GetConfig(c)
	// merge graph data from other GSQL nodes
	if err := mergeGraphData(cfg, graphTempDir, mergingTempDir); err != nil {
		return err
	}

	return nil
}

func exportGUIData(c *gin.Context, dirPath string) error {
	if err := exportKVData(dao.GetManager(c), dirPath); err != nil {
		log.Errorf(c, "Failed to export kv store: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to export key-value store.")
		return err
	}

	if err := exportUserIcons(mw.GetConfig(c), dirPath); err != nil {
		log.Errorf(c, "Failed to export users' icons: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to export users' icons.")
		return err
	}

	return nil
}

func exportKVData(daoManager interfaces.DaoManager, tempDir string) error {
	kvData, err := daoManager.Export("")
	if err != nil {
		return err
	}
	// do not export sensitive data
	delete(kvData, dao.SystemImportKey)
	delete(kvData, dao.GBARInProgressKey)
	delete(kvData, dao.BackupSchedulePrefix)
	delete(kvData, dao.GSQLPasswordPrefix)
	for k := range kvData {
		if strings.HasPrefix(k, dao.SAMLRespPrefix) {
			delete(kvData, k)
		}
		if strings.HasPrefix(k, dao.JobLogPrefix) {
			delete(kvData, k)
		}
		if strings.HasPrefix(k, dao.BackupStatusPrefix) {
			delete(kvData, k)
		}
		if strings.HasPrefix(k, dao.SessionPrefix) {
			delete(kvData, k)
		}
		if strings.HasPrefix(k, apitoken.APITokenPrefix) {
			delete(kvData, k)
		}
		if strings.HasPrefix(k, apitoken.InsightsTokenPrefix) {
			delete(kvData, k)
		}
		if strings.HasPrefix(k, dao.ERPrefix) {
			delete(kvData, k)
		}
		if strings.HasPrefix(k, dao.JobInfoPrefix) {
			var jobInfo []model.LoadingJobInfo
			data, err := db.Decompress(kvData[k])
			if err != nil {
				log.Errorf("Failed to decompress job info data: %v", err)
				return err
			}

			if err := tgJSON.Unmarshal(data, &jobInfo); err == nil {
				for i := range jobInfo {
					sampleData := jobInfo[i].SampleData
					if len(sampleData) > 0 {
						row := make([]string, len(sampleData[0]))
						jobInfo[i].SampleData = [][]string{row}
					}
				}
				if bytes, err := tgJSON.Marshal(jobInfo); err == nil {
					value, err := db.Compress(bytes)
					if err == nil {
						kvData[k] = value
					} else {
						log.Errorf("Failed to compress job info data: %v", err)
						return err
					}
				} else {
					log.Errorf("Failed to marshal job info: %v", err)
					return err
				}
			} else {
				log.Errorf("Failed to unmarshal job info: %v", err)
				return err
			}
		}
	}

	bytes, _ := tgJSON.Marshal(kvData)
	dataFile := path.Join(tempDir, KVDataExportedFile)
	return os.WriteFile(dataFile, bytes, 0600)
}

func exportUserIcons(cfg *config.Config, tempDir string) error {
	src := path.Join(cfg.GetDataDirPath(), data.GetCategory().UserIcon)
	dst := path.Join(tempDir, data.GetCategory().UserIcon)

	if err := os.MkdirAll(src, 0700); err != nil {
		return err
	}
	return copy.Copy(src, dst)
}

func mergeGraphData(cfg *config.Config, graphTempDir string, mergingTempDir string) error {
	GPEHostIDs, err := cfg.GetAllGPEHostIDs()
	if err != nil {
		return errors.Wrap(err, "Failed to get all GSQLHostIDs")
	}
	log.Debug("Export debug: all GSQLHostIDs: ", GPEHostIDs)

	localHostID, err := cfg.GetHostID()
	log.Debug("Export debug: localHostID", localHostID)
	if err != nil {
		return errors.Wrap(err, "Failed to get local host ID")
	}

	tgFS := fs.NewTGFilesystem(cfg)
	log.Debug("Export debug: tg filesystem created")

	for _, hostID := range GPEHostIDs {
		log.Debug("Export debug: start handle hostID", hostID)
		if hostID == localHostID {
			log.Error("Export debug: skip merging graph data from local host: ", hostID)
			continue
		}
		log.Debug("Export debug: merging graph data from host: ", hostID)
		if err := mergeGraphDataWithRemote(tgFS, hostID, localHostID, graphTempDir, mergingTempDir); err != nil {
			return err
		}
	}
	log.Debug("Export debug: finish merging graph data from other hosts")

	return nil
}

func mergeGraphDataWithRemote(tgFS *fs.TGFileSystem, remoteHostID, localHostID string, graphTempDir, mergingTempDir string) error {
	tempCopyDir := path.Join(mergingTempDir, remoteHostID)
	if err := os.MkdirAll(tempCopyDir, 0700); err != nil {
		return errors.Wrap(err, "Failed to create temp copy dir")
	}
	log.Debug("Export debug: merging graph data from host to host", remoteHostID, localHostID)

	// copy exported graph data from remote host to local host
	if err := tgFS.CopyFile(graphTempDir, remoteHostID, tempCopyDir, []string{localHostID}, fs.FileTypeFolder); err != nil {
		return errors.Wrapf(err, "Failed to copy exported graph data from %s to %s", remoteHostID, localHostID)
	}

	// once copied, remove the exported graph data from remote host
	if err := tgFS.Remove(graphTempDir, []string{remoteHostID}); err != nil {
		return errors.Wrapf(err, "Failed to remove exported graph data in %s ", remoteHostID)
	}

	return mergeExportData(graphTempDir, tempCopyDir)
}

func mergeExportData(graphTempDir string, mergingTempDir string) error {

	// merge csv files from other servers
	return ioFS.WalkDir(os.DirFS(mergingTempDir), ".", func(p string, d ioFS.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() && strings.HasSuffix(p, ".csv") {
			srcCsvPath := path.Join(mergingTempDir, p)
			dstCsvPath := path.Join(graphTempDir, p)
			log.Debug("Export debug: merging csv file to ", srcCsvPath, dstCsvPath)

			// if file with same name exists in the local path, append src file to it
			if _, err := os.Stat(dstCsvPath); os.IsNotExist(err) {
				log.Debug("Export debug: file not exist, copy file to ", dstCsvPath)
				return nil
			}

			if err := mergeDataFiles(srcCsvPath, dstCsvPath); err != nil {
				return err
			}

		}
		return nil
	})
}

func mergeDataFiles(srcPath, dstPath string) error {
	srcFile, err := os.Open(srcPath)
	if err != nil {
		log.Error("Export debug: Failed to open source file: ", err)
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.OpenFile(dstPath, os.O_WRONLY|os.O_APPEND|os.O_CREATE, 0644)
	if err != nil {
		log.Error("Export debug: Failed to open destination file: ", err)
		return err
	}
	defer dstFile.Close()

	reader := bufio.NewReader(srcFile)
	writer := bufio.NewWriter(dstFile)

	// skip the first line of the source file, use 0x1c(file separator) as the delimiter
	_, err = reader.ReadBytes(0x1c)
	if err == io.EOF { // if no FS is found, skip
		return nil
	}
	if err != nil {
		log.Error("Export debug: Failed to read source file: ", err)
		return err
	}

	for {
		_, err = io.CopyN(writer, reader, 4096)
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Error(nil, "Export debug: Failed to copy data to destination file: ", err)
			return err
		}
	}

	err = writer.Flush()
	if err != nil {
		log.Error(nil, "Export debug: Failed to flush destination file: ", err)
		return err
	}

	return nil
}
