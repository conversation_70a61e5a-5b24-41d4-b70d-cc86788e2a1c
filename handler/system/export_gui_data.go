package system

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

// ExportGUIData exports kv data and users' icons.
func ExportGUIData(c *gin.Context) {
	tempDir, err := createExportTempDir(mw.GetConfig(c))
	if err != nil {
		log.Errorf(c, "Failed to create directory %s: %v", tempDir, err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to create temporary directory for export.")
		return
	}
	defer os.RemoveAll(tempDir)

	if err = exportGUIData(c, tempDir); err != nil {
		return
	}

	filename := fmt.Sprintf("%s.tar.gz", filepath.Base(tempDir))
	c.<PERSON>er("Content-Type", "application/octet-stream")
	c.<PERSON><PERSON>("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))

	c.Stream(func(w io.Writer) bool {
		if err := fs.Compress(tempDir, w); err != nil {
			log.Errorf(c, "Failed to compress file with path %s: %v", tempDir, err)
		}
		return false
	})
}
