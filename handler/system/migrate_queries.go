package system

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/lib/tg"
	mw "github.com/tigergraph/gus/middleware"
)

type Response struct {
	FailedQueries []string `json:"failedQueries"`
}

func MigrateQueries(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	cfg := mw.GetConfig(c)
	daoManager := dao.GetManager(c)

	keys, err := daoManager.GetAllKeys(dao.QDPrefix)
	if err != nil {
		log.Errorf(c, "Failed to get all draft query keys: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to get draft queries.", nil)
		return
	}

	graphProcessed := make(map[string]bool)
	failedQueries := make([]string, 0)
	for _, key := range keys {
		graphName := strings.Split(key, "/")[1]
		if _, ok := graphProcessed[graphName]; ok {
			continue
		}
		graphProcessed[graphName] = true
		queries, err := daoManager.GetAllQueryDrafts(graphName)
		if err != nil {
			log.Errorf(c, "Failed to get all draft queries for graph %s: %v", graphName, err)
			continue
		}
		for _, query := range queries {
			result, err := tg.RequestGSQLClientByAuthToken(c, cfg, graphName, cfg.GetAuthToken(), query.Code)
			msg := strings.TrimSpace(string(result))
			if err != nil && !strings.Contains(msg, "Saved as draft") {
				log.Errorf(c, "Failed to create query %s for graph %s: %v", query.Name, graphName, err)
				failedQueries = append(failedQueries, query.Name)
				continue
			}
			daoManager.DeleteQueryDraft(graphName, query.Name)
		}
	}
	mw.ReplyWithResult(c, http.StatusOK, "", Response{FailedQueries: failedQueries})
}
