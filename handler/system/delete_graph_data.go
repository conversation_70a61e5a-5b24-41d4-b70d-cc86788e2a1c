package system

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

// DeleteGraphData deletes all data from the graph store.
func DeleteGraphData(c *gin.Context, gsqliClient interfaces.RequestGSQLClient) {
	cmd := "CLEAR GRAPH STORE -HARD"
	output, err := gsqliClient(c, mw.GetConfig(c), "", mw.GetUsername(c), mw.GetPassword(c), cmd)
	cleanOutput := strings.TrimSpace(string(output))
	if err != nil {
		log.Errorf(c, "Failed to delete all data from the graph store: %v: %s", err, cleanOutput)
		mw.Abort(c, http.StatusInternalServerError, cleanOutput)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", cleanOutput)
}
