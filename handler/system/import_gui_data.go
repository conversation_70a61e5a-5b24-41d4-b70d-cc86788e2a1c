package system

import (
	"fmt"
	"net/http"
	"os"
	"path"
	"path/filepath"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/lib/fs"
	mw "github.com/tigergraph/gus/middleware"
)

// ImportGUIData imports kv data and users' icons.
func ImportGUIData(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	header, err := c.FormFile("file")
	if err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	locker, err := lockForImport()
	if err != nil {
		log.Errorf(c, "Failed to lock for import: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to lock for import.")
		return
	}
	defer func() { _ = locker.Unlock(dao.SystemImportKey) }()

	daoManager := dao.GetManager(c)
	if err = daoManager.SetImport(true); err != nil {
		log.Errorf(c, "Failed to set import status to true: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to set import status.")
		return
	}
	defer func() {
		if err = daoManager.SetImport(false); err != nil {
			log.Errorf(c, "Failed to set import status to false: %v", err)
		}
	}()

	tempDir, err := createImportTempDir(mw.GetConfig(c))
	if err != nil {
		log.Errorf(c, "Failed to create directory %s: %v", tempDir, err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to create temporary directory for import.")
		return
	}
	defer os.RemoveAll(tempDir)

	if err = decompressFileToDisk(header, tempDir); err != nil {
		filename := filepath.Base(header.Filename)
		log.Errorf(c, "Failed to decompress file %s to %s: %v", filename, tempDir, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to decompress file '%s' to disk.", filename),
		)
		return
	}

	if err = checkDBCoversion(tempDir); err != nil {
		log.Errorf(c, "Failed to convert database: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to convert database.")
		return
	}

	if err = importGUIData(c, daoManager, tempDir, gsqlClient); err != nil {
		return
	}

	mw.Reply(c, http.StatusOK, "Successfully imported GUI data.")
}

// checkDBCoversion finds the file path that stores the GUI metadata,
// and checks if the file is a SQLite3 database file.
// If it is, converts the data from the SQLite3 database to the JSON file.
func checkDBCoversion(tempDir string) error {
	dbFilePath, shouldConvert, err := checkDBNameForConversion(tempDir)
	if err != nil {
		return err
	}

	if shouldConvert {
		jsonFilePath := path.Join(tempDir, KVDataExportedFile)
		if _, err := os.Create(jsonFilePath); err != nil {
			return err
		}

		return convertDB(dbFilePath, jsonFilePath)
	}
	return nil
}

func checkDBNameForConversion(tempDir string) (string, bool, error) {
	filepath := path.Join(tempDir, GUIDBFile)
	exist, err := fs.Exist(filepath)
	if err != nil {
		return "", false, err
	}
	if exist {
		return filepath, true, nil
	}
	return "", false, nil
}
