package system

import (
	"database/sql"
	"encoding/json"
	"os"
	"path"
	"strings"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/data"
	"github.com/tigergraph/gus/lib/fs"
	tgJSON "github.com/tigergraph/gus/lib/json"
	"github.com/tigergraph/gus/service/db"

	// Go library for SQLite3 operations.
	_ "github.com/mattn/go-sqlite3"
)

const (
	TemplateDir = "template"
	GUIDBFile   = "gui.db"
)

type SQLiteJobLog struct {
	ID         string                 `json:"id" binding:"required"`
	Error      bool                   `json:"error" binding:"required"`
	Message    string                 `json:"message" binding:"required"`
	Expiration string                 `json:"expiration" binding:"required"`
	Progress   map[string]interface{} `json:"progress" binding:"required"`
}

type SQLiteJobInfo struct {
	GraphName              string                 `json:"graphName" binding:"required"`
	LoadingJobName         string                 `json:"loadingJobName" binding:"required"`
	LoadingStatementsStyle []interface{}          `json:"loadingStatementsStyle" binding:"required"`
	DataSourceJSON         map[string]interface{} `json:"dataSourceJson" binding:"required"`
	Header                 []interface{}          `json:"header" binding:"required"`
	SampleData             []interface{}          `json:"sampleData" binding:"required"`
}

// migrateSolution migrates a solution from 3.0.x to 3.1.0+.
func migrateSolution(tempDir string) error {
	if err := restructureDir(tempDir); err != nil {
		return err
	}

	jsonFilePath := path.Join(tempDir, GUIExportedDir, KVDataExportedFile)
	if _, err := os.Create(jsonFilePath); err != nil {
		return err
	}
	dbFilePath := path.Join(tempDir, GUIDBFile)
	return convertDB(dbFilePath, jsonFilePath)
}

// restructureDir restructures the directory of the exported solution
// to be 3.1.0+ compatible.
func restructureDir(tempDir string) error {
	templateDirPath := path.Join(tempDir, TemplateDir)
	if _, err := fs.Exist(templateDirPath); err != nil {
		return err
	}
	graphDirPath := path.Join(tempDir, GraphExportedDir)
	if err := os.Rename(templateDirPath, graphDirPath); err != nil {
		return err
	}

	guiDirPath := path.Join(tempDir, GUIExportedDir)
	if err := os.MkdirAll(guiDirPath, 0700); err != nil {
		return err
	}

	userIconsDirPath := path.Join(tempDir, data.GetCategory().UserIcon)
	if _, err := fs.Exist(userIconsDirPath); err != nil {
		return err
	}
	newUserIconsDirPath := path.Join(guiDirPath, data.GetCategory().UserIcon)
	if err := os.Rename(userIconsDirPath, newUserIconsDirPath); err != nil {
		return err
	}

	return nil
}

// convertDB converts the data from the SQLite3 database to the JSON file.
// Supports from both 2.6.x and from 3.0.x.
// NOTE: 2.6.x does not have a visual_pattern table.
func convertDB(src string, dst string) error {
	if exist, err := fs.Exist(src); !exist {
		return err
	}
	if exist, err := fs.Exist(dst); !exist {
		return err
	}

	jsonKV := make(map[string]interface{})
	if err := convertExplorationResult(src, jsonKV); err != nil {
		log.Warnf("Failed to convert exploration result: %v", err)
	}
	if err := convertKVStore(src, jsonKV); err != nil {
		log.Warnf("Failed to convert kv store: %v", err)
	}
	if err := convertLoadingJobLog(src, jsonKV); err != nil {
		log.Warnf("Failed to convert loading job log: %v", err)
	}
	if err := convertQueryDraft(src, jsonKV); err != nil {
		log.Warnf("Failed to convert query draft: %v", err)
	}
	if err := convertVisualPattern(src, jsonKV); err != nil {
		log.Warnf("Failed to convert visual pattern: %v", err)
	}

	return writeMapToJSONFile(jsonKV, dst)
}

func convertExplorationResult(src string, jsonKV map[string]interface{}) error {
	database, err := sql.Open("sqlite3", src)
	if err != nil {
		return err
	}
	defer database.Close()

	rows, err := database.Query("SELECT * FROM exploration_result")
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var graphName string
		exploration := &model.ExplorationResult{}
		if err = rows.Scan(&exploration.Name, &graphName,
			&exploration.Username, &exploration.Timestamp,
			&exploration.Schema, &exploration.PreviewImage,
			&exploration.Data, &exploration.Description); err != nil {
			return err
		}

		bytes, err := tgJSON.Marshal(exploration)
		if err != nil {
			return err
		}
		value, err := db.Compress(bytes)
		if err != nil {
			return err
		}

		var key string = dao.GetDBKey(dao.ERPrefix, graphName, exploration.Name)
		jsonKV[key] = value
	}
	return rows.Err()
}

// convertKVStore converts two types of key-value pairs stored in v3.0.x:
//   - The first type is global or local graph_style.
//   - The second type is loading_job_info.
func convertKVStore(src string, jsonKV map[string]interface{}) error {
	database, err := sql.Open("sqlite3", src)
	if err != nil {
		return err
	}
	defer database.Close()

	rows, err := database.Query("SELECT * FROM key_value_store")
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var (
			k string
			v string
		)
		if err = rows.Scan(&k, &v); err != nil {
			return err
		}

		k = strings.Trim(k, "\"")
		if strings.HasPrefix(k, "job") {
			if err := convertJobInfo(v, jsonKV); err != nil {
				return err
			}
		} else {
			if err := convertGraphStyle(k, v, jsonKV); err != nil {
				return err
			}
		}
	}
	return rows.Err()
}

func convertJobInfo(data string, jsonKV map[string]interface{}) error {
	var jobInfo []SQLiteJobInfo
	if err := json.Unmarshal([]byte(data), &jobInfo); err != nil {
		return err
	}

	type kvJobInfo struct {
		LoadingJobName         string                 `json:"loadingJobName" binding:"required"`
		LoadingStatementsStyle []interface{}          `json:"loadingStatementsStyle" binding:"required"`
		DataSourceJSON         map[string]interface{} `json:"dataSourceJson" binding:"required"`
		Header                 []interface{}          `json:"header" binding:"required"`
		SampleData             []interface{}          `json:"sampleData" binding:"required"`
	}
	kv := make(map[string][]kvJobInfo)
	for _, info := range jobInfo {
		kvInfo := kvJobInfo{
			LoadingJobName:         info.LoadingJobName,
			LoadingStatementsStyle: info.LoadingStatementsStyle,
			DataSourceJSON:         info.DataSourceJSON,
			Header:                 info.Header,
			SampleData:             info.SampleData,
		}

		key := dao.GetDBKey(dao.JobInfoPrefix, info.GraphName)
		if _, ok := kv[key]; !ok {
			kv[key] = make([]kvJobInfo, 0)
		}
		kv[key] = append(kv[key], kvInfo)
	}

	for key, value := range kv {
		bytes, err := tgJSON.Marshal(value)
		if err != nil {
			return err
		}
		bytes, err = db.Compress(bytes)
		if err != nil {
			return err
		}
		jsonKV[key] = bytes
	}
	return nil
}

func convertGraphStyle(key string, value string, jsonKV map[string]interface{}) error {
	var (
		graphName string
		k         string
	)
	v, err := db.Compress([]byte(value))
	if err != nil {
		return err
	}

	if key == "schemaLayoutStyleContent" {
		k = dao.GetDBKey(dao.GSPrefix, "global")
	} else {
		graphName = strings.TrimPrefix(key, "schemaLayoutStyleContent_")
		k = dao.GetDBKey(dao.GSPrefix, graphName)
	}

	jsonKV[k] = v
	return nil
}

func convertLoadingJobLog(src string, jsonKV map[string]interface{}) error {
	database, err := sql.Open("sqlite3", src)
	if err != nil {
		return err
	}
	defer database.Close()

	rows, err := database.Query("SELECT * FROM loading_job_cache")
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var (
			graphName string
			jobName   string
			log       SQLiteJobLog
			v         string
		)
		if err = rows.Scan(&jobName, &graphName, &log.Expiration, &v); err != nil {
			return err
		}

		if err = json.Unmarshal([]byte(v), &log); err != nil {
			return err
		}
		bytes, err := tgJSON.Marshal(log)
		if err != nil {
			return err
		}
		value, err := db.Compress(bytes)
		if err != nil {
			return err
		}

		var key string = dao.GetDBKey(dao.JobLogPrefix, graphName, jobName)
		jsonKV[key] = value
	}
	return rows.Err()
}

func convertQueryDraft(src string, jsonKV map[string]interface{}) error {
	database, err := sql.Open("sqlite3", src)
	if err != nil {
		log.Errorf(err.Error())
	}
	defer database.Close()

	rows, err := database.Query("SELECT * FROM query_draft")
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var graphName string
		draft := &model.QueryDraft{}
		if err = rows.Scan(&draft.Name, &graphName, &draft.Code); err != nil {
			return err
		}

		bytes, err := tgJSON.Marshal(draft)
		if err != nil {
			return err
		}
		value, err := db.Compress(bytes)
		if err != nil {
			return err
		}

		var key string = dao.GetDBKey(dao.QDPrefix, graphName, draft.Name)
		jsonKV[key] = value
	}
	return rows.Err()
}

func convertVisualPattern(src string, jsonKV map[string]interface{}) error {
	database, err := sql.Open("sqlite3", src)
	if err != nil {
		return err
	}
	defer database.Close()

	rows, err := database.Query("SELECT * FROM visual_pattern")
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var (
			patternName string
			graphName   string
			pattern     string
		)
		if err = rows.Scan(&patternName, &graphName, &pattern); err != nil {
			return err
		}

		value, err := db.Compress([]byte(pattern))
		if err != nil {
			return err
		}

		var key string = dao.GetDBKey(dao.VPPrefix, graphName, patternName)
		jsonKV[key] = value
	}
	return rows.Err()
}

// writeMapToJSONFile writes a map of key-value pairs to the JSON file.
func writeMapToJSONFile(jsonKV map[string]interface{}, dst string) error {
	bytes, err := tgJSON.Marshal(jsonKV)
	if err != nil {
		return err
	}
	if err = os.WriteFile(dst, bytes, 0600); err != nil {
		return err
	}
	return nil
}
