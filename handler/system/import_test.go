package system

import (
	"encoding/base64"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPatchInsightsOwner(t *testing.T) {
	// Create test data
	// 1. Set up GAAllApps key with sample data
	testApps := []map[string]interface{}{
		{
			"Id":         "hdj4QvbrBmosjjusqx3k5S",
			"Title":      "insights_test_app_1",
			"Screenshot": nil,
			"Icon":       "/studio/assets/gvis/icons/builtin/64/060-atomic.png",
			"Username":   "old_user",
			"Version":    "1726721779073706391",
		},
		{
			"Id":         "uvrPmeGV3Gv6pKSFv6gaHz",
			"Title":      "insights_test_app_2",
			"Screenshot": nil,
			"Icon":       "/studio/assets/gvis/icons/builtin/64/060-atomic.png",
			"Username":   "another_old_user",
			"Version":    "1726722153531949127",
		},
	}

	// Marshal and encode data
	jsonData, _ := json.Marshal(testApps)
	encodedData := base64.StdEncoding.EncodeToString(jsonData)

	// 2. Create test kvData with GAAllApps and some GAApp.* keys
	kvData := map[string][]byte{
		"GAAllApps":                                     []byte(encodedData),
		"GAApp.hdj4QvbrBmosjjusqx3k5S.old_user":         []byte("app1_data"),
		"GAApp.uvrPmeGV3Gv6pKSFv6gaHz.another_old_user": []byte("app2_data"),
		"SomeOtherKey":                                  []byte("unrelated_data"),
	}

	// Test patching the insights owner
	newUsername := "new_test_user"
	patchInsightsOwner(kvData, newUsername)

	// Verify the results

	// 1. Check that GAAllApps was properly updated
	if gaAllApps, exists := kvData["GAAllApps"]; assert.True(t, exists) {
		decodedData, err := base64.StdEncoding.DecodeString(string(gaAllApps))
		assert.NoError(t, err)

		var updatedApps []map[string]interface{}
		err = json.Unmarshal(decodedData, &updatedApps)
		assert.NoError(t, err)
		assert.Len(t, updatedApps, 2)

		// Verify all apps have the new username
		for _, app := range updatedApps {
			assert.Equal(t, newUsername, app["Username"])
		}
	}

	// 2. Check that GAApp.* keys were updated
	_, oldApp1Exists := kvData["GAApp.hdj4QvbrBmosjjusqx3k5S.old_user"]
	assert.False(t, oldApp1Exists, "Original GAApp key for app1 should not exist")

	_, oldApp2Exists := kvData["GAApp.uvrPmeGV3Gv6pKSFv6gaHz.another_old_user"]
	assert.False(t, oldApp2Exists, "Original GAApp key for app2 should not exist")

	app1Data, newApp1Exists := kvData["GAApp.hdj4QvbrBmosjjusqx3k5S.new_test_user"]
	assert.True(t, newApp1Exists, "New GAApp key for app1 should exist")
	assert.Equal(t, []byte("app1_data"), app1Data)

	app2Data, newApp2Exists := kvData["GAApp.uvrPmeGV3Gv6pKSFv6gaHz.new_test_user"]
	assert.True(t, newApp2Exists, "New GAApp key for app2 should exist")
	assert.Equal(t, []byte("app2_data"), app2Data)

	// 3. Check that other keys are unaffected
	otherData, otherExists := kvData["SomeOtherKey"]
	assert.True(t, otherExists, "Unrelated keys should not be affected")
	assert.Equal(t, []byte("unrelated_data"), otherData)
}
