package system

import (
	"archive/zip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	tgCfg "github.com/tigergraph/cqrs/tutopia/common/config"
	"github.com/tigergraph/cqrs/tutopia/common/pb"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/data"
	"github.com/tigergraph/gus/handler/datasource"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	tgSync "github.com/tigergraph/gus/lib/sync"
	"github.com/tigergraph/gus/lib/tg"
	"github.com/tigergraph/gus/lib/typeconv"
	mw "github.com/tigergraph/gus/middleware"
	apitoken "github.com/tigergraph/gus/service/api_token"
	"github.com/tigergraph/gus/service/db"
)

const ImportTempDir = "imports"
const ExportedGraph = "ExportedGraph.zip"

// Config string constants
const (
	ConfigTrue  = "true"
	ConfigFalse = "false"
)

type ImportCheckResult struct {
	GraphNames  []string `json:"graphNames"`
	TempDirName string   `json:"tempDirName"`
}

type SolutionCatalogItem struct {
	Name        string   `json:"name"`
	Graph       string   `json:"graph"`
	Depends     string   `json:"depends"`
	Description string   `json:"description"`
	Categories  []string `json:"categories"`
	Algorithms  []string `json:"algorithms"`
	Icon        string   `json:"icon"`
	Images      []string `json:"images"`
	Provider    string   `json:"provider"`
	HasInsights bool     `json:"hasInsights"`
	DocLinks    []string `json:"docLinks"`
	Path        string   `json:"path"`
	InitQuery   string   `json:"initQuery"`
	IsLibrary   bool     `json:"is_library"`
}

// Import imports graph metadata, kv data and users' icons.
func ImportCheck(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	filename := c.Query("filename")
	cfg := mw.GetConfig(c)
	var solutionFilePath string
	var header *multipart.FileHeader
	var graphNames []string
	var err error
	if filename != "" {
		solutionFilePath = path.Join(cfg.GetDataDirPath(), "solution", url.PathEscape(mw.GetRealUsername(c)), filename)
		defer data.RemoveDirFromAllServers(c, cfg, solutionFilePath)
	} else {
		header, err = c.FormFile("file")
		if err != nil {
			log.Warnf(c, "Invalid payload: %+v", err)
			mw.ReplyWithResult(c, http.StatusBadRequest, "Invalid payload.", nil)
			return
		}
	}

	tempDir, err := createImportTempDir(cfg)
	if err != nil {
		log.Warnf(c, "Failed to create directory %s: %+v", tempDir, err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to create temporary directory for import.", nil)
		return
	}

	if solutionFilePath != "" {
		if err = decompressTarball(solutionFilePath, tempDir); err != nil {
			filename := filepath.Base(solutionFilePath)
			log.Infof(c, "Failed to decompress file %s to %s: %+v", filename, tempDir, err)
			mw.ReplyWithResult(c, http.StatusInternalServerError, fmt.Sprintf("Failed to decompress file '%s' to disk.", filename), nil)
			return
		}
	} else {
		if err = decompressFileToDisk(header, tempDir); err != nil {
			filename := filepath.Base(header.Filename)
			log.Infof(c, "Failed to decompress file %s to %s: %+v", filename, tempDir, err)
			mw.ReplyWithResult(c, http.StatusInternalServerError, fmt.Sprintf("Failed to decompress file '%s' to disk.", filename), nil)
			return
		}
	}
	// copy untared files to other nodes since the incoming upgrade request might be routed there
	data.CopyFileToAllServers(c, cfg, tempDir, fs.FileTypeFolder)

	if err = checkMigration(c, tempDir); err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	graphNames, err = getGraphNames(tempDir)

	if err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}
	result := ImportCheckResult{
		GraphNames:  graphNames,
		TempDirName: filepath.Base(tempDir),
	}

	mw.ReplyWithResult(c, http.StatusOK, "", &result)
}

// Import imports graph metadata, kv data and users' icons.
func Import(c *gin.Context, gsqlClient interfaces.RequestGSQLClient, controllerClient pb.ControllerClient) {
	graphNames := c.QueryArray("graphName")
	tempDirName := c.Query("tempDirName")
	cfg := mw.GetConfig(c)
	var err error

	// Parse new query params
	shouldEnableUDF := c.DefaultQuery("enableUDF", "false") == "true"
	installQueries := c.DefaultQuery("installQueries", "false") == "true"

	// Set up SSE headers
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")

	// Send initial status message
	c.SSEvent("message", model.Response{Results: "Starting solution import...\n"})
	c.Writer.Flush()

	udfRestoreNeeded := false

	restoreUDFIfNeeded := func() {
		if udfRestoreNeeded {
			c.SSEvent("message", model.Response{Results: "Restoring UDF configuration...\n"})
			c.Writer.Flush()
			if err := disableUDF(c, controllerClient); err != nil {
				log.Errorf(c, "Failed to disable UDF: %v", err)
				c.SSEvent("error", model.Response{Error: true, Message: "Failed to restore UDF configuration."})
				c.Writer.Flush()
				return
			}
			c.SSEvent("message", model.Response{Results: "Waiting for GSQL service to come online after restoring UDF config...\n"})
			c.Writer.Flush()
			if err := waitForGSQLOnline(c, cfg); err != nil {
				log.Errorf(c, "Failed waiting for GSQL after disabling UDF: %v", err)
				c.SSEvent("error", model.Response{Error: true, Message: "GSQL service did not come online after restoring UDF configuration."})
				c.Writer.Flush()
				return
			}
			c.SSEvent("message", model.Response{Results: "UDF configuration restored.\n"})
			c.Writer.Flush()
		}
	}

	// UDF logic only if enableUDF is true
	if shouldEnableUDF {
		// Check if UDF is already enabled
		c.SSEvent("message", model.Response{Results: "Checking UDF configuration...\n"})
		c.Writer.Flush()

		udfEnabled, err := isUDFAlreadyEnabled(c, controllerClient)
		if err != nil {
			log.Errorf(c, "Failed to check UDF status: %v", err)
			c.SSEvent("error", model.Response{Error: true, Message: "Failed to check UDF status."})
			c.Writer.Flush()
			return
		}

		if !udfEnabled {
			c.SSEvent("message", model.Response{Results: "Enabling UDF support for solution import...\n"})
			c.Writer.Flush()

			log.Infof(c, "Enabling UDF support for solution import")
			if err := enableUDF(c, controllerClient); err != nil {
				log.Errorf(c, "Failed to enable UDF: %v", err)
				c.SSEvent("error", model.Response{Error: true, Message: "Failed to enable UDF support."})
				c.Writer.Flush()
				return
			}

			// Mark that we need to restore UDF config after import
			udfRestoreNeeded = true

			// Wait for GSQL to be online after configuration changes
			c.SSEvent("message", model.Response{Results: "Waiting for GSQL service to come online...\n"})
			c.Writer.Flush()

			if err := waitForGSQLOnline(c, cfg); err != nil {
				log.Errorf(c, "Failed waiting for GSQL: %v", err)
				restoreUDFIfNeeded()
				return
			}

			c.SSEvent("message", model.Response{Results: "Successfully enabled UDF support\n"})
			c.Writer.Flush()
			log.Infof(c, "Successfully enabled UDF support")
		} else {
			c.SSEvent("message", model.Response{Results: "UDF is already enabled, skipping configuration\n"})
			c.Writer.Flush()
			log.Infof(c, "UDF is already enabled, skipping configuration")
		}
	}

	c.SSEvent("message", model.Response{Results: "Acquiring import lock...\n"})
	c.Writer.Flush()

	locker, err := lockForImport()
	if err != nil {
		log.Warnf(c, "Failed to lock for import: %+v", err)
		c.SSEvent("error", model.Response{Error: true, Message: "Failed to lock for import."})
		c.Writer.Flush()
		restoreUDFIfNeeded()
		return
	}
	defer func() { _ = locker.Unlock(dao.SystemImportKey) }()

	daoManager := dao.GetManager(c)
	if err = daoManager.SetImport(true); err != nil {
		log.Warnf(c, "Failed to set import status to true: %+v", err)
		c.SSEvent("error", model.Response{Error: true, Message: "Failed to set import status."})
		c.Writer.Flush()
		restoreUDFIfNeeded()
		return
	}
	defer func() {
		if err = daoManager.SetImport(false); err != nil {
			log.Errorf(c, "Failed to set import status to false: %v", err)
		}
	}()

	tempDir, err := getImportTempDir(cfg, tempDirName)
	if err != nil {
		log.Warnf(c, "Failed to get directory %s: %+v", tempDir, err)
		c.SSEvent("error", model.Response{Error: true, Message: "Failed to get temporary directory for import."})
		c.Writer.Flush()
		restoreUDFIfNeeded()
		return
	}
	defer data.RemoveDirFromAllServers(c, cfg, tempDir)

	c.SSEvent("message", model.Response{Results: "Starting data import...\n"})
	c.Writer.Flush()

	if err = importDataStream(c, tempDir, gsqlClient, graphNames); err != nil {
		c.SSEvent("error", model.Response{Error: true, Message: err.Error()})
		c.Writer.Flush()
		restoreUDFIfNeeded()
		return
	}

	// If graphNames is empty, try to get graph names from the imported solution
	if len(graphNames) == 0 {
		graphNames, err = getGraphNames(tempDir)

		if err != nil {
			log.Errorf(c, "Failed to get graphNames: %v", err)
			c.SSEvent("error", model.Response{Error: true, Message: "Solution imported successfully, but failed to install all queries."})
			c.Writer.Flush()
			restoreUDFIfNeeded()
			return
		}
	}

	// Only install queries if installQueries is true
	if installQueries {
		c.SSEvent("message", model.Response{Results: "Installing all queries"})
		c.Writer.Flush()

		log.Infof(c, "Install all queries for graphs: %s", graphNames)
		if err := installAllQueriesStream(c, cfg, graphNames); err != nil {
			log.Errorf(c, "Failed to install all queries: %v", err)
			c.SSEvent("error", model.Response{Error: true, Message: "Solution imported successfully, but failed to install all queries."})
			c.Writer.Flush()
			restoreUDFIfNeeded()
			return
		}
	}

	// Restore UDF configuration if it was changed
	restoreUDFIfNeeded()

	// Now send the final done SSE event
	c.SSEvent("message", model.Response{
		Error:   false,
		Message: "",
		Results: "Successfully imported solution.\n",
		Done:    typeconv.Bool(true),
	})
	c.Writer.Flush()
}

func lockForImport() (*tgSync.KeyLocker, error) {
	locker, err := tgSync.NewKeyLocker()
	if err != nil {
		return nil, err
	}
	return locker, locker.TryLock(dao.SystemImportKey, time.Minute)
}

func getGraphNames(tempDir string) ([]string, error) {
	graphTempDir := path.Join(tempDir, GraphExportedDir, ExportedGraph)
	var err error
	var graphNames []string

	reader, err := zip.OpenReader(graphTempDir)
	if err != nil {
		log.Error("Import debug: Failed to open export graph directory: ", err)
		return nil, err
	}
	defer reader.Close()

	// Iterate over each file in the zip archive and print the file names
	for _, file := range reader.File {
		name := strings.TrimSuffix(file.Name, ".gsql")
		firstUnderscore := strings.Index(name, "_")
		if firstUnderscore != -1 && strings.HasPrefix(name, "DBImportExport") {
			graphNames = append(graphNames, name[firstUnderscore+1:])
		}
	}

	return graphNames, nil
}

func getImportTempDir(cfg *config.Config, tempDirName string) (string, error) {
	tempDir := path.Join(cfg.GetTempDirPath(), ImportTempDir, tempDirName)
	err := os.Chmod(tempDir, 0700)
	if err != nil {
		return tempDir, err
	}
	_, err = os.Stat(tempDir)
	if err != nil {
		return tempDir, err
	}
	return tempDir, nil
}

func createImportTempDir(cfg *config.Config) (string, error) {
	tempDir := path.Join(cfg.GetTempDirPath(), ImportTempDir)
	if err := os.MkdirAll(tempDir, 0700); err != nil {
		return "", err
	}
	return os.MkdirTemp(tempDir, "import_*")
}

func decompressFileToDisk(header *multipart.FileHeader, tempDir string) error {
	file, err := header.Open()
	if err != nil {
		return err
	}
	defer file.Close()
	return fs.Decompress(file, tempDir)
}

func decompressTarball(filePath string, tempDir string) error {
	// deepcode ignore PT: path is generated by caller using sso info of user
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()
	return fs.Decompress(file, tempDir)
}

// checkMigration checks the version of a solution.
// If its version is 3.0.x, migrates the solution.
func checkMigration(c *gin.Context, tempDir string) error {
	shouldMigrate, err := checkVersionForMigration(tempDir)
	if err != nil {
		log.Errorf(c, "Failed to check version for migration: %v", err)
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return err
	}

	if shouldMigrate {
		if err = migrateSolution(tempDir); err != nil {
			log.Errorf(c, "Failed to migrate solution: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to migrate solution.")
			return err
		}
	}

	return nil
}

func checkVersionForMigration(tempDir string) (bool, error) {
	versionFilePath := path.Join(tempDir, "version")
	exist, err := fs.Exist(versionFilePath)
	if err != nil {
		return false, err
	}
	// If there is no version file, assume version is 3.1.0+.
	if !exist {
		return false, nil
	}

	versionNum, err := os.ReadFile(versionFilePath)
	if err != nil {
		return false, err
	}

	if matched, _ := regexp.MatchString(`^\d\.\d\.\d$`, string(versionNum)); !matched {
		return false, fmt.Errorf("invalid version number, input version is %s", versionNum)
	}

	if !strings.HasPrefix(string(versionNum), "3.0") {
		return false, fmt.Errorf("supported version for migration is 3.0.x, input version is %s", versionNum)
	}
	return true, nil
}

func importDataStream(c *gin.Context, tempDir string, gsqlClient interfaces.RequestGSQLClient, graphNames []string) error {
	c.SSEvent("message", model.Response{Results: "Importing graph data...\n"})
	c.Writer.Flush()

	if err := importGraphDataStream(c, tempDir, graphNames); err != nil {
		// Restrict solution import with UDF.
		if strings.Contains(err.Error(), "GSQL exited with code 214") {
			return fmt.Errorf(
				"The solution contains user-defined functions. " +
					"For security reasons, importing such solutions is disabled on this cluster. " +
					"<NAME_EMAIL> to help you move forward.",
			)
		}

		if strings.Contains(err.Error(), "GSQL exited with code 215") {
			return fmt.Errorf(
				"The solution contains user-defined functions," +
					" and the UDF file failed to import. Possible reasons:" +
					" i. syntax error in parsing the UDF file;" +
					" ii. the UDF file violates UDF policy;" +
					" iii. some function has name conflict with built-in functions (eg: to_string)." +
					" <NAME_EMAIL> to help you move forward.",
			)
		}

		return errors.WithMessage(err, "Failed to import graph")
	}

	c.SSEvent("message", model.Response{Results: "Graph data imported successfully\n"})
	c.Writer.Flush()

	c.SSEvent("message", model.Response{Results: "Importing GUI data...\n"})
	c.Writer.Flush()

	daoManager := dao.GetManager(c)
	guiTempDir := path.Join(tempDir, GUIExportedDir)
	if err := importGUIData(c, daoManager, guiTempDir, gsqlClient); err != nil {
		return err
	}

	c.SSEvent("message", model.Response{Results: "GUI data imported successfully\n"})
	c.Writer.Flush()

	return nil
}

func executeGSQLCommandStream(c *gin.Context, cfg *config.Config, cmd string) error {
	log.Infof(c, "Executing: %s", cmd)

	req := model.GSQLCommandRequest{
		Command: cmd,
		Cookies: make(model.GSQLCookies),
	}

	resp, err := tg.RequestGSQLFileHandler(c, mw.GetUserCredentials(c), cfg, req)
	if err != nil {
		log.Errorf(c, "Failed to get response from GSQL server: %v", err)
		return fmt.Errorf("failed to get response from GSQL server: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 300 {
		errMsg, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Errorf(c, "Failed to read error response from GSQL server: %v", err)
			return fmt.Errorf("failed to read error response from GSQL server: %v", err)
		}
		log.Errorf(c, "GSQL server returned error: %s", string(errMsg))
		return fmt.Errorf("GSQL server returned error: %s", string(errMsg))
	}

	// Use the shared streaming function
	err = tg.StreamGSQLResponseToSSE(c, &req, resp)
	if err != nil {
		return err
	}
	return nil
}

func importGraphDataStream(c *gin.Context, tempDir string, graphNames []string) error {
	graphTempDir := path.Join(tempDir, GraphExportedDir)
	var cmd string
	if len(graphNames) == 0 {
		cmd = "IMPORT GRAPH ALL"
	} else {
		cmd = "IMPORT GRAPH " + strings.Join(graphNames, ",")
	}
	cmd += fmt.Sprintf(" -KU FROM \"%s\"", graphTempDir)

	return executeGSQLCommandStream(c, mw.GetConfig(c), cmd)
}

func installAllQueriesStream(c *gin.Context, cfg *config.Config, graphNames []string) error {
	var commands []string
	for _, graphName := range graphNames {
		commands = append(commands, fmt.Sprintf("USE GRAPH %s\nINSTALL QUERY ALL", graphName))
	}
	cmd := strings.Join(commands, "\n")

	return executeGSQLCommandStream(c, cfg, cmd)
}

func importGUIData(c *gin.Context, daoManager interfaces.DaoManager, dirPath string, gsqlClient interfaces.RequestGSQLClient) error {
	jsonFilePath := path.Join(dirPath, KVDataExportedFile)
	if err := importKVData(c, daoManager, jsonFilePath, gsqlClient); err != nil {
		return errors.WithMessage(err, "Failed to import kv store")
	}

	userIconsDirPath := path.Join(dirPath, data.GetCategory().UserIcon)
	if err := importUserIcons(mw.GetConfig(c), userIconsDirPath); err != nil {
		return errors.WithMessage(err, "Failed to import users' icons")
	}

	return nil
}

func importKVData(c *gin.Context, daoManager interfaces.DaoManager, filepath string, gsqlClient interfaces.RequestGSQLClient) error {
	bytes, err := os.ReadFile(filepath)
	if err != nil {
		return err
	}

	kvData := make(map[string][]byte)
	if err = json.Unmarshal(bytes, &kvData); err != nil {
		return err
	}

	// Patch insights app ownership to current user
	username := mw.GetUsername(c)
	patchInsightsOwner(kvData, username)

	importLegacyDataSources(c, kvData, gsqlClient)

	samlData, err := daoManager.Export(dao.SAMLRespPrefix)
	if err != nil {
		return err
	}
	for k, v := range samlData {
		kvData[k] = v
	}

	pwdData, err := daoManager.Export(dao.GSQLPasswordPrefix)
	if err != nil {
		return err
	}
	for k, v := range pwdData {
		kvData[k] = v
	}

	backupSchedule, err := daoManager.Export(dao.BackupSchedulePrefix)
	if err != nil {
		return err
	}
	for k, v := range backupSchedule {
		kvData[k] = v
	}

	backupStatus, err := daoManager.Export(dao.BackupStatusPrefix)
	if err != nil {
		return err
	}
	for k, v := range backupStatus {
		kvData[k] = v
	}

	sessionData, err := daoManager.Export(dao.SessionPrefix)
	if err != nil {
		return err
	}
	for k, v := range sessionData {
		kvData[k] = v
	}

	apiTokenData, err := daoManager.Export(apitoken.APITokenPrefix)
	if err != nil {
		return err
	}
	for k, v := range apiTokenData {
		kvData[k] = v
	}

	insightTokenData, err := daoManager.Export(apitoken.InsightsTokenPrefix)
	if err != nil {
		return err
	}
	for k, v := range insightTokenData {
		kvData[k] = v
	}

	if err = daoManager.DeleteAll(); err != nil {
		return err
	}

	if len(kvData) == 0 {
		return nil
	}
	return daoManager.Import(kvData)
}

// patchInsightsOwner updates the ownership of insights applications to the specified username
func patchInsightsOwner(kvData map[string][]byte, username string) {
	// Process GAAllApps key
	if gaAllApps, exists := kvData["GAAllApps"]; exists {
		var appsData []map[string]interface{}

		// Try to decode base64 data
		decodedData, err := base64.StdEncoding.DecodeString(string(gaAllApps))
		if err == nil {
			// Unmarshal the decoded JSON data
			if err = json.Unmarshal(decodedData, &appsData); err == nil {
				// Update Username for each app
				for i := range appsData {
					if _, ok := appsData[i]["Username"]; ok {
						appsData[i]["Username"] = username
					}
				}

				// Marshal and re-encode to base64
				if jsonData, err := json.Marshal(appsData); err == nil {
					kvData["GAAllApps"] = []byte(base64.StdEncoding.EncodeToString(jsonData))
				}
			}
		}
	}

	// Process GAApp.* keys
	keysToUpdate := make([]string, 0)
	for key := range kvData {
		if strings.HasPrefix(key, "GAApp.") {
			keysToUpdate = append(keysToUpdate, key)
		}
	}

	for _, key := range keysToUpdate {
		parts := strings.Split(key, ".")
		if len(parts) >= 3 {
			// Create a new key with the updated username
			newKey := strings.Join(parts[:len(parts)-1], ".") + "." + username

			// Only update if the key format is as expected
			if newKey != key {
				kvData[newKey] = kvData[key]
				delete(kvData, key)
			}
		}
	}
}

// Import data sources saved in etcd to GSQL catalog.
// Note: the data source name is not unique in etcd.
// For example, we can have a gcs data source named "dev" and another s3 data source named "dev",
// To handle this, we need to append a suffix to the duplicate data source names
// and update the data source name in loading job info accordingly.
func importLegacyDataSources(c *gin.Context, kvData map[string][]byte, gsqlClient interfaces.RequestGSQLClient) {
	importedKeys := make([]string, 0)
	importedDsNames := make(map[string]int)
	for k, v := range kvData {
		if strings.HasPrefix(k, dao.DSPrefix) {
			keySplits := strings.Split(k, "/")
			graphName := keySplits[1]
			dataSourceType := keySplits[2]
			dataSourceName := keySplits[3]
			cnt, dsNameExist := importedDsNames[dataSourceName]
			if dsNameExist {
				newDsName := fmt.Sprintf("%s_%d", dataSourceName, cnt)
				importedDsNames[dataSourceName] = cnt + 1
				// update the data source name in loading job info
				loadingJobBytes := kvData[dao.GetDBKey(dao.JobInfoPrefix, graphName)]
				if loadingJobBytes != nil {
					var loadingJobInfo []model.LoadingJobInfo
					decompressedBytes, _ := db.Decompress(loadingJobBytes)
					if err := json.Unmarshal(decompressedBytes, &loadingJobInfo); err != nil {
						log.Errorf(c, "Failed to import data source key: %s: %v", k, err)
						continue
					}
					for i := range loadingJobInfo {
						if loadingJobInfo[i].DataSourceJson.DataSourceName == dataSourceName && loadingJobInfo[i].DataSourceJson.DataSourceType == dataSourceType {
							loadingJobInfo[i].DataSourceJson.DataSourceName = newDsName
						}
					}
					loadingJobBytes, _ = json.Marshal(loadingJobInfo)
					compressed, err := db.Compress(loadingJobBytes)
					if err != nil {
						log.Errorf(c, "Failed to import data source key: %s: %v", k, err)
						continue
					}
					kvData[dao.GetDBKey(dao.JobInfoPrefix, graphName)] = compressed
				}
				dataSourceName = newDsName
			} else {
				importedDsNames[dataSourceName] = 1
			}

			value, _ := db.Decompress(v)
			var dataSource interface{}
			switch dataSourceType {
			case model.AmazonS3:
				dataSource = &model.AmazonS3DataSource{}
			case model.GoogleCloudStorage:
				dataSource = &model.GoogleCloudStorageDataSource{}
			case model.AzureBlobStorage:
				dataSource = &model.AzureBlobStorageDataSource{}
			default:
				log.Errorf(c, "Failed to import data source key: %s: %v", k, errors.Errorf("unknown data source type: %s", dataSourceType))
				continue
			}
			if err := json.Unmarshal(value, dataSource); err != nil {
				log.Errorf(c, "Failed to import data source key: %s: %v", k, err)
				continue
			}

			if err := datasource.CreateGSQLDataSource(gsqlClient, c, dataSource, graphName, dataSourceName, dataSourceType); err != nil {
				log.Errorf(c, "Failed to import data source key: %s: %v", k, err)
				continue
			}
			importedKeys = append(importedKeys, k)
		}
	}

	for _, key := range importedKeys {
		delete(kvData, key)
	}
}

func importUserIcons(cfg *config.Config, srcDir string) error {
	hostID, err := cfg.GetHostID()
	if err != nil {
		return err
	}

	tgFS := fs.NewTGFilesystem(cfg)
	iconsInfo, err := tgFS.List(srcDir, hostID)
	if err != nil {
		return err
	}

	dstDir := path.Join(cfg.GetDataDirPath(), data.GetCategory().UserIcon)
	for _, iconInfo := range iconsInfo {
		dst := path.Join(dstDir, iconInfo.Name)
		if err := copyFileToAllGUIServers(cfg, iconInfo.Path, dst); err != nil {
			return err
		}
	}
	return nil
}

func copyFileToAllGUIServers(cfg *config.Config, srcPath, dstPath string) error {
	srcHostID, err := cfg.GetHostID()
	if err != nil {
		return err
	}

	dstHostIDs, err := cfg.GetAllGUIHostIDs()
	if err != nil {
		return err
	}

	tgFS := fs.NewTGFilesystem(cfg)
	return tgFS.CopyFile(srcPath, srcHostID, dstPath, dstHostIDs, fs.FileTypeFile)
}

// isUDFAlreadyEnabled checks if UDF is already properly configured
func isUDFAlreadyEnabled(ctx context.Context, cntlrClient pb.ControllerClient) (bool, error) {
	// Get current config from controller
	resp, err := cntlrClient.PullConfig(ctx, &pb.PullConfigRequest{Type: pb.ConfigType_AppliedConfig})
	if err != nil {
		return false, err
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != 0 {
		return false, fmt.Errorf("controller returned error: %v", resp.GetError())
	}

	cfg, err := tgCfg.NewConfigFromPb(resp.Config)
	if err != nil {
		return false, err
	}

	// Check UDF configuration values
	udfPolicyEnable, err := cfg.GetEntryAsString("GSQL.UDF.Policy.Enable", false)
	if err != nil {
		return false, err
	}

	enablePutExpr, err := cfg.GetEntryAsString("GSQL.UDF.EnablePutExpr", false)
	if err != nil {
		return false, err
	}

	enablePutTokenBank, err := cfg.GetEntryAsString("GSQL.UDF.EnablePutTokenBank", false)
	if err != nil {
		return false, err
	}

	enablePutTgExpr, err := cfg.GetEntryAsString("GSQL.UDF.EnablePutTgExpr", false)
	if err != nil {
		return false, err
	}

	// Check if UDF is already properly configured
	return udfPolicyEnable == ConfigFalse &&
		enablePutExpr == ConfigTrue &&
		enablePutTokenBank == ConfigTrue &&
		enablePutTgExpr == ConfigTrue, nil
}

func applyConfigs(cntlrClient pb.ControllerClient) error {
	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CONFIG_APPLY_TIMEOUT)
	defer cancel()

	resp, err := cntlrClient.ApplyConfig(ctx, &pb.ApplyConfigRequest{
		SpanId:  util.GenSpanId(util.APPLY_KEY),
		Force:   true,
		Initial: false,
	})
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.ApplyConfig)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

// enableUDF enables UDF for GSQL by modifying TigerGraph configurations
func enableUDF(ctx context.Context, cntlrClient pb.ControllerClient) error {
	configs := map[string]string{
		"GSQL.UDF.Policy.Enable":      ConfigFalse,
		"GSQL.UDF.EnablePutExpr":      ConfigTrue,
		"GSQL.UDF.EnablePutTokenBank": ConfigTrue,
		"GSQL.UDF.EnablePutTgExpr":    ConfigTrue,
	}

	c, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	var entries []*pb.ConfigEntry
	for k, v := range configs {
		entries = append(entries, &pb.ConfigEntry{Key: k, Value: v})
	}

	req := &pb.SetConfigRequest{
		SpanId:  util.GenSpanId(util.SET_KEY),
		Entries: entries,
	}
	resp, err := cntlrClient.SetConfig(c, req)
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.SetConfig)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return applyConfigs(cntlrClient)
}

// disableUDF disables UDF for GSQL by restoring TigerGraph configurations to their default (secure) state
func disableUDF(ctx context.Context, cntlrClient pb.ControllerClient) error {
	configs := map[string]string{
		"GSQL.UDF.Policy.Enable":      ConfigTrue,
		"GSQL.UDF.EnablePutExpr":      ConfigFalse,
		"GSQL.UDF.EnablePutTokenBank": ConfigFalse,
		"GSQL.UDF.EnablePutTgExpr":    ConfigFalse,
	}

	c, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	var entries []*pb.ConfigEntry
	for k, v := range configs {
		entries = append(entries, &pb.ConfigEntry{Key: k, Value: v})
	}

	req := &pb.SetConfigRequest{
		SpanId:  util.GenSpanId(util.SET_KEY),
		Entries: entries,
	}
	resp, err := cntlrClient.SetConfig(c, req)
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.SetConfig)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return applyConfigs(cntlrClient)
}

// waitForGSQLOnline polls until GSQL service is online
func waitForGSQLOnline(ctx context.Context, cfg *config.Config) error {
	const maxAttempts = 30 // Maximum number of attempts (5 minutes with 10-second intervals)
	const pollInterval = 10 * time.Second

	for attempt := 0; attempt < maxAttempts; attempt++ {
		cmdCtx, cancel := context.WithTimeout(ctx, cfg.GetHTTPRequestTimeout())
		cmd := exec.CommandContext(cmdCtx, "gadmin", "status", "gsql")
		output, err := cmd.CombinedOutput()
		cancel()

		if err == nil && strings.Contains(string(output), "Online") && strings.Contains(string(output), "Running") {
			log.Infof(ctx, "GSQL is online and running")
			return nil
		}

		log.Infof(ctx, "Waiting for GSQL to be online, attempt %d/%d", attempt+1, maxAttempts)
		time.Sleep(pollInterval)
	}

	return fmt.Errorf("GSQL did not come online after %d attempts", maxAttempts)
}
