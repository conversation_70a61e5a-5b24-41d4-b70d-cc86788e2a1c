package system

import (
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/handler/data"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	tgSync "github.com/tigergraph/gus/lib/sync"
	mw "github.com/tigergraph/gus/middleware"
	apitoken "github.com/tigergraph/gus/service/api_token"
)

const ImportTempDir = "imports"

// Import imports graph metadata, kv data and users' icons.
func Import(c *gin.Context, gsqlClient interfaces.RequestGSQLClient) {
	filename := c.Query("filename")
	cfg := mw.GetConfig(c)
	var solutionFilePath string
	var header *multipart.FileHeader
	var err error
	if filename != "" {
		solutionFilePath = path.Join(cfg.GetDataDirPath(), "solution", url.PathEscape(mw.GetRealUsername(c)), filename)
		defer data.RemoveDirFromAllServers(c, cfg, solutionFilePath)
	} else {
		header, err = c.FormFile("file")
		if err != nil {
			log.Warnf(c, "Invalid payload: %+v", err)
			mw.ReplyWithResult(c, http.StatusBadRequest, "Invalid payload.", nil)
			return
		}
	}

	locker, err := lockForImport()
	if err != nil {
		log.Warnf(c, "Failed to lock for import: %+v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to lock for import.", nil)
		return
	}
	defer func() { _ = locker.Unlock(dao.SystemImportKey) }()

	daoManager := dao.GetManager(c)
	if err = daoManager.SetImport(true); err != nil {
		log.Warnf(c, "Failed to set import status to true: %+v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to set import status.", nil)
		return
	}
	defer func() {
		if err = daoManager.SetImport(false); err != nil {
			log.Errorf(c, "Failed to set import status to false: %v", err)
		}
	}()

	tempDir, err := createImportTempDir(mw.GetConfig(c))
	if err != nil {
		log.Warnf(c, "Failed to create directory %s: %+v", tempDir, err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to create temporary directory for import.", nil)
		return
	}
	defer os.RemoveAll(tempDir)

	if solutionFilePath != "" {
		if err = decompressTarball(solutionFilePath, tempDir); err != nil {
			filename := filepath.Base(solutionFilePath)
			log.Infof(c, "Failed to decompress file %s to %s: %+v", filename, tempDir, err)
			mw.ReplyWithResult(c, http.StatusInternalServerError, fmt.Sprintf("Failed to decompress file '%s' to disk.", filename), nil)
			return
		}
	} else {
		if err = decompressFileToDisk(header, tempDir); err != nil {
			filename := filepath.Base(header.Filename)
			log.Infof(c, "Failed to decompress file %s to %s: %+v", filename, tempDir, err)
			mw.ReplyWithResult(c, http.StatusInternalServerError, fmt.Sprintf("Failed to decompress file '%s' to disk.", filename), nil)
			return
		}
	}

	if err = checkMigration(c, tempDir); err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	if err = importData(c, tempDir, gsqlClient); err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", "Successfully imported solution.")
}

func lockForImport() (*tgSync.KeyLocker, error) {
	locker, err := tgSync.NewKeyLocker()
	if err != nil {
		return nil, err
	}
	return locker, locker.TryLock(dao.SystemImportKey, time.Minute)
}

func createImportTempDir(cfg *config.Config) (string, error) {
	tempDir := path.Join(cfg.GetTempDirPath(), ImportTempDir)
	if err := os.MkdirAll(tempDir, 0700); err != nil {
		return "", err
	}
	return os.MkdirTemp(tempDir, "import_*")
}

func decompressFileToDisk(header *multipart.FileHeader, tempDir string) error {
	file, err := header.Open()
	if err != nil {
		return err
	}
	defer file.Close()
	return fs.Decompress(file, tempDir)
}

func decompressTarball(filePath string, tempDir string) error {
	// deepcode ignore PT: path is generated by caller using sso info of user
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()
	return fs.Decompress(file, tempDir)
}

// checkMigration checks the version of a solution.
// If its version is 3.0.x, migrates the solution.
func checkMigration(c *gin.Context, tempDir string) error {
	shouldMigrate, err := checkVersionForMigration(tempDir)
	if err != nil {
		log.Errorf(c, "Failed to check version for migration: %v", err)
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return err
	}

	if shouldMigrate {
		if err = migrateSolution(tempDir); err != nil {
			log.Errorf(c, "Failed to migrate solution: %v", err)
			mw.Abort(c, http.StatusInternalServerError, "Failed to migrate solution.")
			return err
		}
	}

	return nil
}

func checkVersionForMigration(tempDir string) (bool, error) {
	versionFilePath := path.Join(tempDir, "version")
	exist, err := fs.Exist(versionFilePath)
	if err != nil {
		return false, err
	}
	// If there is no version file, assume version is 3.1.0+.
	if !exist {
		return false, nil
	}

	versionNum, err := os.ReadFile(versionFilePath)
	if err != nil {
		return false, err
	}

	if matched, _ := regexp.MatchString(`^\d\.\d\.\d$`, string(versionNum)); !matched {
		return false, fmt.Errorf("invalid version number, input version is %s", versionNum)
	}

	if !strings.HasPrefix(string(versionNum), "3.0") {
		return false, fmt.Errorf("supported version for migration is 3.0.x, input version is %s", versionNum)
	}
	return true, nil
}

func importData(c *gin.Context, tempDir string, gsqlClient interfaces.RequestGSQLClient) error {
	if err := importGraphData(c, tempDir, gsqlClient); err != nil {
		// Restrict solution import with UDF.
		if strings.Contains(err.Error(), "GSQL exited with code 214") {
			return fmt.Errorf(
				"The solution contains user-defined functions. " +
					"For security reasons, importing such solutions is disabled on this cluster. " +
					"<NAME_EMAIL> to help you move forward.",
			)
		}

		if strings.Contains(err.Error(), "GSQL exited with code 215") {
			return fmt.Errorf(
				"The solution contains user-defined functions," +
					" and the UDF file failed to import. Possible reasons:" +
					" i. syntax error in parsing the UDF file;" +
					" ii. the UDF file violates UDF policy;" +
					" iii. some function has name conflict with built-in functions (eg: to_string)." +
					" <NAME_EMAIL> to help you move forward.",
			)
		}

		return errors.WithMessage(err, "Failed to import graph")
	}

	daoManager := dao.GetManager(c)
	guiTempDir := path.Join(tempDir, GUIExportedDir)
	if err := importGUIData(c, daoManager, guiTempDir); err != nil {
		return err
	}

	return nil
}

func importGraphData(c *gin.Context, tempDir string, gsqliClient interfaces.RequestGSQLClient) error {
	graphTempDir := path.Join(tempDir, GraphExportedDir)
	cmd := fmt.Sprintf("IMPORT GRAPH ALL -KU FROM \"%s\"", graphTempDir)
	output, err := gsqliClient(c, mw.GetConfig(c), "", mw.GetUsername(c), mw.GetPassword(c), cmd)
	if err != nil {
		log.Errorf(c, "Failed to import graph: %v: %s", err, strings.TrimSpace(string(output)))
		return fmt.Errorf("%v %s", err, output)
	}
	return nil
}

func importGUIData(c *gin.Context, daoManager interfaces.DaoManager, dirPath string) error {
	jsonFilePath := path.Join(dirPath, KVDataExportedFile)
	if err := importKVData(daoManager, jsonFilePath); err != nil {
		return errors.WithMessage(err, "Failed to import kv store")
	}

	userIconsDirPath := path.Join(dirPath, data.GetCategory().UserIcon)
	if err := importUserIcons(mw.GetConfig(c), userIconsDirPath); err != nil {
		return errors.WithMessage(err, "Failed to import users' icons")
	}

	return nil
}

func importKVData(daoManager interfaces.DaoManager, filepath string) error {
	bytes, err := os.ReadFile(filepath)
	if err != nil {
		return err
	}

	kvData := make(map[string][]byte)
	if err = json.Unmarshal(bytes, &kvData); err != nil {
		return err
	}

	// do not overwrite these existing data
	samlData, err := daoManager.Export(dao.SAMLRespPrefix)
	if err != nil {
		return err
	}
	for k, v := range samlData {
		kvData[k] = v
	}

	pwdData, err := daoManager.Export(dao.GSQLPasswordPrefix)
	if err != nil {
		return err
	}
	for k, v := range pwdData {
		kvData[k] = v
	}

	backupSchedule, err := daoManager.Export(dao.BackupSchedulePrefix)
	if err != nil {
		return err
	}
	for k, v := range backupSchedule {
		kvData[k] = v
	}

	backupStatus, err := daoManager.Export(dao.BackupStatusPrefix)
	if err != nil {
		return err
	}
	for k, v := range backupStatus {
		kvData[k] = v
	}

	sessionData, err := daoManager.Export(dao.SessionPrefix)
	if err != nil {
		return err
	}
	for k, v := range sessionData {
		kvData[k] = v
	}

	apiTokenData, err := daoManager.Export(apitoken.APITokenPrefix)
	if err != nil {
		return err
	}
	for k, v := range apiTokenData {
		kvData[k] = v
	}

	insightTokenData, err := daoManager.Export(apitoken.InsightsTokenPrefix)
	if err != nil {
		return err
	}
	for k, v := range insightTokenData {
		kvData[k] = v
	}

	if err = daoManager.DeleteAll(); err != nil {
		return err
	}

	if len(kvData) == 0 {
		return nil
	}
	return daoManager.Import(kvData)
}

func importUserIcons(cfg *config.Config, srcDir string) error {
	hostID, err := cfg.GetHostID()
	if err != nil {
		return err
	}

	tgFS := fs.NewTGFilesystem(cfg)
	iconsInfo, err := tgFS.List(srcDir, hostID)
	if err != nil {
		return err
	}

	dstDir := path.Join(cfg.GetDataDirPath(), data.GetCategory().UserIcon)
	for _, iconInfo := range iconsInfo {
		dst := path.Join(dstDir, iconInfo.Name)
		if err := copyFileToAllGUIServers(cfg, iconInfo.Path, dst); err != nil {
			return err
		}
	}
	return nil
}

func copyFileToAllGUIServers(cfg *config.Config, srcPath, dstPath string) error {
	srcHostID, err := cfg.GetHostID()
	if err != nil {
		return err
	}

	dstHostIDs, err := cfg.GetAllGUIHostIDs()
	if err != nil {
		return err
	}

	tgFS := fs.NewTGFilesystem(cfg)
	return tgFS.CopyFile(srcPath, srcHostID, dstPath, dstHostIDs, fs.FileTypeFile)
}
