package system

import (
	"net/http"
	"path"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/handler/data"
	mw "github.com/tigergraph/gus/middleware"
)

// DeleteGUIData deletes all data from the kv store and users' icons.
func DeleteGUIData(c *gin.Context) {
	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteAll(); err != nil {
		log.Errorf(c, "Failed to delete all data from the kv store: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to delete all data from the key-value store.")
		return
	}

	cfg := mw.GetConfig(c)
	userIconsDir := path.Join(cfg.GetDataDirPath(), data.GetCategory().UserIcon)
	if err := data.RemoveDirFromAllServers(c, cfg, userIconsDir); err != nil {
		log.Errorf("Failed to delete user's icons: %v", err)
	}

	mw.Reply(c, http.StatusOK, "Successfully deleted GUI data.")
}
