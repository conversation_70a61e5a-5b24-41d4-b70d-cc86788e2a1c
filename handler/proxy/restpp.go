package proxy

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
	tgJSON "github.com/tigergraph/gus/lib/json"
	mw "github.com/tigergraph/gus/middleware"
)

type nodeResult struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Results interface{} `json:"results"`
	Profile interface{} `json:"profile,omitempty"`
}

func getRESTPPHostnameByNode(cfg *config.Config, node string) (hostname string, err error) {
	if node == "" {
		return cfg.GetRESTPPHostname(), nil
	}
	hostList := cfg.GetConfig().ProtoConf.System.HostList
	for _, host := range hostList {
		if host.ID == node {
			return host.Hostname, nil
		}
	}
	return "", errors.New("RESTPP node not found.")
}

// getRestppAuthorization gets the authorization for RESTPP.
func getRestppAuthorization(c *gin.Context, graphName string) (string, error) {
	cfg := mw.GetConfig(c)
	creds := mw.GetUserCredentials(c)

	if creds == nil {
		return "", nil
	}

	switch creds.AuthType {
	case model.AuthTokenAuthType: // System.AuthToken can be directly used for RESTPP
		return "Bearer " + creds.AuthToken, nil
	case model.GsqlTokenAuthType: // GsqlToken can be directly used for RESTPP
		return "Bearer " + creds.GsqlToken, nil
	default:
		token, err := mw.RequestNewGsqlToken(c, creds.Username, creds.Password, graphName, int64(cfg.GetCookieDuration()))
		if err != nil {
			return "", err
		}
		return "Bearer " + token, nil
	}
}

// ServeRESTPP forwards requests to RESTPP.
func ServeRESTPP(c *gin.Context, proxyPath string, graphName string) {
	cfg := mw.GetConfig(c)
	node := c.Query("node")

	authorization, err := getRestppAuthorization(c, graphName)
	if err != nil {
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	if node == "ALL" {
		hostNames := []string{}
		for _, host := range cfg.GetConfig().ProtoConf.System.HostList {
			hostNames = append(hostNames, host.Hostname)
		}

		results := []nodeResult{}
		resultChan := make(chan nodeResult)
		var wg sync.WaitGroup
		for _, hostname := range hostNames {
			wg.Add(1)

			go func(hostname string) {
				defer wg.Done()
				req, err := buildRequest(c, proxyPath, hostname, authorization)
				if err != nil {
					log.Errorf(c, "Failed to build request to RESTPP %s: %v", hostname, err)
					resultChan <- nodeResult{
						Error:   true,
						Message: fmt.Sprintf("Failed to build request for %s: %v", hostname, err),
					}
					return
				}

				statusCode, message, res, profile := forwardRequestToRESTPP(c, req, proxyPath)
				resultChan <- nodeResult{
					Error:   statusCode != http.StatusOK,
					Message: message,
					Results: res,
					Profile: profile,
				}
			}(hostname)
		}

		go func() {
			wg.Wait()
			close(resultChan)
		}()

		for res := range resultChan {
			results = append(results, res)
		}
		mw.ReplyWithResult(c, http.StatusOK, "Results from all RESTPP nodes", results)

	} else {
		hostname, err := getRESTPPHostnameByNode(cfg, node)
		if err != nil {
			mw.ReplyWithResult(c, http.StatusBadRequest, err.Error(), nil)
			return
		}

		req, err := buildRequest(c, proxyPath, hostname, authorization)
		if err != nil {
			mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
			return
		}

		statusCode, message, results, profile := forwardRequestToRESTPP(c, req, proxyPath)
		mw.ReplyWithResultAndProfile(c, statusCode, message, results, profile)
	}
}

func buildRequest(c *gin.Context, proxyPath string, hostname string, authorization string) (*http.Request, error) {
	cfg := mw.GetConfig(c)

	requestURL := fmt.Sprintf(
		"%s://%s:%d%s%s",
		cfg.GetNginxProtocol(),
		hostname,
		cfg.GetNginxPort(),
		"/restpp",
		url.PathEscape(proxyPath),
	)

	req, err := http.NewRequestWithContext(c.Request.Context(), c.Request.Method, requestURL, c.Request.Body)
	if err != nil {
		return nil, err
	}

	if authorization != "" {
		req.Header.Set("Authorization", authorization)
	}

	req.Header.Set("User-Agent", "GraphStudio")
	// Add X-Forwarded-For
	h.SetForwardedForFromContext(c, req)

	req.Header.Set("RESPONSE-LIMIT", fmt.Sprintf("%d", cfg.GetRESTPPResponseMaxSize()))
	if c.GetHeader("GSQL-TIMEOUT") != "" {
		req.Header.Set("GSQL-TIMEOUT", c.GetHeader("GSQL-TIMEOUT"))
	}

	if c.GetHeader("GSQL-QueryLocalMemLimitMB") != "" {
		req.Header.Set("GSQL-QueryLocalMemLimitMB", c.GetHeader("GSQL-QueryLocalMemLimitMB"))
	}

	if c.GetHeader("GSQL-REPLICA") != "" {
		req.Header.Set("GSQL-REPLICA", c.GetHeader("GSQL-REPLICA"))
	}

	if c.GetHeader("GSQL-ASYNC") != "" {
		req.Header.Set("GSQL-ASYNC", c.GetHeader("GSQL-ASYNC"))
	}

	if c.GetHeader("PROFILE") != "" {
		req.Header.Set("PROFILE", c.GetHeader("PROFILE"))
	}

	req.URL.RawQuery = c.Request.URL.RawQuery

	return req, nil
}

func forwardRequestToRESTPP(c *gin.Context, req *http.Request, proxyPath string) (int, string, interface{}, interface{}) {
	cfg := mw.GetConfig(c)
	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal restpp requests.
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(c, "Failed to get response from RESTPP: %v", err)
		return http.StatusInternalServerError, "Failed to get response from RESTPP.", nil, nil
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from RESTPP: %v", err)
		return http.StatusInternalServerError, "Failed to read response from RESTPP.", nil, nil
	}

	res := Response{}
	if err := tgJSON.Unmarshal(body, &res); err != nil {
		log.Errorf(c, "Failed to parse response from RESTPP: %v", err)
		return http.StatusInternalServerError, "Failed to parse response from RESTPP.", nil, nil
	}
	if res.Results == nil {
		_ = tgJSON.Unmarshal(body, &res.Results)
	}

	if proxyPath == "/rebuildnow" && !res.Error {
		res.Results = res.Message
	}

	message := cast.ToString(res.Message)
	resultsMap, _ := res.Results.(map[string]interface{})
	hasError, _ := resultsMap["error"].(bool)
	if (hasError || res.Error) && resp.StatusCode < http.StatusBadRequest {
		resp.StatusCode = http.StatusInternalServerError
	}
	return resp.StatusCode, message, res.Results, res.Profile
}
