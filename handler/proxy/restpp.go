package proxy

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
	tgJSON "github.com/tigergraph/gus/lib/json"
	mw "github.com/tigergraph/gus/middleware"
)

// For allowExisting to work, the lifetime must be more than 1 day.
const TokenLifetime = 60 * 60 * 24 * 2

type nodeResult struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Results interface{} `json:"results"`
}

func getRESTPPHostnameByNode(cfg *config.Config, node string) (hostname string, err error) {
	if node == "" {
		return cfg.GetRESTPPHostname(), nil
	}
	hostList := cfg.GetConfig().ProtoConf.System.HostList
	for _, host := range hostList {
		if host.ID == node {
			return host.Hostname, nil
		}
	}
	return "", errors.New("RESTPP node not found.")
}

// ServeRESTPP forwards requests to RESTPP.
func ServeRESTPP(c *gin.Context, proxyPath string, graphName string) {
	cfg := mw.GetConfig(c)
	node := c.Query("node")

	gsqlToken, err := getAuthToken(c, graphName, proxyPath)
	if err != nil {
		log.Errorf(c, "Failed to get auth token: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	if node == "ALL" {
		hostNames := []string{}
		for _, host := range cfg.GetConfig().ProtoConf.System.HostList {
			hostNames = append(hostNames, host.Hostname)
		}

		results := []nodeResult{}
		resultChan := make(chan nodeResult)
		var wg sync.WaitGroup
		for _, hostname := range hostNames {
			wg.Add(1)

			go func(hostname string) {
				defer wg.Done()
				req, err := buildRequest(c, proxyPath, gsqlToken, hostname)
				if err != nil {
					log.Errorf(c, "Failed to build request to RESTPP %s: %v", hostname, err)
					resultChan <- nodeResult{
						Error:   true,
						Message: fmt.Sprintf("Failed to build request for %s: %v", hostname, err),
					}
					return
				}

				statusCode, message, res := forwardRequestToRESTPP(c, req, proxyPath)
				resultChan <- nodeResult{
					Error:   statusCode != http.StatusOK,
					Message: message,
					Results: res,
				}
			}(hostname)
		}

		go func() {
			wg.Wait()
			close(resultChan)
		}()

		for res := range resultChan {
			results = append(results, res)
		}
		mw.ReplyWithResult(c, http.StatusOK, "Results from all RESTPP nodes", results)

	} else {
		hostname, err := getRESTPPHostnameByNode(cfg, node)
		if err != nil {
			mw.ReplyWithResult(c, http.StatusBadRequest, err.Error(), nil)
			return
		}

		req, err := buildRequest(c, proxyPath, gsqlToken, hostname)
		if err != nil {
			mw.ReplyWithResult(c, http.StatusInternalServerError, err.Error(), nil)
			return
		}

		statusCode, message, results := forwardRequestToRESTPP(c, req, proxyPath)
		mw.ReplyWithResult(c, statusCode, message, results)
	}
}

func buildRequest(c *gin.Context, proxyPath string, gsqlToken string, hostname string) (*http.Request, error) {
	cfg := mw.GetConfig(c)

	requestURL := fmt.Sprintf(
		"%s://%s:%d%s%s",
		cfg.GetNginxProtocol(),
		hostname,
		cfg.GetNginxPort(),
		"/restpp",
		url.PathEscape(proxyPath),
	)

	req, err := http.NewRequestWithContext(c.Request.Context(), c.Request.Method, requestURL, c.Request.Body)
	if err != nil {
		return nil, err
	}

	if gsqlToken != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", gsqlToken))
	}

	req.Header.Set("User-Agent", "GraphStudio")
	h.SetForwardedForFromContext(c, req)

	req.Header.Set("RESPONSE-LIMIT", fmt.Sprintf("%d", cfg.GetRESTPPResponseMaxSize()))
	if c.GetHeader("GSQL-TIMEOUT") != "" {
		req.Header.Set("GSQL-TIMEOUT", c.GetHeader("GSQL-TIMEOUT"))
	}
	if c.GetHeader("GSQL-QueryLocalMemLimitMB") != "" {
		req.Header.Set("GSQL-QueryLocalMemLimitMB", c.GetHeader("GSQL-QueryLocalMemLimitMB"))
	}
	if c.GetHeader("GSQL-REPLICA") != "" {
		req.Header.Set("GSQL-REPLICA", c.GetHeader("GSQL-REPLICA"))
	}
	req.URL.RawQuery = c.Request.URL.RawQuery

	return req, nil
}

func getAuthToken(c *gin.Context, graphName string, proxyPath string) (string, error) {
	cfg := mw.GetConfig(c)
	systemPaths := []string{"/rebuildnow", "/onlineparser", "/showprocesslistall", "/endpoints"}

	if cfg.GetConfig().ProtoConf.RESTPP.Factory.EnableAuth {
		for _, path := range systemPaths {
			if proxyPath == path {
				return cfg.GetAuthToken(), nil
			}
		}

		if graphName != "" {

			token, err := requestNewGsqlToken(
				c.Copy(), mw.GetUsername(c), mw.GetPassword(c),
				graphName, TokenLifetime)
			if err != nil {
				return "", err
			}

			return token.Token, nil
		}
	}

	return "", nil
}

func forwardRequestToRESTPP(c *gin.Context, req *http.Request, proxyPath string) (int, string, interface{}) {
	cfg := mw.GetConfig(c)
	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal restpp requests.
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(c, "Failed to get response from RESTPP: %v", err)
		return http.StatusInternalServerError, "Failed to get response from RESTPP.", nil
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from RESTPP: %v", err)
		return http.StatusInternalServerError, "Failed to read response from RESTPP.", nil
	}

	res := Response{}
	if err := tgJSON.Unmarshal(body, &res); err != nil {
		log.Errorf(c, "Failed to parse response from RESTPP: %v", err)
		return http.StatusInternalServerError, "Failed to parse response from RESTPP.", nil
	}
	if res.Results == nil {
		_ = tgJSON.Unmarshal(body, &res.Results)
	}

	if proxyPath == "/rebuildnow" && !res.Error {
		res.Results = res.Message
	}

	message := cast.ToString(res.Message)
	resultsMap, _ := res.Results.(map[string]interface{})
	hasError, _ := resultsMap["error"].(bool)
	if (hasError || res.Error) && resp.StatusCode < http.StatusBadRequest {
		resp.StatusCode = http.StatusInternalServerError
	}
	return resp.StatusCode, message, res.Results
}

// Here, use http://localhost:8123/gsqlserver/requesttoken?graph=<GRAPH_NAME>&lifetime=2day to request a new token
func requestNewGsqlToken(c *gin.Context, gsqlUsername, gsqlPassword, graphName string, lifetime int64) (cacheGsqlAuthToken, error) {
	cfg := mw.GetConfig(c)
	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal restpp requests.
	})
	requestURL := fmt.Sprintf(
		"%s://%s:%d/gsqlserver/requesttoken",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
	)
	log.Debug(c, requestURL)

	data, err := json.Marshal(map[string]interface{}{
		"lifetime":      lifetime,
		"graph":         graphName,
		"allowExisting": true,
	})
	if err != nil {
		return cacheGsqlAuthToken{}, errors.WithStack(err)
	}

	req, err := http.NewRequestWithContext(c, http.MethodPost, requestURL, bytes.NewReader(data))
	if err != nil {
		return cacheGsqlAuthToken{}, errors.WithStack(err)
	}

	req.SetBasicAuth(gsqlUsername, gsqlPassword)
	h.SetFromGraphStudio(req)
	h.SetForwardedForFromContext(c, req)

	res, err := client.Do(req)
	if err != nil {
		return cacheGsqlAuthToken{}, errors.WithStack(err)
	}
	defer func() {
		if res.Body != nil {
			err2 := res.Body.Close()
			if err2 != nil {
				log.Error(c, err2)
			}
		}
	}()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return cacheGsqlAuthToken{}, errors.WithMessage(err, "Failed to get response from GSQL server")
	}
	type GsqlAuthToken struct {
		Code       string `json:"code"`
		Expiration int64  `json:"expiration"`
		Error      bool   `json:"error"`
		Message    string `json:"message"`
		Results    struct {
			Token string `json:"token"`
		} `json:"results"`
	}
	result := GsqlAuthToken{}
	if err := json.Unmarshal(body, &result); err != nil {
		return cacheGsqlAuthToken{}, errors.WithMessage(err, "Failed to parse response from GSQL server")
	}
	log.Infof(c, "requestNewGsqlToken: %v", result.Message)
	if result.Error {
		return cacheGsqlAuthToken{}, errors.Errorf("failed to get auth token from gsql: %s", result.Message)
	}
	return cacheGsqlAuthToken{
		Expiration: result.Expiration,
		Token:      result.Results.Token,
	}, nil
}

type cacheGsqlAuthToken struct {
	Expiration int64
	Token      string
}
