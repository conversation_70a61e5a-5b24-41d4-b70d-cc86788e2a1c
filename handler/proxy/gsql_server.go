package proxy

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
	tgJSON "github.com/tigergraph/gus/lib/json"
	mw "github.com/tigergraph/gus/middleware"
)

const (
	gsqlUserEndpoint        = "/gsql/scim/v2/Users"
	gsqlSampleDataProxyPath = "/gsql/v1/sampledata"
)

type GSQLUserCredentials struct {
	Name     string `json:"name" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type GSQLSampleDataBody struct {
	GraphName      string      `json:"graphName" binding:"required"`
	DataSourceType string      `json:"type" binding:"required"`
	DataSourceName string      `json:"dataSource" binding:"required"`
	Filling        string      `json:"filling"`
	Format         string      `json:"format"`
	Parsing        interface{} `json:"parsing"`
	Path           string      `json:"path"`
	Size           int         `json:"size"`
}

type GSQLGoogleCloudStorageSampleDataConfig struct {
	AccountEmail string `json:"file.reader.settings.fs.gs.auth.service.account.email"`
	PrivateKeyId string `json:"file.reader.settings.fs.gs.auth.service.account.private.key.id"`
	PrivateKey   string `json:"file.reader.settings.fs.gs.auth.service.account.private.key"`
	ClientEmail  string `json:"file.reader.settings.client_email"`
	ProjectId    string `json:"file.reader.settings.fs.gs.project.id"`
}

type GSQLAmazonS3SampleDataConfig struct {
	AccessKey string `json:"file.reader.settings.fs.s3a.access.key"`
	SecretKey string `json:"file.reader.settings.fs.s3a.secret.key"`
}

type GSQLAzureBlobStorageSampleDataConfig struct {
	AccountName   string `json:"accountName"`
	AccountKey    string `json:"accountKey"`
	ContainerName string `json:"containerName"`
}

// ServeGSQLServer forwards requests to GSQL server.
func ServeGSQLServer(c *gin.Context) {
	cfg := mw.GetConfig(c)
	var buf bytes.Buffer
	resp, err := requestGSQLServer(c, mw.GetUserCredentials(c), cfg, &buf)
	if err != nil {
		log.Errorf(c, "Failed to get response from GSQL server: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to get response from GSQL server.", nil)
		return
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from GSQL server: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to read response from GSQL server.", nil)
		return
	}

	res := Response{}
	if err = tgJSON.Unmarshal(body, &res); err != nil {
		log.Errorf(c, "Failed to parse response from GSQL server: %v", err)
		mw.ReplyWithResult(c, resp.StatusCode, "Failed to parse response from GSQL server", nil)
		return
	}
	if res.Results == nil {
		_ = tgJSON.Unmarshal(body, &res.Results)
	}

	if !res.Error && strings.Contains(c.Param("proxyPath"), "/gsql/v1/schema/graphs") && c.Request.Method == http.MethodDelete {
		// proxyPath like '/gsql/v1/shema/graphs/{graphName}
		deleteGraphData(dao.GetManager(c), strings.Split(c.Param("proxyPath"), "/")[5])
	}

	if !res.Error && strings.Contains(c.Param("proxyPath"), gsqlUserEndpoint) && c.Request.Method == http.MethodPut {
		updateUserPassword(dao.GetManager(c), cfg, &buf)
	}

	message := cast.ToString(res.Message)
	resultsMap, _ := res.Results.(map[string]interface{})
	hasError, _ := resultsMap["error"].(bool)
	if (hasError || res.Error) && resp.StatusCode < http.StatusBadRequest {
		resp.StatusCode = http.StatusInternalServerError
	}

	mw.ReplyWithResult(c, resp.StatusCode, message, res.Results)
}

func requestGSQLServer(
	c *gin.Context,
	creds *model.UserCredentials,
	cfg *config.Config,
	buf io.Writer,
) (*http.Response, error) {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		// remove gsqlserver prefix
		"",
		c.Param("proxyPath"),
	)

	req, _ := http.NewRequestWithContext(context.Background(), c.Request.Method, requestURL, c.Request.Body)
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	if c.GetHeader("GSQL-TIMEOUT") != "" {
		req.Header.Set("GSQL-TIMEOUT", c.GetHeader("GSQL-TIMEOUT"))
	}

	if c.GetHeader("GSQL-QueryLocalMemLimitMB") != "" {
		req.Header.Set("GSQL-QueryLocalMemLimitMB", c.GetHeader("GSQL-QueryLocalMemLimitMB"))
	}

	if c.GetHeader("PROFILE") != "" {
		req.Header.Set("PROFILE", c.GetHeader("PROFILE"))
	}

	// when changing password, the client will enforce users to input the old password and put it to Authorization header
	// keep the Authorization header so that GSQL can check the old password
	if strings.HasPrefix(c.GetHeader("Authorization"), "Basic ") {
		req.Header.Set("Authorization", c.GetHeader("Authorization"))
	} else {
		h.SetCredentials(req, creds)
	}

	// Add fromGraphStudio cookie to the request.
	h.SetFromGraphStudio(req)

	// Add X-Forwarded-For
	h.SetForwardedForFromContext(c, req)

	req.URL.RawQuery = c.Request.URL.RawQuery

	// If changing the password, request body needs to be recreated since it will be read twice.
	if strings.Contains(c.Param("proxyPath"), gsqlUserEndpoint) && c.Request.Method == http.MethodPut {
		body, err := io.ReadAll(io.TeeReader(req.Body, buf))
		if err != nil {
			return nil, err
		}
		req.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server requests.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})

	return client.Do(req)
}

func deleteGraphData(daoManager interfaces.DaoManager, graphName string) {
	if graphName == "" {
		return
	}

	if err := daoManager.DeleteGraphStyle(graphName); err != nil {
		log.Warnf("Failed to delete local graph style for graph %s: %v", graphName, err)
	}
	if err := daoManager.DeleteLoadingJobInfo(graphName); err != nil {
		log.Warnf("Failed to delete loading job info for graph %s: %v", graphName, err)
	}
	if err := daoManager.DeleteAllQueryDraft(graphName); err != nil {
		log.Warnf("Failed to delete all query drafts from graph %s: %v", graphName, err)
	}
	if err := daoManager.DeleteAllVisualPatterns(graphName); err != nil {
		log.Warnf("Failed to delete all visual patterns from graph %s: %v", graphName, err)
	}
	if err := daoManager.DeleteAllExplorationResults(graphName); err != nil {
		log.Warnf("Failed to delete all exploration results from graph %s: %v", graphName, err)
	}
	if err := daoManager.DeleteAllLoadingJobLogs(graphName); err != nil {
		log.Warnf("Failed to delete logs for all loading jobs from graph %s: %v", graphName, err)
	}
	if err := daoManager.DeleteAllDataSources(graphName); err != nil {
		log.Warnf("Failed to delete all data sources from graph %s: %v", graphName, err)
	}
}

func updateUserPassword(daoManager interfaces.DaoManager, cfg *config.Config, buf *bytes.Buffer) {
	creds := GSQLUserCredentials{}
	if err := json.Unmarshal(buf.Bytes(), &creds); err != nil {
		log.Warnf("Failed to parse user credentials: %v", err)
	}

	if creds.Name == "tigergraph" {
		if err := daoManager.UpsertPassword(creds.Password, cfg.GetAuthToken()); err != nil {
			log.Warnf("Failed to upsert password: %v", err)
		}
	}
}
