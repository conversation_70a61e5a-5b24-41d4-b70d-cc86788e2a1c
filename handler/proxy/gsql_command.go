package proxy

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/tg"
	"github.com/tigergraph/gus/lib/typeconv"
	mw "github.com/tigergraph/gus/middleware"
)

const (
	minimumSendingInterval     = time.Millisecond * 100
	progressBarCompletePattern = "\\[=*\\s*\\]\\s100%[^l]*"
)

func ServerGSQLCommand(c *gin.Context) {
	var req model.GSQLCommandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warnf("Invalid payload: %+v", err)
		mw.ReplyWithResult(c, http.StatusBadRequest, "Invalid payload.", nil)
		return
	}
	cfg := mw.GetConfig(c)
	resp, err := tg.RequestGSQLFileHandler(c, mw.GetUsername(c), mw.GetPassword(c), "", cfg, req)
	if err != nil {
		log.Warnf("Failed to get response from GSQL server: %+v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to get response from GSQL server.", nil)
		return
	}
	if resp.StatusCode >= 300 {
		log.Info(c, resp.Status)
		mw.ReplyWithResult(c, resp.StatusCode, resp.Status, nil)
		return
	}

	defer resp.Body.Close()
	reader := bufio.NewReader(resp.Body)

	var lastSentTime time.Time
	buffer := ""
	newLine := make([]byte, 0)
	for {
		b, err := reader.ReadByte()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Warnf("Failed to decode GSQL response: %+v", err)
			mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to decode GSQLresponse.", nil)
			return
		}
		switch b {
		case '\r':
			// handle special case for progess bar
			newLine = []byte("\r" + string(newLine))
		case '\n':
			newLine = append(newLine, b)
		default:
			newLine = append(newLine, b)
			continue
		}
		re := regexp.MustCompile(progressBarCompletePattern)
		if re.Match(newLine) {
			newLine = []byte("\r" + string(newLine))
		}
		if bytes.HasPrefix(newLine, []byte("__GSQL__INTERACT__")) {
			log.Infof("unsupport command: %+v", newLine)
			err = tg.AbortClientSession(mw.GetUsername(c), mw.GetPassword(c), cfg, req.Cookies)
			if err != nil {
				log.Warnf("Failed to abort client session: %+v", err)
			}
			// once the session is aborted, the sessionId shall not be reused
			delete(req.Cookies, "sessionId")
			newCookieStr, _ := json.Marshal(req.Cookies)
			c.SSEvent("message", model.Response{Results: fmt.Sprintf("__GSQL__COOKIES__,%s\n", newCookieStr)})
			c.SSEvent("error", model.Response{Error: true, Message: "Interactive commands are not supported so far."})
			return
		}

		buffer += string(newLine)
		newLine = make([]byte, 0)
		if time.Since(lastSentTime) > minimumSendingInterval {
			c.SSEvent("message", model.Response{Results: buffer})
			c.Writer.Flush()
			lastSentTime = time.Now()
			buffer = ""
		}

	}

	c.SSEvent("message", model.Response{
		Error:   false,
		Message: "",
		Results: buffer,
		Done:    typeconv.Bool(true),
	})
}
