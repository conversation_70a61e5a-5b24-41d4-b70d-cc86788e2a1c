package proxy

import (
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/tg"
	"github.com/tigergraph/gus/lib/typeconv"
	mw "github.com/tigergraph/gus/middleware"
)

func ServerGSQLCommand(c *gin.Context) {
	var req model.GSQLCommandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warnf("Invalid payload: %+v", err)
		mw.ReplyWithResult(c, http.StatusBadRequest, "Invalid payload.", nil)
		return
	}
	cfg := mw.GetConfig(c)
	resp, err := tg.RequestGSQLFileHandler(c, mw.GetUserCredentials(c), cfg, req)
	if err != nil {
		log.Warnf("Failed to get response from GSQL server: %+v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to get response from GSQL server.", nil)
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 300 {
		errMsg, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Warnf("Failed to read response from GSQL server: %+v", err)
			mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to read response from GSQL server.", nil)
			return
		}
		mw.ReplyWithResult(c, resp.StatusCode, string(errMsg), nil)
		return
	}
	// Use the shared streaming function
	err = tg.StreamGSQLResponseToSSE(c, &req, resp)
	if err != nil {
		c.SSEvent("error", model.Response{Error: true, Message: err.Error()})
		return
	}
	c.SSEvent("message", model.Response{
		Error:   false,
		Message: "",
		Results: "",
		Done:    typeconv.Bool(true),
	})
}
