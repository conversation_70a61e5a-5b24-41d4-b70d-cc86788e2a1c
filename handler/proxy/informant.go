package proxy

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"github.com/tigergraph/gotools/log"
	h "github.com/tigergraph/gus/lib/http"
	mw "github.com/tigergraph/gus/middleware"
)

// ServeInformantServer forwards requests to IFM server.
func ServeInformantServer(c *gin.Context) {
	cfg := mw.GetConfig(c)
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s%s",
		cfg.GetNginxProtocol(),
		cfg.GetIFMHostname(),
		cfg.GetNginxPort(),
		"/informant",
		c.<PERSON>("proxyPath"),
	)

	req, _ := http.NewRequestWithContext(context.Background(), c.Request.Method, requestURL, c.Request.Body)
	req.URL.RawQuery = c.Request.URL.RawQuery

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal informant server requests.
	})

	// Add X-Forwarded-For
	h.SetForwardedForFromContext(c, req)

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(c, "Failed to get response from informant server: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to get response from informant server.", nil)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(c, "Failed to read response from informant server: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, "Failed to read response from informant server.", nil)
		return
	}

	res := Response{}
	if err := json.Unmarshal(body, &res.Results); err != nil {
		log.Errorf(c, "Failed to parse response from informant server: %v", err)
		mw.ReplyWithResult(c, http.StatusInternalServerError, string(body), nil)
		return
	}

	message := cast.ToString(res.Message)
	mw.ReplyWithResult(c, resp.StatusCode, message, res.Results)
}
