package loadingjobinfo

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// Get returns the loading job info for a graph.
func Get(c *gin.Context) {
	graphName := c.Param("graphName")

	daoManager := dao.GetManager(c)
	info, err := daoManager.GetLoadingJobInfo(graphName)

	if err != nil {
		if errors.Is(err, db.ErrNotFound) {
			mw.Abort(
				c,
				http.StatusNotFound,
				fmt.Sprintf("Loading job info for graph '%s' cannot be found.", graphName),
			)
		} else {
			log.Errorf(c, "Failed to get loading job info for graph %s: %v", graphName, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to get loading job info for graph '%s'.", graphName),
			)
		}
		return
	}

	mw.ReplyWithResult(c, http.StatusOK, "", info)
}
