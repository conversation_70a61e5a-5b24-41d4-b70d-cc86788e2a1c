package loadingjobinfo

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Delete deletes the loading job info for a graph.
func Delete(c *gin.Context) {
	graphName := c.Param("graphName")

	daoManager := dao.GetManager(c)
	if err := daoManager.DeleteLoadingJobInfo(graphName); err != nil {
		log.Errorf(c, "Failed to delete loading job info for graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to delete loading job info for graph '%s'.", graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully deleted loading job info for graph '%s'.", graphName),
	)
}
