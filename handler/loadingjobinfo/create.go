package loadingjobinfo

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
	"github.com/tigergraph/gus/service/db"
)

// Create creates the loading job info for a graph.
func Create(c *gin.Context) {
	graphName := c.Param("graphName")

	var jobInfo []interface{}
	if err := c.ShouldBindJSON(&jobInfo); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	daoManager := dao.GetManager(c)
	if err := daoManager.CreateLoadingJobInfo(graphName, jobInfo); err != nil {
		if err == db.ErrAlreadyExist {
			mw.Abort(
				c,
				http.StatusConflict,
				fmt.Sprintf("Loading job info for graph '%s' already exists.", graphName),
			)
		} else {
			log.Errorf(c, "Failed to create loading job info for graph %s: %v", graphName, err)
			mw.Abort(
				c,
				http.StatusInternalServerError,
				fmt.Sprintf("Failed to create loading job info for graph '%s'.", graphName),
			)
		}
		return
	}

	mw.Reply(
		c,
		http.StatusCreated,
		fmt.Sprintf("Successfully created loading job info for graph '%s'.", graphName),
	)
}
