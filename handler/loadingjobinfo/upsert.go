package loadingjobinfo

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao"
	mw "github.com/tigergraph/gus/middleware"
)

// Upsert upserts the loading job info for a graph.
func Upsert(c *gin.Context) {
	graphName := c.Param("graphName")

	var jobInfo []interface{}
	if err := c.ShouldBindJSON(&jobInfo); err != nil {
		log.Warnf(c, "Invalid payload: %v", err)
		mw.Abort(c, http.StatusBadRequest, "Invalid payload.")
		return
	}

	daoManager := dao.GetManager(c)
	err := daoManager.UpsertLoadingJobInfo(graphName, jobInfo)
	if err != nil {
		log.Errorf(c, "Failed to upsert loading job info for graph %s: %v", graphName, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to upsert loading job info for graph '%s'.", graphName),
		)
		return
	}

	mw.Reply(
		c,
		http.StatusOK,
		fmt.Sprintf("Successfully upserted loading job info for graph '%s'.", graphName),
	)
}
