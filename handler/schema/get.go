package schema

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	mw "github.com/tigergraph/gus/middleware"
)

// Get gets a graph schema
func Get(c *gin.Context, schemaService interfaces.SchemaService) {
	graph := c.Query("graph")
	creds := mw.GetUserCredentials(c)

	response, err := schemaService.Get(
		c,
		creds,
		graph,
	)
	if err != nil {
		log.Warnf(c, "Failed to get schema for graph %s: %+v", graph, err)
		mw.Abort(
			c,
			http.StatusInternalServerError,
			fmt.Sprintf("Failed to get schema for graph '%s'.", graph),
		)
	}

	mw.ReplyWithResult(c, http.StatusOK, "", response.Results)
}
