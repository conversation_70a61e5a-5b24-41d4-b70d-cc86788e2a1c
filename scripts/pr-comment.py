#!/usr/bin/env python3

import argparse
import os
import urllib.parse

def gen_download_links(bucket: str, region: str, repo: str, sha: str, path: str) -> dict:
  tmplt = "https://{bucket}.s3-{region}.amazonaws.com/{repo}/{sha}/release/{file}"
  files = os.listdir(path)
  return {
    f: tmplt.format(
      bucket=bucket,
      region=region,
      repo=repo,
      sha=sha,
      file=f
    ) for f in files
  }

def gen_files_markdown(links: dict) -> str:
  result = list()
  for k, v in links.items():
    result.append("* [{filename}]({url})".format(filename=k, url=v))
  return "\n".join(result)

def gen_coverage_badge(coverage_rate: float) -> str:
  tmplt = '![badge](https://img.shields.io/badge/coverage-{coverage}-{color}?logoColor=hsl)'
  color = urllib.parse.quote('hsl({}, 50%, 50%)'.format(int(coverage_rate * 120)))
  coverage = urllib.parse.quote('{:.1f}%'.format(100*coverage_rate))
  return tmplt.format(coverage=coverage, color=color)

def gen_coverage_markdown(bucket: str, region: str, repo: str, sha: str, filename: str, badge: str) -> str:
  tmplt = '[{badge}](https://{bucket}.s3-{region}.amazonaws.com/{repo}/{sha}/coverage/{filename})'
  return tmplt.format(
    badge=badge,
    bucket=bucket,
    region=region,
    repo=repo,
    sha=sha,
    filename=filename
  )

def main():
  parser = argparse.ArgumentParser(description="Argument parser for PR comment generator")
  parser.add_argument("--s3_bucket", type=str, required=True)
  parser.add_argument("--region", type=str, required=True)
  parser.add_argument("--repo", type=str, required=True)
  parser.add_argument("--sha", type=str, required=True)
  parser.add_argument("--files_dir", type=str, required=True)
  parser.add_argument("--coverage", type=str, required=True)
  parser.add_argument("--coverage_file", type=str, required=True)
  args = parser.parse_args()

  files_markdown = gen_files_markdown(gen_download_links(
    args.s3_bucket,
    args.region,
    args.repo,
    args.sha,
    args.files_dir
  ))
  coverage_markdown = gen_coverage_markdown(
    args.s3_bucket,
    args.region,
    args.repo,
    args.sha,
    args.coverage_file,
    gen_coverage_badge(float(args.coverage[:-1])/100)
  )

  print("**Files**\n")
  print(files_markdown)
  print("\n")

  print("**Coverage**\n")
  print(coverage_markdown)

  print("\n**BuildNum**\n")
  print(os.getenv("GITHUB_RUN_NUMBER"))
  print("\n")

if __name__ == "__main__":
  main()
    