#!/bin/bash

set -e

DIR=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
ROOT_DIR=$( dirname "$DIR" )

GO_PATH=$( go env GOPATH )
AIR_BINARY="$GO_PATH"/bin/air
AIR_CONFIG="$ROOT_DIR"/.air.conf

if [ ! -f "$AIR_BINARY" ]; then
  echo "Air is missing, getting some fresh air..."
  cd /tmp; GO111MODULE=on go get github.com/cosmtrek/air 
fi

TG_CFG_FILE=~/.tg.cfg
LOCAL_CFG_FILE=$ROOT_DIR/tg.cfg

if [ ! -f "$LOCAL_CFG_FILE" ]; then
  if [ ! -f "$TG_CFG_FILE" ]; then
    echo "TigerGraph config file not found, did you install TigerGraph?"
    exit 1
  fi
  cp $TG_CFG_FILE "$LOCAL_CFG_FILE"
fi

cd "$ROOT_DIR" && $AIR_BINARY -c "$AIR_CONFIG"
