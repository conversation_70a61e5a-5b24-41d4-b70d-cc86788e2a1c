#!/bin/bash

original_version=$1
current_version=$2

cwd=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
if [ -f $cwd/tiger_utils ]; then
    source $cwd/tiger_utils
fi

if [ -f $cwd/pretty_print ]; then
    source $cwd/pretty_print
fi

if [ -f $cwd/functionutils ]; then
    source $cwd/functionutils
fi

if [ -f $cwd/exit_code ]; then
    source $cwd/exit_code
fi

# Guidelines on editing this script:
# 1. This script needs to be idempotent - user can run this script as many times but will still make the same results,
#    which means cleanup should be happening before and after running the rest of the scripts.
# 2. When this script failed, version is not switched yet, TG will be still at the old version.
#    Instructions will be prompted and guide users to run this and the leftover scripts to finish upgrading.
# 3. This script takes two parameters:
#        $original_version: the original (old) version of TG
#        $current_version: the current (new) version of TG
# 4. Please never combine commands into one using '&&', because that will make it hard to trouble shoot when error happens.
# 5. Always check the returned code of each command using built-in function "check_fail".
#    The exit code of this script will be checked by its caller - switch_version.sh,
#    and apprepriate instructions will be prompted to the users when any error happens during upgrading.
#
# Here's a template to follow:
#    <the-command-to-run>
#    check_fail $? $E_ACTIONFAILED "Failed to run command '<the-command-to-run>'" "red"
