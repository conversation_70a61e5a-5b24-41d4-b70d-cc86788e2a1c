#!/bin/bash

set -e

DIR=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )

SWAGGER_BINARY=swagger-editor-live
SWAGGER_HOST=0.0.0.0
SWAGGER_PORT=14808
SWAGGER_FILE=$( dirname "$DIR" )/api/api.yaml

if [ ! -x "$( command -v $SWAGGER_BINARY )" ]; then
  echo "Zero swag(ger) found, getting some swag..."
  npm i -g $SWAGGER_BINARY
fi

$SWAGGER_BINARY --host $SWAGGER_HOST --port $SWAGGER_PORT "$SWAGGER_FILE" > /dev/null 2>&1 &
echo "Swagger Live Editor is listening on $SWAGGER_HOST:$SWAGGER_PORT, open your browser on http://localhost:$SWAGGER_PORT/."
