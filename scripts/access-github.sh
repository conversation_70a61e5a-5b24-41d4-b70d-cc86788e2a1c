#!/bin/bash

set -e

PRIVATE_REPOS="github.com/tigergraph"
GITHUB_TOKEN="$CICD_GITHUB_TOKEN"
# Avoid github scan and invalidate the token.

go env -w "GOPRIVATE=$PRIVATE_REPOS"
go env -w "GONOSUMDB=$PRIVATE_REPOS"
go env -w "GONOPROXY=$PRIVATE_REPOS"
git config --global --replace-all url."https://$<EMAIL>/".insteadOf "https://github.com/"

mkdir -p ~/.ssh
touch ~/.ssh/known_hosts
ssh-keygen -q -R github.com
ssh-keyscan github.com 2> /dev/null >> ~/.ssh/known_hosts
