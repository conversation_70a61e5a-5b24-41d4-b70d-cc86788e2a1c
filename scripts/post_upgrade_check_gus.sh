#!/bin/bash

original_version=$1
current_version=$2

cwd=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
if [ -f $cwd/tiger_utils ]; then
    source $cwd/tiger_utils
fi

if [ -f $cwd/pretty_print ]; then
    source $cwd/pretty_print
fi

if [ -f $cwd/functionutils ]; then
    source $cwd/functionutils
fi

if [ -f $cwd/exit_code ]; then
    source $cwd/exit_code
fi

# Guidelines on editing this script:
# 1. This script should be idempotent - user can run this script as many times as he/she wants but will still make the same results,
#    which means some cleanup needs be done before and after running the rest of the commands in this script.
# 2. When this script is failed, TG is already switched to the new version. User can use command 'gadmin version' to double check.
#    Instructions will be prompted to user on how to fix the issues and continue upgrading.
# 3. This script takes two parameters:
#        $original_version: the original (old) version of TG
#        $current_version: the current (new) version of TG
# 4. Please never combine commands into one using '&&', because that will make it hard to trouble shoot when error happens.
# 5. Always check the returned code of each command using built-in function "check_fail".
#    The exit code of this script will be checked by its caller - switch_version.sh,
#    and apprepriate instructions will be prompted to the users when any error happens during upgrading.
#
# Here's a template to follow:
#    <A-command-to-run>
#    check_fail $? $E_ACTIONFAILED "Failed to run command '<A-command-to-run>'" "red"
