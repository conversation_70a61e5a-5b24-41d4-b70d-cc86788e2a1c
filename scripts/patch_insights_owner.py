#!/usr/bin/env python3
"""
patch_insights_owner.py - A script to update the owner of insights applications in a solution file.

This script extracts a solution archive, modifies the ownership information in data.json,
and creates a new patched archive.

Usage:
    python patch_insights_owner.py solution_filename username
    
    solution_filename: Path to the .tar.gz solution file
    username: The new username to set as owner
"""

import argparse
import base64
import json
import os
import shutil
import sys
import tarfile
import tempfile


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Patch insights owner in a solution file")
    parser.add_argument("solution_filename", help="Path to the .tar.gz solution file")
    parser.add_argument("username", help="New username to set as owner")
    return parser.parse_args()


def extract_archive(archive_path, extract_dir):
    """Extract the tar.gz archive to the specified directory."""
    try:
        with tarfile.open(archive_path, "r:gz") as tar:
            tar.extractall(path=extract_dir)
        print(f"Extracted archive to {extract_dir}")
    except Exception as e:
        print(f"Error extracting archive: {e}")
        sys.exit(1)


def find_data_json(extract_dir):
    """Find the data.json file in the gui directory."""
    data_json_path = os.path.join(extract_dir, "gui", "data.json")
    if not os.path.exists(data_json_path):
        print(f"data.json not found at {data_json_path}")
        sys.exit(1)
    return data_json_path


def update_data_json(data_json_path, new_username):
    """Update the data.json file with the new username."""
    try:
        # Read the data.json file
        with open(data_json_path, 'r') as f:
            data = json.load(f)
        
        # Process GAAllApps
        if "GAAllApps" in data:
            try:
                # Decode GAAllApps from base64
                decoded_data = base64.b64decode(data["GAAllApps"]).decode('utf-8')
                apps_list = json.loads(decoded_data)
                
                # Update Username for each app
                for app in apps_list:
                    if "Username" in app:
                        app["Username"] = new_username
                
                # Encode back to base64
                encoded_data = base64.b64encode(json.dumps(apps_list).encode('utf-8')).decode('utf-8')
                data["GAAllApps"] = encoded_data
                print(f"Updated Username in GAAllApps for {len(apps_list)} applications")
            except Exception as e:
                print(f"Error processing GAAllApps: {e}")
        
        # Process GAApp.* keys
        updated_app_keys = 0
        for key in list(data.keys()):
            if key.startswith("GAApp."):
                parts = key.split(".")
                if len(parts) >= 3:
                    # Create a new key with the updated username
                    new_key = ".".join(parts[:-1]) + "." + new_username
                    data[new_key] = data[key]
                    del data[key]
                    updated_app_keys += 1
        
        print(f"Updated {updated_app_keys} GAApp.* keys")
        
        # Write the updated data back to data.json
        with open(data_json_path, 'w') as f:
            json.dump(data, f)
        
        print(f"Updated data.json written to {data_json_path}")
        return True
    except Exception as e:
        print(f"Error updating data.json: {e}")
        return False


def create_patched_archive(original_archive, extract_dir):
    """Create a new tar.gz archive with the patched files."""
    try:
        # Determine the output filename in the same directory as the input file
        input_dir = os.path.dirname(os.path.abspath(original_archive))
        base_name = os.path.basename(original_archive)
        
        # Remove extensions (.tar.gz)
        if base_name.endswith('.tar.gz'):
            output_base = base_name[:-7]
        elif base_name.endswith('.gz'):
            output_base = os.path.splitext(base_name)[0]
            if output_base.endswith('.tar'):
                output_base = output_base[:-4]
        else:
            output_base = os.path.splitext(base_name)[0]
        
        # Create full output path in the same directory as the input file
        output_archive = os.path.join(input_dir, f"{output_base}-patched.tar.gz")
        
        # Create the new archive
        with tarfile.open(output_archive, "w:gz") as tar:
            # Change to the extract directory to avoid including full paths
            current_dir = os.getcwd()
            os.chdir(extract_dir)
            
            # Add all files and directories to the new archive
            for item in os.listdir('.'):
                tar.add(item)
            
            # Restore the original working directory
            os.chdir(current_dir)
        
        print(f"Created patched archive: {output_archive}")
        return output_archive
    except Exception as e:
        print(f"Error creating patched archive: {e}")
        return None


def main():
    """Main function to execute the script."""
    args = parse_arguments()
    
    # Validate input
    if not args.solution_filename.endswith(".tar.gz"):
        print("Error: Input file must be a .tar.gz archive")
        sys.exit(1)
    
    if not os.path.exists(args.solution_filename):
        print(f"Error: File not found: {args.solution_filename}")
        sys.exit(1)
    
    # Create a temporary directory for extraction
    temp_dir = tempfile.mkdtemp()
    try:
        # Extract the archive
        extract_archive(args.solution_filename, temp_dir)
        
        # Find and update data.json
        data_json_path = find_data_json(temp_dir)
        if update_data_json(data_json_path, args.username):
            # Create a new patched archive
            patched_archive = create_patched_archive(args.solution_filename, temp_dir)
            if patched_archive:
                print(f"Successfully patched solution file. Output: {patched_archive}")
            else:
                print("Failed to create patched archive")
        else:
            print("Failed to update data.json")
    finally:
        # Clean up the temporary directory
        shutil.rmtree(temp_dir)
        print(f"Cleaned up temporary directory: {temp_dir}")


if __name__ == "__main__":
    main() 