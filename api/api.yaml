openapi: 3.0.3
info:
  title: Application Server
  description: The backend server for GraphStudio and Admin Portal.
  version: 3.1.0

tags:
  - name: Auth
    description: Authentication related APIs.
  - name: Stream
    description: Server-sent events related APIs.
  - name: RESTPP Proxy
    description: RESTPP related APIs.
  - name: GSQL Server Proxy
    description: GSQL server related APIs.
  - name: TS3 Server Proxy
    description: TS3 server related APIs.
  - name: Configuration
    description: System configurations related APIs.
  - name: Service Control
    description: Service control related APIs.
  - name: Data
    description: Data management related APIs.
  - name: Log 
    description: Log management related APIs.
  - name: System
    description: System related APIs.
  - name: GBAR
    description: GBAR related APIs.
  - name: Loading Job
    description: Loading job related APIs.
  - name: Query
    description: Query related APIs.
  - name: GSQL Algorithm
    description: GSQL Algorithm related APIs.
  - name: Visual Pattern
    description: Visual pattern related APIs.
  - name: Graph Style
    description: Graph style related APIs.
  - name: Loading Job Info
    description: Loading job info related APIs.
  - name: Exploration Result
    description: Exploration result related APIs.
  - name: Data Source
    description: Data source related APIs.
  - name: Health Check
    description: Health check related APIs.

paths:
  ##### [Begin] Auth #####
  /api/auth/login:
    post:
      tags:
        - Auth
      summary: Logs the user in.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/LoginRequest"
      responses:
        200:
          description: Successfully logged in.
          headers:
            Set-Cookie:
              schema: 
                allOf:
                  - $ref: "#/components/schemas/SetCookie"
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/UserProfile"
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/auth/logout:
    post:
      tags:
        - Auth
      summary: Logs the user out.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully logged out.
          headers:
            Set-Cookie:
              schema: 
                allOf:
                  - $ref: "#/components/schemas/DeleteCookie"
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully logged out.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Auth #####
  
  ##### [Begin] Stream #####
  /api/stream:
    get:
      tags:
        - Stream
      summary: Subscribes to server-sent events
      responses:
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Stream #####

  ##### [Begin] RESTPP Proxy #####
  /api/restpp/graph/{graphName}/vertices/{vertexType}:
    get:
      tags:
        - RESTPP Proxy
      summary: Retrieves a list of vertices.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/VertexType"
        - $ref: "#/components/parameters/VertexLimit"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          $ref: "#/components/responses/GetVertex"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/restpp/graph/{graphName}/vertices/{vertexType}/{vertexID}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/VertexType"
      - $ref: "#/components/parameters/VertexID"  
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - RESTPP Proxy
      summary: Retrieves a vertex.
      responses:
        200:
          $ref: "#/components/responses/GetVertex"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - RESTPP Proxy
      summary: Deletes a vertex.
      responses:
        200:
          description: Successfully deleted vertex.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteVertexResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/restpp/graph/{graphName}/edges/{fromVertexType}/{fromVertexID}/{edgeType}/{toVertexType}/{toVertexID}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/FromVertexType"
      - $ref: "#/components/parameters/FromVertexID"
      - $ref: "#/components/parameters/EdgeType"
      - $ref: "#/components/parameters/ToVertexType"
      - $ref: "#/components/parameters/ToVertexID"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - RESTPP Proxy
      summary: Retrieves an edge.
      responses:
        200:
          description: Successfully retrieved edge.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetEdgeResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - RESTPP Proxy
      summary: Deletes an edge.
      responses:
        200:
          description: Successfully deleted edge.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteEdgeResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/restpp/graph/{graphName}:
    post:
      tags:
        - RESTPP Proxy
      summary: Upserts a vertex or an edge.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          appication/json:
            schema:
              type: object
            examples:
              vertex:
                value: {
                  "vertices": {
                    "Person": {
                      "1:": {
                        "name": "John"
                      }
                    }    
                  }
                }
              edge: {
                value: {
                  "edges": {
                    "Person": {
                      "1": {
                        "connection": {
                          "Person": {
                            "1": {
                              "strength": 1
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
        required: true
      responses:
        200:
          description: Successfully upserted vertex or edge.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpsertVertexEdgeResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/restpp/builtins/{graphName}:
    post:
      tags:
        - RESTPP Proxy
      summary: Requests statistics for a graph.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          appication/json:
            schema:
              $ref: "#/components/schemas/GraphStatisticsRequest"
        required: true
      responses:
        200:
          description: Successfully retrieved statistics.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GraphStatisticsResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/restpp/query/{graphName}/{queryName}:
    get:
      tags:
        - RESTPP Proxy
      summary: Runs a query.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/QueryNamePath"
      responses:
        200:
          description: Successfully ran query.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: [{ "\"echo works!\"": "echo works!" }]
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/restpp/kstep_expansion/{graphName}:
     post:
      tags:
        - RESTPP Proxy
      summary: Runs k-step expansion.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/KStepExpansionRequest"
        required: true
      responses:
        200:
          description: Successfully ran k-step expansion.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/KStepExpansionResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/restpp/shortestpath/{graphName}:
     post:
      tags:
        - RESTPP Proxy
      summary: Runs shortest path query.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PathQueryRequest"
        required: true
      responses:
        200:
          $ref: "#/components/responses/RunPathQuery"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/restpp/allpaths/{graphName}:
     post:
      tags:
        - RESTPP Proxy
      summary: Runs all paths query.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/PathQueryRequest"
                - properties:
                    allShortestPaths:
                      example: true
        required: true
      responses:
        200:
          $ref: "#/components/responses/RunPathQuery"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/restpp/searchvertex/{graphName}:
     post:
      tags:
        - RESTPP Proxy
      summary: Searches vertices with attribute filter.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/VertexAttributeFilterRequest"
                - properties:
                    limit:
                      example: 5
        required: true
      responses:
        200:
          $ref: "#/components/responses/GetVertex"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/restpp/onlineparser:
    post:
      tags:
        - RESTPP Proxy
      summary: Parses local file.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnlineParserRequest"
        required: true
      responses:
        200:
          description: Successfully parsed file.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnlineParserResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] RESTPP Proxy #####
  
  ##### [Begin] GSQL Server Proxy #####
  /api/gsql-server/gsql/schema:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves schema of a graph.
      parameters:
        - $ref: "#/components/parameters/GraphNameQuery"
      responses:
        200:
          description: Successfully retrieved graph.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetSchemaResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/GraphUnauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - GSQL Server Proxy
      summary: Creates a graph with provided global vertex and edge types.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                GraphName: "MyGraph",
                VertexTypes: ["person", "movie"],
                EdgeTypes: ["rate"]
              }
        required: true
      responses:
        200:
          description: Successfully created graph.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: The graph MyGraph is created.
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/CreateGraphUnauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - GSQL Server Proxy
      summary: Drops a graph.
      parameters:
        - $ref: "#/components/parameters/GraphNameQueryRequired"
      responses:
        200:
          description: Successfully dropped graph.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: The graph MyGraph is dropped.
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/DropGraphUnauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/gsql/schema/change:
    put:
      tags:
        - GSQL Server Proxy
      summary: Changes the global/local schema.
      description: Request body of global schema change contains at least one of "alterVertexTypes", "alterEdgeTypes", "dropVertexTypes", "dropEdgeTypes", "addVertexTypes", "addEdgeTypes" and "graphs". Request body of local schema change contains all of the above fields except "graphs".
      parameters:
        - $ref: "#/components/parameters/GraphNameQuery"
        - $ref: "#/components/parameters/RequestType"        
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                alterEdgeTypes: [
                  {
                    name: "rate",
                    dropAttributes: ["attr1"],
                    addAttributes: [
                      {
                        AttributeName: "attr2",
                        AttributeType: { Name: "STRING" }
                      }
                    ]
                  }
                ],
                alterVertexTypes: [
                  {
                    name: "movie",
                    dropAttributes: ["attr1"],
                    addAttributes: [
                      {
                        AttributeName: "attr2",
                        AttributeType: { Name: "STRING" }
                      }
                    ],
                    dropIndexAttributes: [
                      {
                        attributeName: "attr1",
                        indexName: "index_xxx"
                      }
                    ],
                    addIndexAttributes: [
                      {
                        attributeName: "attr2",
                        indexName: "index_xxx"
                      }
                    ]
                  }
                ],
                dropEdgeTypes: [ "buy" ],
                dropVertexTypes: [ "person" ],
                addVertexTypes: [
                  {
                    Name: "company",
                    PrimaryId: {
                      AttributeName: "id",
                      AttributeType: { Name: "STRING" }
                    },
                    Attributes: []
                  }
                ],
                addEdgeTypes: [
                  {
                    Name: "work_at",
                    FromVertexTypeName: "person",
                    ToVertexTypeName: "company",
                    IsDirected: true,
                    Attributes: [],
                    EdgePairs: [{ From: "person", To: "company" }]
                  }
                ],
                graphs: [
                  {
                    graphName: "MyGraph",
                    dropVertexTypes: [ "person" ],
                    dropEdgeTypes: [ "work_at" ],
                    addVertexTypes: [ "movie" ],
                    addEdgeTypes: [ "rate" ]
                  }
                ]
              }
        required: true
      responses:
        200:
          description: Successfully changed schema.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: Global schema change succeeded.
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/NoGraphNameProvided"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/gsql-server/gsql/typenames:
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves all type names.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved type names.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: {
                          "MyGraph": [
                            "v1",
                            "person",
                            "local_schema_change_defd1"
                          ],
                          "Global": [
                            "0",
                            "1",
                            "MyGraph",
                            "global_schema_change_awrwe1"
                          ]
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/gsql/udt:
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves all user-defined tuples.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved user-defined tuples.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: [
                          {
                            "fields": [
                              {
                                "fieldName": "a",
                                "fieldType": "INT"
                              },
                              {
                                "fieldName": "b",
                                "fieldType": "STRING",
                                "length": 10
                              }
                            ],
                            "name": "MyTuple1"
                          }
                        ]
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/gsql-server/gsql/userdefinedtokenfunctions:
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves all user-defined token functions.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved user-defined token functions.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: [
                          {
                            "code": "extern \"C\" void _Concat(const char* const iToken[], uint32_t iTokenLen[]......",
                            "name": "_Concat",
                            "returnType": "string"
                          }
                        ]
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/gsql/datasources:
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves all data sources for a graph.
      parameters:
        - $ref: "#/components/parameters/GraphNameQueryRequired"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved data sources.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: [
                          {
                            "name": "mys",
                            "type": "s3",
                            "isLocal": true
                          }
                        ]
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - GSQL Server Proxy
      summary: Creates a data source for a graph.
      parameters:
        - $ref: "#/components/parameters/GraphNameQueryRequired"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                name: ds,
                type: s3,
                config: {
                  "file.reader.settings.fs.s3a.access.key": "AKIAJ****4YGHQ",
                  "file.reader.settings.fs.s3a.secret.key": "R8bli****p+dT4"
                }
              }
      responses:
        200:
          description: Successfully created data source.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: Data source ds is created.
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - GSQL Server Proxy
      summary: Deletes a data source for a graph.
      parameters:
        - $ref: "#/components/parameters/GraphNameQueryRequired"
        - $ref: "#/components/parameters/DataSourceNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully deleted data source.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: Data source ds is dropped.
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The requested data source cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Data source MyDataSource does not exist. 
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/gsql/lists3buckets:
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves all s3 buckets of a data source.
      parameters:
        - $ref: "#/components/parameters/DataSourceNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved s3 buckets.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: [
                          "bucket1",
                          "bucket2",
                          "bucket3"
                        ]
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/gsql/listfiles:
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves first level files and directories under a path of a s3 bucket.
      description: If the path points to a file, returns the file. If the path points to a directory, returns first level files and directories under the path.
      parameters:
        - $ref: "#/components/parameters/DataSourceNameQuery"
        - $ref: "#/components/parameters/DataSourcePath"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved files and directories.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: {
                          "folders": [
                            "dir1",
                            "dir2"
                          ],
                          "files": [
                            "file1",
                            "file2"
                          ]
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/gsql/sampledata:
    post:
      tags:
        - GSQL Server Proxy
      summary: Retrieves sample data of a data file.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema: 
              oneOf:
                - $ref: "#/components/schemas/S3SampleDataRequest"
                - $ref: "#/components/schemas/GCSSampleDataRequest"
      responses:
        200:
          description: Successfully retrieved sample data.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: {
                          "data": [
                            ["OpenAccountBranch", "OpenAccountPersonID", "CardNumber", "OpenAccountTime"],
                            ["Bank of America", "0", "0000001", "**********"],
                            ["Bank of America", "1", "********", "**********"],
                            ["Bank of America", "2", "********", "**********"],
                            ["Bank of America", "3", "********", "**********"],
                            ["Bank of America", "3", "********", "**********"],
                            ["Bank of America", "4", "********", "**********"],
                            ["Bank of America", "4", "********", "**********"],
                            ["Bank of America", "5", "********", "**********"],
                            ["Bank of America", "5", "********", "**********"]
                          ],
                          "header": ["column_1", "column_2", "column_3", "column_4"],
                          "json": false
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/gsql-server/gsql/codecheck:
    post:
      tags:
        - GSQL Server Proxy
      summary: Runs code check for a query.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema: 
              type: object
              properties:
                graphName:
                  type: string
                  example: MyGraph
                code:
                  type: string
                  example: "CREATE QUERY q1(int a) FOR GRAPH MyGraph { PRINT a; }"
      responses:
        200:
          description: Successfully ran code check.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: object
                        properties:
                          errors:
                            type: array
                            items:
                              type: object
                            example: []
                          warnings:
                            type: array
                            items:
                              type: object
                            example: []
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/gsql-server/gsql/users:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves users' info.
      parameters:
        - $ref: "#/components/parameters/GraphNameQuery"
        - $ref: "#/components/parameters/Username"
      responses:
        200:
          description: Successfully retrieved users' info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: array
                        items:
                          type: object
                        example: [
                          {
                            name: user1,
                            isSuperUser: false,
                            roles: {},
                            privileges: {},
                            secrets: {}
                          }
                        ]
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - GSQL Server Proxy
      summary: Creates a user.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                name: user2,
                password: 123
              }
      responses:
        200:
          description: Successfully created user.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: object
                        example: {
                          name: user2,
                          isSuperUser: false,
                          roles: {},
                          privileges: {},
                          secrets: {}
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - GSQL Server Proxy
      summary: Changes password of a user.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                name: user1,
                password: new123
              }
      responses:
        200:
          description: Successfully changed password.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: object
                        example: {
                          name: user1,
                          isSuperUser: false,
                          roles: {},
                          privileges: {},
                          secrets: {}
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - GSQL Server Proxy
      summary: Deletes a user.
      parameters:
        - $ref: "#/components/parameters/Username"
      responses:
        200:
          description: Successfully deleted user.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: Successfully remove the user user1.
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/gsql/proxy-group:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - GSQL Server Proxy
      summary: Retrieves proxy groups' info.
      parameters:
        - $ref: "#/components/parameters/GraphNameQuery"
        - $ref: "#/components/parameters/ProxyGroupName"
      responses:
        200:
          description: Successfully retrieved proxy groups' info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: array
                        items:
                          type: object
                        example: [
                          {
                            name: group1,
                            isSuperUser: false,
                            roles: {},
                            privileges: {},
                            secrets: {},
                            rule: "group = hr department"
                          }
                        ]
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - GSQL Server Proxy
      summary: Creates a proxy group.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                name: group2,
                rule: "group = new xxx"
              }
      responses:
        200:
          description: Successfully created proxy group.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: object
                        example: {
                          name: group2,
                          isSuperUser: false,
                          roles: {},
                          privileges: {},
                          secrets: {},
                          rule: group = new xxx
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - GSQL Server Proxy
      summary: Changes proxy rule of a proxy group.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                name: group1,
                rule: "group = new rule"
              }
      responses:
        200:
          description: Successfully changed proxy rule.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: object
                        example: {
                          name: group1,
                          isSuperUser: false,
                          roles: {},
                          privileges: {},
                          secrets: {},
                          rule: "group = new rule"
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - GSQL Server Proxy
      summary: Deletes a proxy group.
      parameters:
        - $ref: "#/components/parameters/ProxyGroupName"
      responses:
        200:
          description: Successfully deleted proxy group.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: Successfully remove the group group1.
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/gsql/secrets:
    parameters:
      - $ref: "#/components/parameters/GraphNameQuery"
      - $ref: "#/components/parameters/Alias"
      - $ref: "#/components/parameters/RequestType"
    post:
      tags:
        - GSQL Server Proxy
      summary: Creates a secret.
      responses:
        200:
          description: Successfully created secret.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: object
                        example: {
                          alias: aaab,
                          value: safarfasdfq2314df
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - GSQL Server Proxy
      summary: Deletes a secret.
      responses:
        200:
          description: Successfully deleted secret.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: Successfully remove the secret for user user1.
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/gsql-server/gsql/roles:
    post:
      tags:
        - GSQL Server Proxy
      summary: Grants role to or revokes role from a user.
      parameters:
        - $ref: "#/components/parameters/RoleAction"
        - $ref: "#/components/parameters/RoleName"
        - $ref: "#/components/parameters/Username"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully granted/revoked role.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: object
                        example: {
                          name: user1,
                          isSuperUser: false,
                          roles: { 
                            MyGraph: [ querywriter ]
                          },
                          privileges: {
                            MyGraph: [
                              "READ_SCHEMA",
                              "READ_LOADINGJOB",
                              "EXECUTE_LOADINGJOB",
                              "READ_QUERY",
                              "WRITE_QUERY",
                              "READ_DATA",
                              "WRITE_DATA"
                            ]
                          },
                          secrets: {}
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/interpreted_query:
    post:
      tags:
        - GSQL Server Proxy
      summary: Runs an interpreted query.
      parameters:
        - $ref: "#/components/parameters/InterpretedQueryParams"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              type: string
              example: "INTERPRET QUERY (int a) FOR GRAPH MyGraph { PRINT a; }"
      responses:
        200:
          description: Successfully ran query in interpreted mode.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: array
                        items:
                          type: object
                          example: { "a": 10 }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"  
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/interpreted_query/params:
    post:
      tags:
        - GSQL Server Proxy
      summary: Gets parameters of an interpreted query.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema: 
              type: string
              example: "INTERPRET QUERY (int a) FOR GRAPH MyGraph { PRINT a; }"
      responses:
        200:
          description: Successfully got parameters.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: array
                        items:
                          type: object
                          properties:
                            paramName:
                              type: string
                              example: a
                            paramType:
                              type: object
                              properties:
                                type:
                                  type: string
                                  example: INT
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/rdbms/meta:
    post:
      tags:
        - GSQL Server Proxy
      parameters:
        - $ref: "#/components/parameters/DBType"
        - $ref: "#/components/parameters/DBServer"
        - $ref: "#/components/parameters/DBPort"
        - $ref: "#/components/parameters/DBName"
        - $ref: "#/components/parameters/RequestType"
      summary: Gets meta info from RDBMS.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                username: user,
                password: 123
              }
        required: true
      responses:
        200:
          description: Successfully got meta info from RDBMS.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: array
                        items:
                          type: object
                          properties:
                            tableName:
                              type: string
                              example: table1
                            columns:
                              type: array
                              items:
                                type: object
                                properties:
                                  name:
                                    type: string
                                    example: name
                                  type:
                                    type: string
                                    example: string
                                  size:
                                    type: number
                                    example: 1024
                            primaryKey:
                              type: array
                              items:
                                type: string
                              example: [id]
                            foreignKeys:
                              type: array
                              items:
                                type: object
                                properties:
                                  pkTableName:
                                    type: string
                                    example: table2
                                  pkColumnName:
                                    type: string
                                    example: col1
                                  fkTableName:
                                    type: string
                                    example: table3
                                  fkColumnName:
                                    type: string
                                    example: col2
                            indices:
                              type: array
                              items:
                                type: object
                                properties:
                                  name:
                                    type: string
                                    example: ind
                                  columns:
                                    type: array
                                    items:
                                      type: string
                                    example: [c1, c2, c3]
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gsql-server/rdbms/dump:
    post:
      tags:
        - GSQL Server Proxy
      summary: Dump selected RDBMS tables as csv files.
      parameters:
        - $ref: "#/components/parameters/DBType"
        - $ref: "#/components/parameters/DBServer"
        - $ref: "#/components/parameters/DBPort"
        - $ref: "#/components/parameters/DBName"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                tables: [table1, table2],
                username: user,
                password: 123
              }
        required: true
      responses:
        200:
          description: Successfully dumped selected RDBMS tables as csv files.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: object
                        example: {
                          table1: path1,
                          table2: path2
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] GSQL Server Proxy #####
  
  ##### [Begin] TS3 Server Proxy #####
  /api/ts3/api/datapoints:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - TS3 Server Proxy
      summary: Retrieves data points from TS3 server.
      parameters:
        - $ref: "#/components/parameters/TS3DataFrom"
        - $ref: "#/components/parameters/TS3DataTo"
        - $ref: "#/components/parameters/TS3DataWhat"
      responses:
        200:
          description: Successfully retrieved data points.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        type: array
                        items:
                          type: object
                        example: [
                          {
                            detail: 29.86,
                            what: "cpu",
                            when: 1623109659,
                            where: "m1",
                            who: "GPE_1#1"
                          }
                        ]
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] TS3 Server Proxy #####
  
  ##### [Begin] Configuration #####
  /api/config:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Configuration
      summary: Retrieves configurations for a list of keys.
      parameters:
        - $ref: "#/components/parameters/ConfigKey"
      responses:
        200:
          description: Successfully retrieved configurations.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                  allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: {
                          GUI.Port: "14242",
                          GSQL.Port: "8123"
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - Configuration
      summary: Sets and applies configurations for a list of keys.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              example: {
                SSO.SAML.AssertionSigned: "true",
                SSO.SAML.AuthnRequestSigned: "true"
              }
        required: true
      responses:
        200:
          description: Successfully set and applied configurations.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully set and applied configurations.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/config/generate-cert:
    post:
      tags:
        - Configuration
      summary: Generates self-signed certificate and private key
      parameters:
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Certificate"
        required: true
      responses:
        200:
          description: Successfully generated self-signed certificate and private key.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: {
                          certificate: "<content>",
                          privateKey: "<content>",
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Configuration #####

  ##### [Begin] Service Control #####
  /api/service/start:
    post:
      tags:
        - Service Control
      summary: Starts a list of services.
      parameters:
        - $ref: "#/components/parameters/ServiceName"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully started services.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully started services.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/service/stop:
    post:
      tags:
        - Service Control
      summary: Stops a list of services.
      parameters:
        - $ref: "#/components/parameters/ServiceName"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully stopped services.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully stopped services.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/service/restart:
    post:
      tags:
        - Service Control
      summary: Restarts a list of services.
      parameters:
        - $ref: "#/components/parameters/ServiceName"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully restarted services.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully restarted services.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Service Control #####
  
  ##### [Begin] Data #####
  /api/data/{category}:
    parameters:
      - $ref: "#/components/parameters/Category"
      - $ref: "#/components/parameters/DataPath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Data
      summary: Retrieves first level files and directories under a path in a category.
      description: If the path points to a file, returns the file. If the path points to a directory, returns the first level files and directories under the path.
      responses:
        200:
          description: Successfully retrieved files and directories.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DataListingResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Data
      summary: Removes all files and directories under a path in a category.
      description: If the path points to a file, removes the file. If the path points to a directory, removes all files and directories under the directory and the directory itself.
      responses:
        200:
          description: Successfully removed all files and directories.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully removed path '/dataset' in category 'loading_data'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
 
  /api/data/{category}/upload:
    parameters:
      - $ref: "#/components/parameters/Category"
      - $ref: "#/components/parameters/DataPath"
      - $ref: "#/components/parameters/RequestType"
    post:
      tags:
        - Data
      summary: Uploads a file to a path in a category.
      requestBody:
        content:
          multipart/form-data:
            schema:
              allOf:
                - $ref: "#/components/schemas/FileUploadInfo"
        required: true
      responses:
        201:
          description: Successfully uploaded file.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully uploaded file 'person.csv' to path '/dataset' in category 'loading_data'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        409:
          $ref: "#/components/responses/FileExist"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - Data
      summary: Uploads and overwrites a file to a path in a category.
      requestBody:
        content:
          multipart/form-data:
            schema:
              allOf:
                - $ref: "#/components/schemas/FileUploadInfo"
        required: true
      responses:
        201:
          description: Successfully uploaded file.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully uploaded file 'person.csv' to path '/dataset' in category 'loading_data'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/data/{category}/upload/chunk:
    parameters:
      - $ref: "#/components/parameters/Category"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Data
      summary: Checks if an uploaded chunk exists for a file.
      parameters:
        - $ref: "#/components/parameters/ResumableChunkNumber"
        - $ref: "#/components/parameters/ResumableChunkSize"
        - $ref: "#/components/parameters/ResumableCurrentChunkSize"
        - $ref: "#/components/parameters/ResumableTotalSize"
        - $ref: "#/components/parameters/ResumableType"
        - $ref: "#/components/parameters/ResumableIdentifier"
        - $ref: "#/components/parameters/ResumableFilename"
        - $ref: "#/components/parameters/ResumableRelativePath"
        - $ref: "#/components/parameters/ResumableTotalChunks"
      responses:
        200:
          description: Successfully retrieved the chunk for the file.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Chunk 1 exists for file 'person.csv' in category 'loading_data'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The specified chunk cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Chunk 1 for file 'person.csv' in category 'loading_data' cannot be found.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - Data
      summary: Uploads a chunk for a file.
      requestBody:
        content:
          multipart/form-data:
            schema:
              allOf:
                - $ref: "#/components/schemas/FileChunkInfo"
        required: true
      responses:
        201:
          description: Successfully uploaded the chunk.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully uploaded chunk 1 for file 'person.csv' in category 'loading_data'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/data/{category}/upload/chunk/assemble:
    parameters:
      - $ref: "#/components/parameters/Category"
      - $ref: "#/components/parameters/DataPath"
      - $ref: "#/components/parameters/ResumableIdentifier"
      - $ref: "#/components/parameters/ResumableFilename"
      - $ref: "#/components/parameters/RequestType"
    post:
      tags:
        - Data
      summary: Assembles uploaded chunks for a file to a path in a category.
      responses:
        201:
          description: Successfully assembled uploaded chunks for the file.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully assembled file 'person.csv' to path '/dataset' in category 'loading_data'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        409:
          $ref: "#/components/responses/FileExist"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - Data
      summary: Assembles and overrides uploaded chunks for a file to a path in a category.
      responses:
        201:
          description: Successfully assembled uploaded chunks for the file.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully assembled file 'person.csv' to path '/dataset' in category 'loading_data'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Data #####

  ##### [Begin] Log #####
  /api/log:
    get:
      tags:
        - Log
      summary: Retrieves first level files and directories under a path.
      description: If the path points to a file, returns the file. If the path points to a directory, returns first level files and directories under the path.
      parameters:
        - $ref: "#/components/parameters/HostID"
        - $ref: "#/components/parameters/LogPath"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved files and directories.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LogListingResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/log/view:
    get:
      tags:
        - Log
      summary: Retrieves the content of a log file.
      parameters:
        - $ref: "#/components/parameters/HostID"
        - $ref: "#/components/parameters/LogPathRequired"
        - $ref: "#/components/parameters/LogOffset"
        - $ref: "#/components/parameters/LogReadLength"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved log content.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: <content>
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/log/search:
    get:
      tags:
        - Log
      summary: Search for a pattern among a list of components.
      parameters:
        - $ref: "#/components/parameters/LogSearchHostIDs"
        - $ref: "#/components/parameters/LogSearchComponents"
        - $ref: "#/components/parameters/LogSearchPattern"
        - $ref: "#/components/parameters/LogSearchLimit"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved search result.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/LogSearchResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/log/download:
    get:
      tags:
        - Log
      summary: Downloads the log file or directory.
      parameters:
        - $ref: "#/components/parameters/HostID"
        - $ref: "#/components/parameters/LogPath"
      responses:
        200:
          description: Successfully downloaded the log file or directory.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Log #####

  ##### [Begin] System #####
  /api/system/export:
    get:
      tags:
        - System
      summary: Exports the solution.
      responses:
        200:
          description: Successfully exported solution.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/system/import:
    post:
      tags:
        - System
      summary: Imports a solution.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully imported solution.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully imported solution.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/system/gui-store:
    get:
      tags:
        - System
      summary: Exports GUI metadata from the key-value store and the users' icons. Requires auth token.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully exported GUI metadata from the key-value store and the users' icons.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - System
      summary: Imports GUI metadata to the key-value store and the users' icons. Requires auth token.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully imported GUI metadata to the key-value store and the users' icons.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully imported GUI data.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - System
      summary: Deletes GUI metadata from the key-value store and the users' icons. Requires auth token.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully deleted GUI metadata from the key-value store and the users' icons.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted GUI data.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/system/graph-store:
    delete:
      tags:
        - System
      summary: Deletes all data from the graph store.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully deleted all data from the graph store.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted all data from the graph store.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] System #####

  ##### [Begin] GBAR #####
  /api/gbar:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - GBAR
      summary: Retrieves all GBAR backups' info.
      responses:
        200:
          description: Successfully retrieved backups' info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
                schema:
                  $ref: "#/components/schemas/BackupListResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/gbar/{backupName}:
    parameters:
      - $ref: "#/components/parameters/RequestType"
      - $ref: "#/components/parameters/BackupNamePath"
    delete:
      tags:
        - GBAR
      summary: Deletes a GBAR backup.
      responses:
        200:
          description: Successfully deleted the GBAR backup.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted the GBAR backup 'daily-20210216115229'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
 
  /api/gbar/schedule:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - GBAR
      summary: Retrieves the schedule for automatic GBAR backup.
      responses:
        200:
          description: Successfully retrieved the schedule for the automatic GBAR backup.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BackupScheduleResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - GBAR
      summary: Schedules a routine automatic GBAR backup.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BackupSchedule"
      responses:
        200:
          description: Successfully scheduled a routine automatic GBAR backup.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully scheduled a routine automatic GBAR backup.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/gbar/schedule/cron:
    parameters:
      - $ref: "#/components/parameters/RequestType" 
    post:
      tags:
        - GBAR
      summary: Updates cron job for automatic GBAR backup.
      responses:
        200:
          description: Successfully updated cron job for automatic GBAR backup.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully updated cron job for automatic GBAR backup.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/gbar/schedule/status:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - GBAR
      summary: Retrieves all automatic GBAR backup status.
      responses:
        200:
          description: Successfully retrieved all automatic GBAR backup status.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BackupStatusListResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/gbar/backup:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    post:
      tags:
        - GBAR
      summary: Run a GBAR backup.
      parameters:
        - $ref: "#/components/parameters/BackupTag"
      responses:
        201:
          description: Successfully ran a GBAR backup.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully ran GBAR backup to archive with tag 'daily'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
      
  /api/gbar/restore:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    post:
      tags:
        - GBAR
      summary: Run a GBAR restore.
      parameters:
        - $ref: "#/components/parameters/BackupName"
      responses:
        200:
          description: Successfully run a GBAR restore.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully ran GBAR restore from archive 'daily-20210216175201'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [END] GBAR #####

  ##### [Begin] Loading Job ####
  /api/loading-jobs/{graphName}/meta:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Loading Job
      summary: Retrieves all loading jobs for a graph.
      responses:
        200:
          description: Successfully retrieved loading jobs.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AllLoadingJobsResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Loading Job
      summary: Deletes all loading jobs from a graph
      responses:
        200:
          description: Successfully deleted loading jobs.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted all loading jobs from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/loading-jobs/{graphName}/meta/{jobName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/LoadingJobNamePath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Loading Job
      summary: Retrieves a loading job.
      responses:
        200:
          description: Successfully retrieved loading job.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoadingJobResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The specified loading job cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Loading job 'load_job_person_csv_1579902877625' for graph 'MyGraph' cannot be found.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - Loading Job
      summary: Creates a loading job.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoadingJob"
        required: true
      responses:
        201:
          description: Successfully created loading job.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully created loading job 'load_job_person_csv_1579902877625' for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        409:
          description: Loading job already exists.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Loading job 'load_job_person_csv_1579902877625' already exists on graph 'MyGraph'.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Loading Job
      summary: Deletes a loading job
      responses:
        200:
          description: Successfully deleted loading job
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted loading job 'load_job_person_csv_1579902877625' from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    
  
  /api/loading-jobs/{graphName}/loading/progress:
    get:
      tags:
        - Loading Job
      summary: Retrieves loading jobs' progress.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/LoadingJobNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully retrieved loading jobs' progress.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoadingJobProgressResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/loading-jobs/{graphName}/loading/start:
    post:
      tags:
        - Loading Job
      summary: Starts loading jobs.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StartLoadingJobRequest"
        required: true
      responses:
        200:
          description: Successfully started loading jobs.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StartLoadingJobResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/loading-jobs/{graphName}/loading/pause:
    post:
      tags:
        - Loading Job
      summary: Pauses loading jobs.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/LoadingJobNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully paused loading jobs.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PauseLoadingJobResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/loading-jobs/{graphName}/loading/resume:
    post:
      tags:
        - Loading Job
      summary: Resumes loading jobs.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/LoadingJobNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully resumed loading jobs.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResumeLoadingJobResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/loading-jobs/{graphName}/loading/stop:
    post:
      tags:
        - Loading Job
      summary: Stops loading jobs.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/LoadingJobNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully stopped loading jobs.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StopLoadingJobResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Loading Job #####
  
  ##### [Begin] Query #####
  /api/queries/{graphName}/info:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Query
      summary: Retrieves all queries' info for a graph.
      responses:
        200:
          description: Successfully retrieved queries' info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AllQueriesInfoResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  /api/queries/{graphName}/drafts:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/RequestType"
    delete:
      tags:
        - Query
      summary: Deletes all query drafts from a graph.
      responses:
        200:
          description: Successfully deleted all queriy drafts.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted all query darft from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/queries/{graphName}/info/{queryName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/QueryNamePath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Query
      summary: Retrieves a query's info.
      responses:
        200:
          description: Successfully retrieved query's info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/QueryInfoResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The specified query cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Query 'q1' for graph 'MyGraph' cannot be found.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Query
      summary: Deletes a query and its draft.
      responses:
        200:
          description: Successfully deleted query.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted query 'q1' from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/queries/{graphName}/info/{queryName}/draft:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/QueryNamePath"
      - $ref: "#/components/parameters/RequestType"
    post:
      tags:
        - Query
      summary: Creates a query draft.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QueryDraft"
      responses:
        201:
          description: Successfully created query draft.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully created query draft 'q1' for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        409:
          description: Query draft already exists.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Query draft 'q1' already exists on graph 'MyGraph'.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - Query
      summary: Upserts a query draft.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QueryDraft"
      responses:
        200:
          description: Successfully upserted query draft.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully upserted query draft 'q1' for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Query
      summary: Deletes a query draft
      responses:
        200:
          description: Successfully deleted query draft.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted query draft 'q1' from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/queries/{graphName}/gsql/add:
    post:
      tags:
        - Query
      summary: Adds query drafts to GSQL.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/QueryNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: List of success and failed queries.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: {
                          "success": ["q1", "q3"],
                          "failed": ["q2", "q4"]
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/queries/{graphName}/gsql/install:
    post:
      tags:
        - Query
      summary: Installs queries to GSQL.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/QueryNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully installed queries.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: {
                          "success": ["q1", "q3"],
                          "failedToDeleteDraft": ["q2", "q4"]
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Query #####

  ##### [Begin] GSQL Algorithm #####

  /api/graph-algorithm:
    get:
      tags:
        - GSQL Algorithm
      summary: Installs queries to GSQL.
      responses:
        200:
          description: Successfully retrieved gsql algorithms.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AllGraphAlgorithmsResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  /api/graph-algorithm/{graphName}/install:
    post:
      tags:
        - GSQL Algorithm
      summary: Installs queries to GSQL.
      parameters:
        - $ref: "#/components/parameters/GraphNamePath"
        - $ref: "#/components/parameters/QueryNameQuery"
        - $ref: "#/components/parameters/RequestType"
      responses:
        200:
          description: Successfully installed queries.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      results:
                        example: {
                          "success": ["q1", "q3"],
                          "failed": ["q2", "q4"]
                        }
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"

  ##### [End] GSQL Algorithm #####
  
  ##### [Begin] Visual Pattern #####
  /api/visual-patterns/{graphName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Visual Pattern
      summary: Retrieves all visual patterns for a graph.
      responses:
        200:
          description: Successfully retrieved visual patterns.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AllVisualPatternsResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Visual Pattern
      summary: Deletes all visual patterns from a graph.
      responses:
        200:
          description: Successfully deleted all visual patterns.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted all visual patterns from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/visual-patterns/{graphName}/{patternName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/PatternName"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Visual Pattern
      summary: Retrieves a visual pattern.
      responses:
        200:
          description: Successfully retrieved visual pattern.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/VisualPatternResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The specified visual pattern cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Visual pattern 'p1' for graph 'MyGraph' cannot be found.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - Visual Pattern
      summary: Creates a visual pattern.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/VisualPattern"
      responses:
        201:
          description: Successfully created visual pattern.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully created visual pattern 'p1' for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        409:
          description: Visual pattern already exists.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Visual pattern 'p1' already exists on graph 'MyGraph'.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - Visual Pattern
      summary: Upserts a visual pattern.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/VisualPattern"
      responses:
        200:
          description: Successfully upserted visual pattern.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully upserted visual pattern 'p1' for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Visual Pattern
      summary: Deletes a visual pattern.
      responses:
        200:
          description: Successfully deleted visual pattern.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted visual pattern 'p1' from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Visual Pattern #####
  
  ##### [Begin] Graph Style #####
  /api/graph-styles/global:
    parameters:
        - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Graph Style
      summary: Retrieves global graph style.
      responses:
        200:
          description: Successfully retrieved global graph style.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GraphStyleResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The global graph style cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Global graph style cannot be found.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - Graph Style
      summary: Upserts global graph style.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GraphStyle"
      responses:
        200:
          description: Successfully upserted global graph style.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully upserted global graph style.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Graph Style
      summary: Deletes global graph style.
      responses:
        200:
          description: Successfully deleted global graph style.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted global graph style.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/graph-styles/local/{graphName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Graph Style
      summary: Retrieves a local graph style.
      responses:
        200:
          description: Successfully retrieved local graph style.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GraphStyleResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The specified graph style cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Local graph style for graph 'MyGraph' cannot be found.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - Graph Style
      summary: Creates a local graph style.
      parameters:
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GraphStyle"
      responses:
        201:
          description: Successfully created local graph style.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully created local graph style for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        409:
          description: Local graph style already exists.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Local graph style for graph 'MyGraph' already exists.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - Graph Style
      summary: Upserts a local graph style.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GraphStyle"
      responses:
        200:
          description: Successfully upserted local graph style.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully upserted local graph style for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Graph Style
      summary: Deletes a local graph style.
      responses:
        200:
          description: Successfully deleted local graph style.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted local graph style for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Graph Style #####
  
  ##### [Begin] Loading Job Info #####
  /api/loading-job-info/{graphName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Loading Job Info
      summary: Retrieves a loading job info.
      responses:
        200:
          description: Successfully retrieved loading job info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoadingJobInfoResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The specified loading job info cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Loading job info for graph 'MyGraph' cannot be found.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - Loading Job Info
      summary: Creates a loading job info
      parameters:
        - $ref: "#/components/parameters/RequestType"
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJobInfo"
      responses:
        201:
          description: Successfully created loading job info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully created loading job info for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        409:
          description: Loading job info already exists.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Loading job info for graph 'MyGraph' already exists.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - Loading Job Info
      summary: Upserts a loading job info.
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJobInfo"
      responses:
        200:
          description: Successfully upserted loading job info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully upserted loading job info for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Loading Job Info
      summary: Deletes a loading job info.
      responses:
        200:
          description: Successfully deleted loading job info.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted loading job info for graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Loading Job Info #####
  
  ##### [Begin] Exploration Result #####
  /api/exploration-results/{graphName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Exploration Result
      summary: Retrieves all exploration result previews for a graph.
      responses:
        200:
          description: Successfully retrieved exploration result previews.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ExplorationResultPreviewResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Exploration Result
      summary: Deletes all exploration results from a graph.
      responses:
        200:
          description: Successfully deleted all exploration results from a graph.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted all exploration results from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  /api/exploration-results/{graphName}/{explorationName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/ExplorationName"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Exploration Result
      summary: Retrieves an exploration result.
      responses:
        200:
          description: Successfully retrieved exploration result.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ExplorationResultDataResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        404:
          description: The specified exploration result cannot be found.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Exploration result 'f1' for graph 'MyGraph' cannot be found.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    post:
      tags:
        - Exploration Result
      summary: Creates an exploration result.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ExplorationResult"
      responses:
        201:
          description: Successfully created exploration result.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully created exploration result 'f1'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        409:
          description: Exploration result already exists.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/FailureResponse"
                  - properties:
                      message:
                        example: Exploration result 'f1' already exists on graph 'MyGraph'.
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    put:
      tags:
        - Exploration Result
      summary: Upserts an exploration result.
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ExplorationResult"
      responses:
        200:
          description: Successfully upserted exploration result.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully upserted exploration result 'f1'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Exploration Result
      summary: Deletes an exploration result.
      responses:
        200:
          description: Successfully deleted exploration result.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted exploration result 'f1' from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Exploration Result #####
  
  
  ##### [Begin] Data Source #####
  /api/data-source/{graphName}/{dataSourceType}/names:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/DataSourceType"
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Data Source
      summary: Retrieves the names of all data sources.
      responses:
        200:
          description: Successfully retrieved the names of all data sources.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AllDataSourceNamesResponse"
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
          
  /api/data-sources/{dataSourceType}/auth:
    parameters:
      - $ref: "#/components/parameters/DataSourceType"
      - $ref: "#/components/parameters/RequestType"
    post:
      tags:
      - Data Source
      summary: Authenticates the credentials of a given data source type.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/DataSourceRequest"
      responses:
        200:
          description: Successfully authenticated the data source.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully authenticated the data source type 'gcs'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  
  /api/data-sources/{graphName}/{dataSourceType}/{dataSourceName}:
    parameters:
      - $ref: "#/components/parameters/GraphNamePath"
      - $ref: "#/components/parameters/DataSourceType"
      - $ref: "#/components/parameters/DataSourceNamePath"
      - $ref: "#/components/parameters/RequestType"
    put:
      tags:
        - Data Source
      summary: Upserts a data source.
      requestBody:
        content:
          application/json:
            schema:
              allOf:
                - $ref: "#/components/schemas/DataSourceRequest"
      responses:
        200:
          description: Successfully upserted data source.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully upserted data source 'my_connection'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        400:
          $ref: "#/components/responses/BadRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
    delete:
      tags:
        - Data Source
      summary: Deletes a data source.
      responses:
        200:
          description: Successfully deleted data source.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: Successfully deleted 'gcs' data source 'my_connection' from graph 'MyGraph'.
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        401:
          $ref: "#/components/responses/Unauthorized"
        500:
          $ref: "#/components/responses/InternalError"
        503:
          $ref: "#/components/responses/ServiceUnavailable"
  ##### [End] Data Source #####
  
  ##### [Begin] Health Check #####
  /api/ping:
    parameters:
      - $ref: "#/components/parameters/RequestType"
    get:
      tags:
        - Health Check
      summary: Checks if the server is running.
      responses:
        200:
          description: Successfully retrieves the status.
          headers:
            RequestID:
              schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - properties:
                      message:
                        example: "pong"
                      results:
                        example: {}
        202:
          $ref: "#/components/responses/LongRequest"
        500:
          $ref: "#/components/responses/InternalError"
  ##### [End] Health Check #####

components:
  parameters:
    ##### [Begin] Common #####
    RequestType:
      name: Request-Type
      in: header
      description: Whether to use short request with response through HTTP or long request with response through EventSource.
      schema:
        type: string
        default: short
        enum: [short, long]
    
    GraphNamePath:
      name: graphName
      in: path
      description: The name of the graph (used in path).
      required: true
      schema:
        type: string
        example: MyGraph
    
    HostID:
      name: hostID
      in: query
      description: The machine host ID.
      schema:
        type: string
        example: m1
    ##### [End] Common #####
    
    ##### [Begin] RESTPP Proxy #####
    VertexType:
      name: vertexType
      in: path
      description: The type of the vertex.
      required: true
      schema:
        type: string
        example: Person
    
    VertexID:
      name: vertexID
      in: path
      description: The ID of the vertex.
      required: true
      schema:
        type: string
        example: 1
    
    VertexLimit:
      name: limit
      in: query
      description: The number of vertices to return.
      schema:
        type: number
        example: 5
    
    FromVertexType:
      name: fromVertexType
      in: path
      description: The type of the from vertex.
      required: true
      schema:
        type: string
        example: Person
    
    FromVertexID:
      name: fromVertexID
      in: path
      description: The ID of the from vertex.
      required: true
      schema:
        type: string
        example: 1
        
    EdgeType:
      name: edgeType
      in: path
      description: The type of the edge.
      required: true
      schema:
        type: string
        example: connection
    
    ToVertexType:
      name: toVertexType
      in: path
      description: The type of the to vertex.
      required: true
      schema:
        type: string
        example: Person
    
    ToVertexID:  
      name: toVertexID
      in: path
      description: The ID of the to vertex.
      required: true
      schema:
        type: string
        example: 1  
    ##### [End] RESTPP Proxy #####
    
    ##### [Begin] GSQL Server Proxy #####
    GraphNameQuery:
      name: graph
      in: query
      description: The name of the graph (used in query). If not provided, means global graph.
      schema:
        type: string
        example: MyGraph

    GraphNameQueryRequired:
      name: graph
      in: query
      description: The name of the graph.
      required: true
      schema:
        type: string
        example: MyGraph

    DataSourceNameQuery:
      name: datasource
      in: query
      description: The name of the data source (used in query).
      required: true
      schema:
        type: string
        example: MyDataSource

    DataSourcePath:
      name: path
      in: query
      description: The file/directory path.
      required: true
      schema:
        type: string
        example: abc

    InterpretedQueryParams:
      name: paramName
      in: query
      description: Parameters of the interpreted query.
      schema:
        type: array
        items:
          type: string
    
    Username:
      name: name
      in: query
      description: Name of the user. If not provided means all users (of a graph).
      schema:
        type: string
        example: user1
    
    ProxyGroupName:
      name: name
      in: query
      description: Name of the proxy group. If not provided means all proxy groups (of a graph).
      schema:
        type: string
        example: group1
    
    Alias:
      name: alias
      in: query
      description: Alias of the secret.
      required: true
      schema:
        type: string
        example: aaab
    
    RoleAction:
      name: action
      in: query
      description: Grant or revoke action.
      required: true
      schema:
        type: string
        example: grant
    
    RoleName:
      name: role
      in: query
      description: Role name.
      required: true
      schema:
        type: string
        example: querywriter

    DBType:
      name: type
      in: query
      description: RDBMS type.
      required: true          
      schema:
        type: string
        example: MySQL
    
    DBServer:
      name: server
      in: query
      description: RDBMS server.
      required: true
      schema:
        type: string
        example: *************

    DBPort:
      name: port
      in: query
      description: RDBMS port.
      required: true
      schema:
        type: string
        example: 3306
    
    DBName:
      name: database
      in: query
      description: The name of the database.
      required: true
      schema:
        type: string
        example: MyData
    ##### [End] GSQL Server Proxy #####
    
    ##### [Begin] TS3 Server Proxy #####
    TS3DataFrom:
      name: from
      in: query
      description: The start time.
      required: true
      schema:
        type: number
        example: 1623109603
    
    TS3DataTo:
      name: to
      in: query
      description: The end time.
      required: true
      schema:
        type: number
        example: 1623109663
    
    TS3DataWhat:
      name: what
      in: query
      description: The metric.
      required: true
      schema:
        type: string
        example: cpu
    ##### [End] TS3 Server Proxy #####
    
    ##### [Begin] Configuration #####
    ConfigKey:
      name: key
      in: query
      description: The name of the configuration keys.
      required: true
      schema:
        type: array
        items:
          type: string
        example: ["GUI.Port", "GSQL.Port"]
    ##### [End] Configuration #####
    
    ##### [Begin] Service Control #####
    ServiceName:
      name: serviceName
      in: query
      description: The name of the services.
      required: true
      schema:
        type: array
        items:
          type: string
        example: ["GPE", "GSE"]
    ##### [End] Service Control #####
    
    ##### [Begin] Data #####
    Category:
      name: category
      in: path
      description: The name of the file category.
      required: true
      schema:
        type: string
        enum: [loading_data, user_icons]
        example: loading_data
    
    DataPath:
      name: path
      in: query
      description: The file/directory path.
      schema:
        type: string
        example: /dataset
    
    ResumableChunkNumber:
      name: resumableChunkNumber
      in: query
      description: The chunk's number.
      required: true
      schema:
        type: number
        example: 1
    
    ResumableChunkSize:
      name: resumableChunkSize
      in: query
      description: The chunk's size.
      required: true
      schema:
        type: number
        example: 1048576
    
    ResumableCurrentChunkSize:
      name: resumableCurrentChunkSize
      in: query
      description: The current chunk's size.
      required: true
      schema:
        type: number
        example: 1048576
    
    ResumableTotalSize:
      name: resumableTotalSize
      in: query
      description: The total file's size.
      required: true
      schema:
        type: number
        example: 5242880
    
    ResumableType:
      name: resumableType
      in: query
      description: The file's type.
      required: true
      schema:
        type: string
        example: text/csv
    
    ResumableIdentifier:
      name: resumableIdentifier
      in: query
      description: The file's identifer.
      required: true
      schema:
        type: string
        example: 50-personcsv
    
    ResumableFilename:
      name: resumableFilename
      in: query
      description: The filename.
      required: true
      schema:
        type: string
        example: person.csv
    
    ResumableRelativePath:
      name: resumableRelativePath
      in: query
      description: The file's relative path.
      required: true
      schema:
        type: string
        example: person.csv
    
    ResumableTotalChunks:
      name: resumableTotalChunks
      in: query
      description: The total number of chunks.
      required: true
      schema:
        type: number
        example: 5
    ##### [End] Data #####
    
    ##### [Begin] Log #####
    LogPath:
      name: path
      in: query
      description: The file/directory path.
      schema:
        type: string
        example: /gui
    
    LogPathRequired:
      name: path
      in: query
      description: The file/directory path.
      required: true
      schema:
        type: string
        example: /gui
        
    LogOffset:
      name: offset
      in: query
      description: The offset position to start reading.
      schema:
        type: number
        example: 0
    
    LogReadLength:
      name: length
      in: query
      description: The number of bytes to read.
      schema:
        type: number
        example: 256
    
    LogSearchHostIDs:
      name: hostID
      in: query
      description: The list of machine host IDs to perform the search. If empty means all hosts.
      schema:
        type: array
        items:
          type: string
        example: ["m1", "m2"]
    
    LogSearchComponents:
      name: component
      in: query
      description: The list of components to perform the search. If empty means all components.
      schema:
        type: array
        items:
          type: string
        example: ["GUI", "GSQL"]
    
    LogSearchPattern:
      name: pattern
      in: query
      description: The search pattern.
      required: true
      schema:
        type: string
        example: abc
    
    LogSearchLimit:
      name: limit
      in: query
      description: The maximum number of search results to return.
      schema:
        type: number
        example: 100
    ##### [End] Log #####

    ##### [Begin] System #####
    IgnoreGraphMetadata:
      name: ignoreGraphMetadata
      in: query
      description: Ignore graph metadata during import or export.
      schema:
        type: boolean
        example: false
    ##### [End] System #####

    ##### [Begin] GBAR #####
    BackupName:
      name: backupName
      in: query
      description: The name of the GBAR backup.
      required: true
      schema:
        type: string
        example: backup-20180607232159

    BackupNamePath:
      name: backupName
      in: path
      description: The name of the GBAR backup.
      required: true
      schema:
        type: string
        example: backup-20180607232159
    
    BackupTag:
      name: backupTag
      in: query
      description: The tag to be used for GBAR backup.
      required: true
      schema:
        type: string
        example: backup
    ##### [End] GBAR #####
    
    ##### [Begin] Loading Job #####
    LoadingJobNamePath:
      name: jobName
      in: path
      description: The name of the loading job.
      required: true
      schema:
        type: string
        example: load_job_person_csv_1579902877625
    
    LoadingJobNameQuery:
      name: jobName
      in: query
      description: The name of the loading job.
      required: true
      schema:
        type: array
        items:
          type: string
        example: ["load_job_person_csv_1579902877625"]
    ##### [End] Loading Job #####
    
    ##### [Begin] Query #####
    QueryNamePath:
      name: queryName
      in: path
      description: The name of the query.
      required: true
      schema:
        type: string
        example: q1
    
    QueryNameQuery:
      name: queryName
      in: query
      description: The name of the queries.
      schema:
        type: array
        items:
          type: string
        example: ["q1"]
    ##### [End] Query #####
    
    ##### [Begin] Visual Pattern #####
    PatternName:
      name: patternName
      in: path
      description: The name of the visual pattern.
      required: true
      schema:
        type: string
        example: p1
    ##### [End] Visual Pattern #####
    
    ##### [Begin] Exploration Result #####
    ExplorationName:
      name: explorationName
      in: path
      description: The name of the exploration result.
      required: true
      schema:
        type: string
        example: f1
    ##### [End] Exploration Result #####
    
    ##### [Begin] Data Source #####
    DataSourceType:
      name: dataSourceType
      in: path
      description: The type of the data source.
      required: true
      schema:
        type: string
        example: gcs

    DataSourceNamePath:
      name: dataSourceName
      in: path
      description: The name of the data source (used in path).
      required: true
      schema:
        type: string
        example: my_connection
    ##### [End] Data Source #####

  responses:
    ##### [Begin] Common #####
    LongRequest:
      description: Response for long request.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessResponse"
              - properties:
                  message:
                    example: Request has been accepted.
                  results:
                    example: {}
            
    BadRequest:
      description: Bad Request.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: Invalid payload.

    Unauthorized:
      description: Unauthorized.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: You are not authorized to use this API.

    InternalError:
      description: Internal server error.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: An unexpected error has occurred. Please try again later.
    
    ServiceUnavailable:
      description: Service unavailable.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: Importing is in progress. Please try again later.
    ##### [End] Common #####
    
    ##### [Begin] RESTPP Proxy #####
    GetVertex:
      description: Successfully retrieved vertices.
      headers:
        RequestID:
          schema:
                allOf:
                  - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/GetVertexResponse"

    RunPathQuery:
      description: Successfully ran path query.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/PathQueryResponse"
    ##### [End] RESTPP Proxy #####
    
    ##### [Begin] GSQL Server Proxy #####
    GraphUnauthorized:
      description: Unauthorized to access the specified graph.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: You cannot access graph 'MyGraph'.
                    
    CreateGraphUnauthorized:
      description: Unauthorized to create a graph.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: Only superuser and globaldesigner can create graph.
                    
    DropGraphUnauthorized:
      description: Unauthorized to drop a graph.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: Only superuser and globaldesigner can drop graph.
    
    NoGraphNameProvided:
      description: Only superuser and globaldesigner can access global graph, other users should provide graph name.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: Please provide graph name via 'graph=xxx' parameter string.
    ##### [End] GSQL Server Proxy #####
    
    ##### [Begin] Data #####
    FileExist:
      description: File already exists.
      headers:
        RequestID:
          schema:
            allOf:
              - $ref: "#/components/schemas/RequestID"
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/FailureResponse"
              - properties:
                  message:
                    example: File 'person.csv' already exists under path '/dataset' in category 'loading_data'.
    ##### [End] Data #####

  schemas:
    ##### [Begin] Common #####
    RequestID:
      type: string
      example: c01d7cf6-ec3f-47f0-9556-a5d6e9009a43
      
    SetCookie:
      type: string
      example: TigerGraphApp=header.payload.signature; path=/; expires=Fri, 31 Jan 2020 22:00:00 GMT; httponly
    
    DeleteCookie:
      type: string
      example: TigerGraphApp=; path=/; httponly
    
    GeneralResponse:
      type: object
      properties:
        error:
          type: boolean
        message:
          type: string
          example: ""
        results:
          oneOf:
            - type: object
            - type: array
              items:
                type: object
            - type: string
      required: [error, message, results]

    SuccessResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/GeneralResponse"
        - properties:
            error:
              example: false

    FailureResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/GeneralResponse"
        - properties:
            error:
              example: true
            results:
              example: {}
    ##### [End] Common #####
    
    ##### [Begin] Auth #####
    LoginRequest:
      type: object
      properties:
        username:
          type: string
          example: tigergraph
        password:
          type: string
          example: tigergraph
      required: [username, password]
      
    UserProfile:
      type: object
      properties:
        name:
          type: string
          example: tigergraph
        roles:
          type: object
          example: { "MyGraph": ["superuser"] }
        privileges:
          type: object
          example: { 
            "MyGraph": [
              "READ_SCHEMA",
              "WRITE_SCHEMA",
              "READ_LOADINGJOB",
              "EXECUTE_LOADINGJOB",
              "WRITE_LOADINGJOB",
              "READ_QUERY",
              "WRITE_QUERY",
              "READ_DATA",
              "WRITE_DATA",
              "WRITE_DATASOURCE",
              "READ_ROLE",
              "WRITE_ROLE",
              "READ_USER",
              "WRITE_USER",
              "READ_PROXYGROUP",
              "WRITE_PROXYGROUP",
              "READ_FILE",
              "WRITE_FILE",
              "DROP_GRAPH",
              "EXPORT_GRAPH",
              "CLEAR_GRAPHSTORE",
              "DROP_ALL",
              "ACCESS_TAG"
            ]
          }
        isSuperUser:
          type: boolean
          example: true
        secrets:
          type: object
          example: { "MyGraph": [{ "alias": "abc", "value": "fpm****c6v" }] }
      required: [name, roles, isSuperUser, secrets]
    ##### [End] Auth #####
    
    ##### [Begin] RESTPP Proxy #####
    RESTPPVertex:
      type: object
      properties:
        v_id:
          type: string
          example: "1"
        v_type:
          type: string
          example: Person
        attributes:
          type: object
      required: [v_id, v_type, attributes]
    
    RESTPPEdge:
      type: object
      properties:
        e_type:
          type: string
          example: connection
        directed:
          type: boolean
          example: false
        from_id:
          type: string
          example: "1"
        from_type:
          type: string
          example: Person
        to_id:
          type: string
          example: "1"
        to_type:
          type: string
          example: Person
        attributes:
          type: object
      required: [e_type, directed, from_id, from_type, to_id, to_type, attributes]
  
    GraphStatisticsRequest:
      type: object
      properties:
        function:
          type: string
          enum: [stat_vertex_number, stat_edge_number]
        type:
          type: string
          example: "*"
      required: [function, type]
      
    KStepExpansionRequest:
      type: object
      properties:
        source_vertices:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                example: Person
              id:
                type: string
                example: 1
            required: [type, id]
        expansion_steps:
          type: array
          items:
            type: object
            properties:
              vTypes:
                type: array
                items:
                  type: string
                  example: Person
              eTypes:
                type: array
                items:
                  type: string
                  example: connection
              local_limit:
                type: number
                example: 20
            required: [vTypes, eTypes, local_limit]
      required: [source_vertices, expansion_steps]
                
    PathQueryRequest:
      type: object
      properties:
        sources:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                example: Person
              id:
                type: string
                example: 1
            required: [type, id]
        targets:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                example: Company
              id:
                type: string
                example: 1
            required: [type, id]
        vertexFilters:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
              condition:
                type: string
            required: [type, condition]
          example: [
            { "type": "Person", "condition": "" },
            { "type": "Company", "condition": "" }
          ]
        edgeFilters:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
              condition:
                type: string
            required: [type, condition]
          example: [
            { "type": "connection", "condition": "" },
            { "type": "works_for", "condition": "" }
          ]
        maxLength:
          type: number
          example: 6
        allShortestPaths:
          type: boolean
          example: false
      required: [sources, targets, vertexFilters, edgeFilters, maxLength, allShortestPaths]

    VertexAttributeFilterRequest:
      type: object
      properties:
        vertex_filters:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                example: Person
              filter:
                type: string
                example: v_id = 1
              is_deny_filter:
                type: boolean
                example: false
      
    DataSourceConfig:
      type: object
      properties:
        type:
          type: string
          example: file
        uri:
          type: string
          example: person.csv
        options:
          type: object
          properties:
            format:
              type: string
              example: csv
            separator:
              type: string
              example: ","
            eol:
              type: string
              example: "\n"
            header:
              type: boolean
              example: true
            quote:
              type: string
              example: ""
          required: [format, separator, eol, header, quote]
      required: [type, uri, options]
      
    OnlineParserRequest:
      type: object
      properties:
        source:
          type: object
          allOf:
            - $ref: "#/components/schemas/DataSourceConfig" 
        size:
          type: number
          example: 10
        column_number_limit:
          type: number
          example: 256
        token_length_limit:
          type: number
          example: 2048
      required: [source, size, column_number_limit, token_length_limit]
      
    GetVertexResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/RESTPPVertex"
                
    GetEdgeResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/RESTPPEdge"
    
    DeleteVertexResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              properties:
                v_type:
                  type: string
                  example: Person
                deleted_vertices:
                  type: number
                  example: 1
              required: [v_type, deleted_vertices]
    
    DeleteEdgeResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              properties:
                e_type:
                  type: string
                  example: connection
                deleted_edges:
                  type: number
                  example: 1
              required: [e_type, deletedeleted_edgesd_vertices]
    
    UpsertVertexEdgeResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                properties:
                  accepted_vertices:
                    type: number
                    example: 1
                  accepted_edges:
                    type: number
                    example: 0
                required: [accepted_vertices, accepted_edges]
    
    GraphStatisticsResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                properties:
                  v_type:
                    type: string
                    example: Person
                  count:
                    type: number
                    example: 1
                required: [v_type, count]
                
    KStepExpansionResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/RESTPPVertex"
                  - properties:
                      attributes:
                        type: object
                        properties:
                          "@link":
                            type: array
                            items:
                              type: object
                              allOf:
                                - $ref: "#/components/schemas/RESTPPEdge"
                        required: ["@link"]
                
    PathQueryResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                properties:
                  vertices:
                    type: array
                    items:
                      type: object
                      allOf:
                        - $ref: "#/components/schemas/RESTPPVertex"
                  edges:
                    type: array
                    items:
                      type: object
                      allOf:
                        - $ref: "#/components/schemas/RESTPPEdge"
                required: [vertices, edges]
    
    OnlineParserResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              properties:
                header:
                  type: array
                  items:
                    type: string
                  example: ["id", "name", "age"]
                data:
                  type: array
                  items:
                    type: array
                    items:
                      type: string
                  example: [
                    ["0", "John", "5"],
                    ["1", "Jack", "7"]
                  ]
              required: [data]
    ##### [End] RESTPP Proxy #####
    
    ##### [Begin] GSQL Server Proxy #####
    SchemaAttribute:
      type: object
      properties:
        AttributeType:
          type: object
          properties:
            Name:
              type: string
              example: STRING COMPRESS
          required: [Name]
        IsPartOfCompositeKey:
          type: boolean
          example: false
        PrimaryIdAsAttribute:
          type: boolean
          example: false
        AttributeName:
          type: string
          example: id
        HasIndex:
          type: boolean
          example: false
        IsPrimaryKey:
          type: boolean
          example: false
      required: [AttributeType, IsPartOfCompositeKey, PrimaryIdAsAttribute, AttributeName, HasIndex, IsPrimaryKey]
    
    Schema:
      type: object
      properties:
        GraphName:
          type: string
          example: MyGraph
        VertexTypes:
          type: array
          items:
            type: object
            properties:
              Attributes:
                type: array
                items:
                  type: object
                  allOf:
                    - $ref: "#/components/schemas/SchemaAttribute"
              PrimaryId:
                type: object
                allOf:
                  - $ref: "#/components/schemas/SchemaAttribute"
                  - properties:
                      AttributeType:
                        type: object
                        properties:
                          Name:
                            type: string
                            example: STRING
                      AttributeName:
                        type: string
                        example: companyId
              Name:
                type: string
                example: company
            required: [Attributes, PrimaryId, Name]
        EdgeTypes:
          type: array
          items:
            type: object
            properties:
              IsDirected:
                type: boolean
                example: false
              ToVertexTypeName:
                type: string
                example: company
              Config:
                type: object
              Attributes:
                type: array
                items:
                  type: object
                  allOf:
                    - $ref: "#/components/schemas/SchemaAttribute"
                example: []
              FromVertexTypeName:
                type: string
                example: company
              Name:
                type: string
                example: member_company
            required: [IsDirected, ToVertexTypeName, Config, Attributes, FromVertexTypeName, Name]
      required: [GraphName, VertexTypes, EdgeTypes]
    
    GetSchemaResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              allOf:
                - $ref: "#/components/schemas/Schema"
                
    S3SampleDataRequest:
      type: object
      properties:
        dataSource:
          type: string
          example: "my_s3_connection"
        type:
          type: string
          example: "s3"
        path:
          type: string
          example: "data.csv"
        format:
          type: string
          example: "none"
        filling:
          type: string
          example: "N/A"
        parsing:
          properties:
            separator:
              type: string
              example: ","
            eol: 
              type: string
              example: "\\n"
            header: 
              type: boolean
              example: false
            quote: 
              type: string
              example: ""
          required: [separator, eol, header, quote]
        size:
          type: integer
          example: 10
      required: [dataSource, type, path, format, filling, parsing, size]
    GCSSampleDataRequest:
      type: object
      properties:
        config:
          properties:
            "file.reader.settings.fs.gs.auth.service.account.email":
              type: object
              example: "<EMAIL>"
            "file.reader.settings.fs.gs.auth.service.account.private.key.id":
              type: object
              example: "55c1d79a46c1f3f59ef72e0df53285a3eef8ec38"
            "file.reader.settings.fs.gs.auth.service.account.private.key":
              type: object
              example: "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
            "file.reader.settings.client_email":
              type: object
              example: "<EMAIL>"
            "file.reader.settings.fs.gs.project.id": 
              type: object
              example: "tigergraph-dev"
          required: ["file.reader.settings.fs.gs.auth.service.account.email", "file.reader.settings.fs.gs.auth.service.account.private.key.id", "file.reader.settings.fs.gs.auth.service.account.private.key", "file.reader.settings.client_email", "file.reader.settings.fs.gs.project.id"]
        dataSource:
          type: string
          example: "my_gcs_connection"
        type:
          type: string
          example: "gcs"
        path:
          type: string
          example: "gs://tg_csv/p.csv"
        format:
          type: string
          example: "none"
        filling:
          type: string
          example: "N/A"
        parsing:
          properties:
            separator:
              type: string
              example: ","
            eol: 
              type: string
              example: "\\n"
            header: 
              type: boolean
              example: false
            quote: 
              type: string
              example: ""
          required: [separator, eol, header, quote]
        size:
          type: integer
          example: 10
      required: [config, dataSource, type, path, format, filling, parsing, size, ]
    ##### [End] GSQL Server Proxy #####
    
    ##### [Begin] Configuration #####
    Certificate:
      type: object
      properties:
        country:
          type: string
          example: USA
        province:
          type: string
          example: California
        locality:
          type: string
          example: Redwood City
        organization:
          type: string
          example: TigerGraph
        organizationUnit:
          type: string
          example: Engineer
        commonName:
          type: string
          example: tigergraph-machine-hostname
      required: [country, province, locality, organization, organizationUnit, commonName]
    ##### [End] Configuration #####
    
    ##### [Begin] Data #####
    FileInfo:
      properties:
        name:
          type: string
          example: person.csv
        path:
          type: string
          example: /home/<USER>/tigergraph/data/gui/loading_data/dataset/person.csv
        size:
          type: number
          example: 50
        isDir:
          type: boolean
          example: false
        modTime:
          type: number
          example: 1586807479659
        sourcePath:
          type: string
          example: ""
      required: [name, path, size, isDir, modTime, sourcePath]
    
    DataListingResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/FileInfo"
    
    FileUploadInfo:
      type: object
      properties:
        file:
          type: string
          format: binary
      required: [file]

    FileChunkInfo:
      type: object
      properties:
        resumableChunkNumber:
          type: number
          example: 1
        resumableChunkSize:
          type: number
          example: 1048576
        resumableCurrentChunkSize:
          type: number
          example: 1048576
        resumableTotalSize: 
          type: number
          example: 5242880
        resumableType:
          type: string
          example: text/csv
        resumableIdentifier:
          type: string
          example: 50-personcsv
        resumableFilename:
          type: string
          example: person.csv
        resumableRelativePath:
          type: string
          example: person.csv
        resumableTotalChunks:
          type: number
          example: 5
        file:
          type: string
          format: binary
      required: [resumableChunkNumber, resumableChunkSize, resumableCurrentChunkSize, resumableTotalSize, resumableType, resumableIdentifier, resumableFilename, resumableRelativePath, resumableTotalChunks, file]
    ##### [End] Data #####
    
    ##### [Begin] Log #####
    LogInfo:
      properties:
        name:
          type: string
          example: GUI#1.out
        path:
          type: string
          example: /home/<USER>/tigergraph/log/gui/GUI#1.out
        size:
          type: number
          example: 50
        isDir:
          type: boolean
          example: false
        modTime:
          type: number
          example: 1586807479659
        sourcePath:
          type: string
          example: ""
      required: [name, path, size, isDir, modTime, sourcePath]
    
    LogListingResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LogInfo"
                  - properties:
                      name:
                        example: "GUI#1.out"
    
    LogSearchResult:
      type: object
      additionalProperties:
        type: array
        items:
          type: object
          properties:
            line:
              type: string
              example: "123 abc 321"
            path:
              type: string
              example: "/home/<USER>/tigergraph/log/gui/GUI#1.out"
            offset:
              type: number
              example: 123
            lineNumber:
              type: number
              example: 1
      example:
        m1: [
          {
            line: "123 abc 321",
            path: "/home/<USER>/tigergraph/log/gui/GUI#1.out",
            offset: 123,
            lineNumber: 1
          },
          {
            line: "456 def 456",
            path: "/home/<USER>/tigergraph/log/gsql/GSQL#1.out",
            offset: 456,
            lineNumber: 2
          }
        ]
    
    LogSearchResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LogSearchResult"
    ##### [End] Log #####

    ##### [Begin] GBAR #####
    Backup:
      type: object
      properties:
        name:
          type: string
          example: "tigergraph_backup-20210215162727"
        createdAt:
          type: string
          example: "2021-02-15T16:27:30.921547229-08:00"
        sizeBytes:
          type: number
          example: 35100
        automatic:
          type: boolean
          example: true
        isThisInstance:
          type: boolean
          example: false
      required: [name, createdAt, sizeBytes, automatic, isThisInstance]

    BackupListResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/Backup"

    BackupStatus:
      type: object
      properties:
        timestamp:
          type: string
          example: "2021-03-11 16:15:00.020271133 -0800 PST m=+872.449359944"
        output:
          type: string
          example: "[16:15:56] Retrieve TigerGraph system configuration..."
        backupName:
          type: string
          example: "automatic_backup-20210311161602"
        error:
          type: string
          example: null
      required: [timestamp, output, backupName, error]
    
    BackupStatusListResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/BackupStatus"
                  
    BackupSchedule:
      type: object
      properties:
        minutes:
          type: string
          example: "22"
        hours:
          type: string
          example: "6"
        dayOfMonth:
          type: string
          example: "12"
        month:
          type: string
          example: "10"
        dayOfWeek:
          type: string
          example: "0"
      required: [minutes, hours, dayOfMonth, month, dayOfWeek]

    BackupScheduleResponse:
      type: object
      allOf:
       - $ref: "#/components/schemas/SuccessResponse"
       - properties:
          results:
            type: object
            allOf:
              - $ref: "#/components/schemas/BackupSchedule"
    ##### [End] GBAR #####
    
    ##### [Begin] Loading Job #####
    LoadingJob:
      type: object
      properties:
        FileNames:
          type: object
          example: { "MyDataSource": "" }
        Type:
          type: string
          example: Offline
        JobName:
          type: string
          example: load_job_person_csv_1579902877625
        GraphName:
          type: string
          example: MyGraph
        Headers:
          type: object
        Filters:
          type: array
          items:
            type: object
          example: []
        LoadingStatements:
          type: array
          items:
            type: object
            properties:
              Type:
                type: string
                example: Vertex
              TargetName:
                type: string
                example: Person
              DataSource:
                type: object
                properties:
                  Type:
                    type: string
                    example: FileVar
                  Value:
                    type: string
                    example: MyDataSource
                required: [Type, Value]
              Mappings:
                type: array
                items:
                  type: object
                  properties:
                    Type:
                      type: string
                      example: SrcColIndex
                    Value:
                      type: number
                      example: 0
                  required: [Type, Value]
              UsingClauses:
                type: object
                properties:
                  QUOTE:
                    type: string
                    example: "double"
                  EOL:
                    type: string
                    example: "\\n"
                  SEPARATOR:
                    type: string
                    example: ","
                  HEADER:
                    type: string
                    example: "true"
                required: [QUOTE, EOL, SEPARATOR, HEADER]
            required: [Type, TargetName, DataSource, Mappings, UsingClauses]
      required: [FileNames, Type, JobName, GraphName, Headers, Filters, LoadingStatements]  
    
    LoadingJobProgress:
      type: object
      properties:
        uri:
          type: string
          example: /home/<USER>/tigergraph/data/gui/loading_data/person.csv
        source:
          type: string
          example: person.csv
        status:
          type: string
          enum: [INITIAL, PAUSED, STOPPED, FINISHED, FAILED, STARTING, RUNNING, PAUSING, RESUMING, STOPPING]
        startTime:
          type: number
          description: in ms
          example: 1580150695972
        endTime:
          type: number
          description: in ms
          example: 1580150700972 
        duration:
          type: number
          description: in ms
          example: 5000
        percentage:
          type: number
          example: 50
        currentSpeed:
          type: number
          description: lines/second
          example: 1234
        averageSpeed:
          type: number
          description: lines/second
          example: 1111
        loadedLines:
          type: number
          example: 1000
        notEnoughTokenLines:
          type: number
          example: 0
        oversizeTokenLines:
          type: number
          example: 0
        rejectedLines:
          type: number
          example: 0
      required: [uri, source, status, startTime, endTime, duration, percentage, currentSpeed, averageSpeed, ]
      
    LoadingJobLog:
      type: object
      properties:
        id:
          type: string
          example: MyGraph.load_job_person_csv_1579905143306.file.m1.1580150256294
        error:
          type: boolean
          example: false
        message:
          type: string
          example: ""
        progress:
          type: object
          allOf:
            - $ref: "#/components/schemas/LoadingJobProgress"
      required: [id, error, message, progress]
    
    LoadingJobResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              allOf:
                - $ref: "#/components/schemas/LoadingJob"
    
    AllLoadingJobsResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJob"
    
    StartLoadingJobRequest:
      type: array
      items:
        type: object
        properties:
          name:
            type: string
            example: load_job_person_csv_1579902877625
          streaming:
            type: boolean
            example: false
          dataSources:
            type: array
            items:
              type: object
              properties:
                filename:
                  type: string
                  example: MyDataSource
                name:
                  type: string
                  example: file
                path:
                  type: string
                  example: person.csv
              required: [filename, name, path]
        required: [name, streaming, dataSources]
                  
    LoadingJobProgressResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJobLog"
                  - properties:
                      progress:
                        type: object
                        properties:
                          status:
                            example: RUNNING
    
    StartLoadingJobResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJobLog"
                  - properties:
                      progress:
                        type: object
                        properties:
                          status:
                            example: STARTING
                  
    PauseLoadingJobResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJobLog"
                  - properties:
                      progress:
                        type: object
                        properties:
                          status:
                            example: PAUSING
                            
    ResumeLoadingJobResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJobLog"
                  - properties:
                      progress:
                        type: object
                        properties:
                          status:
                            example: RESUMING
                            
    StopLoadingJobResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJobLog"
                  - properties:
                      progress:
                        type: object
                        properties:
                          status:
                            example: STOPPING
    ##### [End] Loading Job #####
    
    ##### [Begin] Query #####
    QueryDraft:
      type: object
      properties:
        name:
          type: string
          example: q1
        code:
          type: string
          example: "CREATE QUERY q1(uint k = 5, datetime t) FOR GRAPH MyGraph {  /* Write query logic here */  PRINT \"q1 draft works!\"; }"
      required: [name, code]
    
    QueryInfo:
      type: object
      properties:
        name:
          type: string
          example: q1
        draft:
          type: string
          example: "CREATE QUERY q1(uint k = 5, datetime t) FOR GRAPH MyGraph {  /* Write query logic here */  PRINT \"q1 draft works!\"; }"
        code:
          type: string
          example: "CREATE QUERY q1(uint k = 5, datetime t) FOR GRAPH MyGraph {  /* Write query logic here */  PRINT \"q1 works!\"; }"
        installed:
          type: boolean
          example: true
        installing:
          type: boolean
          example: false
        optimizedLevel:
          type: number
          example: 0
        callerQueries:
          type: array
          items:
            type: string
          example: ["q2"]
        endpoint:
          type: object
          example: {
            "query": {
              "tpc_graph": {
                "test": {
                  "GET/POST": {
                    "summary": "This is query entrance",
                    "alternative_endpoint": "/query/test",
                    "libudf": "libudf_tpc_graph",
                    "graph_name": "tpc_graph",
                    "payload": [
                      {
                        "rule": "AS_QUERY_STRING"
                      }
                    ],
                    "function": "queryDispatcher",
                    "action": "query",
                    "parameters": {
                      "t": {
                        "index": 1,
                        "type": "DATETIME",
                        "min_count": 0
                      },
                      "query": {
                        "default": "test",
                        "type": "STRING"
                      },
                      "k": {
                        "defaultValue": "5l",
                        "index": 0,
                        "type": "UINT64",
                        "min_count": 0
                      }
                    },
                    "target": "GPE"
                  }
                }
              }
            }
          }
      required: [name, draft, code, installed, installing, optimizedLevel, callerQueries, endpoint]
    
    QueryInfoResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              allOf:
                - $ref: "#/components/schemas/QueryInfo"
                
    AllQueriesInfoResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/QueryInfo"
    ##### [End] Query #####

    ##### [Begin] GSQL Algorithm #####
    Category:
      type: object
      description: Only needs high-level details since the content is determined by the client.
      properties:
        name:
          type: string
        description:
          type: string 
        subs:
          type: array
          items:
            $ref: "#/components/schemas/Category"
        algorithms:
          type: array
          items: 
            type: object
            properties:
              name:
                type: string
              description:
                type: string 
              schemaConstraints:
                type: string
      example:
        name: Similarity
        description: calculate similarity between two or more vertices
        subs: 
          - name: cosine
            description: description for cosine
            subs: null
            algorithms: 
              - name: FastRP
                description: FastRP is a very cool embedding algorithm.
                schemaConstraints: This algorithm requires that you have a LIST attribute of type DOUBLE/FLOAT on the target vertices!
        algorithms: null
          
    AllGraphAlgorithmsResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/Category"

    ##### [End] GSQL Algorithm #####
    
    ##### [Begin] Visual Pattern #####
    VisualPattern:
      type: object
      description: Only needs high-level details since the content is determined by the client.
      properties:
        name:
          type: string
          example: p1
        patternVertexSets:
          type: array
          items:
            type: object
          example: []
        patternEdgeSets:
          type: array
          items:
            type: object
          example: []
        patternWidgets:
          type: array
          items:
            type: object
          example: []
        filters:
          type: array
          items:
            type: object
          example: []
        aggregations:
          type: array
          items:
            type: object
          example: []
        postAggregationFilters:
          type: array
          items:
            type: object
          example: []
        skip:
          type: number
          example: 0
        limit:
          type: number
          example: -1
        orderBy:
          type: array
          items:
            type: object
          example: []
        description:
          type: string
          example: ""
      required: [name, patternVertexSets, patternEdgeSets, patternWidgets, filters, aggregations, postAggregationFilters, skip, limit, orderBy, description]
    
    VisualPatternResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              allOf:
                - $ref: "#/components/schemas/VisualPattern"
    
    AllVisualPatternsResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/VisualPattern"
    ##### [End] Visual Pattern #####
    
    ##### [Begin] Graph Style #####
    GraphStyle:
      type: object
      description: Only needs high-level details since the content is determined by the client.
      properties:
        vertexStyles:
          type: object
        edgeStyles:
          type: object
      required: [vertexStyles, edgeStyles]
      
    GraphStyleResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              allOf:
                - $ref: "#/components/schemas/GraphStyle"
    ##### [End] Graph Style #####
    
    ##### [Begin] Loading Job Info #####
    LoadingJobInfo:
      type: object
      properties:
        loadingJobName:
          type: string
          example: load_job_person_csv_1579902877625
        loadingStatementsStyle:
          type: array
          items:
            type: object
          example: []
        dataSourceJson:
          type: object
          allOf:
            - $ref: "#/components/schemas/DataSourceConfig"
        header:
          type: array
          items:
            type: string
          example: ["id", "name", "age"]
        sampleData:
          type: array
          items:
            type: array
            items:
              type: string
          example: [
            ["0", "John", "5"],
            ["1", "Jack", "7"]
          ]
      required: [loadingJobName, loadingStatementsStyle, dataSourceJson, header, sampleData]
    
    LoadingJobInfoResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/LoadingJobInfo"
    ##### [End] Loading Job Info #####
    
    ##### [Begin] Exploration Result #####
    ExplorationResultPreview:
      type: object
      properties:
        name:
          type: string
          example: f1
        username:
          type: string
          example: tigergraph
        timestamp:
          type: number
          description: in ms
          example: 1580345569512
        previewImage:
          type: string
          example: data:image/png;base64,iVBOR...
        description:
          type: string
          example: Result
      required: [name, username, timestamp, previewImage, description]
      
    ExplorationResultData:
      type: object
      properties:
        schema:
          type: object
          allOf:
            - $ref: "#/components/schemas/Schema"
        data:
          type: object
          description: Only needs high-level details since the content is determined by the client.
        config:
          type: object
          description: Only needs high-level details since the content is determined by the client.
      required: [schema, data]
          
    ExplorationResult:
      type: object
      allOf:
        - $ref: "#/components/schemas/ExplorationResultPreview"
        - $ref: "#/components/schemas/ExplorationResultData"
        
    ExplorationResultPreviewResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: object
                allOf:
                  - $ref: "#/components/schemas/ExplorationResultPreview"
                  
    ExplorationResultDataResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: object
              allOf:
                - $ref: "#/components/schemas/ExplorationResultData"
    ##### [End] Exploration Result #####
    
    ##### [Begin] Data Source #####
    DataSourceRequest:
      type: object
      properties:
        type:
          type: string
          example: service_account
        project_id:
          type: string
          example: tigergraph-dev
        private_key_id:
          type: string
          example: 3edb08b4fa9e19b339fbbf0727ad3a510e870492
        private_key:
          type: string
          example: -----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n
        client_email:
          type: string
          example: <EMAIL>
        client_id:
          type: string
          example: 106711921163892985421
        auth_uri: 
          type: string
          example: https://accounts.google.com/o/oauth2/auth
        token_uri: 
          type: string
          example: https://oauth2.googleapis.com/token
        auth_provider_x509_cert_url:
          type: string
          example: https://www.googleapis.com/oauth2/v1/certs
        client_x509_cert_url:
          type: string
          example: https://www.googleapis.com/robot/v1/metadata/x509/gcsconnect%40tigergraph-dev.iam.gserviceaccount.com
      required: [type, project_id, private_key_id, private_key, client_email, client_id, auth_uri, token_uri, auth_provider_x509_cert_url, client_x509_cert_url]
    AllDataSourceNamesResponse:
      type: object
      allOf:
        - $ref: "#/components/schemas/SuccessResponse"
        - properties:
            results:
              type: array
              items:
                type: string
                example: "my_connection"
    ##### [End] Data Source #####
