package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/handler/gbar"
	"github.com/tigergraph/gus/lib/config"
)

func SetGBARRoutes(g *gin.RouterGroup, t2pCLient pb.ControllerClient, cfg *config.Config, daoManager interfaces.DaoManager) {
	gbarAPI := g.Group("/gbar")
	{
		gbarAPI.GET("", func(c *gin.Context) {
			gbar.ListBackup(c, t2pCLient, cfg, daoManager)
		})
		gbarAPI.POST("/backup", func(c *gin.Context) {
			gbar.BackUp(c, t2pCLient, cfg, daoManager)
		})
		gbarAPI.POST("/restore", func(c *gin.Context) {
			gbar.Restore(c, t2pCLient, cfg, daoManager)
		})
		gbarAPI.DELETE("/:backupTag", func(c *gin.Context) {
			gbar.RemoveBackup(c, t2pCLient, cfg, daoManager)
		})

		scheduleAPI := gbarAPI.Group("/schedule")
		{
			scheduleAPI.GET("", gbar.GetSchedule)
			scheduleAPI.POST("", func(c *gin.Context) {
				gbar.UpsertSchedule(c, t2pCLient, cfg)
			})
			scheduleAPI.POST("/cron", func(c *gin.Context) {
				gbar.UpdateCron(c, t2pCLient, cfg)
			})
			scheduleAPI.GET("/status", gbar.GetAllScheduledBackupStatus)
		}
	}
}
