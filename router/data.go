package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/handler/data"
)

func SetDataRoutes(
	g *gin.RouterGroup,
	checkAndCreateCategoryDirFunc interfaces.CheckAndCreateCategoryDirFunc,
) {
	dataAPI := g.Group("/data")
	{
		dataAPI.GET("/:category", data.List(checkAndCreateCategoryDirFunc))
		dataAPI.DELETE("/:category", data.Remove(checkAndCreateCategoryDirFunc))

		uploadFileAPI := dataAPI.Group("/:category/upload")
		{
			uploadFileAPI.POST("", data.Upload(checkAndCreateCategoryDirFunc))
			uploadFileAPI.PUT("", data.Upload(checkAndCreateCategoryDirFunc))
		}

		uploadChunkAPI := dataAPI.Group("/:category/upload/chunk")
		{
			uploadChunkAPI.GET("", data.GetChunk(checkAndCreateCategoryDirFunc))
			uploadChunkAPI.POST("", data.UploadChunk(checkAndCreateCategoryDirFunc))
			uploadChunkAPI.POST("/assemble", data.AssembleChunks(checkAndCreateCategoryDirFunc))
			uploadChunkAPI.PUT("/assemble", data.AssembleChunks(checkAndCreateCategoryDirFunc))
		}
	}
}
