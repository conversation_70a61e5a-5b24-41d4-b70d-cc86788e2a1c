package router

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/graph-gophers/graphql-go"

	"github.com/tigergraph/gus/controller/interfaces"
	graphql2 "github.com/tigergraph/gus/graphql"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/tg"
	mw "github.com/tigergraph/gus/middleware"
)

type Params struct {
	Query         string                 `json:"query"`
	OperationName string                 `json:"operationName"`
	Variables     map[string]interface{} `json:"variables"`
}

func SetGraphQLRoutes(
	g *gin.RouterGroup,
	authService interfaces.AuthenticationService,
	daoManager interfaces.DaoManager,
) {

	g.GET("/v2", func(c *gin.Context) {

		c.Header("Content-Type", "text/html")
		c.String(http.StatusOK, graphiql("/api/v2"))

	})

	g.GET("/v2/download_gsql_output", graphql2.DownloadGSQLOutput)

	g.POST("/v2", func(c *gin.Context) {
		// todo: move schema building logic to server start up
		cfg := mw.GetConfig(c)
		cntlrClient, err := tg.ControllerClient(cfg)
		if err != nil {
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}
		userInfo := mw.GetUserInfo(c)
		queryRoot, err := graphql2.NewQuery(graphql2.Deps{
			Config:     cfg,
			T2pClient:  cntlrClient,
			DaoManager: daoManager,
		}, userInfo, mw.GetUsername(c), mw.GetPassword(c), mw.GetAuthType(c), mw.GetReqID(c).String())
		if err != nil {
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		schema := graphql.MustParseSchema(
			graphql2.Schema(),
			queryRoot,
			graphql.UseFieldResolvers(), graphql.UseStringDescriptions())

		c.Header("Content-Type", "application/json")
		params := Params{}
		b, err := io.ReadAll(c.Request.Body)
		if err != nil {
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		err = json.Unmarshal(b, &params)
		if err != nil {
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}

		/*
			Start GraphQL Execution
		*/
		begin := time.Now()
		res := schema.Exec(c, params.Query, params.OperationName, params.Variables)
		if isDev(cfg) {
			end := time.Now()
			if res.Extensions == nil {
				res.Extensions = make(map[string]interface{})
			}
			res.Extensions["duration"] = end.Sub(begin).String()
		}
		withCode(res)
		b, err = json.Marshal(res)
		if err != nil {
			mw.Abort(c, http.StatusInternalServerError, err.Error())
			return
		}
		c.String(http.StatusOK, "%v", string(b))
	})
}

func withCode(resp *graphql.Response) {
	const (
		CODE = "code"

		UNAUTHENTICATED = "UNAUTHENTICATED"
		UNAUTHORIZED    = "UNAUTHORIZED"

		// uncategorized errors
		GENERIC_ERROR = "GENERIC_ERROR"
	)

	if len(resp.Errors) > 0 {
		for _, err := range resp.Errors {
			if err.Extensions == nil {
				err.Extensions = make(map[string]interface{})
			}
			if strings.Contains(err.Message, graphql2.ERROR_UNAUTHENTICATED) {
				err.Extensions[CODE] = UNAUTHENTICATED
			} else if strings.Contains(err.Message, graphql2.ERROR_UNAUTHORIZED) {
				err.Extensions[CODE] = UNAUTHORIZED
			} else {
				err.Extensions[CODE] = GENERIC_ERROR
			}
		}
	}
}

func isDev(cfg *config.Config) bool {
	return strings.ToLower(cfg.GetConfig().ProtoConf.GUI.BasicConfig.Env) == "dev=true"
}

func graphiql(url string) string {
	return fmt.Sprintf(`
<html>
  <head>
    <title>TigerGraph GraphQL API</title>
    <link href="https://unpkg.com/graphiql/graphiql.min.css" rel="stylesheet" />
  </head>
  <body style="margin: 0;">
    <div id="graphiql" style="height: 100vh;"></div>
    <script
      crossorigin
      src="https://unpkg.com/react/umd/react.production.min.js"
    ></script>
    <script
      crossorigin
      src="https://unpkg.com/react-dom/umd/react-dom.production.min.js"
    ></script>
    <script
      crossorigin
      src="https://unpkg.com/graphiql/graphiql.min.js"
    ></script>
    <script>
      const fetcher = GraphiQL.createFetcher({ url: '%s' });
      ReactDOM.render(
        React.createElement(GraphiQL, { fetcher: fetcher }),
        document.getElementById('graphiql'),
      );
    </script>
  </body>
</html>
`, url)
}
