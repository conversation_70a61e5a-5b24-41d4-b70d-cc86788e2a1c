package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/dao/model"
	gs "github.com/tigergraph/gus/handler/graphstyle"
	"github.com/tigergraph/gus/middleware"
)

func SetGraphStyleRoutes(g *gin.RouterGroup) {
	gsAPI := g.Group("/graph-styles")
	{
		globalStyleAPI := gsAPI.Group("/global")
		{
			globalStyleAPI.GET("", middleware.RequiredPrivileges(model.READ_SCHEMA), gs.GetGlobal)
			globalStyleAPI.PUT("", middleware.RequiredPrivileges(model.WRITE_SCHEMA), gs.UpserGlobal)
			globalStyleAPI.DELETE("", middleware.RequiredPrivileges(model.WRITE_SCHEMA), gs.DeleteGlobal)
		}

		localStyleAPI := gsAPI.Group("/local/:graphName")
		{
			localStyleAPI.GET("", middleware.RequiredPrivileges(model.READ_SCHEMA), gs.Get)
			localStyleAPI.POST("", middleware.RequiredPrivileges(model.WRITE_SCHEMA), gs.Create)
			localStyleAPI.PUT("", middleware.RequiredPrivileges(model.WRITE_SCHEMA), gs.Upsert)
			localStyleAPI.DELETE("", middleware.RequiredPrivileges(model.WRITE_SCHEMA), gs.Delete)
		}
	}
}
