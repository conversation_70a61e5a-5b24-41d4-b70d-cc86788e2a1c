package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/dao/model"
	ds "github.com/tigergraph/gus/handler/datasource"
	"github.com/tigergraph/gus/middleware"
)

func SetDataSourceRoutes(g *gin.RouterGroup) {
	dsAPI := g.Group("/data-sources")
	{
		dsAPI.POST("csv-format-check", ds.CSVFormatCheck)
		dsAPI.POST(":dataSourceType/format-check", ds.FormatCheck)

		nameAPI := dsAPI.Group(":graphName/:dataSourceType")
		nameAPI.Use(middleware.RequiredPrivileges(model.WRITE_DATASOURCE))
		{
			nameAPI.GET("/names", ds.GetAllNames)
			nameAPI.GET("/uris", ds.GetDataSourceTypeUris)

			nameAPI.PUT("/:dataSourceName", ds.Create)
			nameAPI.DELETE("/:dataSourceName", ds.Delete)

			nameAPI.GET("/:dataSourceName/uris", ds.GetDataSourceUris)
			nameAPI.GET("/:dataSourceName/uri", ds.GetDataset)
			nameAPI.PUT("/:dataSourceName/uri", ds.UpsertDataset)
			nameAPI.DELETE("/:dataSourceName/uri", ds.DeleteDataset)
		}
	}

}
