package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/dao/model"
	ds "github.com/tigergraph/gus/handler/datasource"
	"github.com/tigergraph/gus/middleware"
)

func SetDataSourceRoutes(g *gin.RouterGroup) {
	dsAPI := g.Group("/data-sources")
	{
		dsAPI.POST(":dataSourceType/format-check", ds.FormatCheck)

		nameAPI := dsAPI.Group(":graphName/:dataSourceType")
		nameAPI.Use(middleware.RequiredPrivileges(model.WRITE_DATASOURCE))
		{
			nameAPI.GET("/names", ds.GetAllNames)

			nameAPI.PUT("/:dataSourceName", ds.Create)
			nameAPI.DELETE("/:dataSourceName", ds.Delete)
		}
	}

}
