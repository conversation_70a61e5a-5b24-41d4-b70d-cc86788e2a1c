package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gus/handler/config"
	mw "github.com/tigergraph/gus/middleware"
)

func SetConfigRoutes(g *gin.RouterGroup, cntlrClient pb.ControllerClient) {
	configAPI := g.Group("/config") // todo: deprecate it
	{
		configAPI.GET("", func(ctx *gin.Context) {
			config.Get(ctx, cntlrClient)
		})

		protectedAPI := configAPI.Group("", mw.RequiredSuperUser())
		{
			protectedAPI.POST("", func(ctx *gin.Context) {
				config.Set(ctx, cntlrClient)
			})
			protectedAPI.POST("/generate-cert", config.GenerateCert)
		}
	}
}
