package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	ga "github.com/tigergraph/gus/handler/graphalgorithm"
)

func SetGraphAlgorithmRoutes(g *gin.RouterGroup, gsqlClient interfaces.RequestGSQLClient) {
	gaAPI := g.Group("/graph-algorithm")
	{
		gaAPI.GET("", ga.GetAll)
		gaAPI.POST("/:graphName/install", func(c *gin.Context) {
			ga.Install(c, gsqlClient)
		})
	}
}
