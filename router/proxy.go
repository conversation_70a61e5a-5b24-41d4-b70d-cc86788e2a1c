package router

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/handler/proxy"
	mw "github.com/tigergraph/gus/middleware"
)

func SetGSQLServerProxyRoutes(g *gin.RouterGroup) {
	g.<PERSON>("/gsql-server/*proxyPath", func(c *gin.Context) {
		proxy.ServeGSQLServer(c)
	})
	g.POST("/gsql-command", func(c *gin.Context) {
		proxy.ServerGSQLCommand(c)
	})
}

func SetRESTPPProxyRoutes(g *gin.RouterGroup) {

	g.Any("/restpp/*proxypath", func(ctx *gin.Context) {

		if strings.HasPrefix(ctx.Param("proxypath"), "/rebuildnow") ||
			strings.HasPrefix(ctx.Param("proxypath"), "/showprocesslistall") {
			if !mw.IsSuperUser(ctx) {
				mw.ReplyWithResult(ctx, http.StatusUnauthorized, "You are not authorized to use this API.", nil)
				return
			}

		}
		graphName := ""

		words := strings.Split(ctx.Param("proxypath"), "/")
		if len(words) > 2 {
			graphName = words[2]
		}
		if ctx.Query("graph") != "" {
			graphName = ctx.Query("graph")
		}
		proxy.ServeRESTPP(ctx, ctx.Param("proxypath"), graphName)
	})
}

func SetTS3ServerProxyRoutes(g *gin.RouterGroup) {
	g.Any("/ts3/*proxyPath", func(ctx *gin.Context) {
		proxy.ServeTS3Server(ctx)
	})
}

func SetInformantServerProxyRoutes(g *gin.RouterGroup) {
	g.Any("/informant/*proxyPath", func(ctx *gin.Context) {
		proxy.ServeInformantServer(ctx)
	})
}
