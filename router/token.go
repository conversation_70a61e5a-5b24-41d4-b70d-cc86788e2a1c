package router

import (
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/graphql"
	"github.com/tigergraph/gus/handler/token"
)

func SetTokenRoutes(g *gin.RouterGroup, insightsService interfaces.InsightsService, deps graphql.Deps) {
	tokenAPI := g.Group("/token")
	{
		insightsTokenApI := tokenAPI.Group("/insights")
		{
			insightsTokenApI.GET("/:appId", func(ctx *gin.Context) {
				token.Get(ctx, insightsService, deps)
			})

			insightsTokenApI.POST("/:appId", func(ctx *gin.Context) {
				token.Create(ctx, insightsService, deps)
			})

			insightsTokenApI.DELETE("/:appId", func(ctx *gin.Context) {
				token.Delete(ctx, insightsService, deps)
			})

		}
	}

}
