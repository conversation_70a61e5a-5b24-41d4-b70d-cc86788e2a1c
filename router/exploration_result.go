package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/dao/model"
	er "github.com/tigergraph/gus/handler/explorationresult"
	"github.com/tigergraph/gus/middleware"
)

func SetExplorationResultRoutes(g *gin.RouterGroup) {
	erAPI := g.Group("/exploration-results")
	{
		erAPI.GET("/:graphName", middleware.RequiredPrivileges(model.APP_ACCESS_DATA, model.READ_DATA, model.READ_SCHEMA), er.GetAll)
		erAPI.DELETE("/:graphName", middleware.RequiredPrivileges(model.APP_ACCESS_DATA), er.Delete<PERSON>ll)

		erAPI.GET("/:graphName/:explorationName", middleware.RequiredPrivileges(model.APP_ACCESS_DATA, model.READ_DATA, model.READ_SCHEMA), er.Get)
		erAPI.POST("/:graphName/:explorationName", middleware.RequiredPrivileges(model.APP_ACCESS_DATA), er.Create)
		erAPI.PUT("/:graphName/:explorationName", middleware.RequiredPrivileges(model.APP_ACCESS_DATA), er.Upsert)
		erAPI.DELETE("/:graphName/:explorationName", middleware.RequiredPrivileges(model.APP_ACCESS_DATA), er.Delete)
	}
}
