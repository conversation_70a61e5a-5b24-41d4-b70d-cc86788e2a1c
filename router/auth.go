package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/handler/auth"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

type AuthDependencies struct {
	GSQLAuthenticator   interfaces.GSQLAuthenticator
	RequestNewGsqlToken interfaces.RequestNewGsqlToken
	UpsertSAMLResponse  interfaces.UpsertSAMLResponse
	Config              *config.Config
}

func SetAuthRoutes(g *gin.RouterGroup, dep AuthDependencies) {
	authAPI := g.Group("/auth", mw.ImportGuard)
	{
		authAPI.POST("/login", auth.LoginHandler(dep.GSQLAuthenticator, dep.RequestNewGsqlToken))
		authAPI.POST("/logout", auth.LogOut)

		authAPI.Any("/saml/acs", auth.HandleSAMLACS(
			dep.UpsertSAMLResponse,
			dep.Config,
			dep.GSQLAuthenticator,
			dep.RequestNewGsqlToken))
		authAPI.Any("/oidc/callback", auth.HandleOIDCCallback(
			dep.Config,
			dep.GSQLAuthenticator,
			dep.RequestNewGsqlToken))
	}
}
