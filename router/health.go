package router

import (
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gus/handler/health"
	mw "github.com/tigergraph/gus/middleware"
)

func SetHealthRoutes(g *gin.RouterGroup, controllerClient pb.ControllerClient) {
	g.GET("/ping", health.Ping)
	g.GET("/version", mw.ImportGuard, func(ctx *gin.Context) {
		health.Version(ctx, controllerClient)
	})
}
