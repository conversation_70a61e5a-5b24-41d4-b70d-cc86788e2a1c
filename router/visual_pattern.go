package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/dao/model"
	vp "github.com/tigergraph/gus/handler/visualpattern"
	"github.com/tigergraph/gus/middleware"
)

func SetVisualPatternRoutes(g *gin.RouterGroup) {
	vpAPI := g.Group("/visual-patterns")
	{
		vpAPI.GET("/:graphName", middleware.RequiredPrivileges(model.READ_QUERY), vp.GetAll)
		vpAPI.DELETE("/:graphName", middleware.RequiredPrivileges(model.WRITE_QUERY), vp.DeleteAll)

		vpAPI.GET("/:graphName/:patternName", middleware.RequiredPrivileges(model.READ_QUERY), vp.Get)
		vpAPI.POST("/:graphName/:patternName", middleware.RequiredPrivileges(model.WRITE_QUERY), vp.Create)
		vpAPI.PUT("/:graphName/:patternName", middleware.RequiredPrivileges(model.WRITE_QUERY), vp.Upsert)
		vpAPI.DELETE("/:graphName/:patternName", middleware.RequiredPrivileges(model.WRITE_QUERY), vp.Delete)
	}
}
