package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/udf"
	"github.com/tigergraph/gus/middleware"
)

func SetUDFRouters(g *gin.RouterGroup, udfService interfaces.UDFService) {
	udfAPI := g.Group("")
	{
		udfAPI.GET("/udf/:filename",
			middleware.RequiredPrivileges(model.READ_FILE),
			func(ctx *gin.Context) {
				udf.GetUDF(ctx, udfService)
			},
		)
		udfAPI.PUT("/udf/:filename",

			middleware.RequiredPrivileges(model.WRITE_FILE),
			func(ctx *gin.Context) {
				udf.SetUDF(ctx, udfService)
			},
		)
	}
}
