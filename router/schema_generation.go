package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/schemaGeneration"
	"github.com/tigergraph/gus/middleware"
)

func SetSchemaGenerationRoutes(g *gin.RouterGroup) {
	sg := g.Group("/schema-generation")
	{
		sg.POST("",
			middleware.RequiredPrivileges(model.WRITE_SCHEMA),
			func(ctx *gin.Context) {
				schemaGeneration.Create(ctx)
			},
		)
	}
}
