package router

import (
	"github.com/gin-gonic/gin"
	queryCache "github.com/tigergraph/gus/handler/query_cache"
)

func SetQueryResultCacheRoutes(g *gin.RouterGroup) {
	g.POST("/cache/run/gsql-server/interpreted_query", func(c *gin.Context) {
		queryCache.ServerInterpretQuery(c)
	})
	g.POST("/cache/run/restpp/query/:graphName/:queryName", func(c *gin.Context) {
		queryCache.ServerInstalledQuery(c)
	})
}
