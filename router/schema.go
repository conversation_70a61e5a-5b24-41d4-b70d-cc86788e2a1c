package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/schema"
	"github.com/tigergraph/gus/middleware"
)

func SetSchemaRoutes(g *gin.RouterGroup, schemaService interfaces.SchemaService) {
	gsAPI := g.Group("/gsql/v2")
	{
		schemaAPI := gsAPI.Group("/schema")
		{
			schemaAPI.GET("",
				middleware.RequiredPrivileges(model.READ_SCHEMA),
				func(ctx *gin.Context) {
					schema.Get(ctx, schemaService)
				},
			)
		}
	}
}
