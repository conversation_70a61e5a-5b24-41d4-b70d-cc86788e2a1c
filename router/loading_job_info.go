package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/dao/model"
	lji "github.com/tigergraph/gus/handler/loadingjobinfo"
	"github.com/tigergraph/gus/middleware"
)

func SetLoadingJobInfoRoutes(g *gin.RouterGroup) {
	ljiAPI := g.Group("/loading-job-info")
	{
		ljiAPI.GET("/:graphName", middleware.RequiredPrivileges(model.READ_LOADINGJOB), lji.Get)
		ljiAPI.POST("/:graphName", middleware.RequiredPrivileges(model.WRITE_LOADINGJOB), lji.Create)
		ljiAPI.PUT("/:graphName", middleware.RequiredPrivileges(model.WRITE_LOADINGJOB), lji.Upsert)
		ljiAPI.DELETE("/:graphName", middleware.RequiredPrivileges(model.WRITE_LOADINGJOB), lji.Delete)
	}
}
