package router

import (
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	upgradetools "github.com/tigergraph/gus/handler/upgrade_tools"
	mw "github.com/tigergraph/gus/middleware"
)

func SetUpgradeToolsRoutes(g *gin.RouterGroup, controllerClient pb.ControllerClient) {
	serviceAPI := g.Group("/tools", mw.RequiredSuperUser())
	{
		serviceAPI.POST("/upgrade", upgradetools.Upgrade)
		serviceAPI.POST("/remote-upgrade-check", func(ctx *gin.Context) {
			upgradetools.RemoteUpgradeCheck(ctx, controllerClient)
		})
		serviceAPI.POST("/local-upgrade-check", func(ctx *gin.Context) {
			upgradetools.LocalUpgradeCheck(ctx, controllerClient)
		})
	}
}
