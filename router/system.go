package router

import (
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cqrs/tutopia/common/pb"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/system"
	"github.com/tigergraph/gus/middleware"
	mw "github.com/tigergraph/gus/middleware"
)

func SetSystemRoutes(g *gin.RouterGroup, gsqlClient interfaces.RequestGSQLClient, controllerClient pb.ControllerClient) {
	authAPI := g.Group("/system")
	{
		authAPI.GET("/export-check", func(ctx *gin.Context) {
			system.ExportCheck(ctx)
		})

		authAPI.GET("/export", middleware.RequiredPrivileges(model.EXPORT_GRAPH), func(ctx *gin.Context) {
			system.Export(ctx, gsqlClient)
		})

		authAPI.POST("/import-check", middleware.RequiredPrivileges(model.WRITE_SCHEMA), func(ctx *gin.Context) {
			system.ImportCheck(ctx, gsqlClient)
		})

		authAPI.POST("/import", middleware.RequiredPrivileges(model.WRITE_SCHEMA), func(ctx *gin.Context) {
			system.Import(ctx, gsqlClient, controllerClient)
		})

		authAPI.DELETE("/graph-store", mw.RequiredSuperUser(), func(ctx *gin.Context) {
			system.DeleteGraphData(ctx, gsqlClient)
		})

		authAPI.GET("/gstatusgraph", mw.RequiredSuperUser(), system.GetGraphStatus)
		authAPI.GET("/gse-status", mw.RequiredSuperUser(), system.GetGSEStatus)
	}

	internalAPI := g.Group("/system", mw.RequiredAuthToken())
	{
		internalAPI.GET("/gui-store/*prefix", system.ExportGUIData)
		internalAPI.POST("/gui-store", func(ctx *gin.Context) {
			system.ImportGUIData(ctx, gsqlClient)
		})
		internalAPI.DELETE("/gui-store", system.DeleteGUIData)
	}
}
