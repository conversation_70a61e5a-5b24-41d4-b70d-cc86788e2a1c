package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/system"
	"github.com/tigergraph/gus/middleware"
	mw "github.com/tigergraph/gus/middleware"
)

func SetSystemRoutes(g *gin.RouterGroup, gsqlClient interfaces.RequestGSQLClient) {
	authAPI := g.Group("/system")
	{
		authAPI.GET("/export-check", func(ctx *gin.Context) {
			system.ExportCheck(ctx)
		})

		authAPI.GET("/export", middleware.RequiredPrivileges(model.EXPORT_GRAPH), func(ctx *gin.Context) {
			system.Export(ctx, gsqlClient)
		})

		authAPI.POST("/import", middleware.RequiredPrivileges(model.WRITE_SCHEMA), func(ctx *gin.Context) {
			system.Import(ctx, gsqlClient)
		})

		authAPI.DELETE("/graph-store", mw.RequiredSuperUser(), func(ctx *gin.Context) {
			system.DeleteGraphData(ctx, gsqlClient)
		})

		authAPI.GET("/gstatusgraph", mw.RequiredSuperUser(), system.GetGraphStatus)
	}

	internalAPI := g.Group("/system", mw.RequiredAuthToken())
	{
		internalAPI.GET("/gui-store", system.ExportGUIData)
		internalAPI.POST("/gui-store", system.ImportGUIData)
		internalAPI.DELETE("/gui-store", system.DeleteGUIData)
	}
}
