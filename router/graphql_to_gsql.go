package router

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/graphql/web"
	mw "github.com/tigergraph/gus/middleware"
)

func SetGraphQLToGSQLRoutes(g *gin.RouterGroup, syncer web.SchemaSyncer) {
	g.POST("/graphql/gsql/*graphName", func(c *gin.Context) {
		log.Info(mw.GetUsername(c), mw.GetPassword(c))
		cfg := mw.GetConfig(c)
		hostURL := fmt.Sprintf("%s://%s:%d", cfg.GetNginxProtocol(), cfg.GetGSQLServerHostname(), cfg.GetNginxPort())
		if len(c.Param("graphName")) < 1 {
			mw.Abort(c, http.StatusBadRequest, "Empty graph name!")
			return
		}
		graphName := c.Param("graphName")[1:] // trim leading /
		h := web.Handler{
			SchemaSyncer: syncer,
			GraphName:    graphName,
			Host:         hostURL,
			User:         mw.GetUsername(c),
			Password:     mw.GetPassword(c),
			SkipSSL:      cfg.GetNginxSSLEnabled(),
		}
		h.ServeHTTP(c.Writer, c.Request)
	})
}
