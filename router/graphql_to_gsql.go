package router

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/graphql/web"
	mw "github.com/tigergraph/gus/middleware"
)

// need to change the code about this in graphql repo
func SetGraphQLToGSQLRoutes(g *gin.RouterGroup, syncer web.SchemaSyncer) {
	g.POST("/graphql/gsql/*graphName", func(c *gin.Context) {
		cfg := mw.GetConfig(c)
		hostURL := fmt.Sprintf("%s://%s:%d", cfg.GetNginxProtocol(), cfg.GetGSQLServerHostname(), cfg.GetNginxPort())
		if len(c.Param("graphName")) < 1 {
			mw.Abort(c, http.StatusBadRequest, "Empty graph name!")
			return
		}
		graphName := c.Param("graphName")[1:] // trim leading /
		h := web.Handler{
			SchemaSyncer: syncer,
			GraphName:    graphName,
			Host:         hostURL,
			User:         mw.GetUsername(c),
			Password:     mw.GetUserCredentials(c).Password,
			SkipSSL:      cfg.GetNginxSSLEnabled(),
		}
		h.ServeHTTP(c.Writer, c.Request)
	})
}
