package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/query"
	"github.com/tigergraph/gus/middleware"
)

func SetQueryRoutes(
	g *gin.RouterGroup,
	daoManager interfaces.DaoManager,
	gsqlClient interfaces.RequestGSQLClient,
) {
	queryAPI := g.Group("/queries")
	{
		infoAPI := queryAPI.Group("/:graphName/info")
		{
			infoAPI.GET("", middleware.RequiredPrivileges(model.READ_QUERY), query.GetAll)
			infoAPI.GET("/:queryName", middleware.RequiredPrivileges(model.READ_QUERY), query.Get)
			infoAPI.DELETE("/:queryName", middleware.RequiredPrivileges(model.WRITE_QUERY), query.Delete(gsqlClient))

			draftAPI := infoAPI.Group("/:queryName/draft")
			{
				draftAPI.POST("", middleware.RequiredPrivileges(model.WRITE_QUERY), func(ctx *gin.Context) {
					query.CreateDraft(ctx)
				})
				draftAPI.PUT("", middleware.RequiredPrivileges(model.WRITE_QUERY), func(ctx *gin.Context) {
					query.UpsertDraft(ctx)
				})
				draftAPI.DELETE("", middleware.RequiredPrivileges(model.WRITE_QUERY), func(ctx *gin.Context) {
					query.DeleteDraft(ctx)
				})
			}
		}

		draftsAPI := queryAPI.Group(":graphName/drafts")
		{
			draftsAPI.DELETE("", middleware.RequiredPrivileges(model.WRITE_QUERY), query.DeleteAllQueryDraft)
		}

		gsqlOpAPI := queryAPI.Group("/:graphName/gsql")
		{

			gsqlOpAPI.POST("/add", query.Add(daoManager, gsqlClient))
			gsqlOpAPI.POST("/install", func(ctx *gin.Context) {
				query.Install(ctx, gsqlClient)
			})
		}
	}
}
