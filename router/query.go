package router

import (
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/middleware"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/handler/query"
)

func SetQueryRoutes(
	g *gin.RouterGroup,
	daoManager interfaces.DaoManager,
	gsqlClient interfaces.RequestGSQLClient,
) {
	queryAPI := g.Group("/queries")
	{
		infoAPI := queryAPI.Group("/:graphName/info")
		{
			// for `>=4.1.0` version, rm permission middleware, will check permission in gsql
			infoAPI.GET("", query.GetAll)
			infoAPI.GET("/:queryName", query.Get)
			infoAPI.DELETE("/:queryName", query.Delete(gsqlClient))

			draftAPI := infoAPI.Group("/:queryName/draft")
			{
				draftAPI.POST("", func(ctx *gin.Context) {
					query.Create(ctx, gsqlClient)
				})
				draftAPI.PUT("", func(ctx *gin.Context) {
					query.Update(ctx, gsqlClient)
				})
				draftAPI.DELETE("", func(ctx *gin.Context) {
					query.DeleteDraft(ctx)
				})
			}
		}

		gsqlOpAPI := queryAPI.Group("/:graphName/gsql")
		{

			gsqlOpAPI.POST("/add", query.Add(daoManager, gsqlClient))
			gsqlOpAPI.POST("/install", func(ctx *gin.Context) {
				query.Install(ctx, gsqlClient)
			})
		}

		statusAPI := queryAPI.Group(":graphName/query_status")
		{
			statusAPI.GET("", middleware.RequiredPrivileges(model.READ_QUERY), query.QueryStatus)
		}
	}
}
