package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	lj "github.com/tigergraph/gus/handler/loadingjob"
	"github.com/tigergraph/gus/middleware"
)

func SetLoadingJobRoutes(g *gin.RouterGroup, loadingJobService interfaces.LoadingJobService, gsqlClient interfaces.RequestGSQLClient) {
	ljAPI := g.Group("/loading-jobs")
	{
		metaAPI := ljAPI.Group("/:graphName/meta")
		{
			metaAPI.GET("", middleware.RequiredPrivileges(model.READ_LOADINGJOB), func(ctx *gin.Context) {
				lj.GetAll(ctx, gsqlClient)
			})
			metaAPI.DELETE("", middleware.RequiredPrivileges(model.WRITE_LOADINGJOB), func(ctx *gin.Context) {
				lj.Delete<PERSON>ll(ctx, gsqlClient)
			})

			metaAPI.GET("/:jobName", middleware.RequiredPrivileges(model.READ_LOADINGJOB), func(ctx *gin.Context) {
				lj.Get(ctx, gsqlClient)
			})
			metaAPI.POST("/:jobName", middleware.RequiredPrivileges(model.WRITE_LOADINGJOB), func(ctx *gin.Context) {
				lj.Create(ctx, gsqlClient)
			})
			metaAPI.DELETE("/:jobName", middleware.RequiredPrivileges(model.WRITE_LOADINGJOB), func(ctx *gin.Context) {
				lj.Delete(ctx, gsqlClient)
			})
		}

		loadingAPI := ljAPI.Group("/:graphName/loading")
		{
			loadingAPI.GET("/progress", middleware.RequiredPrivileges(model.EXECUTE_LOADINGJOB), func(ctx *gin.Context) {
				lj.GetProgress(ctx, loadingJobService)
			})
			loadingAPI.POST("/start", middleware.RequiredPrivileges(model.EXECUTE_LOADINGJOB), func(ctx *gin.Context) {
				lj.Start(ctx, loadingJobService)
			})
			loadingAPI.POST("/pause", middleware.RequiredPrivileges(model.EXECUTE_LOADINGJOB), func(ctx *gin.Context) {
				lj.Pause(ctx, loadingJobService)
			})
			loadingAPI.POST("/resume", middleware.RequiredPrivileges(model.EXECUTE_LOADINGJOB), func(ctx *gin.Context) {
				lj.Resume(ctx, loadingJobService)
			})
			loadingAPI.POST("/stop", middleware.RequiredPrivileges(model.EXECUTE_LOADINGJOB), func(ctx *gin.Context) {
				lj.Stop(ctx, loadingJobService)
			})
		}
	}
}
