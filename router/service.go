package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gus/handler/service"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

func SetServiceRoutes(g *gin.RouterGroup, cfg *config.Config, cntlrClient pb.ControllerClient) {
	serviceAPI := g.Group("/service", mw.RequiredSuperUser())
	{
		serviceAPI.POST("/start", func(ctx *gin.Context) {
			service.Start(ctx, cfg, cntlrClient)
		})
		serviceAPI.POST("/stop", func(ctx *gin.Context) {
			service.Stop(ctx, cfg, cntlrClient)
		})
		serviceAPI.POST("/restart", func(ctx *gin.Context) {
			service.Restart(ctx, cfg, cntlrClient)
		})
	}
}
