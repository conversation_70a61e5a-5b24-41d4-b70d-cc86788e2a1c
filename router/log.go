package router

import (
	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/log"
	"github.com/tigergraph/gus/lib/config"
	mw "github.com/tigergraph/gus/middleware"
)

func SetLogRoutes(g *gin.RouterGroup,
	cfg *config.Config, tgFS interfaces.TGFileSystem, cntlrClient pb.ControllerClient,

) {
	logAPI := g.Group("/log", mw.RequiredPrivileges(model.APP_ACCESS_LOG))
	{
		logAPI.GET("", log.List)
		logAPI.GET("/view", func(ctx *gin.Context) {
			log.View(ctx, cfg, tgFS, cntlrClient)
		})
		logAPI.GET("/search", log.Search)
		logAPI.GET("/download", log.Download)
	}
}
