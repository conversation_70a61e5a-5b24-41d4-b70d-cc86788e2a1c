package graphql

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"
)

var PageOutdatedErr = errors.New("Another user is working on the same page. Please refresh the browser and try again.")

type Page struct {
	AppId  string
	PageId string
	Config
}

// O2D means Object to Data, map the page object to response data
type PageO2D struct {
	AppId   string
	PageId  string
	Config  string
	Version string
}

// GetDBKey returns a string literal representing the key that be used to query page config in ETCD
// there will return two kinds of config key, depends on if pageId is existed:
// 1. pageKey.AppId.PageId, used as the exact key to get one specific page config.
// 2. pageKey.AppId, used as the prefix to query all pages in an application.
func (page *Page) GetDBKey() string {
	if page.PageId == "" {
		return fmt.Sprintf("%s.%s", pageKey, page.AppId)
	}
	return fmt.Sprintf("%s.%s.%s", pageKey, page.AppId, page.PageId)
}

type GetPagesArgs struct {
	AppId string
}

// Insights_GetPages return all the pages abstract information in given application
//
// args:
// AppId is used to specify the application
func (q *Query) Insights_GetPages(
	ctx context.Context,
	args GetPagesArgs,
) (res []PageO2D, err error) {
	log.Info(ctx, "Insights_GetPages")
	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}

	if err = q.checkPermission(ctx, args.AppId, ReadAppOperation); err != nil {
		return nil, err
	}

	page := Page{
		AppId: args.AppId,
		Config: Config{
			Deps: q.Deps,
		},
	}

	// get pageList, empty list is expected condition, so ignore error
	page.Init(ctx, page.GetDBKey(), false, true)

	for _, config := range page.ConfigList {

		var configStr []byte
		var configMap map[string]interface{}
		json.Unmarshal(config.Value, &configMap)

		configMap[pageWidgetListName] = make(map[string]interface{}, 0)

		configStr, err = json.Marshal(configMap)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
		res = append(res, PageO2D{
			AppId:   args.AppId,
			PageId:  cast.ToString(configMap["id"]),
			Config:  string(configStr),
			Version: cast.ToString(configMap["version"]),
		})
	}

	if len(page.ConfigList) == 0 {
		res, err = q.GetPagesFromAppConfig(ctx, args)
		if err != nil {
			return nil, err
		}
	}

	return res, nil
}

type GetPageArgs struct {
	AppId  string
	PageId string
}

// Insights_GetPage is to query detail information about the page
//
// args:
// AppId, PageId is used to generate the config key related to the specific page
func (q *Query) Insights_GetPage(
	ctx context.Context,
	args GetPageArgs,
) (res PageO2D, err error) {
	log.Info(ctx, "Insights_GetPage")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return PageO2D{}, err
	}

	if err = q.checkPermission(ctx, args.AppId, ReadAppOperation); err != nil {
		return PageO2D{}, err
	}

	page := Page{
		AppId:  args.AppId,
		PageId: args.PageId,
		Config: Config{
			Deps: q.Deps,
		},
	}

	err = page.Init(ctx, page.GetDBKey(), true, false)
	if err != nil {
		// empty page config is expected in two case:
		// 1. when create new page
		res = PageO2D{
			AppId:   page.AppId,
			PageId:  page.PageId,
			Config:  "{}",
			Version: "",
		}
		// 2. old app config does not migrate
		pages, err := q.GetPagesFromAppConfig(ctx, GetPagesArgs{AppId: args.AppId})

		if err != nil {
			return res, err
		}

		for _, tempPage := range pages {
			if page.PageId == tempPage.PageId {
				res = tempPage
			}
		}

		return res, nil
	}

	// init chartMap to empty list
	page.ConfigMap[pageWidgetListName] = make(map[string]interface{}, 0)
	configByte, err := json.Marshal(page.ConfigMap)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	res = PageO2D{
		AppId:   page.AppId,
		PageId:  page.PageId,
		Config:  string(configByte),
		Version: cast.ToString(page.ConfigMap["version"]),
	}

	return res, nil
}

type DeletePageArgs struct {
	PageId  string
	AppId   string
	Version string
}

// Insights_DeletePage is used to delete given page
//
// args:
// PageId, AppId is to generate config key related to the page
func (q *Query) Insights_DeletePage(
	ctx context.Context,
	args DeletePageArgs,
) (res string, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	if err = q.checkPermission(ctx, args.AppId, WriteAppOperation); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	page := Page{
		AppId:  args.AppId,
		PageId: args.PageId,
		Config: Config{
			Deps: q.Deps,
		},
	}

	err = page.Init(ctx, page.GetDBKey(), true, false)
	if err == nil && page.ConfigMap["version"] != args.Version {
		// if page existed and version has been changed
		err = PageOutdatedErr
		//nolint:nakedret // err is already defined in return parameters
		return
	} else {
		err = nil
	}

	keyName := page.GetDBKey()
	resp, err := q.Deps.T2pClient.KVDelete(tempCtx, &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(keyName)},
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	// delete widgets in page
	widgets := Widget{
		AppId:  args.AppId,
		PageId: args.PageId,
	}

	widgetKeys := widgets.GetDBKey()
	_, err = q.Deps.T2pClient.KVDelete(tempCtx, &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(widgetKeys)},
		Prefix:      true,
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	return resp.String(), nil
}

type UpsertPageArgs struct {
	AppId      string
	PageConfig string
}

// Insights_UpsertPage is used to create/update page
//
// args:
// AppId point out which application the page belongs to
// PageConfig is the page config information
func (q *Query) Insights_UpsertPage(
	ctx context.Context,
	args UpsertPageArgs,
) (res string, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	if ctx.Value(skipCheckPermission) == nil || !ctx.Value(skipCheckPermission).(bool) {
		if err = q.checkPermission(ctx, args.AppId, WriteAppOperation); err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}

	version := strconv.FormatInt(time.Now().UnixNano(), 10)

	newConfigMap := make(map[string]interface{}, 0)

	err = json.Unmarshal([]byte(args.PageConfig), &newConfigMap)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	page := Page{
		AppId:  args.AppId,
		PageId: cast.ToString(newConfigMap["id"]),
		Config: Config{
			Deps: q.Deps,
		},
	}

	err = page.Init(ctx, page.GetDBKey(), true, false)
	if err == nil && page.ConfigMap["version"] != newConfigMap["version"] {
		// if page existed and version has been changed
		err = PageOutdatedErr
		//nolint:nakedret // err is already defined in return parameters
		return
	} else {
		err = nil
	}

	page.ConfigMap = newConfigMap

	page.ConfigMap["version"] = version

	pageConfig, err := json.Marshal(page.ConfigMap)

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	_, err = q.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(page.GetDBKey()), Value: pageConfig}},
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	res = version

	return res, nil
}

// GetPagesFromAppConfig is to get pages from old version app config
func (q *Query) GetPagesFromAppConfig(
	ctx context.Context,
	args GetPagesArgs,
) (res []PageO2D, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	// query app config
	keyName := fmt.Sprintf("%s.%s.", appKey, args.AppId)
	resp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(keyName)},
		Prefix:      true,
	})

	if err != nil {
		return []PageO2D{}, err
	}

	configValue := resp.Kvs[0].Value

	appConfigMap := make(map[string]interface{}, 0)
	err = json.Unmarshal(configValue, &appConfigMap)

	if err != nil {
		return []PageO2D{}, err
	}

	if appConfigMap["pages"] == nil {
		return []PageO2D{}, nil
	}

	for pageIndex, pageTemp := range appConfigMap["pages"].([]interface{}) {
		pageConfig := pageTemp.(map[string]interface{})

		// add weight to order pages in app.
		pageConfig["weight"] = (pageIndex + 1) * 10
		pageConfig["version"] = ""

		pageConfig[pageWidgetListName] = make(map[string]interface{}, 0)

		if _, ok := pageConfig["globalParameters"]; !ok {
			pageConfig["globalParameters"] = make(map[string]interface{}, 0)
		}

		var pageConfigTemp []byte
		pageConfigTemp, err = json.Marshal(pageConfig)
		if err != nil {
			return []PageO2D{}, err
		}

		res = append(res, PageO2D{
			AppId:   args.AppId,
			PageId:  cast.ToString(pageConfig["id"]),
			Config:  cast.ToString(pageConfigTemp),
			Version: cast.ToString(pageConfig["version"]),
		})
	}

	return res, nil
}
