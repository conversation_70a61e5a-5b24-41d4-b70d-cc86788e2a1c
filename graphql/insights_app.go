package graphql

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/helper"
	"github.com/tigergraph/gus/service/db"
)

var AppOutdatedErr = errors.New("Another user is working on the same application. Please refresh the browser and try again.")

type AllApps struct {
	Id         string
	Title      string
	Screenshot *string
	Icon       *string
	Username   string
	Version    string
}

type App struct {
	Config
	AppId    string
	Username string
	Version  string
}

// O2D means Object to Data, map the app object to response data
type AppO2D struct {
	Config         string
	Username       string
	Version        string
	UserRoleForApp AppUserRole
}

type AllAppResp struct {
	Id         string
	Title      string
	Screenshot *string
	Icon       *string
	Username   string
	Version    string
	UserRole   AppUserRole
}

// GetDBKey returns a string literal representing the key that be used to query app config in ETCD
//
// args:
// userName used as an argument to generate key (to compatible with old version config key)
func (app *App) GetDBKey(userName string) string {
	return fmt.Sprintf("%s.%s.%s", appKey, app.AppId, userName)
}

func (app *App) deletePages() (err error) {
	tempCtx, cancel := context.WithTimeout(app.Context, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	pages := Page{
		AppId: app.AppId,
	}

	pageKeys := pages.GetDBKey()
	_, err = app.Deps.T2pClient.KVDelete(tempCtx, &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(pageKeys)},
		Prefix:      true,
	})
	if err != nil {
		return err
	}
	return nil
}

func (app *App) deleteWidgets() (err error) {
	tempCtx, cancel := context.WithTimeout(app.Context, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	widgets := Widget{
		AppId: app.AppId,
	}

	widgetKeys := widgets.GetDBKey()
	_, err = app.Deps.T2pClient.KVDelete(tempCtx, &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(widgetKeys)},
		Prefix:      true,
	})
	if err != nil {
		return err
	}
	return nil
}

// export all pages and widgets to a kv map
func (app *App) exportKVData() (map[string][]byte, error) {

	db := db.New(app.Deps.Config)

	kvData := make(map[string][]byte, 0)

	allPages := Page{
		AppId: app.AppId,
	}

	// export all pages
	kvs, err := db.Export(allPages.GetDBKey())
	if err != nil {
		return nil, err
	}
	for k, v := range kvs {
		kvData[k] = v
	}

	// export all widgets
	allWidgets := Widget{
		AppId: app.AppId,
	}
	kvs, err = db.Export(allWidgets.GetDBKey())
	if err != nil {
		return nil, err
	}
	for k, v := range kvs {
		kvData[k] = v
	}

	return kvData, nil
}

// import all pages and widgets from a kv map
func (app *App) importKVData(kvData map[string][]byte) error {
	db := db.New(app.Deps.Config)
	return db.Import(kvData)
}

func (q *Query) saveToAllApps(ctx context.Context, appConfig string, owner string, newVersion string, screenshot *string) (err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	appConfigMap := make(map[string]interface{}, 0)
	err = json.Unmarshal([]byte(appConfig), &appConfigMap)

	if err != nil {
		return err
	}

	id := cast.ToString(appConfigMap["id"])

	app := App{
		AppId: id,
		Config: Config{
			Deps: q.Deps,
		},
	}

	app.Init(ctx, app.GetDBKey(owner), true, false)

	iconInConfig := ""
	if appConfigMap["iconURL"] != nil {
		iconInConfig = cast.ToString(appConfigMap["iconURL"])
		pathValid, _ := iconPathValid(iconInConfig)
		if !pathValid {
			err = errors.New("icon path invalid")
			return err
		}
	}

	appInfo := AllApps{
		Id:         id,
		Title:      cast.ToString(appConfigMap["title"]),
		Screenshot: nil,
		Icon:       &iconInConfig,
		Username:   owner,
		Version:    newVersion,
	}

	if screenshot != nil {
		// when change screenshot
		// front-end only give screenshot, so get icon and other config from db
		icon := cast.ToString(app.ConfigMap["iconURL"])
		appInfo = AllApps{
			Id:         id,
			Title:      cast.ToString(app.ConfigMap["title"]),
			Screenshot: screenshot,
			Icon:       &icon,
			Username:   owner,
			Version:    cast.ToString(app.ConfigMap["version"]),
		}
	}

	allAppsResp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(allAppsKey)},
	})

	if err != nil {
		return err
	}

	allApps := []AllApps{}
	if len(allAppsResp.Kvs) > 0 && len(string(allAppsResp.Kvs[0].Value)) > 0 {
		err = json.Unmarshal([]byte(string(allAppsResp.Kvs[0].Value)), &allApps)
		if err != nil {
			return err
		}
	}

	exist := false
	for index, app := range allApps {
		if app.Id == cast.ToString(appConfigMap["id"]) {
			exist = true
			if appInfo.Screenshot == nil {
				appInfo.Screenshot = app.Screenshot
			}
			allApps[index] = appInfo
		}
	}

	if !exist {
		allApps = append(allApps, appInfo)
	}

	allAppsStr, err := json.Marshal(allApps)
	if err != nil {
		return err
	}
	_, err = q.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(allAppsKey), Value: allAppsStr}},
	})
	return err
}

func (app *App) deleteFromAllApps() (err error) {
	tempCtx, cancel := context.WithTimeout(app.Context, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	allAppsResp, err := app.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(allAppsKey)},
	})

	if err != nil {
		return err
	}

	allApps := []AllApps{}
	err = json.Unmarshal([]byte([]byte(string(allAppsResp.Kvs[0].Value))), &allApps)

	if err != nil {
		return err
	}

	res := []AllApps{}

	for _, eachApp := range allApps {
		if eachApp.Id != app.AppId {
			res = append(res, eachApp)
		}
	}

	allAppsStr, err := json.Marshal(res)
	if err != nil {
		return err
	}
	_, err = app.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(allAppsKey), Value: allAppsStr}},
	})
	if err != nil {
		return err
	}
	return nil
}

func (q *Query) setUserRoleInAllAppsResp(allApps []AllAppResp) error {
	appsPermission, err := q.Deps.DaoManager.GetAllInsightsAppPermission()
	if err != nil {
		return err
	}

	userName := q.userInfo.Name
	for index, app := range allApps {
		if app.Username == userName {
			allApps[index].UserRole = Owner
			continue
		}
		appPermission, ok := appsPermission[app.Id]
		allApps[index].UserRole = NoneRole
		if ok {
			if appPermission.Access == int32(ClusterEdit) || helper.Index(appPermission.Editors, userName) != -1 {
				allApps[index].UserRole = Editor
			} else if appPermission.Access == int32(ClusterView) || helper.Index(appPermission.Viewers, userName) != -1 {
				allApps[index].UserRole = Viewer
			}
		}
	}
	return nil
}

// Insights_GetAllApps is used to get apps abstract information
func (q *Query) Insights_GetAllApps(ctx context.Context) (res []AllAppResp, err error) {
	log.Info(ctx, "Insights_GetAllApps")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}

	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	resp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(allAppsKey)},
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	allApps := []AllAppResp{}
	if len(resp.Kvs) > 0 {
		configValue := resp.Kvs[0].Value
		err = json.Unmarshal(configValue, &allApps)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}

		err = q.setUserRoleInAllAppsResp(allApps)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}

	return allApps, nil
}

type GetAppArgs struct {
	Id string
}

func (q *Query) Insights_GetAppPermission(ctx context.Context, args struct{ AppId string }) (model.AppPermission, error) {
	log.Info(ctx, "Insights_GetAppPermission")

	if err := q.isAuthenticated(); err != nil {
		return model.AppPermission{}, err
	}

	res, err := q.Deps.DaoManager.GetInsightsAppPermission(args.AppId)
	if errors.Is(err, db.ErrNotFound) {
		return model.AppPermission{Access: int32(Restricted), Editors: []string{}, Viewers: []string{}}, nil
	}
	if err != nil {
		return model.AppPermission{}, err
	}
	return *res, nil
}

type SetAppPermissionArgs struct {
	AppId      string
	Permission model.AppPermission
}

func (q *Query) Insights_SetAppPermission(ctx context.Context, args SetAppPermissionArgs) (bool, error) {
	log.Info(ctx, "Insights_SetAppPermission")

	if err := q.isAuthenticated(); err != nil {
		return false, err
	}

	if err := q.checkPermission(ctx, args.AppId, WriteAppOperation); err != nil {
		return false, err
	}

	err := q.Deps.DaoManager.UpsertInsightsAppPermission(args.AppId, args.Permission)
	if err != nil {
		return false, err
	}
	return true, nil
}

// Insights_GetApp is used to get app detail information
//
// args:
// Id is used as app id to generate config key to get the right app config in ETCD
func (q *Query) Insights_GetApp(
	ctx context.Context,
	args GetAppArgs,
) (res AppO2D, err error) {
	log.Info(ctx, "Insights_GetApp")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return AppO2D{}, err
	}
	if err := q.checkPermission(ctx, args.Id, ReadAppOperation); err != nil {
		return AppO2D{}, err
	}

	userRoleForApp, err := q.GetUserRoleForApp(ctx, args.Id)
	if err != nil {
		return AppO2D{}, err
	}

	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	keyName := fmt.Sprintf("%s.%s.", appKey, args.Id)
	resp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(keyName)},
		Prefix:      true,
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	if len(resp.Kvs) <= 0 {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	configValue := resp.Kvs[0].Value

	dbKey := resp.Kvs[0].Key

	appConfigMap := make(map[string]interface{}, 0)
	err = json.Unmarshal(configValue, &appConfigMap)

	if err != nil {
		return AppO2D{}, err
	}

	// to be compatible with the pre app config
	if appConfigMap["pageConfigSeparated"] == nil || !cast.ToBool(appConfigMap["pageConfigSeparated"]) {

		err = q.appConfigConverter(ctx, string(configValue), args.Id)
		if err != nil {
			return AppO2D{}, err
		}
	}

	appConfigMap["pages"] = make([]interface{}, 0)

	configValue, err = json.Marshal(appConfigMap)

	if err != nil {
		return AppO2D{}, err
	}

	res = AppO2D{
		Username:       getOwnerFromKey(dbKey),
		Config:         string(configValue),
		Version:        cast.ToString(appConfigMap["version"]),
		UserRoleForApp: userRoleForApp,
	}

	return res, nil
}

type UpsertAppArgs struct {
	Screenshot *string
	AppConfig  string
}

func IsOutOfDate(newVersion string, oldVersion string) bool {
	if newVersion != "" && newVersion != oldVersion {
		return true
	}
	return false
}

// Insights_UpsertApp is used to create/update app config
//
// args:
// AppConfig is app related information
// Screenshot is for app application page
func (q *Query) Insights_UpsertApp(
	ctx context.Context,
	args UpsertAppArgs,
) (res string, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	appConfigMap := make(map[string]interface{}, 0)
	err = json.Unmarshal([]byte(args.AppConfig), &appConfigMap)
	id := cast.ToString(appConfigMap["id"])

	owner, err := q.getOwner(ctx, id)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	isNewApp := false
	if owner == "" {
		isNewApp = true
		owner = q.userInfo.Name
	}

	if !isNewApp && owner != q.userInfo.Name {
		if err = q.checkPermission(ctx, id, WriteAppOperation); err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}

	newVersion := strconv.FormatInt(time.Now().UnixNano(), 10)

	app := App{
		AppId: id,
		Config: Config{
			Deps: q.Deps,
		},
	}

	if !isNewApp {
		app.Init(ctx, app.GetDBKey(owner), true, false)
	}

	newConfigMap := make(map[string]interface{}, 0)
	err = json.Unmarshal([]byte(args.AppConfig), &newConfigMap)

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	appChangeOutdated := !isNewApp && args.Screenshot == nil && IsOutOfDate(cast.ToString(newConfigMap["version"]), cast.ToString(app.ConfigMap["version"]))
	if appChangeOutdated {
		// if version has been changed (screenshot don't need to check version)
		err = AppOutdatedErr
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	err = q.saveToAllApps(ctx, args.AppConfig, owner, newVersion, args.Screenshot)

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	if args.Screenshot != nil {
		// if change screenshot, will not change app config, just need to change allApps
		return cast.ToString(app.ConfigMap["version"]), nil
	}

	keyName := app.GetDBKey(owner)

	newConfigMap["version"] = newVersion
	var appConfigByte []byte
	appConfigByte, err = json.Marshal(newConfigMap)

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	_, err = q.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(keyName), Value: appConfigByte}},
	})

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	return newVersion, nil
}

type DeleteAppArgs struct {
	Id      string
	Version string
}

// Insights_DeleteApp is used to delete app
//
// args:
// Id is used as app id to generate config key to get the right app config in ETCD
// Version is used to verify whether the app config want to delete is modified by another user
func (q *Query) Insights_DeleteApp(
	ctx context.Context,
	args DeleteAppArgs,
) (res string, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	if err = q.checkPermission(ctx, args.Id, WriteAppOperation); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	app := App{
		AppId:    args.Id,
		Username: q.userInfo.Name,
		Config: Config{
			Deps:    q.Deps,
			Context: ctx,
		},
	}

	err = app.Init(ctx, app.GetDBKey(q.userInfo.Name), true, false)

	if err == nil && IsOutOfDate(args.Version, cast.ToString(app.ConfigMap["version"])) {
		// if app existed and version has been changed
		err = AppOutdatedErr
		//nolint:nakedret // err is already defined in return parameters
		return
	} else {
		err = nil
	}

	keyName := app.GetDBKey(app.Username)
	resp, err := q.Deps.T2pClient.KVDelete(tempCtx, &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(keyName)},
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	// delete pages in app
	app.deletePages()

	// delete widgets in app
	app.deleteWidgets()

	// delete app from all apps
	app.deleteFromAllApps()

	return resp.String(), nil
}
