package graphql

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/helper"
)

const (
	allAppsKey         = "GAAllApps"
	appKey             = "GAApp"
	pageKey            = "GAPage"
	widgetKey          = "GAWidget"
	pageWidgetListName = "chartMap"
)

type AppAccess int32

const (
	Restricted AppAccess = iota
	ClusterView
	ClusterEdit
)

type AppUserRole string

const (
	Owner    AppUserRole = "owner"
	Editor   AppUserRole = "editor"
	Viewer   AppUserRole = "viewer"
	NoneRole AppUserRole = "none"
)

type OperationType string

const (
	ReadAppOperation  OperationType = "read"
	WriteAppOperation OperationType = "write"
)

var PagesNotExistErr = errors.New("pages does not exist in application config")
var PageConfigInvalid = errors.New("invalid page config")
var AppConfNotExistErr = errors.New("Can not find the application config")
var ConfigNotExistErr = errors.New("Can not find the config")
var IsNotOwnerErr = errors.New("You have no permission to edit it")
var ReadForbiddenErr = errors.New("You have no permission to read this app")
var WriteForbiddenErr = errors.New("You have no permission to edit this app")

func PageConfigInvalidErr(op string) error {
	return fmt.Errorf("%w : %s", PageConfigInvalid, op)
}

type SkipCheckPermissionType string

const skipCheckPermission SkipCheckPermissionType = "skipCheckPermission"

type Config struct {
	// the raw string of the first config
	ConfigStr string
	// all the configs that match the key (prefix match)
	ConfigList []*pb.KVPair
	// the config map that generated from the raw string
	ConfigMap map[string]interface{}
	Context   context.Context
	Deps      Deps
}

// Init is to query existed configs related to config key, and fill Config struct
//
// args:
// q, ctx, dbKey are arguments used to perform querying in ETCD
// unmarshal indicate that whether the first config in querying results need to be unmarshalled
// prefix indicate config key adopting prefix match or full match
//
// results:
// err == nil means hit at least one config
func (config *Config) Init(ctx context.Context, dbKey string, unmarshal bool, prefix bool) (err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	resp, err := config.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(dbKey)},
		Prefix:      prefix,
	})
	if err != nil {
		return
	}

	if len(resp.Kvs) == 0 {
		err = ConfigNotExistErr
		return
	}

	config.ConfigList = resp.Kvs

	config.ConfigStr = string(resp.Kvs[0].Value)
	if unmarshal {
		err = json.Unmarshal([]byte(config.ConfigStr), &config.ConfigMap)
	}
	return
}

type DataExportImport struct {
	AppConfig     string
	PageConfigs   []string
	WidgetConfigs []string
}

type ExportWidgetConfig struct {
	PageId string
	Config string
}

type ExportDataArgs struct {
	AppId string
}

type ImportResult struct {
	AppVersion   string
	PagesVersion [][]string
}

func (q *Query) Insights_DuplicateApp(
	ctx context.Context,
	args struct {
		DuplicateAppId string
		NewAppId       string
		NewTitle       string
		Screenshot     *string
	},
) (res ImportResult, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	// resolver level authorization
	if err = q.isAuthenticated(); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	if err = q.checkPermission(ctx, args.DuplicateAppId, ReadAppOperation); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	ret, err := q.Insights_ExportData(ctx, ExportDataArgs{AppId: args.DuplicateAppId})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	var appConfigMap map[string]interface{}
	err = json.Unmarshal([]byte(ret.AppConfig), &appConfigMap)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	appConfigMap["title"] = args.NewTitle

	appConfig, err := json.Marshal(appConfigMap)

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	ret.AppConfig = string(appConfig)

	// update all apps
	var icon *string
	tempIcon := cast.ToString(appConfigMap["iconURL"])
	icon = &tempIcon

	appInfo := AllApps{
		Id:         args.NewAppId,
		Title:      args.NewTitle,
		Screenshot: args.Screenshot,
		Icon:       icon,
		Username:   q.userInfo.Name,
		Version:    cast.ToString(appConfigMap["version"]),
	}

	allAppsResp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(allAppsKey)},
	})

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	allApps := []AllApps{}
	if len(allAppsResp.Kvs) > 0 && len(string(allAppsResp.Kvs[0].Value)) > 0 {
		err = json.Unmarshal([]byte(string(allAppsResp.Kvs[0].Value)), &allApps)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}

	allApps = append(allApps, appInfo)

	allAppsStr, err := json.Marshal(allApps)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	_, err = q.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(allAppsKey), Value: allAppsStr}},
	})

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	res, err = q.Insights_ImportData(ctx, ImportDataArgs{
		DataExportImport: ret,
		AppId:            args.NewAppId,
		Version:          "",
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	return res, nil
}

type ImportDataArgs struct {
	DataExportImport
	AppId   string
	Version string
}

func (q *Query) Insights_ImportData(
	ctx context.Context,
	args ImportDataArgs,
) (res ImportResult, err error) {
	// resolver level authorization
	if err = q.isAuthenticated(); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	if err = q.checkPermission(ctx, args.AppId, WriteAppOperation); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	app := App{
		AppId:    args.AppId,
		Username: q.userInfo.Name,
		Config: Config{
			Deps:    q.Deps,
			Context: ctx,
		},
	}
	// cache apges and widgets incase of rollback
	kvMap, err := app.exportKVData()
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	defer func() {
		if err != nil {
			// if error, rollback
			log.Errorf("error in import data, rollback: %v", err)
			app.deletePages()
			app.deleteWidgets()
			app.importKVData(kvMap)
		}
	}()

	// delete pages in app
	app.deletePages()

	// delete widgets in app
	app.deleteWidgets()

	// change app id in app config, upsert app config
	appConfigMap := make(map[string]interface{}, 0)
	err = json.Unmarshal([]byte(args.AppConfig), &appConfigMap)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	appConfigMap["id"] = args.AppId
	appConfigMap["version"] = args.Version
	var newAppConfig []byte
	newAppConfig, err = json.Marshal(appConfigMap)

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	res.AppVersion, err = q.Insights_UpsertApp(ctx, UpsertAppArgs{
		AppConfig: string(newAppConfig),
	})

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	// upsert page configs
	for _, pageConfig := range args.PageConfigs {
		pageConfigMap := make(map[string]interface{}, 0)
		err = json.Unmarshal([]byte(pageConfig), &pageConfigMap)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
		pageVersion := ""
		pageVersion, err = q.Insights_UpsertPage(ctx, UpsertPageArgs{
			args.AppId,
			pageConfig,
		})
		res.PagesVersion = append(res.PagesVersion, []string{cast.ToString(pageConfigMap["id"]), pageVersion})
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}

	// upsert widget configs
	for _, widgetConfigStr := range args.WidgetConfigs {
		widgetConfig := ExportWidgetConfig{}
		err = json.Unmarshal([]byte(widgetConfigStr), &widgetConfig)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
		_, err = q.Insights_UpsertWidget(ctx, UpsertWidgetArgs{
			args.AppId,
			widgetConfig.PageId,
			widgetConfig.Config,
		})
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}

	return res, err
}

func (q *Query) Insights_ExportData(ctx context.Context, args ExportDataArgs) (res DataExportImport, err error) {
	log.Info(ctx, "Insights_ExportData")
	// resolver level authorization
	if err = q.isAuthenticated(); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}
	if err = q.checkPermission(ctx, args.AppId, ReadAppOperation); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	// export app config
	keyName := fmt.Sprintf("%s.%s.", appKey, args.AppId)
	resp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(keyName)},
		Prefix:      true,
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	if len(resp.Kvs) <= 0 {
		return DataExportImport{}, AppConfNotExistErr
	}

	res.AppConfig = string(resp.Kvs[0].Value)
	// export pages config
	page := Page{
		AppId: args.AppId,
		Config: Config{
			Deps: q.Deps,
		},
	}

	page.Init(ctx, page.GetDBKey(), false, true)

	for _, config := range page.ConfigList {
		res.PageConfigs = append(res.PageConfigs, string(config.Value))
	}
	// export widgets config
	widget := Widget{
		AppId: args.AppId,
		Config: Config{
			Deps: q.Deps,
		},
	}

	widget.Init(ctx, widget.GetDBKey(), false, true)

	for _, config := range widget.ConfigList {
		keys := strings.Split(string(config.Key), ".")
		pageId := keys[len(keys)-2]
		var widgetConfigByte []byte
		widgetConfigByte, err = json.Marshal(ExportWidgetConfig{
			PageId: pageId,
			Config: string(config.Value),
		})
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
		res.WidgetConfigs = append(res.WidgetConfigs, string(widgetConfigByte))
	}

	return res, nil
}

type GlobalConf struct {
	Id         string
	Title      string
	Screenshot *string
	Icon       *string
	Username   string
	Config     string
}

func (q *Query) Insights_ExportAllData(
	ctx context.Context,
) (res []GlobalConf, err error) {
	log.Info(ctx, "Insights_ExportAllData")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}

	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	allAppsInfo := []AllApps{}
	resp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(allAppsKey)},
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	if len(resp.Kvs) > 0 {
		resStr := string(resp.Kvs[0].Value)
		err = json.Unmarshal([]byte(resStr), &allAppsInfo)
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}

		for i := 0; i < len(allAppsInfo); i++ {
			keyName := fmt.Sprintf("%s.%s.", appKey, allAppsInfo[i].Id)
			appResp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
				ServiceName: tgServ.GUI,
				Keys:        [][]byte{[]byte(keyName)},
				Prefix:      true,
			})
			if err != nil {
				break
			}

			if len(appResp.Kvs) > 0 {
				username := "tigergraph"

				if len(allAppsInfo[i].Username) > 0 {
					username = allAppsInfo[i].Username
				}

				res = append(res, GlobalConf{
					allAppsInfo[i].Id,
					allAppsInfo[i].Title,
					allAppsInfo[i].Screenshot,
					allAppsInfo[i].Icon,
					username,
					string(appResp.Kvs[0].Value),
				})
			}
		}
	}

	return res, err
}

func (q *Query) Insights_ImportAllData(
	ctx context.Context,
	args struct {
		Data []GlobalConf
	},
) (res string, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	allApps := []AllApps{}
	data := args.Data

	_, err = q.Deps.T2pClient.KVDelete(tempCtx, &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(appKey)},
		Prefix:      true,
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	_, err = q.Deps.T2pClient.KVDelete(tempCtx, &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(allAppsKey)},
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	for i := 0; i < len(data); i++ {
		allApps = append(allApps, AllApps{
			Id:         data[i].Id,
			Title:      data[i].Title,
			Screenshot: data[i].Screenshot,
			Icon:       data[i].Icon,
			Username:   data[i].Username,
		})

		keyName := fmt.Sprintf("%s.%s.%s", appKey, data[i].Id, data[i].Username)
		_, err = q.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
			ServiceName: tgServ.GUI,
			Kvs:         []*pb.KVPair{{Key: []byte(keyName), Value: []byte(data[i].Config)}},
		})
		if err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	allAppsStr, err := json.Marshal(allApps)
	_, err = q.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(allAppsKey), Value: allAppsStr}},
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	return res, err
}

func (q *Query) getOwner(ctx context.Context, id string) (owner string, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	getKeyName := fmt.Sprintf("%s.%s.", appKey, id)
	getResp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(getKeyName)},
		Prefix:      true,
	})
	if err != nil {
		return
	}

	if len(getResp.Kvs) > 0 {
		return getOwnerFromKey(getResp.Kvs[0].Key), nil
	}
	return "", nil
}

func getOwnerFromKey(key []byte) (owner string) {
	userNameInKeysIndex := 2

	keys := strings.Split(string(key), ".")
	owner = strings.Join(keys[userNameInKeysIndex:], ".")
	return
}

// hasPermission is permission verify function in 3.7.0, will modify in future.
// work needs to do:
// 1. move username from config key into config information, like what we do in page/widget config.
func (q *Query) isOwner(ctx context.Context, id string) (res bool, err error) {
	owner, err := q.getOwner(ctx, id)

	if err != nil {
		return false, err
	}

	if owner == "" {
		// when create app, the owner will be ""
		return true, nil
	}

	res = owner == q.userInfo.Name
	return res, nil
}

func (q *Query) GetUserRoleForApp(ctx context.Context, appId string) (AppUserRole, error) {
	owner, err := q.isOwner(ctx, appId)
	if err != nil {
		return "", err
	}
	if owner {
		return Owner, nil
	}
	if q.creds.AuthType == model.TokenAuthType {
		return Viewer, nil
	}

	appPermission, err := q.Insights_GetAppPermission(ctx, struct{ AppId string }{appId})
	if err != nil {
		return "", err
	}
	var res AppUserRole = NoneRole
	userName := q.userInfo.Name
	if appPermission.Access == int32(ClusterEdit) || helper.Index(appPermission.Editors, userName) != -1 {
		res = Editor
	} else if appPermission.Access == int32(ClusterView) || helper.Index(appPermission.Viewers, userName) != -1 {
		res = Viewer
	}
	return res, nil
}

func (q *Query) checkPermission(ctx context.Context, appId string, operationType OperationType) error {
	role, err := q.GetUserRoleForApp(ctx, appId)
	if err != nil {
		return err
	}

	if operationType == ReadAppOperation && role == NoneRole {
		return ReadForbiddenErr
	}
	if operationType == WriteAppOperation && role != Editor && role != Owner {
		return WriteForbiddenErr
	}
	return nil
}

// appConfigConverter is used to extract pages/widgets config from app config that pageConfigSeparated is false,
// function will return modified app config (remove pages config/ widgets config in app config)
//
// args:
// appConfig is application config that contains pages config, and widgets config
// appId is used to query app config when the appConfig is empty string ("")
func (q *Query) appConfigConverter(ctx context.Context, appConfig string, appId string) (err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	// set skipCheckPermission true, to skip ownership check in upsert page/widget
	ctx = context.WithValue(ctx, skipCheckPermission, true)

	app := App{
		AppId: appId,
	}

	// get the app owner name rather than the login username
	owner, err := q.getOwner(ctx, appId)

	if err != nil {
		return err
	}

	keyName := app.GetDBKey(owner)
	appConfigMap := make(map[string]interface{}, 0)

	if appConfig == "" {
		var resp *pb.KVGetResponse
		resp, err = q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
			ServiceName: tgServ.GUI,
			Keys:        [][]byte{[]byte(keyName)},
			Prefix:      true,
		})
		if err != nil {
			return err
		}

		if len(resp.Kvs) <= 0 {
			return err
		}

		configValue := resp.Kvs[0].Value

		err = json.Unmarshal(configValue, &appConfigMap)

		if err != nil {
			return err
		}

		if appConfigMap["pageConfigSeparated"] != nil && cast.ToBool(appConfigMap["pageConfigSeparated"]) {
			// if app config has been separated, just return
			return nil
		}

	} else {
		json.Unmarshal([]byte(appConfig), &appConfigMap)
	}

	if _, ok := appConfigMap["pages"]; !ok {
		return PagesNotExistErr
	}

	for pageIndex, pageTemp := range appConfigMap["pages"].([]interface{}) {
		pageConfig := pageTemp.(map[string]interface{})
		pageId := cast.ToString(pageConfig["id"])

		if _, ok := pageConfig[pageWidgetListName]; !ok {
			return PageConfigInvalidErr("chartMap does not exist in page config")
		}

		// upsert widget config
		for _, widgetConfig := range pageConfig[pageWidgetListName].(map[string]interface{}) {
			widgetArgs := struct {
				AppId        string
				PageId       string
				WidgetConfig string
			}{
				AppId:  appId,
				PageId: pageId,
			}
			var configByte []byte
			configByte, err = json.Marshal(widgetConfig)
			if err != nil {
				return err
			}
			widgetArgs.WidgetConfig = string(configByte)
			_, err = q.Insights_UpsertWidget(ctx, widgetArgs)
			if err != nil {
				return err
			}
		}

		// upsert page config
		delete(pageConfig, pageWidgetListName)

		// add weight to order pages in app.
		pageConfig["weight"] = (pageIndex + 1) * 10
		pageConfig["version"] = ""

		pageArgs := struct {
			AppId      string
			PageConfig string
		}{
			AppId: appId,
		}
		var pageConfigTemp []byte
		pageConfigTemp, err = json.Marshal(pageConfig)
		if err != nil {
			return err
		}
		pageArgs.PageConfig = string(pageConfigTemp)
		_, err = q.Insights_UpsertPage(ctx, pageArgs)
		if err != nil {
			return err
		}
	}

	appConfigMap["pageConfigSeparated"] = true

	// 2. delete page config from app config
	delete(appConfigMap, "pages")

	configByte, err := json.Marshal(appConfigMap)
	if err != nil {
		return err
	}

	_, err = q.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(keyName), Value: configByte}},
	})

	return err
}

func iconPathValid(path string) (result bool, err error) {
	re1, err := regexp.Compile(`^/(.*)?$`)

	if err != nil {
		return false, err
	}

	re2, err := regexp.Compile(`^https?://((\w-)*.)*((tgcloud-dev.com)|(tgcloud.io))/(.*)?$`)

	if err != nil {
		return false, err
	}

	result = re1.MatchString(path) || re2.MatchString(path)
	return result, nil
}
