package graphql

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"
)

var WidgetOutdatedErr = errors.New("Another user is working on the same widget. Please refresh the browser and try again.")

type Widget struct {
	AppId  string
	PageId string
	Id     string
	Config
}

// O2D means Object to Data, map the widget object to response data
type WidgetO2D struct {
	AppId   string
	PageId  string
	Id      string
	Config  string
	Version string
}

// GetDBKey returns a string literal representing the key that be used to query widget config in ETCD
// there will return three kinds of config key, depends on if pageId/widgetId is existed:
// 1. pageKey.AppId.PageId.WidgetId, used as the exact key to get one specific widget config.
// 2. pageKey.AppId.PageId, used as the prefix to query all widgets in a page.
// 3. pageKey.AppId, used as the prefix to query all pages in an application.
func (widget *Widget) GetDBKey() string {
	if widget.PageId == "" {
		return fmt.Sprintf("%s.%s", widgetKey, widget.AppId)
	} else if widget.Id == "" {
		return fmt.Sprintf("%s.%s.%s", widgetKey, widget.AppId, widget.PageId)
	}
	return fmt.Sprintf("%s.%s.%s.%s", widgetKey, widget.AppId, widget.PageId, widget.Id)
}

type UpsertWidgetArgs struct {
	AppId        string
	PageId       string
	WidgetConfig string
}

// Insights_UpsertWidget is used to create/update widget
//
// args:
// AppId, PageId is used to specify which application and page (in the application) the widget belongs to.
// widgetConfig is the widget config information.
func (q *Query) Insights_UpsertWidget(
	ctx context.Context,
	args UpsertWidgetArgs,
) (res string, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	if ctx.Value(skipCheckPermission) == nil || !ctx.Value(skipCheckPermission).(bool) {
		if err = q.checkPermission(ctx, args.AppId, WriteAppOperation); err != nil {
			//nolint:nakedret // err is already defined in return parameters
			return
		}
	}

	newConfigMap := make(map[string]interface{}, 0)
	err = json.Unmarshal([]byte(args.WidgetConfig), &newConfigMap)
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	widget := Widget{
		AppId:  args.AppId,
		PageId: args.PageId,
		Id:     cast.ToString(newConfigMap["id"]),
		Config: Config{
			Deps: q.Deps,
		},
	}

	err = widget.Init(ctx, widget.GetDBKey(), true, false)
	if err == nil && widget.ConfigMap["version"] != newConfigMap["version"] {
		// if widget existed and version has been changed
		err = WidgetOutdatedErr
		//nolint:nakedret // err is already defined in return parameters
		return
	} else {
		err = nil
	}

	widget.ConfigMap = newConfigMap
	version := strconv.FormatInt(time.Now().UnixNano(), 10)

	widget.ConfigMap["version"] = version

	widgetConfig, err := json.Marshal(widget.ConfigMap)

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	_, err = q.Deps.T2pClient.KVPut(tempCtx, &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(widget.GetDBKey()), Value: widgetConfig}},
	})

	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	res = version

	return res, nil
}

type GetWidgetsArgs struct {
	AppId  string
	PageId string
}

// Insights_GetWidgets is used to get widgets in an given page.
//
// args:
// AppId, PageId is used to generate widgets config key prefix.
func (q *Query) Insights_GetWidgets(
	ctx context.Context,
	args GetWidgetsArgs,
) (res []WidgetO2D, err error) {
	log.Info(ctx, "Insights_GetWidgets")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}

	if err = q.checkPermission(ctx, args.AppId, ReadAppOperation); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	widget := Widget{
		AppId:  args.AppId,
		PageId: args.PageId,
		Config: Config{
			Deps: q.Deps,
		},
	}

	// get widgetList
	widget.Init(ctx, widget.GetDBKey(), false, true)

	for _, config := range widget.ConfigList {

		var configMap map[string]interface{}
		json.Unmarshal(config.Value, &configMap)

		res = append(res, WidgetO2D{
			AppId:   args.AppId,
			PageId:  args.PageId,
			Id:      cast.ToString(configMap["id"]),
			Config:  string(config.Value),
			Version: cast.ToString(configMap["version"]),
		})
	}

	if len(widget.ConfigList) == 0 {
		res, err = q.GetWidgetsFromAppConfig(ctx, args)
		if err != nil {
			return nil, err
		}
	}

	return res, nil
}

type GetWidgetArgs struct {
	AppId  string
	PageId string
	Id     string
}

// Insights_GetWidget is used to ge specific widget
//
// args:
// AppId, PageId, WidgetId is to generate the exact key related to the widget
func (q *Query) Insights_GetWidget(
	ctx context.Context,
	args GetWidgetArgs,
) (res WidgetO2D, err error) {
	log.Info(ctx, "Insights_GetWidget")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return WidgetO2D{}, err
	}

	if err = q.checkPermission(ctx, args.AppId, ReadAppOperation); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	widget := Widget{
		AppId:  args.AppId,
		PageId: args.PageId,
		Id:     args.Id,
		Config: Config{
			Deps: q.Deps,
		},
	}

	err = widget.Init(ctx, widget.GetDBKey(), true, false)

	if err != nil {
		// old app config does not migrate
		widgets, _ := q.GetWidgetsFromAppConfig(ctx, GetWidgetsArgs{
			AppId:  args.AppId,
			PageId: args.PageId,
		})
		for _, tempWidget := range widgets {
			if args.Id == tempWidget.Id {
				res = tempWidget
			}
		}

		return res, nil
	} else {
		res = WidgetO2D{
			AppId:   widget.AppId,
			PageId:  widget.PageId,
			Id:      widget.Id,
			Config:  widget.ConfigStr,
			Version: cast.ToString(widget.ConfigMap["version"]),
		}
	}

	return res, nil
}

type DeleteWidgetArgs struct {
	PageId  string
	AppId   string
	Id      string
	Version string
}

// Insights_DeleteWidget is used to delete specific widget
//
// args:
// PageId, AppId, WidgetId is used to generate config key.
// Version is used to verify whether the widget config has been modified by another user.
func (q *Query) Insights_DeleteWidget(
	ctx context.Context,
	args DeleteWidgetArgs,
) (res string, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	if err = q.checkPermission(ctx, args.AppId, WriteAppOperation); err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	widget := Widget{
		AppId:  args.AppId,
		PageId: args.PageId,
		Id:     args.Id,
		Config: Config{
			Deps: q.Deps,
		},
	}

	err = widget.Init(ctx, widget.GetDBKey(), true, false)
	if err == nil && widget.ConfigMap["version"] != args.Version {
		// if widget existed and version changed
		err = WidgetOutdatedErr
		//nolint:nakedret // err is already defined in return parameters
		return
	} else {
		err = nil
	}

	keyName := widget.GetDBKey()
	resp, err := q.Deps.T2pClient.KVDelete(tempCtx, &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(keyName)},
	})
	if err != nil {
		//nolint:nakedret // err is already defined in return parameters
		return
	}

	return resp.String(), nil
}

// GetWidgetsFromAppConfig is to get pages from old version app config
func (q *Query) GetWidgetsFromAppConfig(
	ctx context.Context,
	args GetWidgetsArgs,
) (res []WidgetO2D, err error) {
	tempCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	// query app config
	keyName := fmt.Sprintf("%s.%s.", appKey, args.AppId)
	resp, err := q.Deps.T2pClient.KVGet(tempCtx, &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(keyName)},
		Prefix:      true,
	})

	if err != nil {
		return []WidgetO2D{}, err
	}

	configValue := resp.Kvs[0].Value

	appConfigMap := make(map[string]interface{}, 0)

	err = json.Unmarshal(configValue, &appConfigMap)

	if err != nil {
		return []WidgetO2D{}, err
	}

	if _, ok := appConfigMap["pages"]; !ok {
		// if pages does not existed return empty widget list.
		return []WidgetO2D{}, nil
	}

	for _, pageTemp := range appConfigMap["pages"].([]interface{}) {
		pageConfig := pageTemp.(map[string]interface{})

		if cast.ToString(pageConfig["id"]) != args.PageId {
			continue
		}

		for _, widgetTemp := range pageConfig[pageWidgetListName].(map[string]interface{}) {
			widgetConfig := widgetTemp.(map[string]interface{})

			var widgetConfigTemp []byte
			widgetConfigTemp, err = json.Marshal(widgetConfig)
			if err != nil {
				return []WidgetO2D{}, err
			}
			res = append(res, WidgetO2D{
				AppId:   args.AppId,
				PageId:  cast.ToString(pageConfig["id"]),
				Id:      cast.ToString(widgetConfig["id"]),
				Config:  cast.ToString(widgetConfigTemp),
				Version: cast.ToString(widgetConfig["version"]),
			})
		}
	}

	return res, nil
}
