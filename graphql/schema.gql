type Query {
    "Search files in a TigerGraph cluster"
    SearchFile(
        FileMeta:    FileMeta!
        FileFilter:  String!
        Pattern:     String!
        Limit:       Int!
    ): [SearchFileSingleResponse!]!

    ReadFile(
        FileMeta:   FileMeta!
        Page:       Pagination!
    ): [ReadFileSingleResponse!]!

    ListFile(
        FileMeta:       FileMeta!
        FileRegxFilter: String!
    ): [ListFileSingleResponse!]!


    License: SystemLicense!

    Log: LogResponse!

    "Insights config"
    Insights_GetAllApps(): [Insights_AllApps!]!

    Insights_GetApp(id: String!): Insights_APP!
                                    
    Insights_GetPages(appId: String!): [Insights_Pages!]!

    Insights_GetPage(appId: String!, pageId: String!): Insights_Pages!

    Insights_GetWidgets(appId: String!, pageId: String!): [Insights_Widgets!]!

    Insights_GetWidget(appId: String!, pageId: String!, id: String!): Insights_Widgets!

    Insights_ExportData(appId: String!): Insights_Export!

    Insights_ExportAllData(): [Insights_Exports!]!

    Insights_GetAppPermission(appId: String!): Insights_AppPermission!

    """
    [Instable]
    TigerGraph version
    """
    Version: String!
    Config: Config!
}

type Mutation {
    "return a bool to indicate success or failure"
    SetConfig(name: String!, value: String!): SetConfigResponse

    ############
    # Insights #
    ############
    Insights_UpsertApp(
        screenshot: String
        appConfig: String!
    ): String!

    Insights_DeleteApp(
        id: String!
        version: String!
    ): String!

    Insights_UpsertPage(
        appId: String!
        pageConfig: String!
    ): String!

    Insights_DeletePage(
        pageId: String!
        appId: String!
        version: String!
    ): String!

    Insights_UpsertWidget(
        pageId: String!
        appId: String!
        widgetConfig: String!
    ): String!

    Insights_DeleteWidget(
        pageId: String!
        appId: String!
        id: String!
        version: String!
    ): String!

    Insights_ImportAllData(data: [Import!]!): String!

    Insights_ImportData(
      appId: String!
      version: String!
      appConfig:     String!
      pageConfigs:   [String!]!
      widgetConfigs: [String!]!
    ): Insights_Import!

    Insights_DuplicateApp(
      duplicateAppId: String!
      newAppId: String!
      newTitle: String!
      screenshot: String
    ): Insights_Import!

    Insights_SetAppPermission(appId: String!, permission: Insights_AppPermissionInput!): Boolean!
}

##########
# Inputs #
##########

input FileMeta {
    "ABS path for the file/folder"
    FilePath: String!
    "Where the file/folder is located"
    Sd: ServiceDescriptorInput!
}

"""
replica and partition identify a host id.

If all 3 are given, HostID overrides other 2.

If none is given, default to all nodes in the cluster
"""
input ServiceDescriptorInput {
    ServiceName: String!
    "0 means all replicas"
    Replica: Int
    "0 means all partitions"
    Partition: Int
    "If host id given, no need to give replica and partition. vice-versa"
    HostID: String
}

"Pagination for reading file content"
input Pagination {
    Offset: Int!
    Length: Int!
}

"Graph Analyzer"
input Import {
    id: String!
    title: String!
    screenshot: String
    icon: String
    username: String!
    config: String!
}

input Insights_AppPermissionInput {
  access: Int!
  editors: [String!]!
  viewers: [String!]!
}

##############
# File Store #
##############

type ReadFileSingleResponse {
    Sd:     ServiceDescriptor!
    Data:   String!
}

type ListFileSingleResponse {
    Sd:     ServiceDescriptor!
    Infos:  [FileInfo!]!
}

type SearchFileSingleResponse {
    Sd:           ServiceDescriptor!
    SearchResult: [SearchResult!]!
}

type SearchResult {
    "Path of the file"
    Path:       String!
    "the content of the line which the pattern is matched"
    Line:       String!
    "the offset where the pattern is found"
    Offset:     Int!
    "the line numeber where the pattern is found"
    LineNumber: Int!
}

type ServiceDescriptor {
    ServiceName: String!
    Replica:     Int!
    Partition:   Int!
    HostID:      String!
}

type FileInfo {		
  Name:     String!		
  Path:     String!
  Size:     Int!	
  IsDir:    Boolean!
  ModTime:  Int!
  """
  if it is link, then SourcePath is the path it points to(it will not follow through)
  
  otherwise, it will be empty
  """
  SourcePath: String!

  "Content of the file. Pagination is optional"
  Content(page: Pagination): String!

  DownloadUrl: String!
}

###########
# License #
###########
type SystemLicense {
	Issuer:          String! 
	Audience:        String! 
	StartTime:       Float!    
	EndTime:         Float!    
	IssueTime:       Float!    
	Edition:         String! 
	Version:         String! 
	Host:            Host!
	Topology:        Topology!
	GST:             GST!
	Insights:        ToolsApp!
	GSQLShell:       ToolsApp!
	GraphQL:         ToolsApp!
	RuntimeMemory:   RuntimeMemory!
	Status:          LicenseStatus!
}

type Host {
    MaxCPUCore:             Float!
    MaxPhysicalMemoryBytes: Float!
    MaxClusterNodeNumber:   Float!
}

type Topology {
    MaxVertexNumber:  Float! 
    MaxEdgeNumber:    Float! 
    MaxGraphNumber:   Float!   
    MaxTopologyBytes: Float! 
}

type GST {
    Enable:            Boolean!   
    ZoomChartsLicense: String! 
}

type ToolsApp {
    Enable:            Boolean!    
}

type RuntimeMemory {
    MaxUserResidentSetBytes: Float! 
}

enum LicenseStatus {
    Valid
	Invalid
	Expired
	NoLicense
}

#######
# Log #
#######
"""
replica and partition identify a host id.

If all 3 are given, HostID overrides other 2.

If none is given, default to all nodes in the cluster
"""
type LogResponse {
    """ 
    If replica equals to 0, it means all replicas.

    If partition equals to 0, it means all partitions.

    If host id given, no need to give replica and partition. vice-versa.
    """
    List(
        FilePath: String!
        ServiceName: String!
        Replica: Int
        Partition: Int
        HostID: String
        FileRegxFilter: String!
    ): [ListFileSingleResponse!]!

    Content(
        FilePath: String!
        ServiceName: String!
        Replica: Int
        Partition: Int
        HostID: String
        Page: Pagination!
    ): [ReadFileSingleResponse!]!

    Search(
        HostIDs: [String!]
        Components: [String!]
        Limit: Int!
    ): [LogSearchResponse!]!
}

type LogSearchResponse {
    HostID: String!
    Results(
        FileFilter:  String!
        Pattern:     String!
    ): [SearchResult!]!
}

############
# Insights #
############
"Graph Analyzer config"
enum AppUserRole {
  owner
  editor
  viewer
  none
}

type Insights_AllApps {
    id: String!
    title: String!
    screenshot: String
    icon: String
    username: String!
    version: String!
    userRole: AppUserRole!
}

type Insights_APP {
    username: String!
    config: String!
    version: String!
    userRoleForApp: AppUserRole!
}

type Insights_AppPermission {
  access: Int!
  editors: [String!]!
  viewers: [String!]!
}

type Insights_Pages {
  appId: String!
  pageId: String!
  config: String!
  version: String!
}

type Insights_Widgets {
  appId: String!
  pageId: String!
  id: String!
  config: String!
  version: String!
}

type Insights_Import {
  appVersion: String!
  pagesVersion: [[String!]!]!
}

type Insights_Export {
  appConfig:     String!
	pageConfigs:   [String!]!
	widgetConfigs: [String!]!
}

type Insights_Exports {
    id: String!
    title: String!
    screenshot: String
    icon: String
    username: String!
    config: String!
}

##########
# Config #
##########
type Config {
  ZK: ZK
  System: System
  Security: Security
  RESTPP: Restpp
  Nginx: Nginx
#   KafkaStreamLL: KafkaStreamLl
  KafkaLoader: KafkaLoader
  KafkaConnect: KafkaConnect
#   Kafka: Kafka
  Informant: Informant
  Gadmin: Gadmin
  GUI: GUI
  GSQL: GSQL
  GSE: GSE
  GPE: GPE
  FileLoader: FileLoader
  Executor: Executor
  ETCD: ETCD
  Dict: Dict
#   Controller: Controller
  Admin: Admin
}

type Nodes {
  HostID: String!
  Partition: Int!
  Replica: Int!
}

type LogConfig {
  LogFileMaxDurationDay: Int!
  LogFileMaxSizeMB: Int!
  LogLevel: String!
  LogRotationFileNumber: Int!
}

type BasicConfig {
  Env: String!
  LogDirRelativePath: String!
  Nodes: [Nodes]!
  LogConfig: LogConfig
}

type ZK {
  DataRelativePath: String!
  ElectionPort: Int!
  ForceSync: Boolean!
  InitLimit: Int!
  Port: Int!
  QuorumPort: Int!
  StartTimeoutMS: Int!
  BasicConfig: BasicConfig
}

type User {
  Password: String!
  Privatekey: String!
  Username: String!
}

type SSH {
  ConfigFileRelativePath: String!
  Port: Int!
  User: User
}

type Metrics {
  CPUIntervalSec: Int!
  DiskspaceIntervalSec: Int!
  MemoryIntervalSec: Int!
  NetworkIntervalSec: Int!
  QPSIntervalSec: Int!
}

type HostList {
  Hostname: String!
  ID: String!
  Region: String!
}

type Event {
  EventInputTopic: String!
  EventOffsetFolderRelativePath: String!
  EventOutputTopic: String!
  MetricsTopic: String!
}

type ElasticCluster {
  Enabled: Boolean!
  MountPrefix: String!
}

type CrossRegionReplication {
  Enabled: Boolean!
  PrimaryKafkaIPs: String!
  PrimaryKafkaPort: Int!
  TopicPrefix: String!
}

type S3 {
  AWSAccessKeyID: String!
  AWSSecretAccessKey: String!
  BucketName: String!
  Enable: Boolean!
}

type Local {
  Enable: Boolean!
  Path: String!
}

type BackupConfig {
  CompressProcessNumber: Int!
  # wait for https://graphsql.atlassian.net/browse/TP-2079
#   TimeoutSec: Int!
  S3: S3
  Local: Local
}

type System {
  AppRoot: String!
  AuthToken: String!
  DataRoot: String!
  License: String!
  LogRoot: String!
  TempRoot: String!
  SSH: SSH
#   Metrics: Metrics
  HostList: [HostList]!
  Event: Event
  CrossRegionReplication: CrossRegionReplication
  Backup: BackupConfig
}

type Sp {
  Hostname: String!
  PrivateKey: String!
  X509Cert: String!
}

type Idp {
  EntityId: String!
  SSOUrl: String!
  X509Cert: String!
}

type Saml {
  AssertionSigned: Boolean!
  AuthnRequestSigned: Boolean!
  BuiltinUser: String!
  Enable: Boolean!
  MetadataSigned: Boolean!
  RequestedAuthnContext: String!
  ResponseSigned: Boolean!
  SignatureAlgorithm: String!
  SP: Sp
  IDP: Idp
}

type Op {
  ClientId: String!
  ClientSecret: String!
  JWKSUrl: String!
  SSOUrl: String!
  SigAlgorithm: String!
}

type Oidc {
  BuiltinUser: String!
  CallBackUrl: String!
  Enable: Boolean!
  OrganizationId: String!
  ResponseType: String!
  Scope: String!
  OP: Op
}

type Sso {
  SAML: Saml
  OIDC: Oidc
}

type Secure {
  Protocol: String!
  TrustAll: Boolean!
  TruststoreFormat: String!
  TruststorePassword: String!
  TruststorePath: String!
}

type Ldap {
  AdminDN: String!
  AdminPassword: String!
  BaseDN: String!
  Enable: Boolean!
  Hostname: String!
  Port: Int!
  SearchFilter: String!
  UsernameAttribute: String!
  Secure: Secure
}

type Security {
  SSO: Sso
  LDAP: Ldap
}

type WorkLoadManager {
  MaxHeavyBuiltinQueries: Int!
}

type HttpServer {
  Enable: Boolean!
  Port: Int!
  WorkerNum: Int!
}

type Factory {
  DefaultLoadingTimeoutSec: Int!
  DefaultQueryTimeoutSec: Int!
  DynamicEndpointRelativePath: String!
  DynamicSchedulerRelativePath: String!
  EnableAuth: Boolean!
  HandlerCount: Int!
  QueryMemoryLimitMB: Int!
  StatsIntervalSec: Int!
}

type Restpp {
  FCGISocketBackLogMaxCnt: Int!
  FCGISocketFileRelativePath: String!
  GPEResponsePort: Int!
  GSEResponsePort: Int!
#   LoadedOffsetTraceBackHr: Int!
  NginxPort: Int!
#   WorkLoadManager: WorkLoadManager
  HttpServer: HttpServer
  Factory: Factory
  BasicConfig: BasicConfig
}

type Ssl {
  Cert: String!
  Enable: Boolean!
  Key: String!
}

type ResponseHeaders {
  FieldName: String!
  FieldValue: String!
}

type Nginx {
  AllowedCIDRList: String!
  ClientMaxBodySize: Int!
  ConfigTemplate: String!
  Port: Int!
  ProxySSLVerify: Boolean!
  WorkerProcessNumber: Int!
  SSL: Ssl
  ResponseHeaders: [ResponseHeaders]!
  BasicConfig: BasicConfig
}

type KafkaStreamLl {
  MaxPartitionFetchBytes: Int!
  Port: Int!
#   ReplicaNumber: Int!
  StateDirRelativePath: String!
  BasicConfig: BasicConfig
}

type KafkaLoader {
  GPEResponseBasePort: Int!
  GSEResponseBasePort: Int!
  ReplicaNumber: Int!
  Factory: Factory
  BasicConfig: BasicConfig
}

type KafkaConnect {
  MaxRequestSize: Int!
  OffsetFlushIntervalMS: Int!
  Port: Int!
  ReconnectBackoffMS: Int!
  RetryBackoffMS: Int!
  BasicConfig: BasicConfig
}

type Kafka {
#   DataRelativePath: String!
  IOThreads: Int!
  LogFlushIntervalMS: Int!
  LogFlushIntervalMessage: Int!
  MessageMaxSizeMB: Int!
  MinInsyncReplicas: Int!
  NetworkThreads: Int!
  Port: Int!
  RetentionHours: Int!
  RetentionSizeGB: Int!
  StartTimeoutMS: Int!
  TopicReplicaFactor: Int!
  BasicConfig: BasicConfig
}

type Informant {
  DBRelativePath: String!
  GrpcPort: Int!
  RestPort: Int!
  RetentionPeriodDay: Int!
  BasicConfig: BasicConfig
}

type Gadmin {
  StartServiceDefaultTimeoutMS: Int!
  StartStopRequestTimeoutMS: Int!
  StopServiceDefaultTimeoutMS: Int!
}

type HttpRequest {
  RetryMax: Int!
  RetryWaitMaxSec: Int!
  RetryWaitMinSec: Int!
  TimeoutSec: Int!
}

type GraphQlConfig {
  SchemaRefreshPeriod: Int!
}

type Cookie {
  DurationSec: Int!
  SameSite: Int!
}

type GUI {
  ClientIdleTimeSec: Int!
  DataDirRelativePath: String!
  EnableDarkTheme: Boolean!
  GraphStatCheckIntervalSec: Int!
  Port: Int!

#   RESTPPResponseMaxSizeBytes: Int!
  TempDirRelativePath: String!
  TempFileMaxDurationDay: Int!
  HTTPRequest: HttpRequest
  GraphQLConfig: GraphQlConfig @deprecated(reason: "Never supported")
  Cookie: Cookie
  BasicConfig: BasicConfig
}

type UserInfoLimit {
  TokenSizeLimit: Int!
#   UserCatalogFileMaxSizeByte: Int!
  UserSizeLimit: Int!
}

type TokenCleaner {
  GraceTimeSec: Int!
  IntervalTimeSec: Int!
}

type GSQL {
  CatalogBackupFileMaxDurationDay: Int!
  CatalogBackupFileMaxNumber: Int!
  DataRelativePath: String!
  EnableStringCompress: Boolean!
  GithubBranch: String!
  GithubPath: String!
  GithubRepository: String!
  GithubUrl: String!
  GithubUserAcessToken: String!
  GrpcMessageMaxSizeMB: Int!
  ManageCatalogTimeoutSec: Int!
  MaxAuthTokenLifeTimeSec: Int!
  OutputTokenBufferSize: Int!
  Port: Int!
#   QueryResponseMaxSizeByte: Int!
  RESTPPRefreshTimeoutSec: Int!
  SchemaIndexFileNumber: Int!
  WaitServiceOnlineTimeoutSec: Int!
  UserInfoLimit: UserInfoLimit
  TokenCleaner: TokenCleaner
  FileOutputPolicy: [String!]!
  BasicConfig: BasicConfig
}

type GSE {
  IdRequestPort: Int!
  JournalTopicPrefix: String!
  LeaderElectionTTLSec: Int!
  RLSPort: Int!
  StopTimeoutMS: Int!
  BasicConfig: BasicConfig
}

type Disk {
  CompressMethod: String!
  DiskStoreRelativePath: String!
  LoadThreadNumber: Int!
  SaveThreadNumber: Int!
}

type GPE {
  EdgeDataMemoryLimit: Int!
  GPE2GPEResponsePort: Int!
  GPERequestPort: Int!
  IdResponsePort: Int!
  LeaderElectionTTLSec: Int!
  MemoryLimitMB: Int!
  NumberOfHashBucketInBit: Int!
  RebuildThreadNumber: Int!
  StopTimeoutMS: Int!
  VertexDataMemoryLimit: Int!
#   Kafka: Kafka
  Disk: Disk
  BasicConfig: BasicConfig
}

type FileLoader {
  GPEResponseBasePort: Int!
  GSEResponseBasePort: Int!
  ReplicaNumber: Int!
  Factory: Factory
  BasicConfig: BasicConfig
}

type Executor {
  DataRelativePath: String!
  FileTransferConcurrency: Int!
  FileTransferPort: Int!
  FileVersionNum: Int!
  Port: Int!
  WatchDogIntervalMS: Int!
  BasicConfig: BasicConfig
}

type ETCD {
  ClientPort: Int!
  DataRelativePath: String!
  ElectionTimeoutMS: Int!
  HeartbeatIntervalMS: Int!
  MaxRequestBytes: Int!
  MaxSnapshots: Int!
  MaxTxnOps: Int!
  MaxWals: Int!
  PeerPort: Int!
  SnapshotCount: Int!
  BasicConfig: BasicConfig
}

type Dict {
  Port: Int!
  BasicConfig: BasicConfig
}

type Connect {
  PruneIntervalMin: Int!
}

type Controller {
  ConfigRepoRelativePath: String!
  FileRepoRelativePath: String!
  FileRepoVersionNum: Int!
  LeaderElectionHeartBeatIntervalMS: Int!
  LeaderElectionHeartBeatMaxMiss: Int!
  Port: Int!
  Connect: Connect
  BasicConfig: BasicConfig
}

type Admin {
  Port: Int!
  BasicConfig: BasicConfig
}

type SetConfigResponse {
    SessionID: String!
    Diffs: String!
}