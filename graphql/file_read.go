package graphql

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/fs"
	h "github.com/tigergraph/gus/lib/http"
	mw "github.com/tigergraph/gus/middleware"
)

type ReadFileSingleResponse struct {
	Sd   ServiceDescriptor
	Data string
}

type ReadFileInput struct {
	FileMeta FileMetaInput
	Page     Pagination
}

func (q *Query) ReadFile(ctx context.Context, args ReadFileInput) ([]ReadFileSingleResponse, error) {
	log.Info(ctx, "ReadFile")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}
	if !q.hasGlobalPrivilege(model.APP_ACCESS_DATA) {
		return nil, errors.New(ERROR_UNAUTHORIZED)
	}

	ctx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	// check if file path is in white list and outside of blacklist
	cfg := q.Deps.Config
	requestURL := fmt.Sprintf(
		"%s://%s:%d/%s?path=%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		// remove gsqlserver prefix
		"/gsql/v1/internal/check/file-policy",
		args.FileMeta.FilePath,
	)
	res, err := requestToGSQL(ctx,
		http.MethodGet, requestURL, nil,
		q.creds, cfg.GetNginxSSLEnabled())
	if err != nil {
		return nil, err
	}
	if res.Error {
		return nil, errors.Errorf("%s", res.Message)
	}

	// convert hostID to replicat and partition number
	replica, partition, err := getReplicaAndPartition(q.Deps.Config.GetConfig(), args.FileMeta.Sd)
	if err != nil {
		return nil, err
	}

	// read the file
	fileRead, err := q.Deps.T2pClient.ReadFile(ctx, &pb.ReadFileRequest{
		Meta: &pb.FileMeta{
			FilePath: args.FileMeta.FilePath,
			Sd: &pb.ServiceDescriptor{
				ServiceName: args.FileMeta.Sd.ServiceName,
				Replica:     replica,
				Partition:   partition,
			},
		},
		Offset: int64(args.Page.Offset),
		Length: int64(args.Page.Length),
	})
	if err != nil {
		return nil, err
	}

	if fileRead.Error != nil {
		if fileRead.Error.Message != "" {
			return nil, fileRead.Error
		}
	}

	var resp []ReadFileSingleResponse
	for _, r := range fileRead.Responses {
		resp = append(resp, ReadFileSingleResponse{
			Sd: ServiceDescriptor{
				ServiceName: r.Sd.ServiceName,
				Replica:     r.Sd.Replica,
				Partition:   r.Sd.Partition,
			},
			Data: string(r.Data),
		})
	}

	return resp, nil
}

func requestToGSQL(c context.Context,
	method string, requestURL string, requestBody io.Reader, creds *model.UserCredentials, nginxSSLEnabled bool,
) (Response, error) {

	req, _ := http.NewRequestWithContext(c, method, requestURL, requestBody)

	h.SetCredentials(req, creds)
	h.SetFromGraphStudio(req)
	h.SetForwardedForFromContext(c, req)

	client := h.NewClient(&h.ClientConfigs{
		InsecureSkipVerify: nginxSSLEnabled, // skip verify for internal GSQL server requests.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})

	resp, err := client.Do(req)
	if err != nil {
		return Response{}, errors.WithMessage(err, "Failed to get response from GSQL server")
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return Response{}, errors.WithMessage(err, "Failed to get response from GSQL server")
	}

	res := Response{}
	if err := json.Unmarshal(body, &res); err != nil {
		return Response{}, errors.WithMessage(err, "Failed to parse response from GSQL server")
	}
	if res.Results == nil {
		_ = json.Unmarshal(body, &res.Results)
	}
	return res, nil
}

type Response struct {
	Error   bool        `json:"error"`
	Message interface{} `json:"message"` // use generic because this can be an object.
	Results interface{} `json:"results"`
}

func DownloadGSQLOutput(c *gin.Context) {

	cfg := mw.GetConfig(c)
	src := c.Query("filePath")
	ctx, cancel := context.WithTimeout(c, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	// check if file path is in white list and outside of blacklist
	requestURL := fmt.Sprintf(
		"%s://%s:%d/%s?path=%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		"/gsql/v1/internal/check/file-policy",
		src,
	)
	res, err := requestToGSQL(ctx,
		http.MethodGet, requestURL, nil,
		mw.GetUserCredentials(c), cfg.GetNginxSSLEnabled())
	if err != nil {
		mw.Abort(c, http.StatusInternalServerError, err.Error())
		return
	}
	if res.Error {
		mw.Abort(c, http.StatusInternalServerError, res.Message.(string))
		return
	}

	tgFS := fs.NewTGFilesystem(cfg)

	tempDir, err := os.MkdirTemp(cfg.GetTempDirPath(), "gsql_output_*")
	if err != nil {
		log.Errorf(c, "Failed to create temporary directory %s: %v", tempDir, err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to create temporary directory for gsql output.")
		return
	}
	defer os.RemoveAll(tempDir)

	srcHostIDs := make([]string, 0)
	hostID := c.Query("hostID")
	exeHostIDs, err := cfg.GetAllEXEHostIDs()
	if err != nil {
		log.Errorf(c, "Failed to get all exe host IDs: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get all exe host IDs.")
		return
	}

	if hostID != "" {
		srcHostIDs = append(srcHostIDs, hostID)
	} else {
		// if hostID is not provided, download from all exe hosts
		srcHostIDs = append(srcHostIDs, exeHostIDs...)
	}

	localHostID, err := cfg.GetHostID()
	if err != nil {
		log.Errorf(c, "Failed to get local host ID: %v", err)
		mw.Abort(c, http.StatusInternalServerError, "Failed to get local host ID.")
		return
	}

	for _, srcHostID := range srcHostIDs {
		// copy from aaa.txt to /tmp/gsql_output_<timestamp>/hostID/aaa.txt
		dst := filepath.Join(tempDir, fmt.Sprintf("%s_%s", srcHostID, filepath.Base(src)))
		if err := tgFS.CopyFile(src, srcHostID, dst, []string{localHostID}, fs.FileTypeFile); err != nil {
			log.Errorf(c, "Failed to copy %s to %s: %v", src, dst, err)
			mw.Abort(c, http.StatusInternalServerError, fmt.Sprintf("Failed to copy gsql output file:%s from %s to %s.", src, srcHostID, localHostID))
			return
		}
	}

	filename := fmt.Sprintf("%s.tar.gz", filepath.Base(src))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))

	c.Stream(func(w io.Writer) bool {
		if err := fs.Compress(tempDir, w); err != nil {
			log.Errorf(c, "Failed to compress file with path %s: %v", tempDir, err)
		}
		return false
	})
}
