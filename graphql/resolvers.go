package graphql

import (
	"github.com/pkg/errors"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
)

func NewQuery(Deps Deps, userInfo *model.UserInfo, username string, password string, authType model.AuthType, reqID string) (*Query, error) {
	if Deps.Config == nil {
		return nil, errors.New(ERROR_EMPTYCONFIG)
	}
	return &Query{
		userInfo: userInfo,
		username: username,
		password: password,
		authType: authType,
		reqID:    reqID,
		Deps:     Deps,
	}, nil
}

type Query struct {
	userInfo *model.UserInfo
	username string
	password string
	authType model.AuthType
	reqID    string
	Deps     Deps
}

func (q Query) isAuthenticated() error {
	if q.userInfo == nil {
		return errors.New(ERROR_UNAUTHENTICATED)
	}
	return nil
}

func (q Query) hasGlobalPrivilege(privilege model.Privilege) bool {
	if q.userInfo == nil {
		return false
	}
	for _, p := range q.userInfo.Privileges["1"].Privileges {
		if p == privilege {
			return true
		}
	}
	return false
}

// Dependencies
type Deps struct {
	T2pClient  pb.ControllerClient
	Config     *config.Config
	DaoManager interfaces.DaoManager
}

type ServiceDescriptor struct {
	deps        Deps
	ServiceName string
	Replica     int32
	Partition   int32
}

func (sd ServiceDescriptor) HostID() (string, error) {
	hostIDs, err := sd.deps.Config.ResolveServiceHostID(service.ServiceDescriptor{
		ServiceDescriptor: &pb.ServiceDescriptor{
			ServiceName: sd.ServiceName,
			Replica:     sd.Replica,
			Partition:   sd.Partition,
		},
	})
	return hostIDs[0], err
}
