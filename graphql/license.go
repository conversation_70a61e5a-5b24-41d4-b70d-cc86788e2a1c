package graphql

import (
	"context"
	_ "embed"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/pkg/errors"
	tgCfg "github.com/tigergraph/cqrs/tutopia/common/config"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"
	"github.com/tigergraph/gotools/log"
)

func (q *Query) License(ctx context.Context) (*SystemLicense, error) {
	log.Info(ctx, "License")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}

	licenseStr, err := pullLicenseStr(q.Deps.T2pClient)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to fetch license info")
	}
	if licenseStr == "" {
		return &SystemLicense{Status: LicenseStatusNoLicense}, nil
	}

	license, err := parseLicense(licenseStr)
	if err != nil {
		return nil, errors.WithMessage(err, "Parse license failed")
	}
	if err2 := validateLicense(q.Deps.T2pClient); err2 != nil {
		license.Status = LicenseStatusInvalid
		if license.GST.Enable {
			if int64(license.EndTime) < time.Now().Unix() {
				license.Status = LicenseStatusExpired
			}
		}
	} else {
		license.Status = LicenseStatusValid
	}
	return license, nil
}

func pullLicenseStr(cntlrClient pb.ControllerClient) (string, error) {

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	resp, err := cntlrClient.PullConfig(ctx, &pb.PullConfigRequest{Type: pb.ConfigType_AppliedConfig})
	if err != nil {
		return "", rpc.Error(err, tgServ.CONTROLLER, cntlrClient.PullConfig)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return "", tgErr.NewErrFromPbError(resp.GetError())
	}

	cfg, err := tgCfg.NewConfigFromPb(resp.Config)
	if err != nil {
		return "", err
	}

	licenseStr, err := cfg.GetEntryAsString("System.License", false)
	if err != nil {
		return "", errors.WithMessage(err, "Failed to get config value for key System.License")
	}
	return licenseStr, nil
}

func validateLicense(cntlrClient pb.ControllerClient) error {

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	resp, err := cntlrClient.GetLicense(ctx, &pb.PullConfigRequest{Type: pb.ConfigType_AppliedConfig})
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.GetLicense)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

func parseLicense(licenseStr string) (*SystemLicense, error) {
	lic := &SystemLicense{}
	// 1. old license file do not have license for tools app, set tools app license default to true, so existing user can continue to use tools app.
	// 2. new license file do have license for tools app (either true or false), so will overwrite the default value.
	lic.Insights.Enable = true
	lic.GSQLShell.Enable = true
	lic.GraphQL.Enable = true
	_, _, err := new(jwt.Parser).ParseUnverified(licenseStr, lic)
	return lic, err
}

type LicenseStatus string

const (
	LicenseStatusValid     LicenseStatus = "Valid"
	LicenseStatusInvalid   LicenseStatus = "Invalid"
	LicenseStatusExpired   LicenseStatus = "Expired"
	LicenseStatusNoLicense LicenseStatus = "No License"
)

type SystemLicense struct {
	Issuer    string
	Audience  string
	StartTime float64
	EndTime   float64
	IssueTime float64
	Edition   string
	Version   string
	Host      struct {
		MaxCPUCore             float64
		MaxPhysicalMemoryBytes float64
		MaxClusterNodeNumber   float64
	}
	Topology struct {
		MaxVertexNumber  float64
		MaxEdgeNumber    float64
		MaxGraphNumber   float64
		MaxTopologyBytes float64
	}
	GST struct {
		Enable            bool
		ZoomChartsLicense string
	}
	Insights struct {
		Enable bool
	}
	GSQLShell struct {
		Enable bool
	}
	GraphQL struct {
		Enable bool
	}
	RuntimeMemory struct {
		MaxUserResidentSetBytes float64
	}
	Status LicenseStatus
}

func (SystemLicense) Valid() error {
	return nil
}
