package graphql

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	cqrsErr "github.com/tigergraph/cqrs/util/errors"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/dao/model"
)

const (
	SEARCH_FILE_TIMEOUT = 5 * time.Minute
)

type SearchFileSingleResponse struct {
	Sd           ServiceDescriptor
	SearchResult []SearchResult
}

type SearchResult struct {
	Path       string
	Line       string
	Offset     int32
	LineNumber int32
}

type SearchFileInput struct {
	FileMeta   FileMetaInput
	FileFilter string
	Pattern    string
	Limit      int32
}

func (q *Query) SearchFile(
	ctx context.Context,
	args SearchFileInput,
) ([]SearchFileSingleResponse, error) {
	log.Info(ctx, "SearchFile")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}
	if !q.hasGlobalPrivilege(model.APP_ACCESS_DATA) {
		return nil, errors.New(ERROR_UNAUTHORIZED)
	}

	ctx, cancel := context.WithTimeout(ctx, SEARCH_FILE_TIMEOUT)
	defer cancel()

	replica, partition, err := getReplicaAndPartition(q.Deps.Config.GetConfig(), args.FileMeta.Sd)
	if err != nil {
		return nil, err
	}

	req := &pb.SearchFileRequest{
		Location: &pb.SearchFileLocation{
			Meta: &pb.FileMeta{
				FilePath: args.FileMeta.FilePath,
				Sd: &pb.ServiceDescriptor{
					ServiceName: args.FileMeta.Sd.ServiceName,
					Replica:     replica,
					Partition:   partition,
				},
			},
			FileFilter: args.FileFilter,
			Recursive:  true,
			Offset:     0,
		},
		Pattern: args.Pattern,
		Limit:   args.Limit,
	}
	resp, err := q.Deps.T2pClient.SearchFile(ctx, req)
	if err != nil {
		return nil, err
	}
	if resp.GetError() != nil || resp.GetError().GetCode() != cqrsErr.EcodeOk {
		return nil, errors.WithStack(resp.GetError())
	}

	var files []SearchFileSingleResponse
	for i, r := range resp.GetResponses() {
		files = append(files, SearchFileSingleResponse{
			Sd: ServiceDescriptor{
				ServiceName: r.Sd.ServiceName,
				Replica:     r.Sd.Replica,
				Partition:   r.Sd.Partition,
			},
		})
		for _, rr := range r.GetResults() {
			files[i].SearchResult = append(files[i].SearchResult, SearchResult{
				Path: rr.Path,
				Line: string(rr.Line),
				//nolint:gosec // #nosec G115: integer overflow conversion int64 -> uint32
				Offset: int32(rr.Offset),
				//nolint:gosec // #nosec G115: integer overflow conversion int64 -> uint32
				LineNumber: int32(rr.LineNumber),
			})
		}
	}
	return files, err
}
