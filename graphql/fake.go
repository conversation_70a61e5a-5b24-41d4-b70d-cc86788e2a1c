package graphql

import (
	"context"
	"strings"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	grpc "google.golang.org/grpc"
)

type fakeControllerClient struct {
	pb.ControllerClient
	data map[string][]byte
}

func (f *fakeControllerClient) KVPut(ctx context.Context, in *pb.KVPutRequest, opts ...grpc.CallOption) (*pb.KVPutResponse, error) {
	if f.data == nil {
		f.data = make(map[string][]byte)
	}

	for i := range in.Kvs {
		key := in.Kvs[i].Key
		value := in.Kvs[i].Value

		var isExist bool = false
		for Key := range f.data {
			if Key == string(key) {
				isExist = true
			}
		}

		if isExist {
			f.data[string(key)] = []byte(value)
		} else if !isExist {
			f.data[string(key)] = []byte(value)
		}
	}

	return &pb.KVPutResponse{
		Error: nil,
	}, nil
}

func (f *fakeControllerClient) KVGet(ctx context.Context, in *pb.KVGetRequest, opts ...grpc.CallOption) (*pb.KVGetResponse, error) {
	maps := make(map[string][]byte)

	if in.Prefix {
		for key, value := range f.data {
			if strings.HasPrefix(key, string(in.Keys[0])) {
				maps[key] = value
			}
		}
	} else if !in.Prefix {
		key := string(in.Keys[0])
		value, ok := f.data[key]
		if ok {
			maps[key] = value
		}
	}

	var kvs []*pb.KVPair
	for key, value := range maps {
		kv := &pb.KVPair{
			Key:   []byte(key),
			Value: value,
		}
		kvs = append(kvs, kv)
	}

	return &pb.KVGetResponse{
		Kvs:   kvs,
		Error: nil,
	}, nil
}

func (f *fakeControllerClient) KVDelete(ctx context.Context, in *pb.KVDeleteRequest, opts ...grpc.CallOption) (*pb.KVDeleteResponse, error) {
	if in.Prefix {
		for key := range f.data {
			if strings.HasPrefix(key, string(in.Keys[0])) {
				delete(f.data, key)
			}
		}
	} else if !in.Prefix {
		key := string(in.Keys[0])
		_, ok := f.data[key]
		if ok {
			delete(f.data, key)
		}
	}

	return &pb.KVDeleteResponse{}, nil
}
