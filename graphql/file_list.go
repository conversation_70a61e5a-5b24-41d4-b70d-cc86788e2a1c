package graphql

import (
	"context"
	"net/url"
	"path/filepath"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/cqrs/tutopia/common/config"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao/model"
)

type ListFileInput struct {
	FileMeta       FileMetaInput
	FileRegxFilter string
}

func (q *Query) ListFile(
	ctx context.Context,
	args ListFileInput,
) ([]ListFileSingleResponse, error) {
	log.Info(ctx, "ListFile")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}
	if !q.hasGlobalPrivilege(model.APP_ACCESS_DATA) {
		return nil, errors.New(ERROR_UNAUTHORIZED)
	}

	tmpCtx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	replica, partition, err := getReplicaAndPartition(q.Deps.Config.GetConfig(), args.FileMeta.Sd)
	if err != nil {
		return nil, err
	}

	res, err := q.Deps.T2pClient.ListFile(tmpCtx, &pb.ListFileRequest{
		Meta: &pb.FileMeta{
			FilePath: args.FileMeta.FilePath,
			Sd: &pb.ServiceDescriptor{
				ServiceName: args.FileMeta.Sd.ServiceName,
				Replica:     replica,
				Partition:   partition,
			},
		},
		FileRegxFilter: args.FileRegxFilter,
	})
	if err != nil {
		return nil, err
	}

	// dealing with symbolic link
	if len(res.Responses) == 1 && len(res.Responses[0].Infos) == 1 {
		info := res.Responses[0].Infos[0]
		if info.Path == args.FileMeta.FilePath && info.SourcePath != "" {
			dir, _ := filepath.Split(args.FileMeta.FilePath)
			sourceDir, base := filepath.Split(info.SourcePath)
			if sourceDir == "" { // source is at the same dir of symbolic link
				info.SourcePath = filepath.Join(dir, base)
			}
			args.FileMeta.FilePath = info.SourcePath
			return q.ListFile(ctx, args)
		}
	}

	var resp []ListFileSingleResponse
	for i, r := range res.Responses {
		resp = append(resp, ListFileSingleResponse{
			Infos: []FileInfo{},
			Sd: ServiceDescriptor{
				deps:        q.Deps,
				ServiceName: r.Sd.ServiceName,
				Replica:     r.Sd.Replica,
				Partition:   r.Sd.Partition,
			},
		})
		for _, info := range r.Infos {
			resp[i].Infos = append(resp[i].Infos, NewFileInfo(*info, q.Deps, q.userInfo, q.username, q.password, *r.Sd))
		}
	}
	return resp, nil
}

type ListFileSingleResponse struct {
	Infos []FileInfo
	Sd    ServiceDescriptor
}

type FileInfo struct {
	deps     Deps
	userInfo *model.UserInfo
	username string
	password string
	Sd       ServiceDescriptor
	Name     string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	Path     string `protobuf:"bytes,2,opt,name=Path,proto3" json:"Path,omitempty"`
	Size     int32  `protobuf:"varint,3,opt,name=Size,proto3" json:"Size,omitempty"`
	IsDir    bool   `protobuf:"varint,4,opt,name=IsDir,proto3" json:"IsDir,omitempty"`
	ModTime  int32  `protobuf:"varint,5,opt,name=ModTime,proto3" json:"ModTime,omitempty"`
	// if it is link, then SourcePath is the path it points to(it will not follow through)
	// otherwise, it will be empty
	SourcePath string `protobuf:"bytes,6,opt,name=SourcePath,proto3" json:"SourcePath,omitempty"`
}

func (f FileInfo) Content(
	ctx context.Context,
	args struct {
		Page *Pagination
	},
) (string, error) {
	q := Query{Deps: f.deps, userInfo: f.userInfo, username: f.username, password: f.password}

	var offset int32 = 0
	var length int32 = f.Size
	if args.Page != nil {
		offset = args.Page.Offset
		length = args.Page.Length
	}

	res, err := q.ReadFile(ctx, ReadFileInput{
		FileMeta: FileMetaInput{
			FilePath: f.Path,
			Sd: ServiceDescriptorInput{
				ServiceName: f.Sd.ServiceName,
				Partition:   &f.Sd.Partition,
				Replica:     &f.Sd.Replica,
			},
		},
		Page: Pagination{
			Offset: offset,
			Length: length,
		},
	})
	if err != nil {
		return "", err
	}

	return string(res[0].Data), nil
}

func (f FileInfo) DownloadUrl() (string, error) {
	u := url.URL{}
	u.Path = "/api/v2/download_gsql_output"
	v := make(url.Values)
	v.Set("filePath", f.Path)
	hostID, err := f.Sd.HostID()
	if err != nil {
		return "", err
	}
	v.Set("hostID", hostID)
	u.RawQuery = v.Encode()
	return u.RequestURI(), nil
}

func NewFileInfo(info pb.FileInfo, deps Deps, userInfo *model.UserInfo, username, password string, Sd pb.ServiceDescriptor) FileInfo {
	return FileInfo{
		deps:     deps,
		userInfo: userInfo,
		username: username,
		password: password,
		Sd: ServiceDescriptor{
			deps:        deps,
			ServiceName: Sd.ServiceName,
			Replica:     Sd.Replica,
			Partition:   Sd.Partition,
		},
		Name: info.Name,
		Path: info.Path,
		//nolint:gosec // #nosec G115: integer overflow conversion int64 -> uint32
		Size:  int32(info.Size),
		IsDir: info.IsDir,
		//nolint:gosec // #nosec G115: integer overflow conversion int64 -> uint32
		ModTime:    int32(info.ModTime),
		SourcePath: info.SourcePath,
	}
}

func getReplicaAndPartition(c *config.Config, srvDesc ServiceDescriptorInput) (int32, int32, error) {
	var replica, partition int32 = 0, 0
	if srvDesc.Replica != nil {
		replica = *srvDesc.Replica
	}
	if srvDesc.Partition != nil {
		partition = *srvDesc.Partition
	}
	if srvDesc.HostID != nil && *srvDesc.HostID != "" {
		var err error
		replica, partition, err = getReplicaAndPartitionFromHostIdOfServiceName(c, *srvDesc.HostID, srvDesc.ServiceName)
		if err != nil {
			return replica, partition, err
		}
	}

	return replica, partition, nil
}

func getReplicaAndPartitionFromHostIdOfServiceName(
	c *config.Config, hostID string, serviceName string,
) (replica int32, partition int32, err error) {
	switch strings.ToUpper(serviceName) {
	case tgServ.ADMIN:
		replica, partition = replica_partition(c.ProtoConf.Admin.BasicConfig.Nodes, hostID)
	case tgServ.CONTROLLER:
		replica, partition = replica_partition(c.ProtoConf.Controller.BasicConfig.Nodes, hostID)
	case tgServ.DICT:
		replica, partition = replica_partition(c.ProtoConf.Dict.BasicConfig.Nodes, hostID)
	case tgServ.ETCD:
		replica, partition = replica_partition(c.ProtoConf.ETCD.BasicConfig.Nodes, hostID)
	case tgServ.EXECUTOR:
		replica, partition = replica_partition(c.ProtoConf.Executor.BasicConfig.Nodes, hostID)
	case tgServ.FILELOADER:
		replica, partition = replica_partition(c.ProtoConf.FileLoader.BasicConfig.Nodes, hostID)
	case tgServ.GPE:
		replica, partition = replica_partition(c.ProtoConf.GPE.BasicConfig.Nodes, hostID)
	case tgServ.GSE:
		replica, partition = replica_partition(c.ProtoConf.GSE.BasicConfig.Nodes, hostID)
	case tgServ.GSQL:
		replica, partition = replica_partition(c.ProtoConf.GSQL.BasicConfig.Nodes, hostID)
	case tgServ.GUI:
		replica, partition = replica_partition(c.ProtoConf.GUI.BasicConfig.Nodes, hostID)
	case tgServ.INFORMANT:
		replica, partition = replica_partition(c.ProtoConf.Informant.BasicConfig.Nodes, hostID)
	case tgServ.KAFKA:
		replica, partition = replica_partition(c.ProtoConf.Kafka.BasicConfig.Nodes, hostID)
	case tgServ.KAFKACONN:
		replica, partition = replica_partition(c.ProtoConf.KafkaConnect.BasicConfig.Nodes, hostID)
	case tgServ.KAFKALOADER:
		replica, partition = replica_partition(c.ProtoConf.KafkaLoader.BasicConfig.Nodes, hostID)
	case tgServ.KAFKASTRMLL:
		replica, partition = replica_partition(c.ProtoConf.KafkaStreamLL.BasicConfig.Nodes, hostID)
	case tgServ.NGINX:
		replica, partition = replica_partition(c.ProtoConf.Nginx.BasicConfig.Nodes, hostID)
	case tgServ.RESTPP:
		replica, partition = replica_partition(c.ProtoConf.RESTPP.BasicConfig.Nodes, hostID)
	case tgServ.TS3:
		replica, partition = replica_partition(c.ProtoConf.TS3.BasicConfig.Nodes, hostID)
	case tgServ.TS3SERV:
		replica, partition = replica_partition(c.ProtoConf.TS3Server.BasicConfig.Nodes, hostID)
	case tgServ.ZK:
		replica, partition = replica_partition(c.ProtoConf.ZK.BasicConfig.Nodes, hostID)
	default:
		return 0, 0, nil
	}
	if replica == -1 && partition == -1 {
		return -1, -1, errors.Errorf("%s does not exist in %s", serviceName, hostID)
	}
	return replica, partition, nil
}

func replica_partition(nodeInfoSlice []*pb.NodeInfo, hostID string) (replica int32, partition int32) {
	for _, nodeInfo := range nodeInfoSlice {
		if nodeInfo.HostID == hostID {
			return nodeInfo.Replica, nodeInfo.Partition
		}
	}
	return -1, -1
}
