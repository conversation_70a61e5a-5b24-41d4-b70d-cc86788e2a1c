package graphql

import (
	"context"
	"math"
	"path"
	"path/filepath"
	"strings"

	"github.com/pkg/errors"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao/model"
)

type LogResponse struct {
	deps     Deps
	userInfo *model.UserInfo
	password string
}

func (q *Query) Log(ctx context.Context) (LogResponse, error) {
	log.Info(ctx, "Log")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return LogResponse{}, err
	}
	if !q.userInfo.IsSuperUser {
		return LogResponse{}, errors.New("superuser is required")
	}

	return LogResponse{deps: q.Deps, userInfo: q.userInfo, password: q.password}, nil
}

func (l LogResponse) List(
	ctx context.Context,
	args struct {
		FilePath       string
		ServiceName    string
		Replica        *int32
		Partition      *int32
		HostID         *string
		FileRegxFilter string
	},
) ([]ListFileSingleResponse, error) {
	log.Info(ctx, "LogList")

	// arguments validation
	if args.Replica != nil && *args.Replica < 0 {
		return nil, errors.New("Replica should be greater than or equal to 0.")
	}
	if args.Partition != nil && *args.Partition < 0 {
		return nil, errors.New("Partition should be greater than or equal to 0.")
	}

	ctx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	q := Query{Deps: l.deps, userInfo: l.userInfo}

	// prevent path traversal
	filePath := path.Join(q.Deps.Config.GetLogRootDirPath(), args.FilePath)
	abs, err := filepath.Abs(filePath)
	if err != nil {
		log.Error(ctx, err)
		return nil, err
	}
	if !strings.HasPrefix(abs, q.Deps.Config.GetLogRootDirPath()) {
		return nil, errors.Errorf("Path '%s' is not inside %s", args.FilePath, q.Deps.Config.GetLogRootDirPath())
	}

	list, err := q.ListFile(ctx, ListFileInput{
		FileMeta: FileMetaInput{
			FilePath: filePath,
			Sd: ServiceDescriptorInput{
				ServiceName: args.ServiceName,
				Replica:     args.Replica,
				Partition:   args.Partition,
				HostID:      args.HostID,
			},
		},
		FileRegxFilter: args.FileRegxFilter,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (l LogResponse) Content(
	ctx context.Context,
	args struct {
		FilePath    string
		ServiceName string
		Replica     *int32
		Partition   *int32
		HostID      *string
		Page        Pagination
	},
) ([]ReadFileSingleResponse, error) {
	log.Info(ctx, "LogContent")

	// arguments validation
	if args.Replica != nil && *args.Replica < 0 {
		return nil, errors.New("Replica should be greater than or equal to 0.")
	}
	if args.Partition != nil && *args.Partition < 0 {
		return nil, errors.New("Partition should be greater than or equal to 0.")
	}
	if args.Page.Offset < 0 {
		return nil, errors.New("Offset should be greater than or equal to 0.")
	}
	if args.Page.Length < 0 {
		return nil, errors.New("Length should be greater than or equal to 0.")
	}

	ctx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	q := Query{Deps: l.deps, userInfo: l.userInfo, password: l.password}

	// prevent path traversal
	filePath := path.Join(q.Deps.Config.GetLogRootDirPath(), args.FilePath)
	abs, err := filepath.Abs(filePath)
	if err != nil {
		log.Error(ctx, err)
		return nil, err
	}
	if !strings.HasPrefix(abs, q.Deps.Config.GetLogRootDirPath()) {
		return nil, errors.Errorf("Path '%s' is not inside %s", args.FilePath, q.Deps.Config.GetLogRootDirPath())
	}
	// convert hostID to replicat and partition number
	replica, partition, err := getReplicaAndPartition(q.Deps.Config.GetConfig(), ServiceDescriptorInput{
		ServiceName: args.ServiceName,
		Replica:     args.Replica,
		Partition:   args.Partition,
		HostID:      args.HostID,
	})
	if err != nil {
		return nil, err
	}

	fileRead, err := q.Deps.T2pClient.ReadFile(ctx, &pb.ReadFileRequest{
		Meta: &pb.FileMeta{
			FilePath: filePath,
			Sd: &pb.ServiceDescriptor{
				ServiceName: args.ServiceName,
				Replica:     replica,
				Partition:   partition,
			},
		},
		Offset: int64(args.Page.Offset),
		Length: int64(args.Page.Length),
	})
	if err != nil {
		return nil, err
	}

	if fileRead.Error != nil {
		if fileRead.Error.Message != "" {
			return nil, fileRead.Error
		}
	}

	var resp []ReadFileSingleResponse
	for _, r := range fileRead.Responses {
		resp = append(resp, ReadFileSingleResponse{
			Sd: ServiceDescriptor{
				ServiceName: r.Sd.ServiceName,
				Replica:     r.Sd.Replica,
				Partition:   r.Sd.Partition,
			},
			Data: string(r.Data),
		})
	}
	return resp, nil
}

type SearchInput struct {
	HostIDs    *[]string
	Components *[]string
	Limit      int32
}

func (l LogResponse) Search(ctx context.Context, args SearchInput) ([]LogSearchResponse, error) {
	log.Info(ctx, "Search")

	// arguments validation
	if args.Limit < 0 {
		return nil, errors.New("Limit should be greater than or equal to 0.")
	}

	ctx, cancel := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	q := Query{Deps: l.deps, userInfo: l.userInfo}

	hostIDs, cmpts, err := getHostAndCmptList(ctx, q, args)
	if err != nil {
		return nil, err
	}

	logSearchResponses := make([]LogSearchResponse, len(hostIDs)*len(cmpts))
	limit := int32(math.Round(float64(args.Limit) / float64(len(hostIDs))))
	for i, hostID := range hostIDs {
		for j, cmpt := range cmpts {
			k := i*len(cmpts) + j
			logSearchResponses[k] = LogSearchResponse{
				deps:      q.Deps,
				userInfo:  q.userInfo,
				component: cmpt,
				limit:     limit,
				HostID:    hostID,
			}
		}
	}

	return logSearchResponses, nil
}

type LogSearchResponse struct {
	deps      Deps
	userInfo  *model.UserInfo
	component string
	limit     int32
	HostID    string
}

type ResultsInput struct {
	FileFilter string
	Pattern    string
}

func (l LogSearchResponse) Results(ctx context.Context, args ResultsInput) ([]SearchResult, error) {
	log.Info(ctx, "Results")

	q := Query{Deps: l.deps, userInfo: l.userInfo}

	descs, err := q.Deps.Config.GetServDescFromHostID(l.HostID)
	if err != nil {
		return nil, err
	}
	if len(descs) < 1 {
		return nil, errors.New("Not found service descriptor.")
	}
	desc := descs[0].ServiceDescriptor

	// prevent path traversal
	filePath := path.Join(q.Deps.Config.GetLogRootDirPath(), l.component)
	abs, err := filepath.Abs(filePath)
	if err != nil {
		log.Error(ctx, err)
		return nil, err
	}
	if !strings.HasPrefix(abs, q.Deps.Config.GetLogRootDirPath()) {
		return nil, errors.Errorf("Path '%s' is not inside %s", l.component, q.Deps.Config.GetLogRootDirPath())
	}

	searchResult, err := q.SearchFile(ctx, SearchFileInput{
		FileMeta: FileMetaInput{
			FilePath: filePath,
			Sd: ServiceDescriptorInput{
				ServiceName: desc.ServiceName,
				Replica:     &desc.Replica,
				Partition:   &desc.Partition,
				HostID:      &l.HostID,
			},
		},
		FileFilter: args.FileFilter,
		Pattern:    args.Pattern,
		Limit:      l.limit,
	})
	if err != nil {
		return nil, err
	}

	results := make([]SearchResult, 0)
	for _, r := range searchResult {
		results = append(results, r.SearchResult...)
	}

	return results, nil
}

func getHostAndCmptList(ctx context.Context, q Query, args SearchInput) ([]string, []string, error) {
	var hostIDs []string
	if args.HostIDs == nil {
		hostIDs = q.Deps.Config.GetAllHostIDs()
	} else {
		hostIDs = *args.HostIDs
	}
	var cmpts []string
	if args.Components == nil {
		var err error
		cmpts, err = listCmptsUnderLogRoot(ctx, q)
		if err != nil {
			return nil, nil, err
		}
	} else {
		cmpts = *args.Components
	}
	return hostIDs, cmpts, nil
}

func listCmptsUnderLogRoot(ctx context.Context, q Query) ([]string, error) {
	defaultValue := int32(0)
	list, err := q.ListFile(ctx, ListFileInput{
		FileMeta: FileMetaInput{
			FilePath: q.Deps.Config.GetLogRootDirPath(),
			Sd: ServiceDescriptorInput{
				ServiceName: "EXE",
				Replica:     &defaultValue,
				Partition:   &defaultValue,
			},
		},
		FileRegxFilter: "",
	})
	if err != nil {
		return nil, err
	}

	cmpts := make([]string, 0)
	if len(list) > 0 {
		set := make(map[string]struct{})
		m := struct{}{}
		for _, e := range list {
			for _, v := range e.Infos {
				set[v.Name] = m
			}
		}
		cmpts = make([]string, 0, len(set))
		for k := range set {
			cmpts = append(cmpts, k)
		}
	}
	return cmpts, nil
}
