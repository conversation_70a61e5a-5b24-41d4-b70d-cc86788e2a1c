package graphql

import (
	"context"

	"github.com/pkg/errors"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
)

func (q Query) Config(ctx context.Context) (*pb.Configuration, error) {
	log.Info(ctx, "Config")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return &pb.Configuration{}, err
	}
	if !q.userInfo.IsSuperUser {
		return &pb.Configuration{}, errors.New(ERROR_UNAUTHORIZED)
	}

	return q.Deps.Config.GetConfig().ProtoConf, nil
}

func (q Query) SetConfig(ctx context.Context, args struct {
	Name  string
	Value string
}) (*SetConfigResponse, error) {
	log.Info(ctx, "SetConfig")

	// resolver level authorization
	if err := q.isAuthenticated(); err != nil {
		return nil, err
	}
	if !q.userInfo.IsSuperUser {
		return nil, errors.New(ERROR_UNAUTHORIZED)
	}

	var res *pb.ConfigResponse
	var err error
	req := &pb.SetConfigRequest{
		SpanId:  q.reqID,
		Entries: []*pb.ConfigEntry{{Key: args.Name, Value: args.Value}},
	}
	if args.Name == "System.License" {
		res, err = q.Deps.T2pClient.SetLicenseConfig(ctx, req)
	} else {
		res, err = q.Deps.T2pClient.SetConfig(ctx, req)
	}
	if err != nil {
		log.Warnf(ctx, "%+v", err)
		return nil, err
	}
	if res.GetError() != nil {
		log.Warnf(ctx, "%+v", res.GetError())
		return nil, res.GetError()
	}
	return &SetConfigResponse{
		ConfigResponse: res,
		deps:           q.Deps,
	}, nil

}

type SetConfigResponse struct {
	*pb.ConfigResponse
	deps Deps
}

func (s SetConfigResponse) Diffs(c context.Context) (string, error) {
	res, err := s.deps.T2pClient.DiffConfig(c, &pb.EmptyMessage{})
	if err != nil {
		log.Warnf(c, "%+v", err)
		return "", err
	}
	if res.GetError() != nil {
		log.Warnf(c, "%+v", res.GetError())
		return "", res.GetError()
	}
	return res.Diffs, nil
}

func (s SetConfigResponse) SessionID() string {
	return s.ConfigResponse.SessionID
}
