package graphql

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
)

type testIconPath struct {
	Path           string
	ExpectedResult bool
}

var tests = []testIconPath{
	{
		Path:           "/assets/img/user-uploaded-icons/image (7).png",
		ExpectedResult: true,
	},
	{
		Path:           "https://regression-380-1104.i.tgcloud.io/assets/img/user-uploaded-icons/lightbulb (7).png",
		ExpectedResult: true,
	},
	{
		Path:           "https://regression-380-1104.i.tgcloud-dev.com/assets/img/user-uploaded-icons/lightbulb (7).png",
		ExpectedResult: true,
	},
	{
		Path:           "https://ddb8cebb2974449e914a5c7b8b39b800.i.tgcloud-dev.com/assets/img/user-uploaded-icons/截屏2022-11-08 下午12.07.33.png",
		ExpectedResult: true,
	},
	{
		Path:           "https://www.techfunnel.com/wp-content/uploads/2017/12/7-Types-of-Hackers.jpg",
		ExpectedResult: false,
	},
}

func TestIconPathValid(t *testing.T) {
	for _, testPath := range tests {
		pathValid, err := iconPathValid(testPath.Path)
		if err != nil {
			t.Fatal("TestIconPathValid err: ", err)
		}
		if pathValid != testPath.ExpectedResult {
			t.Errorf("For: %v, icon path validation does not match. want: %v, got: %v.", testPath.Path, testPath.ExpectedResult, pathValid)
		} else {
			t.Logf("For: %v, icon path validation matched. want: %v, got: %v.", testPath.Path, testPath.ExpectedResult, pathValid)
		}
	}
}

type testGetOwner struct {
	key            []byte
	ExpectedResult string
}

var testGetOwnerFromKeyCases = []testGetOwner{
	{
		key:            []byte("<EMAIL>"),
		ExpectedResult: "<EMAIL>",
	},
	{
		key:            []byte("GAApp.appid.qe"),
		ExpectedResult: "qe",
	},
}

func TestGetOwnerFromKey(t *testing.T) {
	for _, testCase := range testGetOwnerFromKeyCases {
		owner := getOwnerFromKey(testCase.key)
		if owner != testCase.ExpectedResult {
			t.Errorf("get owner does not match. want: %v, got: %v.", testCase.key, testCase.ExpectedResult)
		}
	}
}

// test function: Insights_UpsertPage, Insights_GetPage, Insights_GetPages, Insights_DeletePage
func TestInsightsAppFunction(t *testing.T) {
	query, err := setUpQuery()
	require.NoError(t, err)
	context := context.Background()

	upsertPageArgs1 := UpsertPageArgs{
		AppId:      "1",
		PageConfig: `{"id": "1"}`,
	}
	upsertPageArgs2 := UpsertPageArgs{
		AppId:      "1",
		PageConfig: `{"id": "2"}`,
	}
	upsertPageRes1, err := query.Insights_UpsertPage(context, upsertPageArgs1)
	require.NoError(t, err)
	_, err = query.Insights_UpsertPage(context, upsertPageArgs2)
	require.NoError(t, err)

	getPageArgs := GetPageArgs{
		AppId:  "1",
		PageId: "1",
	}
	getPageRes, err := query.Insights_GetPage(context, getPageArgs)
	require.NoError(t, err)
	require.Equal(t, getPageRes.PageId, getPageArgs.PageId)

	getPagesArgs := GetPagesArgs{
		AppId: "1",
	}
	getPagesRes, err := query.Insights_GetPages(context, getPagesArgs)
	require.NoError(t, err)
	require.Equal(t, 2, len(getPagesRes))

	deletePageArgs := DeletePageArgs{
		PageId:  "1",
		AppId:   "1",
		Version: upsertPageRes1,
	}
	_, err = query.Insights_DeletePage(context, deletePageArgs)
	require.NoError(t, err)

	getPagesRes, err = query.Insights_GetPages(context, getPagesArgs)
	require.NoError(t, err)
	require.Equal(t, 1, len(getPagesRes))
}

func TestGetPagesFromAppConfig(t *testing.T) {
	query, err := setUpQuery()
	require.NoError(t, err)
	context := context.Background()

	args := GetPagesArgs{
		AppId: "1",
	}
	data := make(map[string][]byte)
	data["GAApp.1.1"] = []byte(`{"id": "1", "version": "1.0", "pages": [{"id": 1,"version": "1.0"}]}`)
	query.Deps.T2pClient = &fakeControllerClient{
		data: data,
	}

	res, err := query.GetPagesFromAppConfig(context, args)
	require.NoError(t, err)
	require.Equal(t, []PageO2D{{AppId: "1", PageId: "1", Config: "{\"chartMap\":{},\"globalParameters\":{},\"id\":1,\"version\":\"\",\"weight\":10}", Version: ""}}, res)
}

func setUpQuery() (Query, error) {
	cfg, err := config.New("../test/integration/test.cfg", 1)
	if err != nil {
		return Query{}, err
	}

	dep := Deps{
		T2pClient: &fakeControllerClient{},
		Config:    cfg,
	}

	query := Query{
		Deps: dep,
		userInfo: &model.UserInfo{
			Name: "tigergraph",
		},
	}

	return query, nil
}
