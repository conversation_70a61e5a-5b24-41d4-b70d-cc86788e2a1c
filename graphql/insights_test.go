package graphql

import (
	"testing"
)

type testIconPath struct {
	Path           string
	ExpectedResult bool
}

var tests = []testIconPath{
	{
		Path:           "/assets/img/user-uploaded-icons/image (7).png",
		ExpectedResult: true,
	},
	{
		Path:           "https://regression-380-1104.i.tgcloud.io/assets/img/user-uploaded-icons/lightbulb (7).png",
		ExpectedResult: true,
	},
	{
		Path:           "https://regression-380-1104.i.tgcloud-dev.com/assets/img/user-uploaded-icons/lightbulb (7).png",
		ExpectedResult: true,
	},
	{
		Path:           "https://ddb8cebb2974449e914a5c7b8b39b800.i.tgcloud-dev.com/assets/img/user-uploaded-icons/截屏2022-11-08 下午12.07.33.png",
		ExpectedResult: true,
	},
	{
		Path:           "https://www.techfunnel.com/wp-content/uploads/2017/12/7-Types-of-Hackers.jpg",
		ExpectedResult: false,
	},
}

func TestIconPathValid(t *testing.T) {
	for _, testPath := range tests {
		pathValid, err := iconPathValid(testPath.Path)
		if err != nil {
			t.Fatal("TestIconPathValid err: ", err)
		}
		if pathValid != testPath.ExpectedResult {
			t.Errorf("For: %v, icon path validation does not match. want: %v, got: %v.", testPath.Path, testPath.ExpectedResult, pathValid)
		} else {
			t.Logf("For: %v, icon path validation matched. want: %v, got: %v.", testPath.Path, testPath.ExpectedResult, pathValid)
		}
	}
}

type testGetOwner struct {
	key            []byte
	ExpectedResult string
}

var testGetOwnerFromKeyCases = []testGetOwner{
	{
		key:            []byte("<EMAIL>"),
		ExpectedResult: "<EMAIL>",
	},
	{
		key:            []byte("GAApp.appid.qe"),
		ExpectedResult: "qe",
	},
}

func TestGetOwnerFromKey(t *testing.T) {
	for _, testCase := range testGetOwnerFromKeyCases {
		owner := getOwnerFromKey(testCase.key)
		if owner != testCase.ExpectedResult {
			t.Errorf("get owner does not match. want: %v, got: %v.", testCase.key, testCase.ExpectedResult)
		}
	}
}
