package graphql

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
)

// a hard coded string for compatibility with cloud instanceID
// see getInstanceID for more info
const onPermInstanceID = "on_perm"

const (
	AutoBackupTag      = "auto-backup"
	maxBackup          = 7
	maxManualBackup    = 6
	maxAutomaticBackup = 7
)

var ErrMaxBackup = errors.New("maximum number of backups has been reached")

type BackupStreamResult struct {
	Progress string
	Error    error
}

type CreateBackupArgs struct {
	BackupCreateRequest *pb.BackupCreateRequest
	IsAutomaticBackup   bool
}

func (q *Query) CreateBackup(ctx context.Context, args CreateBackupArgs) ([]string, error) {
	stream, err := q.CreateBackupStream(ctx, args)
	defer func() {
		if err := q.Deps.DaoManager.SetGBARInProgress(false); err != nil {
			log.Errorf(ctx, "Failed to set GBAR in progress to false: %v", err)
		}
	}()

	if err != nil {
		return nil, err
	}
	var progress []string
	for chunk := range stream {
		if chunk.Error != nil {
			return progress, chunk.Error
		}
		progress = append(progress, chunk.Progress)
	}
	q.CleanUp(ctx)
	return progress, nil
}

func (q *Query) CreateBackupStream(c context.Context, args CreateBackupArgs) (<-chan BackupStreamResult, error) {
	if err := q.Deps.DaoManager.SetGBARInProgress(true); err != nil {
		log.Errorf(c, "Failed to set GBAR in progress to true: %v", err)
		return nil, err
	}

	instanceID, err := getInstanceID(q.Deps.Config)
	if err != nil {
		log.Warn(c, err)
		instanceID = onPermInstanceID
	}
	args.BackupCreateRequest.Tag = instanceID + "-" + args.BackupCreateRequest.Tag + "-" + time.Now().Format("20060102150405")

	q.CleanUp(c)
	if canBackup, err := q.checkBackupCount(c, args.IsAutomaticBackup); !canBackup {
		return nil, err
	}

	stream, err := q.Deps.T2pClient.CreateBackup(c, args.BackupCreateRequest)
	if err != nil {
		return nil, err
	}
	ch := make(chan BackupStreamResult)

	go func() {
		for {
			resp, err := stream.Recv()
			if err == io.EOF {
				break
			}
			if err != nil {
				ch <- BackupStreamResult{
					Error: err,
				}
				break
			}
			if resp.Error != nil {
				ch <- BackupStreamResult{
					Error: resp.Error,
				}
				break
			}
			if len(resp.Progress) > 0 {
				ch <- BackupStreamResult{
					Progress: resp.Progress,
				}
			}
		}
		close(ch)
	}()
	return ch, nil
}

func (q *Query) CleanUp(ctx context.Context) error {
	ctx1, cancel1 := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel1()

	backups, err := q.ListBackup(ctx1, "", false)
	if err != nil {
		log.Errorf(ctx, "Failed to get backup list: %v", err)
		return err
	}

	backupsToRemove := len(backups.Backups) - maxBackup
	tags := make([]string, 0, len(backups.Backups))
	for i := len(backups.Backups) - 1; i >= 0 && backupsToRemove > 0; i-- {
		if backups.Backups[i].Automatic {
			tags = append(tags, backups.Backups[i].Tag)
			backupsToRemove--
		}
	}

	ctx2, cancel2 := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel2()

	if len(tags) > 0 {
		if progress, err := q.RemoveBackup(ctx2, struct{ Tags []string }{Tags: tags}); err != nil {
			log.Errorf(ctx, "Failed to delete backup: %v: %v\n", err, progress)
			return err
		}
		log.Infof(ctx, "Deleted automatic backup: %v\n", tags)
	}

	return nil
}

func (q *Query) checkBackupCount(ctx context.Context, isAutomaticBackup bool) (bool, error) {
	ctx1, cancel1 := context.WithTimeout(ctx, util.GRPC_CLIENT_TIMEOUT)
	defer cancel1()

	backups, err := q.ListBackup(ctx1, "", false)
	if err != nil {
		log.Errorf(ctx, "Failed to get backup list: %v", err)
		return false, err
	}

	var manualBackups int
	var automaticBackups int
	for _, backup := range backups.Backups {
		if backup.Automatic {
			automaticBackups++
		} else {
			manualBackups++
		}
	}

	if !isAutomaticBackup && manualBackups >= maxManualBackup {
		log.Warnf("The number of manual backups %d is more than %d", manualBackups, maxManualBackup)
		return false, ErrMaxBackup
	}

	// Hard cap the number of automatic backups in case of continuous cleanup failure.
	if isAutomaticBackup && automaticBackups > maxAutomaticBackup {
		log.Warnf("The number of backups %d is more than %d", len(backups.Backups), maxBackup)
		return false, ErrMaxBackup
	}

	return true, err
}

type BackupList struct {
	MetaData string
	Backups  []Backup
}

type Backup struct {
	Tag       string
	SizeBytes float64
	Time      string
	Automatic bool
}

func isAutomatic(tag string) bool {
	return strings.Contains(tag, AutoBackupTag)
}

func (q *Query) ListBackup(ctx context.Context, backupTag string, wantMetadata bool) (BackupList, error) {
	res, err := q.Deps.T2pClient.ListBackup(ctx, &pb.BackupListRequest{
		Tag: backupTag,
	})
	if err != nil {
		return BackupList{}, err
	}

	// todo: error
	backupList := BackupList{
		MetaData: res.Metadata,
	}
	for _, backup := range res.GetBackup() {
		backupList.Backups = append(backupList.Backups, Backup{
			Tag:       backup.Tag,
			SizeBytes: float64(backup.SizeBytes),
			Time:      backup.Time,
			Automatic: isAutomatic(backup.Tag),
		})
	}
	sort.Slice(backupList.Backups, func(i, j int) bool {
		return backupList.Backups[i].Time > backupList.Backups[j].Time
	})

	if res.Error != nil {
		return BackupList{}, res.Error
	}
	return backupList, nil
}

type RestoreBackupArgs struct {
	BackupRestoreRequest *pb.BackupRestoreRequest
}

func (q *Query) RestoreBackup(c context.Context, args RestoreBackupArgs) ([]string, error) {
	stream, err := q.RestoreBackupStream(c, args)
	defer func() {
		if err := q.Deps.DaoManager.SetGBARInProgress(false); err != nil {
			log.Errorf(c, "Failed to set GBAR in progress to false: %v", err)
		}
	}()

	if err != nil {
		return nil, err
	}
	var progress []string
	for chunk := range stream {
		if chunk.Error != nil {
			return progress, chunk.Error
		}
		progress = append(progress, chunk.Progress)
	}
	return progress, nil
}

func (q *Query) RestoreBackupStream(c context.Context, args RestoreBackupArgs) (<-chan BackupStreamResult, error) {
	if err := q.Deps.DaoManager.SetGBARInProgress(true); err != nil {
		log.Errorf(c, "Failed to set GBAR in progress to true: %v", err)
		return nil, err
	}

	req := pb.BackupRestoreRequest{}
	if args.BackupRestoreRequest.Tag != "" {
		req.Tag = args.BackupRestoreRequest.Tag
	}
	stream, err := q.Deps.T2pClient.RestoreBackup(c, &req)
	if err != nil {
		return nil, err
	}
	ch := make(chan BackupStreamResult)
	go func() {
		for {
			resp, err := stream.Recv()
			if err == io.EOF {
				break
			}
			if err != nil {
				ch <- BackupStreamResult{
					Error: err,
				}
				break
			}
			if resp.Error != nil {
				ch <- BackupStreamResult{
					Error: resp.Error,
				}
				break
			}
			if len(resp.Progress) > 0 {
				ch <- BackupStreamResult{
					Progress: resp.Progress,
				}
			}
		}
		close(ch)
	}()
	return ch, nil
}

func (q *Query) RemoveBackup(c context.Context, args struct{ Tags []string }) ([]string, error) {
	stream, err := q.RemoveBackupStream(c, args)
	if err != nil {
		return nil, err
	}
	var progress []string
	for chunk := range stream {
		if chunk.Error != nil {
			return progress, chunk.Error
		}
		progress = append(progress, chunk.Progress)
	}
	return progress, nil
}

func (q *Query) RemoveBackupStream(c context.Context, args struct{ Tags []string }) (<-chan BackupStreamResult, error) {
	stream, err := q.Deps.T2pClient.RemoveBackup(c, &pb.BackupRemoveRequest{
		Tag: args.Tags,
	})
	if err != nil {
		return nil, err
	}
	ch := make(chan BackupStreamResult)

	go func() {
		for {
			resp, err := stream.Recv()
			if err == io.EOF {
				break
			}
			if err != nil {
				ch <- BackupStreamResult{
					Error: err,
				}
				break
			}
			if resp.Error != nil {
				ch <- BackupStreamResult{
					Error: resp.Error,
				}
				break
			}
			if len(resp.Progress) > 0 {
				ch <- BackupStreamResult{
					Progress: resp.Progress,
				}
			}
		}
		close(ch)
	}()
	return ch, nil
}

func getInstanceID(cfg *config.Config) (string, error) {
	requestURL := "http://localhost:8080/meta"
	req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, requestURL, nil)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:  cfg.GetHTTPRequestTimeout(),
		RetryMax: cfg.GetHTTPRequestRetryMax(),
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Warnf("Failed to get response from %s: %v", requestURL, err)
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("Failed to read response from %s: %v", requestURL, err)
		return "", err
	}

	res := &model.CloudMetaResponse{}
	if err = json.Unmarshal(body, res); err != nil {
		log.Errorf("Failed to parse response from %s: %v", requestURL, err)
		return "", err
	}

	if res.Error {
		log.Errorf("Failed to get instance id: %s", res.Message)
		return "", errors.New(res.Message)
	}
	return res.Result.InstanceID, err
}
