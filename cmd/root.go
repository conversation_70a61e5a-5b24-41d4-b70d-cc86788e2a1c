package cmd

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/graphql/web"

	"github.com/tigergraph/gus/build"
	"github.com/tigergraph/gus/controller"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/handler/data"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	"github.com/tigergraph/gus/lib/tg"
	"github.com/tigergraph/gus/middleware"
	apitoken "github.com/tigergraph/gus/service/api_token"
	"github.com/tigergraph/gus/service/db"
	lj "github.com/tigergraph/gus/service/loadingjob"
	s "github.com/tigergraph/gus/service/schema"
)

const (
	binaryName                 = "tg_app_guid"
	startTimeout               = 10 * time.Second
	ProductionExpCheckInterval = time.Minute
)

var (
	cfgFile string
	replica int
	command *cobra.Command
)

func Execute() error {
	initialize()
	return command.Execute()
}

func initialize() {
	command = &cobra.Command{
		Use:   binaryName,
		Short: "Application Server is the backend server for GraphStudio and Admin Portal",
		Long: "Application Server is the backend server for GraphStudio and Admin Portal\n" +
			"See more details at: https://docs.tigergraph.com/",
		RunE: func(cmd *cobra.Command, args []string) error {
			printInfo()

			cntlr, err := startController()
			if err != nil {
				return err
			}

			stop := make(chan os.Signal, 1)
			signal.Notify(stop, os.Interrupt, syscall.SIGTERM)
			restart := make(chan os.Signal, 1)
			signal.Notify(restart, syscall.SIGHUP)

			for {
				select {
				case <-stop:
					log.Info("Stop signal received, stopping controller...")
					return cntlr.Stop()
				case <-restart:
					log.Info("Restart signal received, starting new controller...")
					if newCntlr, err := startController(); err == nil {
						log.Info("Successfully started new controller, stopping old controller...")
						_ = cntlr.Stop()
						cntlr = newCntlr
					} else {
						log.Info("Failed to start new controller")
					}
				}
			}
		},
	}

	command.Flags().StringVarP(&cfgFile, "config", "c", "", "path to config file")
	command.Flags().IntVarP(&replica, "replica", "r", 1, "ID of the replica")
	_ = command.MarkFlagRequired("config")
}

func printInfo() {
	log.Infof("Go version: %s", runtime.Version())
	log.Infof("Go OS/Arch: %s/%s", runtime.GOOS, runtime.GOARCH)
	log.Infof("BuildTime: %s", build.BuildTime)
	log.Infof("GitCommit: %s", build.GitCommit)
	log.Infof("BuildNum: %s", build.BuildNum)
	log.Infof(
		"Setting maximum number of CPUs to %d, total number of available CPUs is %d",
		runtime.GOMAXPROCS(0),
		runtime.NumCPU(),
	)
	log.Infof("Config file path: %s", cfgFile)
	log.Infof("Replica ID: %d", replica)
}

func startController() (*controller.Controller, error) {
	cfg, err := config.New(cfgFile, replica)
	if err != nil {
		log.Errorf("Failed to load config: %v", err)
		return nil, err
	}

	dbManager := db.New(cfg)
	daoManager := dao.New(dbManager)
	var t2pControllerClient pb.ControllerClient

	for {
		t2pControllerClient, err = tg.ControllerClient(cfg)
		if err != nil {
			log.Errorf("Failed to connect to controller: %v", err)
			time.Sleep(time.Second)
		} else {
			break
		}
	}

	gsqlHTTPClient := tg.NewGSQLHTTPClientV2(cfg)
	tokenService := apitoken.NewTokenService(cfg, dbManager, middleware.AuthenticateWithGSQL)

	cntlr, err := controller.New(
		controller.ControllerDependencies{
			AuthenticationService:         middleware.NewKVStoreAuthenticationService(dbManager),
			CheckAndCreateCategoryDirFunc: data.CheckAndCreateCategoryDir,
			GSQLAuthenticator:             middleware.AuthenticateWithGSQL,
			SecurityChecker:               middleware.LoginWithGSQL,
			ExpCheckInterval:              ProductionExpCheckInterval,
			DaoManager:                    daoManager,
			DatabaseManager:               dbManager,
			TGFileSystem:                  fs.NewTGFilesystem(cfg),
			SchemaService:                 s.New(gsqlHTTPClient),
			LoadingJobService:             lj.New(cfg, daoManager),
			UpsertSAMLResponse:            daoManager.CreateSAMLResponse,
			CntlrClient:                   t2pControllerClient,
			RequestGSQLClient:             tg.RequestGSQLClient,
			SchemaSyncer:                  web.NewSchemaSyncer(os.Stdout),
			TokenService:                  tokenService,
			InsightsService:               apitoken.NewInsightsService(dbManager, tokenService),
		},
		controller.ConfigFromFile(cfgFile, replica),
	)
	if err != nil {
		return nil, err
	}
	startTime := time.Now()
	if err := cntlr.AsyncStart(); err != nil {
		return nil, err
	}
	log.Infof("Controller asyncStart started in %s", time.Since(startTime))

	startCtx, cancel := context.WithTimeout(context.Background(), startTimeout)
	defer cancel()

	select {
	case <-cntlr.Ready():
	case <-startCtx.Done():
	}
	log.Infof("Controller ready in %s", time.Since(startTime))
	if startCtx.Err() != nil {
		return nil, fmt.Errorf("controller cannot start after %ds", startTimeout/time.Second)
	}
	return cntlr, nil
}
