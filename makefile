# Specify using bash as default shell.
SHELL = env /bin/bash

# Set up paths.
ROOT_DIR     = $(shell pwd)
SCRIPT_DIR   = $(ROOT_DIR)/scripts
RELEASE_DIR  = $(ROOT_DIR)/release
COVERAGE_DIR = $(ROOT_DIR)/coverage
APPROOT      = gadmin config get System.AppRoot --file ~/.tg.cfg 
DATE 		 = $(shell date)


# Go commands.
GO_GET = cd /tmp/; GO111MODULE=on go get

.PHONY: clean build test report-license

default: build

clean:
	@echo "Cleaning artifacts and cache..."
	@rm -rf $(RELEASE_DIR)/*
	@go clean -modcache
	@echo "Successfully cleaned artifacts and cache!"

access-github:
	@echo "Setting up GitHub access..."
	@$(SCRIPT_DIR)/access-github.sh
	@echo "Successfully set up GitHub access!"

build:
	@echo "Building binary..."
	@mkdir -p $(RELEASE_DIR)
	go run scripts/prebuild.go
	CGO_ENABLED=0 go build  -ldflags \
	"-X 'github.com/tigergraph/gus/build.GitCommit=$(GIT_COMMIT)' \
	-X 'github.com/tigergraph/gus/build.BuildTime=$(DATE)' \
	-X 'github.com/tigergraph/gus/build.BuildNum=${GITHUB_RUN_NUMBER}'"\
	 -o $(RELEASE_DIR)/tg_app_guid main.go
	@echo "Successfully built binary!"

lint:
	@echo "Running linters..."
	@shellcheck $(SCRIPT_DIR)/*.sh
	@$(GO_GET) github.com/golangci/golangci-lint/cmd/golangci-lint
	$$(go env GOPATH)/bin/golangci-lint run -c $(ROOT_DIR)/.golangci.yaml
	@echo "Lint passed!"

test:
	@echo "Running unit test..."
	@mkdir -p $(COVERAGE_DIR)
	@go test -v -short -coverpkg=./... -coverprofile=$(COVERAGE_DIR)/coverage.prof ./...
	@go tool cover -html=$(COVERAGE_DIR)/coverage.prof -o $(COVERAGE_DIR)/coverage.html
	@go tool cover -func=$(COVERAGE_DIR)/coverage.prof -o $(COVERAGE_DIR)/coverage.txt
	@echo "Unit test passed!"

e2e:
	@go test -v test/e2e/*.go

report-license:
	@echo "Generating third-party license report..."
	@go install github.com/google/go-licenses@v0.0.0-20210715153004-8751804a5b80
	@mkdir -p $(RELEASE_DIR)
	@echo "name,repository,license" > $(RELEASE_DIR)/third-party.csv
	@go-licenses csv "github.com/tigergraph/gus" | sed '/tigergraph/d' >> $(RELEASE_DIR)/third-party.csv
	@echo "Successfully generated third-party license report!"

start-swagger:
	@$(SCRIPT_DIR)/start-swagger.sh

stop-swagger:
	@$(SCRIPT_DIR)/stop-swagger.sh

dev-deploy:
	gadmin stop gui -y
	cp release/tg_app_guid `$(APPROOT)`/bin/gui/tg_app_guid
	gadmin start gui
