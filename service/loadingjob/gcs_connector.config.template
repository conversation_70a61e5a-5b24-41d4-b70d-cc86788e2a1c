connector.class=com.tigergraph.kafka.connect.filesystem.gcp.GCPSourceConnector
file.reader.settings.fs.gs.auth.service.account.email={{.ClientEmail}}
file.reader.settings.fs.gs.auth.service.account.private.key.id={{.PrivateKeyId}}
file.reader.settings.fs.gs.auth.service.account.private.key="{{.PrivateKey}}"
file.reader.settings.client_email="{{.ClientEmail}}"
file.reader.settings.fs.gs.project.id="tigergraph-dev"
file.reader.settings.fs.gs.auth.service.account.enable=true
file.reader.settings.fs.gs.impl=com.google.cloud.hadoop.fs.gcs.GoogleHadoopFileSystem
file.reader.settings.fs.AbstractFileSystem.gs.impl="com.google.cloud.hadoop.fs.gcs.GoogleHadoopFS"
