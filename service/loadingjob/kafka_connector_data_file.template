mode=eof
file.regexp=".*"
file.recursive=true
file.reader.type=text
file.reader.batch.size=10000
file.reader.text.eol="{{.Eol}}"
file.reader.text.header={{.Header}}
file.reader.text.archive.type=auto
file.reader.text.archive.extensions.tar=tar
file.reader.text.archive.extensions.zip=zip
file.reader.text.archive.extensions.gzip=gz
file.reader.text.archive.extensions.tar.gz=tar.gz,tgz

[{{.ConnectorTitle}}]
name={{.ConnectorName}}
tasks.max=1
topic={{.Topic}}
file.uris={{.FileUri}}
