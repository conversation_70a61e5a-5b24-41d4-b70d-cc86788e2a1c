connector.class=com.tigergraph.kafka.connect.filesystem.azure.AzureBlobSourceConnector
file.reader.settings.fs.defaultFS="abfss://{{.ContainerName}}@{.AzureBlobStorageDataSource.AccountName}.dfs.core.windows.net/"
file.reader.settings.fs.azure.account.key.{{.AzureBlobStorageDataSource.AccountName}}.dfs.core.windows.net="{{.AzureBlobStorageDataSource.AccountKey}}"
file.reader.settings.fs.abfs.impl="org.apache.hadoop.fs.azurebfs.AzureBlobFileSystem"
file.reader.settings.fs.abfss.impl="org.apache.hadoop.fs.azurebfs.SecureAzureBlobFileSystem"
file.reader.settings.fs.AbstractFileSystem.abfs.impl="org.apache.hadoop.fs.azurebfs.Abfs"
file.reader.settings.fs.AbstractFileSystem.abfss.impl="org.apache.hadoop.fs.azurebfs.Abfss"
file.reader.settings.fs.azure.account.auth.type="SharedKey"