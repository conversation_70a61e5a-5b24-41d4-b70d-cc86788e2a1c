package loadingjob

type JobState string

const (
	Running JobState = "RUNNING"

	Initial  JobState = "INITIAL"
	Skipped  JobState = "SKIPPED"
	Paused   JobState = "PAUSED"
	Stopped  JobState = "STOPPED"
	Finished JobState = "FINISHED"
	Failed   JobState = "FAILED"

	Starting JobState = "STARTING"
	Pausing  JobState = "PAUSING"
	Resuming JobState = "RESUMING"
	Stopping JobState = "STOPPING"
)

func isValidJobState(state JobState) bool {
	switch state {
	case
		Running,
		Initial,
		Skipped,
		Paused,
		Stopped,
		Finished,
		Failed,
		Starting,
		Pausing,
		Resuming,
		Stopping:
		return true
	}
	return false
}
