package loadingjob

import (
	"bytes"
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	gcache "github.com/patrickmn/go-cache"
	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
	tgJSON "github.com/tigergraph/gus/lib/json"
	tgSync "github.com/tigergraph/gus/lib/sync"
	mw "github.com/tigergraph/gus/middleware"
)

// Command is the action to control a loading job.
type Command string

const (
	Start       Command = "START"
	Pause       Command = "PAUSE"
	Resume      Command = "RESUME"
	Stop        Command = "STOP"
	GetProgress Command = "GETPROGRESS"
)

const (
	GCSLoadingJobPrefix = "gcs_load_job_"
	S3LoadingJobPrefix  = "kafka_s3_load_job_"
	ABSLoadingJobPrefix = "abs_load_job_"
)

var KafkaLoadingJobPrefix = [...]string{GCSLoadingJobPrefix, S3LoadingJobPrefix, ABSLoadingJobPrefix}

type CommandContext struct {
	GraphName string
	Command   Command
	ValidJobs []string
	Results   model.LoadingJobCommandResult
}

const (
	defaultExpiration = 5 * time.Second
	cleanupInterval   = 1 * time.Hour
	ljPrefix          = "loading-job"
)

// Controller is the loading job service controller.
type Controller struct {
	cfg        *config.Config
	daoManager interfaces.DaoManager
	cache      *gcache.Cache
	locker     *tgSync.KeyLocker
}

// Enforce loading job service to implement LifeCycle.
var _ interfaces.LifeCycle = (*Controller)(nil)

func New(cfg *config.Config, daoManager interfaces.DaoManager) *Controller {
	return &Controller{cfg: cfg, daoManager: daoManager}
}

// SetUp prepares the loading job service.
func (c *Controller) SetUp() error {
	var err error
	if c.locker, err = tgSync.NewKeyLocker(); err != nil {
		return err
	}

	c.cache = gcache.New(defaultExpiration, cleanupInterval)
	c.cache.OnEvicted(func(key string, value interface{}) {
		log.Debugf("Deleted expired cache with key %v", key)
	})

	return nil
}

// TearDown flushes the cache.
func (c *Controller) TearDown() {
	if c.cache != nil {
		c.cache.Flush()
	}
}

// StartJobs starts the given loading jobs.
func (c *Controller) StartJobs(
	ctx *gin.Context,
	graphName string,
	jobInfo []model.StartJobInfo,
) model.LoadingJobCommandResult {
	jobNames := make([]string, len(jobInfo))
	for i, info := range jobInfo {
		jobNames[i] = info.Name
	}

	cmdCtx := c.initContext(graphName, jobNames, Start)
	prevState := c.transitionState(cmdCtx, Starting)
	loadingJobInfos, err := c.daoManager.GetLoadingJobInfo(graphName)
	if err != nil {
		log.Error(ctx, "Failed to retrieve loading job infos.")
		return cmdCtx.Results
	}

	// <key = job name, value = parsing option>
	options := make(map[string]model.CSVDataSourceOption)
	for _, info := range loadingJobInfos {
		options[info.LoadingJobName] = info.DataSourceJson.Options
	}

	startJobPayload := make([]model.StartJobInfo, 0)
	startJobNames := make([]string, 0)
	failedStartJobNames := make(map[string]bool)

	for _, jobName := range cmdCtx.ValidJobs {
		for _, info := range jobInfo {
			if jobName != info.Name {
				continue
			}

			_, ok := options[jobName]
			if !ok {
				failedStartJobNames[jobName] = true
				log.Warnf(ctx, "Failed to retrieve loading job info: %s.", jobName)
				break
			}

			if len(info.DataSources) == 0 {
				failedStartJobNames[jobName] = true
				log.Errorf(ctx, "Missing data source config for %s.", jobName)
				break
			}

			startJobPayload = append(startJobPayload, info)
			startJobNames = append(startJobNames, jobName)
		}
	}

	if len(startJobPayload) > 0 {
		log.Debugf(ctx, "Starting loading jobs: %v", startJobNames)
		// toDo, Start supported, format not the same like before
		// url changed
		res := c.requestGSQLServer(ctx, graphName, nil, startJobPayload, Start)
		c.updateResults(nil, cmdCtx, startJobNames, res, prevState, Running)
	}

	return cmdCtx.Results
}

func isKafkaLoadingJob(jobName string) bool {
	for _, prefix := range KafkaLoadingJobPrefix {
		if strings.HasPrefix(jobName, prefix) {
			return true
		}
	}
	return false
}

// PauseJobs pauses the given loading jobs.
func (c *Controller) PauseJobs(
	ctx *gin.Context,
	graphName string,
	jobNames []string,
) model.LoadingJobCommandResult {
	cmdCtx := c.initContext(graphName, jobNames, Pause)
	prevState := c.transitionState(cmdCtx, Pausing)

	jobIDs := make([]string, len(cmdCtx.ValidJobs))
	validJobNames := make([]string, len(cmdCtx.ValidJobs))
	for i, jobName := range cmdCtx.ValidJobs {
		jobIDs[i] = cmdCtx.Results[jobName].ID
		validJobNames[i] = jobName
	}

	if len(jobIDs) > 0 {
		log.Debugf(ctx, "Pausing loading jobs: %v", cmdCtx.ValidJobs)
		// toDo, Pause supported, format not the same like before
		// url changed
		res := c.requestGSQLServer(ctx, graphName, jobIDs, nil, Pause)
		c.updateResults(ctx, cmdCtx, validJobNames, res, prevState, Paused)
	}

	return cmdCtx.Results
}

// ResumeJobs resumes the given loading jobs.
func (c *Controller) ResumeJobs(
	ctx *gin.Context,
	graphName string,
	jobNames []string,
) model.LoadingJobCommandResult {
	cmdCtx := c.initContext(graphName, jobNames, Resume)
	prevState := c.transitionState(cmdCtx, Resuming)

	jobIDs := make([]string, 0)
	validJobNames := make([]string, 0)

	for _, jobName := range cmdCtx.ValidJobs {
		jobID := cmdCtx.Results[jobName].ID
		jobIDs = append(jobIDs, jobID)
		validJobNames = append(validJobNames, jobName)
	}

	if len(jobIDs) > 0 {
		log.Debugf(ctx, "Resuming loading jobs: %v", cmdCtx.ValidJobs)
		// toDo, Resume need changed, json format not the same, and now can only Resume a job
		// url changed
		res := c.requestGSQLServer(ctx, graphName, jobIDs, nil, Resume)
		c.updateResults(ctx, cmdCtx, validJobNames, res, prevState, Running)
	}

	return cmdCtx.Results
}

// StopJobs stops the given loading jobs.
func (c *Controller) StopJobs(
	ctx *gin.Context,
	graphName string,
	jobNames []string,
) model.LoadingJobCommandResult {
	cmdCtx := c.initContext(graphName, jobNames, Stop)
	prevState := c.transitionState(cmdCtx, Stopping)

	jobIDs := make([]string, len(cmdCtx.ValidJobs))
	validJobNames := make([]string, len(cmdCtx.ValidJobs))
	for i, jobName := range cmdCtx.ValidJobs {
		jobIDs[i] = cmdCtx.Results[jobName].ID
		validJobNames[i] = jobName
	}

	if len(jobIDs) > 0 {
		log.Debugf(ctx, "Stopping loading jobs: %v", cmdCtx.ValidJobs)
		// toDo abort supported,format not the same like before
		// url changed
		res := c.requestGSQLServer(ctx, graphName, jobIDs, nil, Stop)
		c.updateResults(ctx, cmdCtx, validJobNames, res, prevState, Stopped)
	}

	return cmdCtx.Results
}

// GetJobsProgress retrieves and updates the progress of the given loading jobs.
func (c *Controller) GetJobsProgress(
	ctx *gin.Context,
	graphName string,
	jobNames []string,
) model.LoadingJobCommandResult {
	cmdResults := make(model.LoadingJobCommandResult)
	jobIDToName := make(map[string]string)

	jobsToUpdate := c.getJobsToUpdate(graphName, jobNames, cmdResults, jobIDToName)
	if len(jobsToUpdate) == 0 {
		return cmdResults
	}

	log.Debugf(ctx, "Retrieving progress for loading jobs with ids: %v", jobsToUpdate)
	// toDo, not such api; json format not the same, and now can only GetProgress a job
	// url changed
	res := c.requestGSQLServer(ctx, graphName, jobsToUpdate, nil, GetProgress)

	if !res.Error && res.Results != nil {
		resultSlice := cast.ToSlice(res.Results)

		for _, result := range resultSlice {
			prog, err := processLoadingProgressPayload(result, res.Message)
			if err != nil {
				continue
			}

			progress := cast.ToStringMap(result)
			overall := cast.ToStringMap(progress["overall"])

			jobID := cast.ToString(overall["id"])
			jobName := jobIDToName[jobID]
			jobLog := cmdResults[jobName]

			currState := JobState(jobLog.Progress.Status)
			nextState := JobState(prog.Status)
			if !isValidJobState(nextState) {
				prog.Status = string(currState)
			}
			// Since GSQL doesn't have STOPPED state (only PAUSED),
			// it is required to maintain STOPPED state.
			if (currState == Stopped || currState == Stopping) && nextState == Paused {
				prog.Status = string(Stopped)
			}

			if isKafkaLoadingJob(jobName) {
				if nextState == Stopped {
					if currState == Running {
						prog.Status = string(Finished)

					}
					if currState == Finished || currState == Paused || currState == Failed {
						prog.Status = string(currState)
					}
				} else if nextState == Failed || nextState == Finished {
					prog.Status = string(nextState)
				}
			}

			if JobState(prog.Status) == Finished {
				jobLog.Error = false
				jobLog.Message = ""
			}

			jobLog.Progress = prog
			c.updateCacheAndDB(graphName, jobName, jobLog)
		}
	}

	for _, jobName := range jobNames {
		key := genJobKey(graphName, jobName)
		_ = c.locker.Unlock(key)
	}

	return cmdResults
}

func processLoadingProgressPayload(result interface{}, message string) (model.LoadingJobProgress, error) {
	progress := cast.ToStringMap(result)
	// Handle error.
	if err := cast.ToBool(progress["error"]); err {
		return model.LoadingJobProgress{}, errors.New("")
	}

	overall := cast.ToStringMap(progress["overall"])
	// Workers.
	workers := cast.ToSlice(progress["workers"])
	if len(workers) == 0 {
		return model.LoadingJobProgress{}, nil
	}
	worker := cast.ToStringMap(workers[0])

	// Tasks.
	tasks := cast.ToSlice(worker["tasks"])
	if len(tasks) == 0 {
		return model.LoadingJobProgress{}, nil
	}
	// Assumption: a GUI loading job only loads from 1 file.
	// TODO: Get rid of the assumption.
	task := cast.ToStringMap(tasks[0])

	// File-level.
	statistics := cast.ToStringMap(overall["statistics"])
	if _, ok := statistics["fileLevel"]; !ok {
		return model.LoadingJobProgress{}, nil
	}
	fileLevel := cast.ToStringMap(statistics["fileLevel"])

	// TODO: Object level.

	progMap := map[string]any{
		"URI":                 task["filename"],
		"Source":              task["filename"],
		"Status":              overall["status"],
		"Message":             message,
		"StartTime":           overall["startTime"],
		"EndTime":             overall["endTime"],
		"Duration":            cast.ToFloat64(overall["duration"]) / 1000,
		"Percentage":          cast.ToFloat64(overall["progress"]) * 100,
		"CurrentSpeed":        overall["currentSpeed"],
		"AverageSpeed":        overall["averageSpeed"],
		"LoadedSize":          overall["size"],
		"LoadedLines":         fileLevel["validLine"],
		"NotEnoughTokenLines": fileLevel["notEnoughToken"],
		"OversizeTokenLines":  fileLevel["tokenExceedsBuffer"],
		"RejectedLines":       fileLevel["rejectLine"],
	}
	prog := model.LoadingJobProgress{}
	progJson, _ := tgJSON.Marshal(progMap)
	json.Unmarshal(progJson, &prog)
	return prog, nil
}

func (c *Controller) getJobsToUpdate(
	graphName string,
	jobNames []string,
	cmdResults model.LoadingJobCommandResult,
	jobIDToName map[string]string,
) []string {
	jobsToUpdate := make([]string, 0)

	if len(jobNames) > 0 {
		keys := make([]string, len(jobNames))
		for i, jobName := range jobNames {
			keys[i] = genJobKey(graphName, jobName)
			cmdResults[jobName] = c.getJobLog(graphName, jobName)
		}

		if err := c.locker.LockAll(keys); err != nil {
			log.Errorf("Failed to lock %v: %v", keys, err)
			return jobsToUpdate
		}

		for i, jobName := range jobNames {
			jobLog := cmdResults[jobName]
			if len(jobLog.ID) > 0 && jobLog.Expiration.Before(time.Now()) {
				jobIDToName[jobLog.ID] = jobName
				jobsToUpdate = append(jobsToUpdate, jobLog.ID)
			} else {
				_ = c.locker.Unlock(keys[i])
			}
		}
	}

	return jobsToUpdate
}

func (c *Controller) initContext(graphName string, jobNames []string, command Command) *CommandContext {
	validJobs := make([]string, 0)
	results := make(model.LoadingJobCommandResult)
	if len(jobNames) > 0 {
		keys := make([]string, len(jobNames))
		for i, jobName := range jobNames {
			keys[i] = genJobKey(graphName, jobName)
			results[jobName] = c.getJobLog(graphName, jobName)
		}

		if err := c.locker.LockAll(keys); err != nil {
			log.Errorf("Failed to lock %v: %v", keys, err)
			return &CommandContext{
				GraphName: graphName,
				Command:   command,
				ValidJobs: validJobs,
				Results:   results,
			}
		}

		for i, jobName := range jobNames {
			isValid := checkCommand(command, JobState(results[jobName].Progress.Status))

			if isValid {
				validJobs = append(validJobs, jobName)
			} else {
				_ = c.locker.Unlock(keys[i])
			}
		}
	}

	return &CommandContext{
		GraphName: graphName,
		Command:   command,
		ValidJobs: validJobs,
		Results:   results,
	}
}

func (c *Controller) transitionState(
	cmdCtx *CommandContext,
	nextState JobState,
) map[string]JobState {
	prevState := make(map[string]JobState)
	for _, jobName := range cmdCtx.ValidJobs {
		jobLog := cmdCtx.Results[jobName]
		prevState[jobName] = JobState(jobLog.Progress.Status)
		// If the START command fails in INITIAL state, transition to FAILED.
		// Since only START command is valid on INITIAL state, no further check is needed.
		if jobLog.Progress.Status == string(Finished) {
			prevState[jobName] = Failed
		}

		jobLog.Progress.Status = string(nextState)
		c.updateCacheAndDB(cmdCtx.GraphName, jobName, jobLog)
	}

	return prevState
}

func (c *Controller) updateResults(
	_ *gin.Context,
	cmdCtx *CommandContext,
	jobNames []string,
	res *model.Response,
	prevState map[string]JobState,
	nextState JobState,
) {
	for _, jobName := range jobNames {
		jobLog := cmdCtx.Results[jobName]

		if res.Error {
			jobLog.Error = true
			jobLog.Message = res.Message
			jobLog.Progress.Status = string(prevState[jobName])
		} else {
			id := jobLog.ID
			if cmdCtx.Command == Start {
				id = jobName
			}

			result := handleGSQLResponse(cmdCtx, res, jobLog, id)
			if !jobLog.Error {
				jobLog.Progress.Status = string(nextState)
				// If START was executed, save the job id and re-initialize the loading progress.
				if cmdCtx.Command == Start {
					jobLog.ID = cast.ToString(result["result"])
					jobLog.Progress = model.LoadingJobProgress{Status: string(Running)}
				}
			} else {
				jobLog.Progress.Status = string(prevState[jobName])
			}
		}

		c.updateCacheAndDB(cmdCtx.GraphName, jobName, jobLog)
		key := genJobKey(cmdCtx.GraphName, jobName)
		_ = c.locker.Unlock(key)
	}
}

func handleGSQLResponse(cmdCtx *CommandContext, res *model.Response, jobLog *model.LoadingJobLog, jobId string) map[string]interface{} {
	result, err := cast.ToStringMapE(cast.ToStringMap(res.Results)[jobId])
	if err != nil {
		result["error"] = true
		result["message"] = fmt.Sprintf("Failed to get the result of %s.", cmdCtx.Command)
	}

	jobLog.Error = cast.ToBool(result["error"])
	jobLog.Message = cast.ToString(result["message"])
	return result
}

func (c *Controller) getJobLog(graphName, jobName string) *model.LoadingJobLog {
	key := genJobKey(graphName, jobName)

	// Step 1: Attempt to retrieve job log from cache.
	if jobLog, found := c.cache.Get(key); found {
		return jobLog.(*model.LoadingJobLog)
	}

	// Step 2: If there is no job log in cache, attempt to retrieve from db.
	if jobLog, err := c.daoManager.GetLoadingJobLog(graphName, jobName); err == nil {
		now := time.Now()
		if jobLog.Expiration.After(now) {
			c.cache.Set(key, jobLog, jobLog.Expiration.Sub(now))
		}
		return jobLog
	}

	// Step 3: If no job log can be retrieved, return a new one.
	jobLog := &model.LoadingJobLog{
		ID: "",
		Progress: model.LoadingJobProgress{
			Status: string(Initial),
		},
	}
	c.updateCacheAndDB(graphName, jobName, jobLog)
	return jobLog
}

func (c *Controller) updateCacheAndDB(graphName, jobName string, jobLog *model.LoadingJobLog) {
	key := genJobKey(graphName, jobName)

	c.cache.SetDefault(key, jobLog)

	_, jobLog.Expiration, _ = c.cache.GetWithExpiration(key)
	if err := c.daoManager.UpsertLoadingJobLog(graphName, jobName, *jobLog); err != nil {
		log.Errorf(
			"Failed to upsert log for loading job %s in graph %s: %v",
			jobName,
			graphName,
			err,
		)
	}
}

func (c *Controller) requestGSQLServer(
	ctx *gin.Context,
	graphName string,
	jobIDs []string,
	jobInfo []model.StartJobInfo,
	command Command,
) *model.Response {
	var urlPath string

	switch command {
	case Start:
		urlPath = "/gsql/v1/loading-jobs/run"
	case Pause:
		urlPath = "/gsql/v1/loading-jobs/abort"
	case Stop:
		urlPath = "/gsql/v1/loading-jobs/abort"
	case Resume:
		urlPath = "/gsql/v1/loading-jobs/resume"
	case GetProgress:
		urlPath = "/gsql/v1/loading-jobs/status"
	default:
		log.Error("Invalid command")
		return nil
	}

	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		c.cfg.GetNginxProtocol(),
		c.cfg.GetGSQLServerHostname(),
		c.cfg.GetNginxPort(),
		urlPath,
	)

	query := make(url.Values)
	query.Add("graph", graphName)
	query.Add("action", string(command))
	if command != Start {
		query.Add("jobIds", strings.Join(jobIDs, ","))
	}

	// specify the host to run the loading job, otherwise it will run on all nodes, led to duplicated loading jobs
	hostId, _ := c.cfg.GetHostID()
	if command == Start {
		for i := range jobInfo {
			for j := range jobInfo[i].DataSources {
				if jobInfo[i].DataSources[j].Name == "file" && jobInfo[i].DataSources[j].Hostname == "" {
					jobInfo[i].DataSources[j].Hostname = hostId
				}

			}
		}
	}

	req := createHTTPRequest(command, requestURL, jobInfo)
	// body using application/json type
	req.Header.Set("Content-Type", "application/json")
	creds := mw.GetUserCredentials(ctx)
	h.SetCredentials(req, creds)
	req.URL.RawQuery = query.Encode()
	h.SetFromGraphStudio(req)
	// Add X-Forwarded-For
	h.SetForwardedForFromContext(ctx, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            c.cfg.GetHTTPRequestTimeout(),
		RetryMax:           0,
		RetryWaitMin:       c.cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       c.cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: c.cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
		CheckRetry:         h.DefaultReqGSQLServerRetryPolicy,
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(ctx, "Failed to get response from GSQL server: %v", err)
		return &model.Response{Error: true}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(ctx, "Failed to read response from GSQL server: %v", err)
		return &model.Response{Error: true}
	}

	res := &model.Response{}
	if err := json.Unmarshal(body, res); err != nil {
		log.Errorf(ctx, "Failed to parse response from GSQL server: %v", err)
		return &model.Response{Error: true, Message: string(body)}
	}

	return res
}

func createHTTPRequest(command Command, requestURL string, jobInfo []model.StartJobInfo) *http.Request {
	var req *http.Request
	switch command {
	case Start:
		body, _ := tgJSON.Marshal(jobInfo)
		req, _ = http.NewRequest(http.MethodPost, requestURL, bytes.NewBuffer(body))
	default:
		req, _ = http.NewRequest(http.MethodGet, requestURL, nil)
	}
	return req
}

func checkCommand(command Command, state JobState) bool {
	switch command {
	case Start:
		switch state {
		case
			Initial,
			Stopped,
			Finished,
			Failed:
			return true

		default:
			return false
		}

	case Pause:
		if state == Running {
			return true
		}
		return false

	case Resume:
		if state == Paused {
			return true
		}
		return false

	case Stop:
		if state == Running || state == Paused {
			return true
		}
		return false

	// GetProgress is always valid.
	default:
		return true
	}
}

func genJobKey(graphName, jobName string) string {
	return fmt.Sprintf("%s/%s/%s", ljPrefix, graphName, jobName)
}
