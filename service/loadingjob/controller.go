package loadingjob

import (
	"bytes"
	"context"
	"crypto/sha256"
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"text/template"
	"time"

	"github.com/gin-gonic/gin"
	gcache "github.com/patrickmn/go-cache"
	"github.com/spf13/cast"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/proxy"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/helper"
	h "github.com/tigergraph/gus/lib/http"
	tgJSON "github.com/tigergraph/gus/lib/json"
	tgSync "github.com/tigergraph/gus/lib/sync"
	"github.com/tigergraph/gus/lib/tg"
	mw "github.com/tigergraph/gus/middleware"
)

// Command is the action to control a loading job.
type Command string

const (
	Start       Command = "START"
	Pause       Command = "PAUSE"
	Resume      Command = "RESUME"
	Stop        Command = "STOP"
	GetProgress Command = "GETPROGRESS"
)

const (
	GCSLoadingJobPrefix = "gcs_load_job_"
	S3LoadingJobPrefix  = "kafka_s3_load_job_"
	ABSLoadingJobPrefix = "abs_load_job_"
)

const (
	numOfRetries             int           = 5
	kafkaLoadingJobRetryTime time.Duration = 2 * time.Second
	createTopicWaitTime      time.Duration = 5 * time.Second
)

var KafkaLoadingJobPrefix = [...]string{GCSLoadingJobPrefix, S3LoadingJobPrefix, ABSLoadingJobPrefix}

//go:embed s3_connector.config.template
var s3_connector_config_template string

//go:embed gcs_connector.config.template
var gcs_connector_config_template string

//go:embed abs_connector.config.template
var abs_connector_config_template string

//go:embed kafka_connector_data_file.template
var kafka_connector_data_file_template string

//go:embed kafka_connector.config.template
var kafka_connector_config_template string

type absConnectorConfigData struct {
	model.AzureBlobStorageDataSource
	ContainerName string
}

type kafkaConnectorDataFileConfigData struct {
	Eol            string
	Header         bool
	ConnectorTitle string
	ConnectorName  string
	Topic          string
	FileUri        string
}

type kafkaConnectorConfigData struct {
	KafkaConnectorDaraSourceConfig string
	KafkaConnectorDataFileConfig   string
}

type CommandContext struct {
	GraphName string
	Command   Command
	ValidJobs []string
	Results   model.LoadingJobCommandResult
}

const (
	defaultExpiration = 5 * time.Second
	cleanupInterval   = 1 * time.Hour
	ljPrefix          = "loading-job"
)

// Controller is the loading job service controller.
type Controller struct {
	cfg        *config.Config
	daoManager interfaces.DaoManager
	cache      *gcache.Cache
	locker     *tgSync.KeyLocker
}

var eolMap = map[string]string{
	"\\n":     `\\n`,
	"\\t":     `\\t`,
	"\\u0020": `\\u0020`,
	"\\\\":    `\\\\`,
	"\\1":     `\\1`,
	"\\2":     `\\2`,
	"\\3":     `\\3`,
	"\\4":     `\\4`,
	"\\5":     `\\5`,
	"\\6":     `\\6`,
	"\\7":     `\\7`,
}

// Enforce loading job service to implement LifeCycle.
var _ interfaces.LifeCycle = (*Controller)(nil)

func New(cfg *config.Config, daoManager interfaces.DaoManager) *Controller {
	return &Controller{cfg: cfg, daoManager: daoManager}
}

// SetUp prepares the loading job service.
func (c *Controller) SetUp() error {
	var err error
	if c.locker, err = tgSync.NewKeyLocker(); err != nil {
		return err
	}

	c.cache = gcache.New(defaultExpiration, cleanupInterval)
	c.cache.OnEvicted(func(key string, value interface{}) {
		log.Debugf("Deleted expired cache with key %v", key)
	})

	return nil
}

// TearDown flushes the cache.
func (c *Controller) TearDown() {
	if c.cache != nil {
		c.cache.Flush()
	}
}

// StartJobs starts the given loading jobs.
func (c *Controller) StartJobs(
	ctx *gin.Context,
	graphName string,
	jobInfo []model.StartJobInfo,
) model.LoadingJobCommandResult {
	jobNames := make([]string, len(jobInfo))
	for i, info := range jobInfo {
		jobNames[i] = info.Name
	}

	cmdCtx := c.initContext(graphName, jobNames, Start)
	prevState := c.transitionState(cmdCtx, Starting)
	loadingJobInfos, err := c.daoManager.GetLoadingJobInfo(graphName)
	if err != nil {
		log.Error(ctx, "Failed to retrieve loading job infos.")
		return cmdCtx.Results
	}

	// <key = job name, value = parsing option>
	options := make(map[string]model.CSVDataSourceOption)
	for _, info := range loadingJobInfos {
		options[info.LoadingJobName] = info.DataSourceJson.Options
	}

	nonKafkaPayload := make([]model.StartJobInfo, 0)
	nonKafkaJobNames := make([]string, 0)

	kafkaPayload := make([]model.StartJobInfo, 0)
	// set <key = name of the Kafka loading jobs that can't be started>
	failedStartKafkaJobs := make(map[string]bool)

	for _, jobName := range cmdCtx.ValidJobs {
		for _, info := range jobInfo {
			if jobName != info.Name {
				continue
			}

			if !isKafkaLoadingJob(jobName) {
				nonKafkaPayload = append(nonKafkaPayload, info)
				nonKafkaJobNames = append(nonKafkaJobNames, jobName)
				break
			}

			options, ok := options[jobName]
			if !ok {
				failedStartKafkaJobs[jobName] = true
				log.Warnf(ctx, "Failed to retrieve loading job info: %s.", jobName)
				break
			}

			if len(info.DataSources) == 0 {
				failedStartKafkaJobs[jobName] = true
				log.Errorf(ctx, "Missing data source config for %s.", jobName)
				break
			}

			connectorName, topicName, err := c.createTGKafkaConnector(
				jobName,
				info.DataSources[0].Name,
				info.DataSources[0].Path,
				graphName,
				options,
			)

			if err != nil {
				failedStartKafkaJobs[jobName] = true
				log.Errorf(ctx, "Failed to start job: %s.", jobName)
				break
			}
			cmdCtx.Results[jobName].ConnectorName = connectorName

			kafkaDataSource := &model.GSQLDataSource{
				Filename: info.DataSources[0].Filename,
				Name:     getKafkaDataSourceName(jobName),
				Path:     "",
				Config:   getKafkaDataSourceConfig(topicName),
			}
			info.DataSources[0] = *kafkaDataSource

			kafkaPayload = append(kafkaPayload, info)
		}
	}

	if len(nonKafkaPayload) > 0 {
		log.Debugf(ctx, "Starting loading jobs: %v", nonKafkaJobNames)
		res := c.requestGSQLServer(ctx, graphName, nil, nonKafkaPayload, Start)
		c.updateResults(ctx, cmdCtx, nonKafkaJobNames, res, prevState, Running)
	}

	if len(kafkaPayload) > 0 {
		time.Sleep(createTopicWaitTime)
		c.startKafkaJobs(ctx, cmdCtx, prevState, kafkaPayload, graphName)
	}

	if len(failedStartKafkaJobs) > 0 {
		c.updateFailedKafkaJobResults(cmdCtx, failedStartKafkaJobs, prevState)
	}

	return cmdCtx.Results
}

func isKafkaLoadingJob(jobName string) bool {
	for _, prefix := range KafkaLoadingJobPrefix {
		if strings.HasPrefix(jobName, prefix) {
			return true
		}
	}
	return false
}

func getKafkaDataSourceName(jobName string) string {
	shaJobName := sha256.Sum256([]byte(jobName))
	currTime := time.Now().Unix()
	prefix := ""
	if isGCSLoadingJob(jobName) {
		prefix = GCSLoadingJobPrefix
	} else if isS3LoadingJob(jobName) {
		prefix = S3LoadingJobPrefix
	} else if isABSLoadingJob(jobName) {
		prefix = ABSLoadingJobPrefix
	} else {
		return ""
	}

	return fmt.Sprintf("%s%x_%d", prefix, shaJobName[0:10], currTime)
}

func isS3LoadingJob(jobName string) bool {
	return strings.HasPrefix(jobName, S3LoadingJobPrefix)
}

func isGCSLoadingJob(jobName string) bool {
	return strings.HasPrefix(jobName, GCSLoadingJobPrefix)
}

func isABSLoadingJob(jobName string) bool {
	return strings.HasPrefix(jobName, ABSLoadingJobPrefix)
}

func getKafkaDataSourceConfig(topicName string) model.KafkaDataSourceConfig {
	kafkaDataSourceConfig := model.KafkaDataSourceConfig{
		Topic:              topicName,
		DefaultStartOffset: -2,
	}

	return kafkaDataSourceConfig
}

func (c *Controller) startKafkaJobs(
	ctx *gin.Context,
	cmdCtx *CommandContext,
	prevState map[string]JobState,
	kafkaPayload []model.StartJobInfo,
	graphName string,
) {
	wg := sync.WaitGroup{}
	for _, payload := range kafkaPayload {
		kafkaIp, err := c.cfg.GetKafkaEndPoints()
		if err != nil {
			log.Warnf(ctx, "Failed to get kafka endpoints.")
			continue
		}
		body := model.GSQLKafkaDataSource{
			Name:           payload.DataSources[0].Name,
			DataSourceType: "kafka",
			Config: model.GSQLKafkaDataSourceConfig{
				Broker: kafkaIp,
			},
		}

		err = c.createGSQLKafkaDataSource(ctx, graphName, body)
		if err != nil {
			c.updateFailedKafkaJobResult(
				cmdCtx,
				payload.Name,
				prevState,
				err.Error(),
			)
			continue
		}

		wg.Add(1)
		jobLog := cmdCtx.Results[payload.Name]
		jobLog.DataSourceName = body.Name
		go func(payload model.StartJobInfo) {
			for i := 0; i < numOfRetries+1; i++ {
				log.Debugf(ctx, "Starting loading jobs: %v", payload.Name)
				res := c.requestGSQLServer(ctx, graphName, nil, []model.StartJobInfo{payload}, Start)
				if res.Error {
					jobLog.Error = true
					jobLog.Message = res.Message
					jobLog.Progress.Status = string(prevState[payload.Name])
					c.deleteTGKafkaConnector(jobLog.ConnectorName)
					break
				}

				result := handleGSQLResponse(cmdCtx, res, jobLog, payload.Name)
				if !jobLog.Error {
					jobLog.ID = cast.ToString(result["results"])
					jobLog.Progress = model.LoadingJobProgress{Status: string(Running)}
					break
				}

				jobLog.Progress.Status = string(prevState[payload.Name])

				// regex pattern to match "[ERROR] Topic name: '…' does not exist in kafka broker".
				regex, _ := regexp.Compile(
					`\[ERROR\]\sTopic\sname:\s'.+'\sdoes\snot\sexist\sin\sKafka\sbroker`,
				)
				if found := regex.MatchString(jobLog.Message); !found {
					break
				}

				if i < numOfRetries {
					time.Sleep(kafkaLoadingJobRetryTime)
				}
			}

			c.updateCacheAndDB(cmdCtx.GraphName, payload.Name, jobLog)
			key := genJobKey(cmdCtx.GraphName, payload.Name)
			_ = c.locker.Unlock(key)
			wg.Done()
		}(payload)
	}
	wg.Wait()
}

// PauseJobs pauses the given loading jobs.
func (c *Controller) PauseJobs(
	ctx *gin.Context,
	graphName string,
	jobNames []string,
) model.LoadingJobCommandResult {
	cmdCtx := c.initContext(graphName, jobNames, Pause)
	prevState := c.transitionState(cmdCtx, Pausing)

	jobIDs := make([]string, len(cmdCtx.ValidJobs))
	validJobNames := make([]string, len(cmdCtx.ValidJobs))
	for i, jobName := range cmdCtx.ValidJobs {
		jobIDs[i] = cmdCtx.Results[jobName].ID
		validJobNames[i] = jobName
	}

	if len(jobIDs) > 0 {
		log.Debugf(ctx, "Pausing loading jobs: %v", cmdCtx.ValidJobs)
		res := c.requestGSQLServer(ctx, graphName, jobIDs, nil, Pause)
		c.updateResults(ctx, cmdCtx, validJobNames, res, prevState, Paused)
	}

	return cmdCtx.Results
}

// ResumeJobs resumes the given loading jobs.
func (c *Controller) ResumeJobs(
	ctx *gin.Context,
	graphName string,
	jobNames []string,
) model.LoadingJobCommandResult {
	cmdCtx := c.initContext(graphName, jobNames, Resume)
	prevState := c.transitionState(cmdCtx, Resuming)

	jobIDs := make([]string, 0)
	validJobNames := make([]string, 0)

	// set <key = name of the Kafka loading jobs that failed to be resumed>
	failedResumeKafkaJobs := make(map[string]bool)

	for _, jobName := range cmdCtx.ValidJobs {
		jobID := cmdCtx.Results[jobName].ID
		if !isKafkaLoadingJob(jobName) {
			jobIDs = append(jobIDs, jobID)
			validJobNames = append(validJobNames, jobName)
			continue
		}

		if err := c.resumeTGKafkaConnector(cmdCtx.Results[jobName].ConnectorName); err != nil {
			failedResumeKafkaJobs[jobName] = true
			log.Errorf(ctx, "Failed to resume job: %s.", jobName)
			continue
		}
		jobIDs = append(jobIDs, jobID)
		validJobNames = append(validJobNames, jobName)
	}

	if len(jobIDs) > 0 {
		log.Debugf(ctx, "Resuming loading jobs: %v", cmdCtx.ValidJobs)
		res := c.requestGSQLServer(ctx, graphName, jobIDs, nil, Resume)
		c.updateResults(ctx, cmdCtx, validJobNames, res, prevState, Running)
	}

	if len(failedResumeKafkaJobs) > 0 {
		c.updateFailedKafkaJobResults(cmdCtx, failedResumeKafkaJobs, prevState)
	}

	return cmdCtx.Results
}

// StopJobs stops the given loading jobs.
func (c *Controller) StopJobs(
	ctx *gin.Context,
	graphName string,
	jobNames []string,
) model.LoadingJobCommandResult {
	cmdCtx := c.initContext(graphName, jobNames, Stop)
	prevState := c.transitionState(cmdCtx, Stopping)

	jobIDs := make([]string, len(cmdCtx.ValidJobs))
	validJobNames := make([]string, len(cmdCtx.ValidJobs))
	for i, jobName := range cmdCtx.ValidJobs {
		jobIDs[i] = cmdCtx.Results[jobName].ID
		validJobNames[i] = jobName
	}

	if len(jobIDs) > 0 {
		log.Debugf(ctx, "Stopping loading jobs: %v", cmdCtx.ValidJobs)
		res := c.requestGSQLServer(ctx, graphName, jobIDs, nil, Stop)
		c.updateResults(ctx, cmdCtx, validJobNames, res, prevState, Stopped)
	}

	return cmdCtx.Results
}

// GetJobsProgress retrieves and updates the progress of the given loading jobs.
func (c *Controller) GetJobsProgress(
	ctx *gin.Context,
	graphName string,
	jobNames []string,
) model.LoadingJobCommandResult {
	cmdResults := make(model.LoadingJobCommandResult)
	jobIDToName := make(map[string]string)

	jobsToUpdate := c.getJobsToUpdate(graphName, jobNames, cmdResults, jobIDToName)
	if len(jobsToUpdate) == 0 {
		return cmdResults
	}

	log.Debugf(ctx, "Retrieving progress for loading jobs with ids: %v", jobsToUpdate)
	res := c.requestGSQLServer(ctx, graphName, jobsToUpdate, nil, GetProgress)

	if !res.Error && res.Results != nil {
		resultSlice := cast.ToSlice(res.Results)

		for _, result := range resultSlice {
			prog, err := processLoadingProgressPayload(result, res.Message)
			if err != nil {
				continue
			}

			progress := cast.ToStringMap(result)
			overall := cast.ToStringMap(progress["overall"])

			jobID := cast.ToString(overall["id"])
			jobName := jobIDToName[jobID]
			jobLog := cmdResults[jobName]

			currState := JobState(jobLog.Progress.Status)
			nextState := JobState(prog.Status)
			if !isValidJobState(nextState) {
				prog.Status = string(currState)
			}
			// Since GSQL doesn't have STOPPED state (only PAUSED),
			// it is required to maintain STOPPED state.
			if (currState == Stopped || currState == Stopping) && nextState == Paused {
				prog.Status = string(Stopped)
			}

			if isKafkaLoadingJob(jobName) {
				if nextState == Stopped {
					c.kafkaLoadingJobCleanUp(
						ctx,
						graphName,
						jobLog.DataSourceName,
						jobName,
						jobLog.ConnectorName,
					)
					if currState == Running {
						prog.Status = string(Finished)

					}
					if currState == Finished || currState == Paused || currState == Failed {
						prog.Status = string(currState)
					}
				} else if nextState == Failed || nextState == Finished {
					c.kafkaLoadingJobCleanUp(
						ctx,
						graphName,
						jobLog.DataSourceName,
						jobName,
						jobLog.ConnectorName,
					)
					prog.Status = string(nextState)
				}
			}

			if JobState(prog.Status) == Finished {
				jobLog.Error = false
				jobLog.Message = ""
			}

			jobLog.Progress = prog
			c.updateCacheAndDB(graphName, jobName, jobLog)
		}
	}

	for _, jobName := range jobNames {
		key := genJobKey(graphName, jobName)
		_ = c.locker.Unlock(key)
	}

	return cmdResults
}

func processLoadingProgressPayload(result interface{}, message string) (model.LoadingJobProgress, error) {
	progress := cast.ToStringMap(result)
	// Handle error.
	if err := cast.ToBool(progress["error"]); err {
		return model.LoadingJobProgress{}, errors.New("")
	}

	overall := cast.ToStringMap(progress["overall"])
	// Workers.
	workers := cast.ToSlice(progress["workers"])
	if len(workers) == 0 {
		return model.LoadingJobProgress{}, nil
	}
	worker := cast.ToStringMap(workers[0])

	// Tasks.
	tasks := cast.ToSlice(worker["tasks"])
	if len(tasks) == 0 {
		return model.LoadingJobProgress{}, nil
	}
	// Assumption: a GUI loading job only loads from 1 file.
	// TODO: Get rid of the assumption.
	task := cast.ToStringMap(tasks[0])

	// File-level.
	statistics := cast.ToStringMap(overall["statistics"])
	if _, ok := statistics["fileLevel"]; !ok {
		return model.LoadingJobProgress{}, nil
	}
	fileLevel := cast.ToStringMap(statistics["fileLevel"])

	// TODO: Object level.

	progMap := map[string]any{
		"URI":                 task["filename"],
		"Source":              task["filename"],
		"Status":              overall["status"],
		"Message":             message,
		"StartTime":           overall["startTime"],
		"EndTime":             overall["endTime"],
		"Duration":            cast.ToFloat64(overall["duration"]) / 1000,
		"Percentage":          cast.ToFloat64(overall["progress"]) * 100,
		"CurrentSpeed":        overall["currentSpeed"],
		"AverageSpeed":        overall["averageSpeed"],
		"LoadedSize":          overall["size"],
		"LoadedLines":         fileLevel["validLine"],
		"NotEnoughTokenLines": fileLevel["notEnoughToken"],
		"OversizeTokenLines":  fileLevel["tokenExceedsBuffer"],
		"RejectedLines":       fileLevel["rejectLine"],
	}
	prog := model.LoadingJobProgress{}
	progJson, _ := tgJSON.Marshal(progMap)
	json.Unmarshal(progJson, &prog)
	return prog, nil
}

func (c *Controller) kafkaLoadingJobCleanUp(
	ctx *gin.Context,
	graphName string,
	dataSourceName string,
	jobName string,
	connectorName string,
) {
	if err := c.deleteGSQLKafkaDataSource(
		ctx,
		graphName,
		dataSourceName,
	); err != nil {
		log.Warnf(ctx,
			"Failed to delete data source %s for job %s",
			dataSourceName,
			jobName,
		)
	}
	if err := c.deleteTGKafkaConnector(connectorName); err != nil {
		log.Warnf(ctx, "Failed to delete connector %s.", connectorName)
	}
}

func (c *Controller) getJobsToUpdate(
	graphName string,
	jobNames []string,
	cmdResults model.LoadingJobCommandResult,
	jobIDToName map[string]string,
) []string {
	jobsToUpdate := make([]string, 0)

	if len(jobNames) > 0 {
		keys := make([]string, len(jobNames))
		for i, jobName := range jobNames {
			keys[i] = genJobKey(graphName, jobName)
			cmdResults[jobName] = c.getJobLog(graphName, jobName)
		}

		if err := c.locker.LockAll(keys); err != nil {
			log.Errorf("Failed to lock %v: %v", keys, err)
			return jobsToUpdate
		}

		for i, jobName := range jobNames {
			jobLog := cmdResults[jobName]
			if len(jobLog.ID) > 0 && jobLog.Expiration.Before(time.Now()) {
				jobIDToName[jobLog.ID] = jobName
				jobsToUpdate = append(jobsToUpdate, jobLog.ID)
			} else {
				_ = c.locker.Unlock(keys[i])
			}
		}
	}

	return jobsToUpdate
}

func (c *Controller) initContext(graphName string, jobNames []string, command Command) *CommandContext {
	validJobs := make([]string, 0)
	results := make(model.LoadingJobCommandResult)
	if len(jobNames) > 0 {
		keys := make([]string, len(jobNames))
		for i, jobName := range jobNames {
			keys[i] = genJobKey(graphName, jobName)
			results[jobName] = c.getJobLog(graphName, jobName)
		}

		if err := c.locker.LockAll(keys); err != nil {
			log.Errorf("Failed to lock %v: %v", keys, err)
			return &CommandContext{
				GraphName: graphName,
				Command:   command,
				ValidJobs: validJobs,
				Results:   results,
			}
		}

		for i, jobName := range jobNames {
			isValid := checkCommand(command, JobState(results[jobName].Progress.Status))

			if isValid {
				validJobs = append(validJobs, jobName)
			} else {
				_ = c.locker.Unlock(keys[i])
			}
		}
	}

	return &CommandContext{
		GraphName: graphName,
		Command:   command,
		ValidJobs: validJobs,
		Results:   results,
	}
}

func (c *Controller) transitionState(
	cmdCtx *CommandContext,
	nextState JobState,
) map[string]JobState {
	prevState := make(map[string]JobState)
	for _, jobName := range cmdCtx.ValidJobs {
		jobLog := cmdCtx.Results[jobName]
		prevState[jobName] = JobState(jobLog.Progress.Status)
		// If the START command fails in INITIAL state, transition to FAILED.
		// Since only START command is valid on INITIAL state, no further check is needed.
		if jobLog.Progress.Status == string(Finished) {
			prevState[jobName] = Failed
		}

		jobLog.Progress.Status = string(nextState)
		c.updateCacheAndDB(cmdCtx.GraphName, jobName, jobLog)
	}

	return prevState
}

func (c *Controller) updateResults(
	ctx *gin.Context,
	cmdCtx *CommandContext,
	jobNames []string,
	res *model.Response,
	prevState map[string]JobState,
	nextState JobState,
) {
	for _, jobName := range jobNames {
		jobLog := cmdCtx.Results[jobName]

		if res.Error {
			jobLog.Error = true
			jobLog.Message = res.Message
			jobLog.Progress.Status = string(prevState[jobName])
		} else {
			id := jobLog.ID
			if cmdCtx.Command == Start {
				id = jobName
			}

			result := handleGSQLResponse(cmdCtx, res, jobLog, id)
			if !jobLog.Error {
				jobLog.Progress.Status = string(nextState)
				// If START was executed, save the job id and re-initialize the loading progress.
				if cmdCtx.Command == Start {
					jobLog.ID = cast.ToString(result["results"])
					jobLog.Progress = model.LoadingJobProgress{Status: string(Running)}
				}

				if isKafkaLoadingJob(jobName) {
					var err error
					if cmdCtx.Command == Pause {
						err = c.pauseTGKafkaConnector(jobLog.ConnectorName)
					} else if cmdCtx.Command == Stop {
						if err := c.deleteGSQLKafkaDataSource(
							ctx,
							cmdCtx.GraphName,
							jobLog.DataSourceName,
						); err != nil {
							log.Warnf(ctx,
								"Failed to delete data source %s, for job %s.",
								jobLog.DataSourceName,
								jobName,
							)
						}
						err = c.deleteTGKafkaConnector(jobLog.ConnectorName)
					}

					if err != nil {
						log.Warnf(ctx,
							"Failed to %s connector %s.",
							strings.ToLower(string(cmdCtx.Command)),
							jobLog.ConnectorName,
						)
					}
				}
			} else {
				jobLog.Progress.Status = string(prevState[jobName])
			}
		}

		c.updateCacheAndDB(cmdCtx.GraphName, jobName, jobLog)
		key := genJobKey(cmdCtx.GraphName, jobName)
		_ = c.locker.Unlock(key)
	}
}

func (c *Controller) updateFailedKafkaJobResults(
	cmdCtx *CommandContext,
	failedKafkaJobs map[string]bool,
	prevState map[string]JobState,
) {
	for jobName := range failedKafkaJobs {
		c.updateFailedKafkaJobResult(
			cmdCtx,
			jobName,
			prevState,
			fmt.Sprintf(
				"Failed to %s loading job: %s.",
				strings.ToLower(string(cmdCtx.Command)),
				jobName,
			),
		)
	}
}

func (c *Controller) updateFailedKafkaJobResult(
	cmdCtx *CommandContext,
	failedKafkaJobName string,
	prevState map[string]JobState,
	errorMessage string,
) {
	jobLog := cmdCtx.Results[failedKafkaJobName]
	jobLog.Error = true
	jobLog.Message = errorMessage
	jobLog.Progress.Status = string(prevState[failedKafkaJobName])
	c.updateCacheAndDB(cmdCtx.GraphName, failedKafkaJobName, jobLog)
	key := genJobKey(cmdCtx.GraphName, failedKafkaJobName)
	_ = c.locker.Unlock(key)
}

func handleGSQLResponse(cmdCtx *CommandContext, res *model.Response, jobLog *model.LoadingJobLog, jobId string) map[string]interface{} {
	result, err := cast.ToStringMapE(cast.ToStringMap(res.Results)[jobId])
	if err != nil {
		result["error"] = true
		result["message"] = fmt.Sprintf("Failed to get the result of %s.", cmdCtx.Command)
	}

	jobLog.Error = cast.ToBool(result["error"])
	jobLog.Message = cast.ToString(result["message"])
	return result
}

func (c *Controller) getJobLog(graphName, jobName string) *model.LoadingJobLog {
	key := genJobKey(graphName, jobName)

	// Step 1: Attempt to retrieve job log from cache.
	if jobLog, found := c.cache.Get(key); found {
		return jobLog.(*model.LoadingJobLog)
	}

	// Step 2: If there is no job log in cache, attempt to retrieve from db.
	if jobLog, err := c.daoManager.GetLoadingJobLog(graphName, jobName); err == nil {
		now := time.Now()
		if jobLog.Expiration.After(now) {
			c.cache.Set(key, jobLog, jobLog.Expiration.Sub(now))
		}
		return jobLog
	}

	// Step 3: If no job log can be retrieved, return a new one.
	jobLog := &model.LoadingJobLog{
		ID: "",
		Progress: model.LoadingJobProgress{
			Status: string(Initial),
		},
	}
	c.updateCacheAndDB(graphName, jobName, jobLog)
	return jobLog
}

func (c *Controller) updateCacheAndDB(graphName, jobName string, jobLog *model.LoadingJobLog) {
	key := genJobKey(graphName, jobName)

	c.cache.SetDefault(key, jobLog)

	_, jobLog.Expiration, _ = c.cache.GetWithExpiration(key)
	if err := c.daoManager.UpsertLoadingJobLog(graphName, jobName, *jobLog); err != nil {
		log.Errorf(
			"Failed to upsert log for loading job %s in graph %s: %v",
			jobName,
			graphName,
			err,
		)
	}
}

func (c *Controller) requestGSQLServer(
	ctx *gin.Context,
	graphName string,
	jobIDs []string,
	jobInfo []model.StartJobInfo,
	command Command,
) *model.Response {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s",
		c.cfg.GetNginxProtocol(),
		c.cfg.GetGSQLServerHostname(),
		c.cfg.GetNginxPort(),
		"/gsqlserver/gsql/loading-jobs",
	)

	query := make(url.Values)
	query.Add("graph", graphName)
	query.Add("action", strings.ToLower(string(command)))
	if command != Start {
		for _, id := range jobIDs {
			query.Add("jobId", id)
		}
	}

	// specify the host to run the loading job, otherwise it will run on all nodes, led to duplicated loading jobs
	hostId, _ := c.cfg.GetHostID()
	if command == Start {
		for i := range jobInfo {
			for j := range jobInfo[i].DataSources {
				if jobInfo[i].DataSources[j].Name == "file" && jobInfo[i].DataSources[j].Hostname == "" {
					jobInfo[i].DataSources[j].Hostname = hostId
				}

			}
		}
	}

	req := createHTTPRequest(command, requestURL, jobInfo)
	req.SetBasicAuth(mw.GetUsername(ctx), mw.GetPassword(ctx))
	req.URL.RawQuery = query.Encode()
	h.SetFromGraphStudio(req)
	// Add X-Forwarded-For
	h.SetForwardedForFromContext(ctx, req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            c.cfg.GetHTTPRequestTimeout(),
		RetryMax:           0,
		RetryWaitMin:       c.cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       c.cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: c.cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server request.
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(ctx, "Failed to get response from GSQL server: %v", err)
		return &model.Response{Error: true}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(ctx, "Failed to read response from GSQL server: %v", err)
		return &model.Response{Error: true}
	}

	res := &model.Response{}
	if err := json.Unmarshal(body, res); err != nil {
		log.Errorf(ctx, "Failed to parse response from GSQL server: %v", err)
		return &model.Response{Error: true, Message: string(body)}
	}

	return res
}

// TODO: refactor this, it is a duplicate implementation of requestGSQLServer().
func requestGSQLServerDataSource(
	ctx *gin.Context,
	cfg *config.Config,
	body []byte,
	httpMethod string,
	rawQuery string,
) (*proxy.Response, error) {
	requestURL := fmt.Sprintf(
		"%s://%s:%d%s%s",
		cfg.GetNginxProtocol(),
		cfg.GetGSQLServerHostname(),
		cfg.GetNginxPort(),
		"/gsqlserver",
		"/gsql/data-sources",
	)

	reqBody := io.NopCloser(bytes.NewBuffer(body))

	req, _ := http.NewRequestWithContext(context.Background(), httpMethod, requestURL, reqBody)
	if ctx.GetHeader("GSQL-TIMEOUT") != "" {
		req.Header.Set("GSQL-TIMEOUT", ctx.GetHeader("GSQL-TIMEOUT"))
	}
	req.URL.RawQuery = rawQuery
	req.SetBasicAuth(mw.GetUsername(ctx), mw.GetPassword(ctx))
	h.SetFromGraphStudio(req)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:            cfg.GetHTTPRequestTimeout(),
		RetryMax:           cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin:       cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax:       cfg.GetHTTPRequestRetryWaitMax(),
		InsecureSkipVerify: cfg.GetNginxSSLEnabled(), // skip verify for internal GSQL server requests.
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf(ctx, "Failed to get response from GSQL server: %v", err)
		return nil, err
	}
	if resp.StatusCode >= 300 {
		log.Warnf(ctx,
			"GSQL server response status code: %v, response status: %v",
			resp.StatusCode,
			resp.Status,
		)
		return nil, fmt.Errorf(
			"GSQL server response status code >= 300: %v, GSQL response status: %v",
			resp.StatusCode,
			resp.Status,
		)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf(ctx, "Failed to read response from GSQL server: %v", err)
		return nil, err
	}

	res := proxy.Response{}
	if err = json.Unmarshal(respBody, &res); err != nil {
		log.Errorf(ctx, "Failed to parse response from GSQL server: %v", err)
		return nil, err
	}

	return &res, nil
}

func createHTTPRequest(command Command, requestURL string, jobInfo []model.StartJobInfo) *http.Request {
	var req *http.Request
	switch command {
	case Start:
		body, _ := tgJSON.Marshal(jobInfo)
		req, _ = http.NewRequest(http.MethodPost, requestURL, bytes.NewBuffer(body))
	case GetProgress:
		req, _ = http.NewRequest(http.MethodGet, requestURL, nil)
	default:
		req, _ = http.NewRequest(http.MethodPost, requestURL, nil)
	}
	return req
}

func (c *Controller) createGSQLKafkaDataSource(
	ctx *gin.Context,
	graphName string,
	dataSource model.GSQLKafkaDataSource,
) error {
	body, err := tgJSON.Marshal(dataSource)
	if err != nil {
		return err
	}

	log.Debugf(ctx, "Creating Kafka data source: %s", dataSource.Name)
	resp, err := requestGSQLServerDataSource(
		ctx,
		c.cfg,
		body,
		http.MethodPost,
		fmt.Sprintf("graph=%s", graphName),
	)

	errMessage := fmt.Sprintf("Failed to create Kafka data source %s: ", dataSource.Name)
	if err != nil {
		log.Warnf(ctx, "%s: %s", errMessage, err.Error())
		return err
	}

	if resp.Error {
		log.Warnf(ctx, "%s: %s", errMessage, resp.Message)
		return fmt.Errorf("%s: %s", errMessage, resp.Message)
	}
	log.Debugf(ctx, "Created Kafka data source: %s", dataSource.Name)

	return nil
}

func (c *Controller) deleteGSQLKafkaDataSource(
	ctx *gin.Context,
	graphName string,
	dataSourceName string,
) error {
	body := []byte{}

	log.Debugf(ctx, "Deleting Kafka data source: %s", dataSourceName)
	resp, err := requestGSQLServerDataSource(
		ctx,
		c.cfg,
		body,
		http.MethodDelete,
		fmt.Sprintf("graph=%s&name=%s", graphName, dataSourceName),
	)
	errMessage := fmt.Sprintf("Failed to delete Kafka data source %s: ", dataSourceName)
	if err != nil {
		log.Warnf(ctx, "%s: %s", errMessage, err.Error())
		return err
	}

	if resp.Error {
		log.Warnf(ctx, "%s: %s", errMessage, resp.Message)
		return fmt.Errorf("%s: %s", errMessage, resp.Message)
	}

	log.Debugf(ctx, "Deleted Kafka data source: %s", dataSourceName)
	return nil
}

func checkCommand(command Command, state JobState) bool {
	switch command {
	case Start:
		switch state {
		case
			Initial,
			Stopped,
			Finished,
			Failed:
			return true

		default:
			return false
		}

	case Pause:
		if state == Running {
			return true
		}
		return false

	case Resume:
		if state == Paused {
			return true
		}
		return false

	case Stop:
		if state == Running || state == Paused {
			return true
		}
		return false

	// GetProgress is always valid.
	default:
		return true
	}
}

func genJobKey(graphName, jobName string) string {
	return fmt.Sprintf("%s/%s/%s", ljPrefix, graphName, jobName)
}

func (c *Controller) createTGKafkaConnector(
	jobName string,
	dataSourceName string,
	fileURI string,
	graphName string,
	parsingOptions model.CSVDataSourceOption,
) (string, string, error) {
	cntlrClient, err := tg.ControllerClient(c.cfg)
	if err != nil {
		log.Warnf("Failed to create controller client: %v", err)
		return "", "", err
	}

	dataSourceType := getDataSourceType(jobName)
	dataSource, err := c.daoManager.GetDataSource(graphName, dataSourceType, dataSourceName)
	if err != nil {
		log.Warnf("Failed to retrieve data source %s for type %s: %v", dataSourceName, dataSourceType, err)
		return "", "", err
	}

	connectorName, topicName, kafkaConfigContent, err := c.createKafkaConfigContent(
		dataSourceType,
		dataSource,
		jobName,
		fileURI,
		parsingOptions,
	)
	if err != nil {
		log.Warnf("Failed to create Kafka connector config for connector: %s for loading job: %s", connectorName, jobName)
		return "", "", err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	req := &pb.ConnectorCreationRequest{
		FileBody: []byte(kafkaConfigContent),
	}
	log.Debugf("Creating Kafka connector %s for Kafka topic %s.", connectorName, topicName)
	resp, err := cntlrClient.NewConnector(ctx, req)
	if err != nil {
		rpcError := rpc.Error(err, tgServ.CONTROLLER, cntlrClient.NewConnector)
		log.Warnf("Failed to create Kafka connector %s for Kafka topic %s: %v.", connectorName, topicName, rpcError)
		return "", "", rpcError
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		pbError := tgErr.NewErrFromPbError(resp.GetError())
		log.Warnf("Failed to create Kafka connector %s for Kafka topic %s: %v.", connectorName, topicName, pbError)
		return "", "", pbError
	}
	log.Debugf("Created Kafka connector %s for Kafka topic %s.", connectorName, topicName)

	return connectorName, topicName, nil
}

func getDataSourceType(jobName string) string {
	if isGCSLoadingJob(jobName) {
		return model.GoogleCloudStorage
	} else if isS3LoadingJob(jobName) {
		return model.AmazonS3
	} else if isABSLoadingJob(jobName) {
		return model.AzureBlobStorage
	}
	// TODO: Support more data source types in the future.

	return ""
}

func createGCSConnectorConfigContent(gcsDataSource model.GoogleCloudStorageDataSource) (string, error) {
	// Replace '\n' with '\\n' in the private key because we need to escape '\' in go string.
	gcsDataSource.PrivateKey = strings.Replace(gcsDataSource.PrivateKey, "\n", `\n`, -1)
	gcsConnectorConfig, err := applyDataToTemplateAndGetResult(
		gcs_connector_config_template,
		gcsDataSource,
	)
	if err != nil {
		return "", err
	}

	return gcsConnectorConfig, err
}

func createS3ConnectorConfigContent(s3DataSource model.AmazonS3DataSource) (string, error) {
	s3ConnectorConfig, err := applyDataToTemplateAndGetResult(
		s3_connector_config_template,
		s3DataSource,
	)
	if err != nil {
		return "", err
	}

	return s3ConnectorConfig, nil
}

func createABSConnectorConfigContent(
	absDataSource model.AzureBlobStorageDataSource,
	containerName string,
) (string, error) {
	absConnectorConfigData := absConnectorConfigData{
		AzureBlobStorageDataSource: absDataSource,
		ContainerName:              containerName,
	}
	absConnectorConfig, err := applyDataToTemplateAndGetResult(
		abs_connector_config_template,
		absConnectorConfigData,
	)
	if err != nil {
		return "", err
	}

	return absConnectorConfig, nil
}

func (c *Controller) createKafkaConfigContent(
	dataSourceType string,
	dataSource interface{},
	jobName string,
	fileURI string,
	parsingOptions model.CSVDataSourceOption,
) (string, string, string, error) {
	var kafkaConnectorDaraSourceConfig string
	var err error

	switch dataSourceType {
	case model.AmazonS3:
		s3DataSource := dataSource.(*model.AmazonS3DataSource)
		kafkaConnectorDaraSourceConfig, err = createS3ConnectorConfigContent(*s3DataSource)
	case model.GoogleCloudStorage:
		gcsDataSource := dataSource.(*model.GoogleCloudStorageDataSource)
		kafkaConnectorDaraSourceConfig, err = createGCSConnectorConfigContent(*gcsDataSource)
	case model.AzureBlobStorage:
		var containerName string

		absDataSource := dataSource.(*model.AzureBlobStorageDataSource)
		containerName, err = helper.FindContainerNameFromABSUri(fileURI)
		if err != nil {
			return "", "", "", err
		}
		kafkaConnectorDaraSourceConfig, err = createABSConnectorConfigContent(*absDataSource, containerName)
		// TODO: Support more data sources in the future.
	}

	if err != nil {
		return "", "", "", err
	}

	eolChar := parsingOptions.Eol
	if tempEolChar, ok := eolMap[parsingOptions.Eol]; ok {
		eolChar = tempEolChar
	}
	connectorCreationTime := time.Now().Unix()
	connectorName := fmt.Sprintf("kafka_connector_%s_%d", jobName, connectorCreationTime)
	topicName := fmt.Sprintf("kafka_topic_%s_%d", jobName, connectorCreationTime)

	kafkaConnectorDataFileConfigData := kafkaConnectorDataFileConfigData{
		Eol:            eolChar,
		Header:         parsingOptions.Header,
		ConnectorTitle: fmt.Sprintf("connector_%s", jobName),
		ConnectorName:  connectorName,
		Topic:          topicName,
		FileUri:        fileURI,
	}

	kafkaConnectorDataFileConfig, err := applyDataToTemplateAndGetResult(
		kafka_connector_data_file_template,
		kafkaConnectorDataFileConfigData,
	)
	if err != nil {
		return "", "", "", err
	}

	kafkaConnectorConfigData := kafkaConnectorConfigData{
		KafkaConnectorDaraSourceConfig: kafkaConnectorDaraSourceConfig,
		KafkaConnectorDataFileConfig:   kafkaConnectorDataFileConfig,
	}

	kafkaConfigContent, err := applyDataToTemplateAndGetResult(
		kafka_connector_config_template,
		kafkaConnectorConfigData,
	)
	if err != nil {
		return "", "", "", err
	}

	return connectorName, topicName, kafkaConfigContent, nil
}

func applyDataToTemplateAndGetResult(baseTemplate string, data interface{}) (string, error) {
	var result strings.Builder
	t, err := template.New("").Parse(baseTemplate)
	if err != nil {
		log.Errorf("Initializing template failed with error: %v", err)
		return "", err
	}

	if err := t.Execute(&result, data); err != nil {
		log.Errorf("Applying template failed with error: %v", err)
		return "", err
	}

	return result.String(), nil
}

func (c *Controller) deleteTGKafkaConnector(connectorName string) error {
	cntlrClient, err := tg.ControllerClient(c.cfg)
	if err != nil {
		log.Warnf("Failed to create controller client: %v", err)
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	req := &pb.ConnectorRequest{
		Names: []string{connectorName},
	}
	log.Debugf("Deleting Kafka connector %s.", connectorName)
	resp, err := cntlrClient.DeleteConnector(ctx, req)
	if err != nil {
		rpcError := rpc.Error(err, tgServ.CONTROLLER, cntlrClient.DeleteConnector)
		log.Warnf("Failed to delete Kafka connector %s: %v.", connectorName, rpcError)
		return rpcError
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		pbError := tgErr.NewErrFromPbError(resp.GetError())
		log.Warnf("Failed to delete Kafka connector %s: %v.", connectorName, pbError)
		return pbError
	}
	log.Debugf("Deleted Kafka connector %s.", connectorName)

	return nil
}

func (c *Controller) pauseTGKafkaConnector(connectorName string) error {
	cntlrClient, err := tg.ControllerClient(c.cfg)
	if err != nil {
		log.Warnf("Failed to create controller client: %v", err)
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	req := &pb.ConnectorRequest{
		Names: []string{connectorName},
	}
	log.Debugf("Pausing Kafka connector %s.", connectorName)
	resp, err := cntlrClient.PauseConnector(ctx, req)
	if err != nil {
		rpcError := rpc.Error(err, tgServ.CONTROLLER, cntlrClient.PauseConnector)
		log.Warnf("Failed to pause Kafka connector %s: %v.", connectorName, rpcError)
		return rpcError
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		pbError := tgErr.NewErrFromPbError(resp.GetError())
		log.Warnf("Failed to pause Kafka connector %s: %v.", connectorName, pbError)
		return pbError
	}
	log.Debugf("Paused Kafka connector %s.", connectorName)

	return nil
}

func (c *Controller) resumeTGKafkaConnector(connectorName string) error {
	cntlrClient, err := tg.ControllerClient(c.cfg)
	if err != nil {
		log.Warnf("Failed to create controller client: %v", err)
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()

	req := &pb.ConnectorRequest{
		Names: []string{connectorName},
	}
	log.Debugf("Resuming Kafka connector %s.", connectorName)
	resp, err := cntlrClient.ResumeConnector(ctx, req)
	if err != nil {
		rpcError := rpc.Error(err, tgServ.CONTROLLER, cntlrClient.ResumeConnector)
		log.Warnf("Failed to resume connector %s: %v.", connectorName, rpcError)
		return rpcError
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		pbError := tgErr.NewErrFromPbError(resp.GetError())
		log.Warnf("Failed to resume Kafka connector %s: %v.", connectorName, pbError)
		return pbError
	}
	log.Debugf("Resumed Kafka connector %s.", connectorName)

	return nil
}
