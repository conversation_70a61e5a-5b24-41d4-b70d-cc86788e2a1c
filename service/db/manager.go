package db

import (
	"bytes"
	"compress/gzip"
	"context"
	"errors"
	"io"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/cqrs/tutopia/common/rpc"
	tgServ "github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/cqrs/tutopia/invoker/util"
	tgErr "github.com/tigergraph/cqrs/util/errors"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/config"
	tgSync "github.com/tigergraph/gus/lib/sync"
	"github.com/tigergraph/gus/lib/tg"
)

var (
	ErrNotFound     = errors.New("key not found")
	ErrAlreadyExist = errors.New("key already exists")
)

// Manager is the database service manager.
type Manager struct {
	cfg    *config.Config
	locker *tgSync.KeyLocker
}

// Enforce database service to implement LifeCycle.
var _ interfaces.LifeCycle = (*Manager)(nil)

func New(cfg *config.Config) *Manager {
	return &Manager{cfg: cfg}
}

// SetUp prepares the database service.
func (m *Manager) SetUp() error {
	var err error
	m.locker, err = tgSync.NewKeyLocker()
	log.Info("set up manager", m.locker)
	return err
}

// TearDown doesn't do anything at the moment.
func (m *Manager) TearDown() {}

// Get returns the value of the given key in the kv store.
// Key should not start with "/", for example: "a/b/c".
func (m *Manager) Get(key string) ([]byte, error) {
	results, err := m.get(key, false)
	if err != nil {
		return nil, err
	}

	if len(results) == 0 {
		return nil, ErrNotFound
	}
	return results[0], nil
}

// GetAll returns the list of keys which match a prefix in the kv store.
// Key should not start with "/", for example: "a/b/c".
func (m *Manager) GetAllKeys(key string) ([]string, error) {
	return m.getKeys(key, true)
}

// GetAll returns the list of values which keys match a prefix in the kv store.
// Key should not start with "/", for example: "a/b/c".
func (m *Manager) GetAll(key string) ([][]byte, error) {
	return m.get(key, true)
}

// Create inserts a value into the kv store.
// Key should not start with "/", for example: "a/b/c".
func (m *Manager) Create(key string, value []byte) error {
	if m.locker == nil {
		err := m.SetUp()
		if err != nil {
			return err
		}
	}
	if err := m.locker.Lock(key); err != nil {
		return err
	}
	defer func() { _ = m.locker.Unlock(key) }()

	_, err := m.Get(key)
	if err == nil {
		return ErrAlreadyExist
	} else if !errors.Is(err, ErrNotFound) {
		return err
	}

	return m.put(key, value)
}

// Upsert inserts a value into or replaces a value in the kv store.
// Key should not start with "/", for example: "a/b/c".
func (m *Manager) Upsert(key string, value []byte) error {
	return m.put(key, value)
}

// Delete deletes a key from the kv store.
// Key should not start with "/", for example: "a/b/c".
func (m *Manager) Delete(key string) error {
	return m.delete(key, false)
}

// DeleteAll deletes all keys matching a prefix from the kv store.
// Key should not start with "/", for example: "a/b/c".
func (m *Manager) DeleteAll(key string) error {
	return m.delete(key, true)
}

// Import imports data into the kv store.
func (m *Manager) Import(data map[string][]byte) error {
	cntlrClient, err := tg.ControllerClient(m.cfg)
	if err != nil {
		return err
	}

	i := 0
	kvs := make([]*pb.KVPair, len(data))
	for k, v := range data {
		kvs[i] = &pb.KVPair{Key: []byte(k), Value: v}
		i++
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         kvs,
	}
	resp, err := cntlrClient.KVPut(ctx, req)
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.KVPut)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

// Export exports data from the kv store.
func (m *Manager) Export(key string) (map[string][]byte, error) {
	cntlrClient, err := tg.ControllerClient(m.cfg)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(key)},
		Prefix:      true,
	}
	resp, err := cntlrClient.KVGet(ctx, req)
	if err != nil {
		return nil, rpc.Error(err, tgServ.CONTROLLER, cntlrClient.KVGet)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return nil, tgErr.NewErrFromPbError(resp.GetError())
	}

	results := make(map[string][]byte)
	for _, kv := range resp.GetKvs() {
		results[string(kv.Key)] = kv.Value
	}

	return results, nil
}

func (m *Manager) getKeys(key string, matchPrefix bool) ([]string, error) {
	kvs, err := getKvs(m.cfg, key, matchPrefix)
	if err != nil {
		return nil, err
	}

	results := make([]string, len(kvs))
	for i, kv := range kvs {
		results[i] = string(kv.Key)
	}

	return results, nil
}

func (m *Manager) get(key string, matchPrefix bool) ([][]byte, error) {
	kvs, err := getKvs(m.cfg, key, matchPrefix)
	if err != nil {
		return nil, err
	}

	results := make([][]byte, len(kvs))
	for i, kv := range kvs {
		results[i], err = Decompress(kv.Value)
		if err != nil {
			return nil, err
		}
	}

	return results, nil
}

func getKvs(cfg *config.Config, key string, matchPrefix bool) ([]*pb.KVPair, error) {
	cntlrClient, err := tg.ControllerClient(cfg)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.KVGetRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(key)},
		Prefix:      matchPrefix,
	}
	resp, err := cntlrClient.KVGet(ctx, req)
	if err != nil {
		return nil, rpc.Error(err, tgServ.CONTROLLER, cntlrClient.KVGet)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return nil, tgErr.NewErrFromPbError(resp.GetError())
	}

	return resp.GetKvs(), nil
}

func (m *Manager) put(key string, value []byte) error {
	cntlrClient, err := tg.ControllerClient(m.cfg)
	if err != nil {
		return err
	}

	v, err := Compress(value)
	if err != nil {
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.KVPutRequest{
		ServiceName: tgServ.GUI,
		Kvs:         []*pb.KVPair{{Key: []byte(key), Value: v}},
	}
	resp, err := cntlrClient.KVPut(ctx, req)
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.KVPut)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

func (m *Manager) delete(key string, matchPrefix bool) error {
	cntlrClient, err := tg.ControllerClient(m.cfg)
	if err != nil {
		return err
	}

	ctx, cancel := context.WithTimeout(context.Background(), util.GRPC_CLIENT_TIMEOUT)
	defer cancel()
	req := &pb.KVDeleteRequest{
		ServiceName: tgServ.GUI,
		Keys:        [][]byte{[]byte(key)},
		Prefix:      matchPrefix,
	}
	resp, err := cntlrClient.KVDelete(ctx, req)
	if err != nil {
		return rpc.Error(err, tgServ.CONTROLLER, cntlrClient.KVDelete)
	}
	if resp.GetError() != nil && resp.GetError().GetCode() != tgErr.EcodeOk {
		return tgErr.NewErrFromPbError(resp.GetError())
	}

	return nil
}

func Compress(value []byte) ([]byte, error) {
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)

	if _, err := writer.Write(value); err != nil {
		return nil, err
	}

	if err := writer.Close(); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func Decompress(value []byte) ([]byte, error) {
	reader, err := gzip.NewReader(bytes.NewBuffer(value))
	if err != nil {
		return nil, err
	}

	bytes, err := io.ReadAll(reader)
	if err != nil {
		return nil, err
	}

	if err := reader.Close(); err != nil {
		return nil, err
	}

	return bytes, nil
}
