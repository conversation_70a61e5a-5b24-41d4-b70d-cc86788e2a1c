package db

import (
	"bytes"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/tigergraph/cqrs/tutopia/common/pb"
)

const (
	key1      = "test_key1"
	key2      = "test_key2"
	keyPrefix = "test_key"

	value1         = "test_value1"
	value2         = "test_value2"
	modified_value = "modified_value"
)

// test function: Create, Get, Delete
func TestCreateAndDelete(t *testing.T) {
	cntlrClient, err := SetupCntlrClient(t)
	if err != nil {
		t.Fatal(err)
	}

	keyLocker := &fakeKeyLocker{}

	m := Manager{
		locker:      keyLocker,
		cntlrClient: cntlrClient,
	}

	_, err = m.Get(key1)
	require.Error(t, err)
	require.Equal(t, ErrNotFound, err)

	err = m.Create(key1, []byte(value1))
	require.NoError(t, err)

	resultValue, err := m.Get(key1)
	require.NoError(t, err)
	require.Equal(t, []byte(value1), resultValue)

	err = m.Delete(key1)
	require.NoError(t, err)

	_, err = m.Get(key1)
	require.Error(t, err)
	require.Equal(t, ErrNotFound, err)
}

// test function: Upsert, GetAll, GetAllKey, DeleteAll
func TestUpsertAndDeleteAll(t *testing.T) {
	cntlrClient, err := SetupCntlrClient(t)
	if err != nil {
		t.Fatal(err)
	}

	keyLocker := &fakeKeyLocker{}

	m := Manager{
		locker:      keyLocker,
		cntlrClient: cntlrClient,
	}

	_, err = m.Get(key1)
	require.Error(t, err)
	require.Equal(t, ErrNotFound, err)

	t.Run("case upsert datas", func(t *testing.T) {

		err = m.Upsert(key1, []byte(value1))
		require.NoError(t, err)

		err = m.Upsert(key2, []byte(value2))
		require.NoError(t, err)

		resultValue, err := m.GetAll(keyPrefix)
		require.NoError(t, err)
		resultKey, err := m.GetAllKeys(keyPrefix)
		require.NoError(t, err)

		if !containsValue(resultValue, []byte(value1)) || !containsValue(resultValue, []byte(value2)) {
			t.Fatal(ErrNotFound)
		}
		if !containsKey(resultKey, key1) || !containsKey(resultKey, key2) {
			t.Fatal(ErrNotFound)
		}

		err = m.DeleteAll(keyPrefix)
		require.NoError(t, err)

		result, err := m.GetAll(keyPrefix)
		require.NoError(t, err)
		if containsValue(result, []byte(value1)) || containsValue(result, []byte(value2)) {
			t.Fatal(ErrNotFound)
		}
	})

	t.Run("case replace one data", func(t *testing.T) {
		err = m.Create(key1, []byte(value1))
		require.NoError(t, err)

		resultValue, err := m.Get(key1)
		require.NoError(t, err)
		require.Equal(t, []byte(value1), resultValue)

		err = m.Upsert(key1, []byte(modified_value))
		require.NoError(t, err)

		result, err := m.Get(key1)
		require.NoError(t, err)
		require.Equal(t, []byte(modified_value), result)
	})
}

// test function: import, export
func TestImportAndExport(t *testing.T) {
	cntlrClient, err := SetupCntlrClient(t)
	if err != nil {
		t.Fatal(err)
	}

	keyLocker := &fakeKeyLocker{}

	m := Manager{
		locker:      keyLocker,
		cntlrClient: cntlrClient,
	}

	_, err = m.Get(key1)
	require.Error(t, err)
	require.Equal(t, ErrNotFound, err)

	compressValue1, _ := Compress([]byte(value1))
	compressValue2, _ := Compress([]byte(value2))
	data := make(map[string][]byte)
	data[key1] = []byte(compressValue1)
	data[key2] = []byte(compressValue2)
	err = m.Import(data)
	require.NoError(t, err)

	result, err := m.GetAll(keyPrefix)
	require.NoError(t, err)
	if !containsValue(result, []byte(value1)) || !containsValue(result, []byte(value2)) {
		t.Fatal(ErrNotFound)
	}

	compressedValue1, _ := Compress([]byte(value1))
	compressedValue2, _ := Compress([]byte(value2))
	dataMap := make(map[string][]byte)
	dataMap[key1] = []byte(compressedValue1)
	dataMap[key2] = []byte(compressedValue2)
	data, err = m.Export(keyPrefix)
	require.NoError(t, err)
	require.Equal(t, data, dataMap)
}

func SetupCntlrClient(t *testing.T) (pb.ControllerClient, error) {
	data := make(map[string][]byte)

	cntlrClient := &fakeControllerClient{
		data: data,
	}

	return cntlrClient, nil
}

func containsKey(slices []string, key string) bool {
	for i := range slices {
		if slices[i] == key {
			return true
		}
	}
	return false
}

func containsValue(slices [][]byte, value []byte) bool {
	for i := range slices {
		if bytes.Equal(slices[i], value) {
			return true
		}
	}
	return false
}
