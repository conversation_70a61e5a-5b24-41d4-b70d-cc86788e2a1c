package udf

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
)

type udfService struct {
	gsqlHTTPClient interfaces.GSQLHTTPClient
}

func New(gsqlHTTPClient interfaces.GSQLHTTPClient) interfaces.UDFService {
	return &udfService{
		gsqlHTTPClient: gsqlHTTPClient,
	}
}

func (s *udfService) GetUDF(ctx *gin.Context, creds *model.UserCredentials, filename string) (string, error) {
	request := &model.Request{
		Method: http.MethodGet,
		Url:    "/gsql/v1/udt/files/" + filename,
	}

	response := &model.Response{}
	err := s.gsqlHTTPClient.Request(
		ctx,
		creds,
		request,
		response,
	)
	if err != nil {
		return "", err
	}

	if response.Error {
		return "", errors.New(response.Message)
	}

	results := response.Results.(map[string]interface{})
	udf, ok := results[filename].(string)
	if !ok {
		return "", errors.New("failed to get UDF content")
	}

	return udf, nil
}

func (s *udfService) SetUDF(ctx *gin.Context, creds *model.UserCredentials, filename string, udf string) error {
	request := &model.Request{
		Method: http.MethodPut,
		Url:    "/gsql/v1/udt/files/" + filename,
		Body:   []byte(udf),
		Header: map[string]string{
			"Content-Type": "text/plain",
		},
	}

	response := &model.Response{}
	err := s.gsqlHTTPClient.Request(
		ctx,
		creds,
		request,
		response,
	)
	if err != nil {
		return err
	}

	if response.Error {
		return errors.New(response.Message)
	}

	return nil

}
