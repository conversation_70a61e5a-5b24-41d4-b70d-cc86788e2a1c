package gstatusgraph

import (
	"reflect"
	"testing"

	"github.com/spf13/cast"
)

func Test_parseLine(t *testing.T) {
	type args struct {
		line string
	}
	tests := []struct {
		name string
		args args
		want *NodeInfo
	}{
		{
			name: "normal node info",
			args: args{
				line: "[m1     ] Partition size: 382GiB, IDS size: 195GiB, Vertex count: 5160659122, Edge count: 7093844889, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0",
			},
			want: &NodeInfo{
				Name:                 "m1",
				PartitionSize:        382 * 1024 * 1024 * 1024,
				IDSsize:              195 * 1024 * 1024 * 1024,
				VertexCount:          5160659122,
				EdgeCount:            7093844889,
				NumOfDeletedVertices: 0,
				NumOfSkippedVertices: 0,
			},
		},
		{
			name: "zero patition size",
			args: args{
				line: "[m1     ] Partition size: 0GiB, IDS size: 0B, Vertex count: 0, Edge count: 0, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0",
			},
			want: &NodeInfo{
				Name:                 "m1",
				PartitionSize:        0,
				IDSsize:              0,
				VertexCount:          0,
				EdgeCount:            0,
				NumOfDeletedVertices: 0,
				NumOfSkippedVertices: 0,
			},
		},
		{
			name: "float patition size",
			args: args{
				line: "[m1     ] Partition size: 1.1GiB, IDS size: 19.5GiB, Vertex count: 5160659122, Edge count: 7093844889, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0",
			},
			want: &NodeInfo{
				Name:                 "m1",
				PartitionSize:        cast.ToInt64(1.1 * 1024 * 1024 * 1024),
				IDSsize:              cast.ToInt64(19.5 * 1024 * 1024 * 1024),
				VertexCount:          5160659122,
				EdgeCount:            7093844889,
				NumOfDeletedVertices: 0,
				NumOfSkippedVertices: 0,
			},
		},
		{
			name: "empty line",
			args: args{
				line: "",
			},
			want: nil,
		},
		{
			name: "invalid line",
			args: args{
				line: "invalid line",
			},
			want: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := parseLine(tt.args.line); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseLine() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_parseOutput(t *testing.T) {
	type args struct {
		output  []byte
		replica int
	}
	tests := []struct {
		name           string
		args           args
		topology_bytes int64
		wantErr        bool
	}{
		{
			name: "multi node graph",
			args: args{
				output: []byte(`[GRAPH  ] Graph was loaded (/home/<USER>/tigergraph/data/gstore):
				[m1     ] Partition size: 382GiB, IDS size: 195GiB, Vertex count: 5160659122, Edge count: 7093844889, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m2     ] Partition size: 382GiB, IDS size: 195GiB, Vertex count: 5160691829, Edge count: 7093710967, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m3     ] Partition size: 327GiB, IDS size: 168GiB, Vertex count: 4423317860, Edge count: 6080242602, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m4     ] Partition size: 327GiB, IDS size: 168GiB, Vertex count: 4423413159, Edge count: 6080199201, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m5     ] Partition size: 327GiB, IDS size: 168GiB, Vertex count: 4423507016, Edge count: 6080523891, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m6     ] Partition size: 382GiB, IDS size: 195GiB, Vertex count: 5160659122, Edge count: 7093844889, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m7     ] Partition size: 382GiB, IDS size: 196GiB, Vertex count: 5160691829, Edge count: 7093710967, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m8     ] Partition size: 327GiB, IDS size: 168GiB, Vertex count: 4423317860, Edge count: 6080242602, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m9     ] Partition size: 327GiB, IDS size: 168GiB, Vertex count: 4423413159, Edge count: 6080199201, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[m10    ] Partition size: 327GiB, IDS size: 168GiB, Vertex count: 4423507016, Edge count: 6080523891, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0`),
				replica: 3,
			},
			topology_bytes: 1889427696298,
			wantErr:        false,
		},
		{
			name: "single node graph",
			args: args{
				output: []byte(`[GRAPH  ] Graph was loaded (/home/<USER>/tigergraph/data/gstore):
				[m1     ] Partition size: 642KiB, IDS size: 2.2MiB, Vertex count: 169989, Edge count: 0, NumOfDeletedVertices: 0 NumOfSkippedVertices: 0
				[WARN   ] Above vertex and edge counts are for internal use which show approximate topology size of the local graph partition. Use DML to get the correct graph topology information`),
				replica: 1,
			},
			topology_bytes: 2964275,
			wantErr:        false,
		},
		{
			name: "zero replica",
			args: args{
				output:  []byte(``),
				replica: 0,
			},
			topology_bytes: 0,
			wantErr:        true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotGraphStatus, err := parseOutput(tt.args.output, tt.args.replica)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseOutput() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && gotGraphStatus.TopologyBytes != tt.topology_bytes {
				t.Errorf("parseOutput() = %v, want %v", gotGraphStatus.TopologyBytes, tt.topology_bytes)
			}

		})
	}
}
