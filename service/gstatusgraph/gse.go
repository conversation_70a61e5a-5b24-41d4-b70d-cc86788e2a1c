package gstatusgraph

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/tigergraph/gotools/log"
)

func (c *GraphController) cleanOldDumpFiles(maxAge time.Duration) {
	matches, err := filepath.Glob(filepath.Join(c.cfg.GetGSELogPath(), "dump_gse_*.txt"))
	if err != nil {
		log.Errorf("Failed to list dump files: %v", err)
		return
	}

	for _, match := range matches {
		// dump_gse_1#1_20241204-110632.txt
		filenameRegex := regexp.MustCompile(`dump_gse_\d+#\d+_(\d{8}-\d{6}).txt`)
		regmatches := filenameRegex.FindStringSubmatch(match)
		if len(regmatches) != 2 {
			log.Errorf("Failed to parse dump file name %s", match)
			continue
		}

		ftime, err := time.ParseInLocation("20060102-150405", regmatches[1], time.Now().Location())
		if err != nil {
			log.Errorf("Failed to parse dump file name %s with error: %v", match, err)
			continue
		}

		if time.Since(ftime) > maxAge {
			log.Infof("Removing old dump file %s", match)
			err = os.Remove(match)
			if err != nil {
				log.Errorf("Failed to remove dump file %s with error: %v", match, err)
			}
		}

	}

}

func (c *GraphController) GetGSEStatus() (graphStatus *GraphStatus, err error) {
	// clean up old dump files
	c.cleanOldDumpFiles(24 * time.Hour)
	cmdCtx, cancel := context.WithTimeout(context.Background(), c.cfg.GetHTTPRequestTimeout())
	defer cancel()

	// only send signal to GSE when it is online
	cmd := exec.CommandContext(cmdCtx, "sh", "-c", "gadmin status | grep GSE | grep -q 'Online' && /bin/kill -SIGUSR2 $(pgrep gsed)")
	log.Infof("Executing command: %s", strings.Join(cmd.Args, " "))
	err = cmd.Run()
	if err != nil {
		log.Errorf("Failed to send signal to GSE: %v", err)
		return nil, err
	}
	// wait for dump files to be generated
	time.Sleep(1000 * time.Millisecond)
	matches, err := filepath.Glob(filepath.Join(c.cfg.GetGSELogPath(), "dump_gse_*.txt"))
	if err != nil {
		log.Errorf("Failed to list dump files: %v", err)
		return nil, err
	}

	if len(matches) == 0 {
		log.Errorf("No dump files found")
		return nil, fmt.Errorf("No dump files found")
	}

	sort.Strings(matches)
	// parse the latest dump file
	match := matches[len(matches)-1]
	log.Infof("Parsing dump file %s", match)
	graphStatus, err = c.parseGSEDumpFile(match)
	if err != nil {
		log.Errorf("Failed to parse dump file %s with error: %v", match, err)
		return nil, err
	}

	return graphStatus, nil
}

// License: normal
// ====================================================
// current time: **********, expiretime: **********
// current vertex number: 0, limit: 9007199254740991
// current edge number: 0, limit: 9007199254740991
// current topology size: 0, limit: 9007198180999168
// current RSS: **********, limit: 1125899906842624
func (c *GraphController) parseGSEDumpFile(file string) (graphStatus *GraphStatus, err error) {
	f, err := os.Open(file)
	if err != nil {
		log.Errorf("Failed to open dump file %s with error: %v", file, err)
		return nil, err
	}

	reVertextNumber := regexp.MustCompile(`current vertex number: (\d+), limit: (\d+)`)
	reEdgeNumber := regexp.MustCompile(`current edge number: (\d+), limit: (\d+)`)
	reTopologySize := regexp.MustCompile(`current topology size: (\d+), limit: (\d+)`)

	defer f.Close()
	graphStatus = &GraphStatus{}
	scanner := bufio.NewScanner(f)
	i := 0
	for ; i < 7 && scanner.Scan(); i++ {
		line := scanner.Text()
		if reVertextNumber.MatchString(line) {
			matches := reVertextNumber.FindStringSubmatch(line)
			graphStatus.VertexCount = cast.ToInt64(matches[1])
		}

		if reEdgeNumber.MatchString(line) {
			matches := reEdgeNumber.FindStringSubmatch(line)
			graphStatus.EdgeCount = cast.ToInt64(matches[1])
		}

		if reTopologySize.MatchString(line) {
			matches := reTopologySize.FindStringSubmatch(line)
			graphStatus.TopologyBytes = cast.ToInt64(matches[1])
			graphStatus.TopologyLimitBytes = cast.ToInt64(matches[2])
		}

	}
	// must have at least 7 lines
	// the first 7 lines are created quite quickly, other lines are created later, we just need the first 7 lines
	if i != 7 {
		log.Errorf("Failed to parse dump file %s", file)
		return nil, fmt.Errorf("Failed to parse dump file %s", file)
	}

	log.Infof("Parsed graph status: %+v", graphStatus)
	return graphStatus, nil
}
