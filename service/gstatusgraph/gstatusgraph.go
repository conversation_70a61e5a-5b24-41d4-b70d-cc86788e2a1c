package gstatusgraph

import (
	"bufio"
	"context"
	"errors"
	"os/exec"
	"regexp"
	"strconv"
	"strings"

	"github.com/tigergraph/cqrs/tutopia/common/service"
	"github.com/tigergraph/gotools/log"

	"github.com/tigergraph/gus/lib/config"
)

const (
	GraphStatusCmd = "gstatusgraph"
)

type GraphController struct {
	cfg *config.Config
}

func New(cfg *config.Config) *GraphController {
	return &GraphController{
		cfg: cfg,
	}
}

type NodeInfo struct {
	Name                 string `json:"name"`
	PartitionSize        int64  `json:"partition_size"`
	IDSsize              int64  `json:"ids_size"`
	VertexCount          int64  `json:"vertex_count"`
	EdgeCount            int64  `json:"edge_count"`
	NumOfDeletedVertices int64  `json:"num_of_deleted_vertices"`
	NumOfSkippedVertices int64  `json:"num_of_skipped_vertices"`
}

type GraphStatus struct {
	Nodes              []NodeInfo `json:"nodes"`
	TopologyBytes      int64      `json:"topology_bytes"`
	TopologyLimitBytes int64      `json:"topology_limit_bytes"`
	VertexCount        int64      `json:"vertex_count"`
	EdgeCount          int64      `json:"edge_count"`
}

func (c *GraphController) GetGraphStatus(isForce bool) (graphStatus *GraphStatus, err error) {

	cmdCtx, cancel := context.WithTimeout(context.Background(), c.cfg.GetHTTPRequestTimeout())
	defer cancel()

	forceArg := ""
	if isForce {
		forceArg = "--force"
	}

	cmd := exec.CommandContext(cmdCtx, GraphStatusCmd, forceArg)
	log.Infof("Executing command: %s", strings.Join(cmd.Args, " "))
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Errorf("Failed to get graph status: %v: %s", err, output)
		return nil, err
	}
	_, replicaCnt, err := c.cfg.GetPartitionReplicaCnt(service.GPE)
	if err != nil {
		log.Errorf("Failed to get partition and replica count: %v", err)
		return nil, err
	}
	return parseOutput(output, replicaCnt)
}

func parseLine(line string) (node *NodeInfo) {
	re := regexp.MustCompile(`\[(\w+)\s*\]\s*Partition size:\s*([\d\.]*)([KMGTPEZY]?i?B),\s*IDS size:\s*([\d\.]*)([KMGTPEZY]?i?B),\s*Vertex count:\s*(\d+),\s*Edge count:\s*(\d+),\s*NumOfDeletedVertices:\s*(\d+)\s*NumOfSkippedVertices:\s*(\d+)`)
	match := re.FindStringSubmatch(line)
	if match != nil {
		node_name := match[1]
		partition_size, _ := strconv.ParseFloat(match[2], 64)
		partition_unit := match[3]
		ids_size, _ := strconv.ParseFloat(match[4], 64)
		ids_unit := match[5]
		vertex_count, _ := strconv.ParseInt(match[6], 10, 64)
		edge_count, _ := strconv.ParseInt(match[7], 10, 64)
		num_deleted_vertices, _ := strconv.ParseInt(match[8], 10, 64)
		num_skipped_vertices, _ := strconv.ParseInt(match[9], 10, 64)

		// Convert units to bytes
		partition_size *= unitToBytes(partition_unit)
		ids_size *= unitToBytes(ids_unit)

		return &NodeInfo{
			Name:                 node_name,
			PartitionSize:        int64(partition_size),
			IDSsize:              int64(ids_size),
			VertexCount:          vertex_count,
			EdgeCount:            edge_count,
			NumOfDeletedVertices: num_deleted_vertices,
			NumOfSkippedVertices: num_skipped_vertices,
		}
	}
	return nil
}

func parseOutput(output []byte, replica int) (graphStatus *GraphStatus, err error) {
	if replica <= 0 {
		return nil, errors.New("Invalid replica number")
	}

	nodeList := make([]NodeInfo, 0)

	scanner := bufio.NewScanner(strings.NewReader(string(output)))
	for scanner.Scan() {
		line := scanner.Text()
		node := parseLine(line)
		if node != nil {
			nodeList = append(nodeList, *node)
		}
	}

	totalBytes := int64(0)
	totalVertices := int64(0)
	totalEdges := int64(0)

	for _, node := range nodeList {
		totalBytes += (node.PartitionSize + node.IDSsize)
		totalVertices += node.VertexCount
		totalEdges += node.EdgeCount
	}

	topology_bytes := totalBytes / int64(replica)
	vertices_count := totalVertices / int64(replica)
	edges_count := totalEdges / int64(replica)

	return &GraphStatus{
		Nodes:         nodeList,
		TopologyBytes: topology_bytes,
		VertexCount:   vertices_count,
		EdgeCount:     edges_count,
	}, nil

}

func unitToBytes(unit string) float64 {
	switch unit {
	case "KiB":
		return 1024
	case "MiB":
		return 1024 * 1024
	case "GiB":
		return 1024 * 1024 * 1024
	case "TiB":
		return 1024 * 1024 * 1024 * 1024
	case "PiB":
		return 1024 * 1024 * 1024 * 1024 * 1024
	case "EiB":
		return 1024 * 1024 * 1024 * 1024 * 1024 * 1024
	case "ZiB":
		return 1024 * 1024 * 1024 * 1024 * 1024 * 1024 * 1024
	case "YiB":
		return 1024 * 1024 * 1024 * 1024 * 1024 * 1024 * 1024 * 1024
	default:
		return 1
	}
}
