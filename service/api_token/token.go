package apitoken

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/codec"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/service/db"
)

type apiTokenService struct {
	cfg           *config.Config
	db            interfaces.DatabaseManager
	authenticator interfaces.GSQLAuthenticator
}

type tokenData struct {
	Username     string `json:"username"`
	Token        string `json:"token"`
	CreatedAt    int64  `json:"createdAt"`
	EncryptedPwd string `json:"encryptedPwd"`
}

const (
	APITokenPrefix = "api_token"
)

var _ interfaces.APITokenService = (*apiTokenService)(nil)

func NewTokenService(cfg *config.Config, db interfaces.DatabaseManager, authenticator interfaces.GSQLAuthenticator) interfaces.APITokenService {
	return &apiTokenService{
		cfg:           cfg,
		db:            db,
		authenticator: authenticator,
	}
}

func (s *apiTokenService) Create(ctx *gin.Context, creds *model.UserCredentials) (*model.APIToken, error) {
	cfg := s.cfg
	info, err := s.authenticator(ctx, cfg, creds, false)
	if err != nil {
		return nil, errors.New("Failed to authenticate with GSQL")
	}

	if info.IsSuperUser {
		return nil, errors.New("Create token for superuser is not allowed")
	}

	encryptedPwd, err := codec.AESCBCEncrypt(cfg.GetAuthToken(), creds.Password)
	if err != nil {
		return nil, errors.New("Failed to encrypt password")
	}

	token := uuid.New().String()
	tokenData := &tokenData{
		Username:     info.Name,
		Token:        token,
		CreatedAt:    time.Now().Unix(),
		EncryptedPwd: encryptedPwd,
	}

	bytes, err := json.Marshal(tokenData)
	if err != nil {
		return nil, errors.New("Failed to marshal token data")
	}

	key := dao.GetDBKey(APITokenPrefix, token)
	if err := s.db.Create(key, bytes); err != nil {
		return nil, errors.New("Failed to create token")
	}

	return &model.APIToken{
		Username:  tokenData.Username,
		Token:     token,
		CreatedAt: tokenData.CreatedAt,
	}, nil
}

func (s *apiTokenService) Delete(ctx *gin.Context, token string) error {
	key := dao.GetDBKey(APITokenPrefix, token)
	if err := s.db.Delete(key); err != nil {
		return errors.New("Failed to delete token")
	}
	return nil
}

func (s *apiTokenService) List(ctx *gin.Context) ([]*model.APIToken, error) {
	results, err := s.db.GetAll(APITokenPrefix)
	if err != nil {
		return nil, errors.New("Failed to get all tokens")
	}

	APITokenList := make([]*model.APIToken, len(results))
	for i, result := range results {
		var tokenData tokenData
		if err = json.Unmarshal(result, &tokenData); err != nil {
			return nil, errors.New("Failed to unmarshal token data")
		}
		APITokenList[i] = &model.APIToken{
			Username:  tokenData.Username,
			Token:     tokenData.Token,
			CreatedAt: tokenData.CreatedAt,
		}
	}
	return APITokenList, nil
}

func (s *apiTokenService) Parse(ctx *gin.Context, token string) (*model.UserCredentials, error) {
	key := dao.GetDBKey(APITokenPrefix, token)
	result, err := s.db.Get(key)
	if err != nil {
		if err == db.ErrNotFound {
			return nil, errors.New("Token not found")
		}
		return nil, errors.New("Failed to get token")
	}

	var tokenData tokenData
	if err = json.Unmarshal(result, &tokenData); err != nil {
		return nil, errors.New("Failed to unmarshal token data")
	}

	decryptedPwd, err := codec.AESCBCDecrypt(s.cfg.GetAuthToken(), tokenData.EncryptedPwd)
	if err != nil {
		return nil, errors.New("Failed to decrypt password")
	}

	return &model.UserCredentials{
		Username: tokenData.Username,
		Password: decryptedPwd,
		AuthType: model.TokenAuthType,
	}, nil
}

func (s *apiTokenService) Get(ctx *gin.Context, token string) (*model.APIToken, error) {
	key := dao.GetDBKey(APITokenPrefix, token)
	result, err := s.db.Get(key)
	if err != nil {
		return nil, errors.New("Failed to get token")
	}

	var tokenData tokenData
	if err = json.Unmarshal(result, &tokenData); err != nil {
		return nil, errors.New("Failed to unmarshal token data")
	}

	return &model.APIToken{
		Username:  tokenData.Username,
		Token:     tokenData.Token,
		CreatedAt: tokenData.CreatedAt,
	}, nil
}
