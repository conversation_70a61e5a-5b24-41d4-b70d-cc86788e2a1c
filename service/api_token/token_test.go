package apitoken_test

import (
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	apitoken "github.com/tigergraph/gus/service/api_token"
)

type mockedDB struct {
	data map[string][]byte
}

func (m *mockedDB) Get(key string) ([]byte, error) {
	return m.data[key], nil
}

func (m *mockedDB) GetAll(key string) ([][]byte, error) {
	results := make([][]byte, 0)
	for k, v := range m.data {
		if strings.HasPrefix(k, key) {
			results = append(results, v)
		}
	}
	return results, nil
}

func (m *mockedDB) Create(key string, value []byte) error {
	m.data[key] = value
	return nil
}

func (m *mockedDB) Upsert(key string, value []byte) error {
	m.data[key] = value
	return nil
}

func (m *mockedDB) Delete(key string) error {
	delete(m.data, key)
	return nil
}

func (m *mockedDB) DeleteAll(key string) error {

	return nil
}

func (m *mockedDB) Import(data map[string][]byte) error {
	return nil
}

func (m *mockedDB) Export(key string) (map[string][]byte, error) {
	return nil, nil
}

func (m *mockedDB) GetAllKeys(key string) ([]string, error) {
	return nil, nil
}

func (m *mockedDB) SetUp() error {
	return nil
}

func (m *mockedDB) TearDown() {

}

func mockedAuthenticator(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {
	if creds.Username != "test" {
		return nil, errors.New("invalid credentials")
	}
	return &model.UserInfo{
		Name: creds.Username,
	}, nil
}

var createTokenService = func(t *testing.T) interfaces.APITokenService {
	cfg, err := config.New("../../test/integration/test.cfg", 1)
	require.NoError(t, err)

	return apitoken.NewTokenService(
		cfg,
		&mockedDB{
			data: make(map[string][]byte),
		},
		mockedAuthenticator,
	)

}

func Test_APITokenService(t *testing.T) {
	service := createTokenService(t)

	var token *model.APIToken
	var err error
	t.Run("create token with invalid credentials", func(t *testing.T) {
		token, err = service.Create(&gin.Context{}, &model.UserCredentials{
			Username: "invalid",
			Password: "test",
		})
		require.ErrorContains(t, err, "Failed to authenticate with GSQL")
	})

	t.Run("create token should not return error", func(t *testing.T) {
		token, err = service.Create(&gin.Context{}, &model.UserCredentials{
			Username: "test",
			Password: "test",
		})
		require.NoError(t, err)
		require.NotNil(t, token)
		require.Equal(t, "test", token.Username)
		require.NotEmpty(t, token.Token)
		require.NotEmpty(t, token.CreatedAt)
	})

	t.Run("parse token ", func(t *testing.T) {
		creds, err := service.Parse(&gin.Context{}, token.Token)
		require.NoError(t, err)
		require.Equal(t, "test", creds.Username)
		require.Equal(t, "test", creds.Password)
	})

	t.Run("parse token with invalid token", func(t *testing.T) {
		creds, err := service.Parse(&gin.Context{}, "invalid_token")
		require.Error(t, err)
		require.Nil(t, creds)
	})

	t.Run("list all tokens", func(t *testing.T) {
		tokens, err := service.List(&gin.Context{})
		require.NoError(t, err)
		require.Len(t, tokens, 1)
		require.Equal(t, "test", tokens[0].Username)
		require.Equal(t, token.Token, tokens[0].Token)
		require.Equal(t, token.CreatedAt, tokens[0].CreatedAt)
	})

	t.Run("delete token", func(t *testing.T) {
		err := service.Delete(&gin.Context{}, token.Token)
		require.NoError(t, err)
	})

}

func Test_InsightService(t *testing.T) {
	tokenService := createTokenService(t)
	insightsService := apitoken.NewInsightsService(&mockedDB{
		data: make(map[string][]byte),
	}, tokenService)

	var insightsToken *model.APIToken
	var err error

	t.Run("create insight token", func(t *testing.T) {
		insightsToken, err = insightsService.CreateToken(&gin.Context{},
			"123456",
			&model.UserCredentials{
				Username: "test",
				Password: "test",
			})
		require.NoError(t, err)
		require.NotNil(t, insightsToken)
		require.Equal(t, "test", insightsToken.Username)
		require.NotEmpty(t, insightsToken.Token)
		require.NotEmpty(t, insightsToken.CreatedAt)
	})

	t.Run("get insight token", func(t *testing.T) {
		token, err := insightsService.GetToken(&gin.Context{}, "123456")
		require.NoError(t, err)
		require.NotNil(t, token)
		require.Equal(t, insightsToken.Token, token.Token)
	})

	t.Run("delete insight token", func(t *testing.T) {
		err = insightsService.DeleteToken(&gin.Context{}, "123456")
		require.NoError(t, err)
	})
}
