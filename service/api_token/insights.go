package apitoken

import (
	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/service/db"
)

const (
	InsightsTokenPrefix = "insights_token"
)

type insightsService struct {
	db           interfaces.DatabaseManager
	tokenService interfaces.APITokenService
}

func NewInsightsService(db interfaces.DatabaseManager, tokenService interfaces.APITokenService) interfaces.InsightsService {
	return &insightsService{
		db:           db,
		tokenService: tokenService,
	}
}

func (s *insightsService) CreateToken(ctx *gin.Context, appId string, creds *model.UserCredentials) (*model.APIToken, error) {
	apiToken, err := s.tokenService.Create(ctx, creds)
	if err != nil {
		return nil, err
	}

	err = s.db.Create(dao.GetDBKey(InsightsTokenPrefix, appId), []byte(apiToken.Token))
	if err != nil {
		return nil, err
	}

	return apiToken, nil
}

func (s *insightsService) GetToken(ctx *gin.Context, appId string) (*model.APIToken, error) {
	token, err := s.db.Get(dao.GetDBKey(InsightsTokenPrefix, appId))
	if err != nil {
		if err == db.ErrNotFound {
			return nil, nil
		}
		return nil, err
	}

	tokenData, err := s.tokenService.Get(ctx, string(token))
	if err != nil {
		return nil, err
	}

	return tokenData, nil
}

func (s *insightsService) DeleteToken(ctx *gin.Context, appId string) error {
	token, err := s.db.Get(dao.GetDBKey(InsightsTokenPrefix, appId))
	if err != nil {
		return err
	}

	err = s.db.Delete(dao.GetDBKey(InsightsTokenPrefix, appId))
	if err != nil {
		return err
	}

	err = s.tokenService.Delete(ctx, string(token))
	if err != nil {
		return err
	}
	return nil
}
