package cron

import (
	"context"
	"fmt"
	"time"

	rcron "github.com/robfig/cron/v3"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/runner"
)

const stopTimeout = 5 * time.Second

// Manager is the cron service manager.
type Manager struct {
	runner.Runner

	cron *rcron.Cron
}

// Enforce cron service to implement Runnable.
var _ interfaces.Runnable = (*Manager)(nil)

func New() *Manager {
	service := &Manager{}
	service.Runner = runner.New(service)
	return service
}

// SetUp prepares the cron service.
func (m *Manager) SetUp() error {
	m.cron = rcron.New(
		rcron.WithChain(
			rcron.SkipIfStillRunning(rcron.DiscardLogger),
			rcron.Recover(&cronLogger{}),
		),
		rcron.WithLogger(rcron.DiscardLogger),
	)
	return nil
}

// Run starts the cron service asynchronously, then it waits until the context is finished.
func (m *Manager) Run(ctx context.Context) error {
	m.cron.Start()
	<-ctx.Done()

	stopCtx, cancel := context.WithTimeout(context.Background(), stopTimeout)
	defer cancel()

	select {
	case <-m.cron.Stop().Done():
	case <-stopCtx.Done():
	}

	if stopCtx.Err() != nil {
		return fmt.Errorf("cron cannot be stopped after %vs", stopTimeout/time.Second)
	}
	return nil
}

// TearDown doesn't do anything at the moment.
func (m *Manager) TearDown() {}

// AddJob adds a cron job to be run.
func (m *Manager) AddJob(job Job) (JobID, error) {
	id, err := m.cron.AddJob(job.Schedule, job)
	return JobID(id), err
}

// RemoveJob removes a cron job from being run in the future.
func (m *Manager) RemoveJob(id JobID) {
	m.cron.Remove(rcron.EntryID(id))
}

// RemoveJobs removes a list of cron jobs from being run in the future.
func (m *Manager) RemoveJobs(ids []JobID) {
	for _, id := range ids {
		m.RemoveJob(id)
	}
}

type cronLogger struct{}

// Enforce logger to implement cron Logger.
var _ rcron.Logger = (*cronLogger)(nil)

func (l *cronLogger) Info(msg string, args ...interface{}) {
	log.Info(msg, args)
}

func (l *cronLogger) Error(err error, msg string, args ...interface{}) {
	log.Error(msg, err, args)
}
