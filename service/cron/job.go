package cron

import rcron "github.com/robfig/cron/v3"

type JobID rcron.EntryID

// Job is the cron job to be executed by the cron service.
type Job struct {
	Name     string
	Schedule string
	Execute  func()
}

// Enforce cron job to implement Job.
var _ rcron.Job = (*Job)(nil)

func NewJob(name, schedule string, execute func()) Job {
	return Job{
		Name:     name,
		Schedule: schedule,
		Execute:  execute,
	}
}

func (j Job) Run() {
	j.Execute()
}
