package gbar

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"path"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/tigergraph/cqrs/tutopia/common/pb"
	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/graphql"
	"github.com/tigergraph/gus/lib/config"
	h "github.com/tigergraph/gus/lib/http"
	tgSync "github.com/tigergraph/gus/lib/sync"
	"github.com/tigergraph/gus/service/cron"
)

const (
	maxStatus         = 100
	expBackoffBase    = 2
	ctxKeyGbarService = "GBARService"
)

var ErrMaxBackup = errors.New("maximum number of backups has been reached")

func StagingPath(config *config.Config) string {
	if config.GetBackupLocalEnabled() && config.GetBackupLocalPath() != "" {
		return path.Join(config.GetBackupLocalPath(), "backup_staging")
	}
	return path.Join(config.GetDataDirPath(), "backup_staging")
}

type GUIServiceStatus struct {
	ServiceStatusEvents []ServiceStatusEvents
}

type ServiceStatusEvents struct {
	ServiceStatus     string
	ProcessState      string
	ServiceDescriptor struct {
		Replica     int
		ServiceName string
	}
}

// Controller is the GBAR service controller.
type Controller struct {
	t2pClient   pb.ControllerClient
	cfg         *config.Config
	daoManager  interfaces.DaoManager
	cronService *cron.Manager
	locker      *tgSync.KeyLocker

	sMux                 *sync.Mutex
	oMux                 *sync.Mutex
	scheduledBackupJobID cron.JobID
	graphqlResolver      graphql.Query
}

// Enforce GBAR service to implement LifeCycle.
var _ interfaces.LifeCycle = (*Controller)(nil)

func New(cfg *config.Config, daoManager interfaces.DaoManager, cronService *cron.Manager, t2pClient pb.ControllerClient) *Controller {
	graphqlResolver := graphql.Query{Deps: graphql.Deps{T2pClient: t2pClient, Config: cfg}}
	return &Controller{
		cfg:                  cfg,
		daoManager:           daoManager,
		cronService:          cronService,
		sMux:                 &sync.Mutex{},
		oMux:                 &sync.Mutex{},
		scheduledBackupJobID: -1,
		t2pClient:            t2pClient,
		graphqlResolver:      graphqlResolver,
	}
}

// GinCtxSetter returns a middleware that sets the GBAR service for the current context.
func GinCtxSetter(cntlr *Controller) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(ctxKeyGbarService, cntlr)
	}
}

func GetService(c *gin.Context) *Controller {
	value, _ := c.Get(ctxKeyGbarService)
	service, _ := value.(*Controller)
	return service
}

func (c *Controller) SetUp() error {
	var err error
	if c.locker, err = tgSync.NewKeyLocker(); err != nil {
		return err
	}

	if err = c.daoManager.SetGBARInProgress(false); err != nil {
		log.Errorf("Failed to set GBAR in progress to false: %v", err)
		return err
	}

	backupSchedulerEnable, err := c.BackupSchedulerEnable()
	if err != nil {
		log.Warnf("Failed to schedule a routine automatic gadmin backup: %v", err)
		return nil
	}
	if backupSchedulerEnable {
		if err := c.ScheduleBackup(c.t2pClient, c.cfg); err != nil {
			log.Warnf("Failed to schedule a routine automatic gadmin backup: %v", err)
			return nil
		}
		log.Info("Successfully scheduled a routine automatic gadmin backup")
	}

	return nil
}

func (c *Controller) BackupSchedulerEnable() (bool, error) {
	if c.cfg.GetBackupSchedulerEnabled() {
		if c.cfg.GetBackupLocalEnabled() && c.cfg.GetBackupS3Enabled() {
			return false, fmt.Errorf("System.Backup.Local.Enable and System.Backup.S3.Enable shouldn't be enabled at the same time")
		}
		if !c.cfg.GetBackupLocalEnabled() && !c.cfg.GetBackupS3Enabled() {
			return false, fmt.Errorf("System.Backup.Local.Enable or System.Backup.S3.Enable should be enabled")
		}
		return true, nil
	} else {
		return false, nil
	}
}

func (c *Controller) TearDown() {
	if c.scheduledBackupJobID != -1 {
		c.cronService.RemoveJob(c.scheduledBackupJobID)
		c.scheduledBackupJobID = -1
	}
}

// ScheduleBackup schedules a routine automatic GBAR backup.
func (c *Controller) ScheduleBackup(t2pClient pb.ControllerClient, cfg *config.Config) error {
	c.sMux.Lock()
	defer c.sMux.Unlock()

	backupSched, err := c.daoManager.GetBackupSchedule()
	if err != nil {
		return err
	}

	frequency := fmt.Sprintf(
		"%s %s %s %s %s",
		backupSched.Minutes,
		backupSched.Hours,
		backupSched.DayOfMonth,
		backupSched.Month,
		backupSched.DayOfWeek,
	)
	oldScheduledBackupJobID := c.scheduledBackupJobID
	c.scheduledBackupJobID, err = c.cronService.AddJob(c.createAutomaticBackupJob(frequency, t2pClient, cfg))
	if err != nil {
		return err
	}

	if oldScheduledBackupJobID != -1 {
		c.cronService.RemoveJob(oldScheduledBackupJobID)
	}

	return nil
}

func (c *Controller) createAutomaticBackupJob(frequency string, t2pClient pb.ControllerClient, cfg *config.Config) cron.Job {
	task := func() {
		graphqlResolver := graphql.Query{
			Deps: graphql.Deps{
				T2pClient:  t2pClient,
				Config:     cfg,
				DaoManager: c.daoManager,
			},
		}
		// Node x only runs scheduled backup jobs if nodes 1 to (x-1) are all down.
		if !c.previousNodesDown() {
			return
		}

		backupStatusList := make([]model.ScheduledBackupStatus, 0)
		for i := 0; i < 5; i++ {
			timeStamp := time.Now()

			output, err := graphqlResolver.CreateBackup(
				context.Background(),
				graphql.CreateBackupArgs{
					BackupCreateRequest: &pb.BackupCreateRequest{
						Tag:         graphql.AutoBackupTag,
						StagingPath: StagingPath(cfg),
					},
					IsAutomaticBackup: true,
				})

			combinedOutput := strings.Join(output, "\n")
			backupStatus := model.ScheduledBackupStatus{
				Timestamp: timeStamp.Format("2006-01-02T15:04:05.000000000Z"),
				Output:    combinedOutput,
			}
			if err == nil {
				regex := regexp.MustCompile(`\S+\w+\-\w+\-\d+`)
				backupStatus.BackupName = regex.FindString(backupStatus.Output)
			} else {
				log.Errorf("Failed to run automatic back up: %v: %s", err, combinedOutput)
				backupStatus.BackupName = "N/A"
				backupStatus.Error = err.Error()
			}
			backupStatusList = append(backupStatusList, backupStatus)

			// If back up succeeds OR elapsed time is > 10 minutes, break out of retry.
			if err == nil || time.Since(timeStamp) > time.Minute*10 {
				break
			}

			expBackoff := math.Pow(float64(expBackoffBase), float64(i))
			maxJitter := expBackoff * 0.25
			// Generate random jitter amount in range [-maxJitter, maxJitter].
			//nolint:gosec // this result is not used in a secure application.
			randomizedJitter := -maxJitter + rand.Float64()*(2*maxJitter)
			timeout := expBackoff + randomizedJitter
			log.Infof("Automatic backup failed, retrying in %f seconds", timeout)
			time.Sleep(time.Duration(timeout) * time.Second)
		}

		// Store status/result of automatic backup and any retries.
		for _, backupStatus := range backupStatusList {
			daoErr := c.daoManager.CreateScheduledBackupStatus(backupStatus)
			if daoErr != nil {
				log.Errorf("Failed to create scheduled backup status: %v", daoErr)
			}
		}
		c.cleanUpBackupStatus()
	}

	return cron.NewJob(graphql.AutoBackupTag, frequency, task)
}

func (c *Controller) previousNodesDown() bool {
	replica := c.cfg.GetReplica()
	guiServiceStatus, err := c.requestGUIServiceStatus()
	if err != nil {
		log.Errorf("Failed to get GUI service status: %v", err)
		return true
	}

	for i := replica - 1; i >= 1; i-- {
		if guiServiceStatus.ServiceStatusEvents[i-1].ServiceStatus == "Online" &&
			guiServiceStatus.ServiceStatusEvents[i-1].ProcessState == "Running" {
			return false
		}
	}

	return true
}

func (c *Controller) requestGUIServiceStatus() (*GUIServiceStatus, error) {
	requestURL := fmt.Sprintf(
		"http://%s:%d%s",
		c.cfg.GetIFMHostname(),
		c.cfg.GetIFMRestPort(),
		"/current-service-status",
	)
	requestBody := []byte(`{"ServiceDescriptors":[{"ServiceName":"gui"}]}`)

	req, _ := http.NewRequestWithContext(
		context.Background(),
		http.MethodPost,
		requestURL,
		bytes.NewBuffer(requestBody),
	)

	client := h.NewClient(&h.ClientConfigs{
		Timeout:      c.cfg.GetHTTPRequestTimeout(),
		RetryMax:     c.cfg.GetHTTPRequestRetryMax(),
		RetryWaitMin: c.cfg.GetHTTPRequestRetryWaitMin(),
		RetryWaitMax: c.cfg.GetHTTPRequestRetryWaitMax(),
	})

	resp, err := client.Do(req)
	if err != nil {
		log.Errorf("Failed to get response from IFM: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("Failed to read response from IFM: %v", err)
		return nil, err
	}

	guiServiceStatus := &GUIServiceStatus{}
	err = json.Unmarshal(body, guiServiceStatus)
	if err != nil {
		log.Errorf("Failed to parse response from IFM: %v", err)
		return nil, err
	}

	// Return service status of nodes in increasing order by replica.
	sort.Slice(guiServiceStatus.ServiceStatusEvents, func(i, j int) bool {
		return guiServiceStatus.ServiceStatusEvents[i].ServiceDescriptor.Replica <
			guiServiceStatus.ServiceStatusEvents[j].ServiceDescriptor.Replica
	})
	return guiServiceStatus, nil
}

func (c *Controller) cleanUpBackupStatus() {
	status, err := c.daoManager.GetAllScheduledBackupStatus()
	if err != nil {
		log.Errorf("Failed to get all scheduled backup status: %v", err)
		return
	}

	statusToRemove := len(status) - maxStatus
	for i := len(status) - 1; i >= 0 && statusToRemove > 0; i-- {
		err := c.daoManager.DeleteScheduledBackupStatus(status[i].BackupName, status[i].Timestamp)
		if err != nil {
			log.Errorf("Failed to delete scheduled backup status: %v", err)
		}
		statusToRemove--
	}
}
