package schema

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/dao/model"
)

var (
	schemaURL = "/gsql/v1/schema/graphs"
)

// Controller is the schema service controller.
type SchemaController struct {
	gsqlHTTPClient interfaces.GSQLHTTPClient
}

func New(gsqlHTTPClient interfaces.GSQLHTTPClient) *SchemaController {
	return &SchemaController{
		gsqlHTTPClient: gsqlHTTPClient,
	}
}

// Get
func (c *SchemaController) Get(ctx *gin.Context, creds *model.UserCredentials, graph string) (*model.GetSchemaResponse, error) {
	request := &model.Request{
		Method: http.MethodGet,
		Url:    schemaURL + "/" + graph,
	}
	response := &model.GetSchemaResponse{}
	err := c.gsqlHTTPClient.Request(
		ctx,
		creds,
		request,
		response,
	)

	return response, err
}
