mutation mutation_test {
  MyGraph {
    insert1: insert_Patient(
      objects: [
        {patient_id: "**********", birth_year: 1996, state: "isolated"}, 
        {patient_id: "**********", birth_year: 1995, state: "released"}]
    ) {
      ...PatientAttribute
    }
    
    update1_City:insert_City(objects:[{city_id:"Bonghwa-gun", academy_ratio:1}]){
      city_id
      academy_ratio
    }

    delete1: delete_Patient(where: {patient_id: {_gte: "**********"}}) {
      ...PatientAttribute
    }
  }
}

fragment PatientAttribute on mutation_Patient_output {
  patient_id
  birth_year
  state
}