query query_test($gt_patient_id: String, $limitShow: Int64) {
  MyGraph {
    hop_of_n: Patient(
      where: {patient_id: {_gt: $gt_patient_id}}
      order_by: {patient_id: desc}
      limit: $limitShow
    ) {
      ... PatientAttribute
      INFECTED_BY {
        to {
          ... PatientAttribute
        }
      }
    }
    hop_of_1: Patient(limit: $limitShow) {
      ... PatientAttribute
    }
  }
}

fragment PatientAttribute on Patient {
  patient_id
  birth_year
  state
}