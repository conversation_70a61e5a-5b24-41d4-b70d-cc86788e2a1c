# create QUERY Value_null_json_result_check
USE graph MyGraph
CREATE QUERY Value2_null_json_result_check() FOR GRAPH MyGraph SYNTAX V2 {
  /**********************************************************
  *  "@@ageMap": {
  *        "": "",
  *        "isolated": "",
  *        "released": ""
  *      }
   *********************************************************/
  MapAccum<STRING, STRING> @@ageMap;
  Start = {Patient.*};

  Start = select s from Start:s
          accum @@ageMap +=  (s.state-> "");
  print "assert_table_view";
  print @@ageMap;
}