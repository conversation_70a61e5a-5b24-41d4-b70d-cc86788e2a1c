# create graph Graph_test_udt
USE GLOBAL
TYPEDEF TUPLE <f1 INT > My_Tuple_udt   // define a UDT
CREATE VERTEX Vertex_udt  (PRIMARY_ID id STRING , att_udt My_Tuple_udt)
CREATE GRAPH Graph_test_udt (Vertex_udt)
CREATE LOADING JOB load_udt FOR GRAPH Graph_test_udt {
    DEFINE FILENAME f;
    LOAD f TO VERTEX Vertex_udt VALUES ($0, My_Tuple_udt($1));
    // $1 is loaded as f1, $2 is loaded as f2, and so on
}
RUN LOADING JOB load_udt USING f="/home/<USER>/tigergraph/data/gui/loading_data/tigergraph/gshell_udt.csv"

# check data is loaded
INTERPRET query () for graph Graph_test_udt {
  result = 
  select t from Vertex_udt:t;
  print result;
}