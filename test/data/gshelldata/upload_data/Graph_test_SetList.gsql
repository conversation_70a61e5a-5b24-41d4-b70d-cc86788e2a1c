# create graph Graph_test_SetList
USE GLOBAL
CREATE VERTEX Vertex_SetList (PRIMARY_ID id STRING, iset SET<INT>, ilist LIST<INT>)
CREATE UNDIRECTED EDGE Edge_SetList(FROM Vertex_SetList, TO Vertex_SetList)
CREATE GRAPH Graph_test_SetList (Vertex_SetList,Edge_SetList)

CREATE LOADING JOB load_set_list FOR GRAPH Graph_test_SetList {
  DEFINE FILENAME f;
  LOAD f TO VERTEX Vertex_SetList VALUES ($0, $1, $1);
}
RUN LOADING JOB load_set_list USING f="/home/<USER>/tigergraph/data/gui/loading_data/tigergraph/gshell_list_set_vertex.csv"

# check data is loaded
INTERPRET query () for graph Graph_test_SetList {
  result = 
  select t from Vertex_SetList:t;
  print result;
}