#Drop Graph_test_udt's JOB and Query
use GLOBAL
DROP JOB load_udt
drop graph Graph_test_udt
drop vertex Vertex_udt
drop TUPLE My_Tuple_udt

#Drop Graph_test_SetList's JOB and Query
use GLOBAL
DROP JOB load_set_list
drop graph Graph_test_SetList
drop vertex Vertex_SetList
drop edge Edge_SetList


#Drop Graph_test_Map's JOB and Query
use GLOBAL
DROP JOB load_map
drop graph Graph_test_Map
drop vertex Vertex_map

