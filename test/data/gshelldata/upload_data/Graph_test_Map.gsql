# create graph Graph_test_Map
USE GLOBAL
CREATE VERTEX Vertex_map  (PRIMARY_ID id STRING, att_map MAP<INT, STRING>)
CREATE GRAPH Graph_test_Map (Vertex_map)
CREATE LOADING JOB load_map FOR GRAPH Graph_test_Map {
    DEFINE FILENAME f;
    LOAD f TO VERTEX Vertex_map  VALUES ($0, SPLIT($1, ":", "#"));
}

RUN LOADING JOB load_map USING f="/home/<USER>/tigergraph/data/gui/loading_data/tigergraph/gshell_multi_key_value.csv"

# check data is loaded
INTERPRET query () for graph Graph_test_Map {
  result = 
  select t from Vertex_map:t;
  print result;
}