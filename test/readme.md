End-to-end tests are in `e2e`.

Integration (API) tests are in `integration`.

# End to End Test
GraphStudio is highly integrated with TigerGraph. Therefore, the most reliable & complete (in terms of coverage) way of testing is end-to-end testing.

Historically, before May 2022, there weren't any e2e tests in `gus`. We relied on frontend's e2e tests and QA's manual tests.

This approach has been problematic in 3 aspects:
1. The process was __slow__ because MIT/WIP pipeline takes more than 4 hours. Waiting for QA to verify most logic can also queue up for 1-5 days.
2. Bugs were not reproduced through tests.
3. Backend engineers gradually __lost product sense__ because there weren't tests that describes the product from a user's pespective.

To increase __product development efficiency__, __quality__, and __engieers' product responsibility__, we need to write e2e tests for each new feature and for each bug report.

## New Feature
To be continued

## Bug Report
For each bug report, create a test file under `test/e2e` with the name pattern `project-XXX_test.go`.

For example, `gst-1956_test.go` is the reproduction of https://graphsql.atlassian.net/browse/GST-1956

This way, we are confident that
1. This test reproduces this bug.
2. New code actually fixes this bug.

We should always write the reproducible test first, before fixing the bug.