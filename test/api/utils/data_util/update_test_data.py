import argparse
from data_resolver import read_test_data, update_test_data

tgcloud_test_data_file = "tgcloud_test_data.json"
tools_test_data_file = "tools_test_data.json"

def update_test_data_base_on_params(test_env,params,test_data_file):
    test_data=read_test_data(test_data_file)
    if test_data is None: return
    test_data["test_env"] = test_env
    if params is not None:
        for param in params:
            keys=param.split("=")[0].split("."); value=param.split("=")[1]
            if len(keys)==1:
                test_data[keys[0]] = value
            if len(keys)==2:
                test_data[keys[0]][keys[1]] = value
            if len(keys)==3:
                for test_case in test_data[keys[0]]:
                    if test_case['case_name'] == keys[1]:
                        test_case[keys[2]] = value
    update_test_data(test_data,test_data_file)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Update Cloud test input parameters')
    parser.add_argument("--test_env", type=str, help="test environment url", required=True)
    parser.add_argument("--cloud_param", type=str, help="cloud_param")
    parser.add_argument("--fot_param", type=str, help="fot_param")

    args = parser.parse_args()
    cloud_params=args.cloud_param.strip().split(";") if args.cloud_param else []
    fot_params=args.fot_param.strip().split(";") if args.fot_param else []
    update_test_data_base_on_params(args.test_env, cloud_params, tgcloud_test_data_file)
    update_test_data_base_on_params(args.test_env, fot_params, tools_test_data_file)