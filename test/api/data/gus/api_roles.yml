post_roles:
  default_roles:
    payload: {"roles":["default_role"]}
  multi_roles:
    payload: {"roles":["role_1", "role_2", "role3"]}

post_roles_invalid:
  special_character_name:
    payload: {"roles": ["role****&^"]}
    state_code: 400
    error: True
    message: "is not a valid identifier"
  empty_role_name:
    payload: {"roles": [""]}
    state_code: 400
    error: True
    message: "is not a valid identifier"
  invalid_key_name:
    payload: {"role": ["invalid_key_name"]}
    state_code: 400
    error: True
    message: "JSON payload doesn't contain 'roles'"
  duplicate_name:
    payload: {"roles": ["default_role"]}
    state_code: 500
    error: True
    message: "Failed to create global roles"

grant_roles:
  payload: {"roles":["globalobserver"],"users":["grant_roles"]}
  