create_users:
  payload: {"username":"api_test_user","password":"123456"}

post_user_invalid_payload:
  empty_password:
    payload: {"username":"api_test2","password":""}
    state_code: 500
    error: True
    message: 'The password cannot be empty'
  empty_username:
    payload: {"username":"","password":"123456"}
    state_code: 400
    error: True
    message: 'Failed to create user'
  reserved_word:
    payload: {"username": "user", "password": "123456"}
    state_code: 400
    error: True
    message: 'Failed to create user'

delete_secret:
  payload: {"secrets":["test001"]}
  