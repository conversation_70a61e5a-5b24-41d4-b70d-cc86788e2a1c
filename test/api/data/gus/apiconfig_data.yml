#define the valid get the configs
valid_configs:  
  MaxConcurrentQueries:
    key: "RESTPP.WorkLoadManager.MaxConcurrentQueries"
  HostList:
    key: "System.HostList"
  LogRoot:
    key: "System.LogRoot"
  ClientIdleTimeSec:
    key: "GUI.ClientIdleTimeSec,GUI.EnableDarkTheme"
  LDAP:
    key: "Security.LDAP.Enable,Security.LDAP.Hostname,Security.LDAP.Port,Security.LDAP.BaseDN,Security.LDAP.SearchFilter,Security.LDAP.GroupFilter,Security.LDAP.GroupHierarchyRefreshIntervalMin,Security.LDAP.UsernameAttribute,Security.LDAP.AdminDN,Security.LDAP.AdminPassword,Security.LDAP.Secure.Protocol,Security.LDAP.Secure.TruststorePath,Security.LDAP.Secure.TruststoreFormat,Security.LDAP.Secure.TruststorePassword,Security.LDAP.Secure.TrustAll"
  SSO:
    key: "Security.SSO.SAML.Enable,Security.SSO.SAML.SP.Hostname,Security.SSO.SAML.SP.X509Cert,Security.SSO.SAML.SP.PrivateKey,Security.SSO.SAML.IDP.EntityId,Security.SSO.SAML.IDP.SSOUrl,Security.SSO.SAML.IDP.X509Cert,Security.SSO.SAML.AuthnRequestSigned,Security.SSO.SAML.AssertionSigned,Security.SSO.SAML.ResponseSigned,Security.SSO.SAML.MetadataSigned,Security.SSO.SAML.SignatureAlgorithm,Security.SSO.SAML.RequestedAuthnContext,Security.SSO.SAML.SP.SignonBinding,Security.SSO.SAML.IDP.SignonBinding"
  Nginx:
    key: "Nginx.AllowedCIDRList,Nginx.Port,Nginx.ResponseHeaders,Nginx.SSL.Enable,Nginx.SSL.Cert,Nginx.SSL.Key"
  RESTPP:
    key: "RESTPP.Factory.DefaultQueryTimeoutSec,RESTPP.WorkLoadManager.MaxHeavyBuiltinQueries,RESTPP.WorkLoadManager.MaxConcurrentQueries,RESTPP.WorkLoadManager.MaxDelayQueueSize"
  GSQL:
    key: "GSQL.QueryResponseMaxSizeByte"
  GPE:
    key: "GPE.QueryLocalMemLimitMB"
  CORS:
    key: "GUI.EnableCORS"
  Backup:
    key: "System.Backup.Local.Enable,System.Backup.S3.Enable"
  GUI:
    key: "GUI.RESTPPResponseMaxSizeBytes,GUI.ClientIdleTimeSec,GUI.Cookie.DurationSec,GUI.TempFileMaxDurationDay,GUI.HTTPRequest.TimeoutSec,GUI.HTTPRequest.RetryMax,GUI.HTTPRequest.RetryWaitMaxSec,GUI.HTTPRequest.RetryWaitMinSec"
  Kafka:
    key: "KafkaLoader.ReplicaNumber,KafkaConnect.AllowedTaskPerCPU"

#define the invalid get the configs
invalid_configs:  
  empty_key:
    key: ""
    status_code: 400
    error: True
    message: "No configuration keys."
  invalid_key:
    key: "not_exist_key"
    status_code: 500
    error: True
    message: "NotFound (entry: not_exist_key doesn't exist)"
  no_privilege_key:
    key: "System.S3.AWSAccessKeyID"
    status_code: 400
    error: True
    message: "System.S3.AWSAccessKeyID is an invalid key"
  

#define the valid post the configs
valid_post_configs:  
  RESTPP1:
    data: '{
            "RESTPP.Factory.DefaultQueryTimeoutSec": "1001",
            "RESTPP.WorkLoadManager.MaxHeavyBuiltinQueries": "200",
            "RESTPP.WorkLoadManager.MaxConcurrentQueries": "15000",
            "RESTPP.WorkLoadManager.MaxDelayQueueSize": "20"
          }'
  RESTPP2:
    data: '{
            "RESTPP.Factory.DefaultQueryTimeoutSec": "1010"
          }'
  GSQL:
    data: '{
              "GSQL.QueryResponseMaxSizeByte": "23554440"
          }'
  GPE:
    data: '{
            "GPE.QueryLocalMemLimitMB": "0"
          }'
  GUI:
    data: '{
            "GUI.RESTPPResponseMaxSizeBytes": "33554432",
            "GUI.ClientIdleTimeSec": "604800",
            "GUI.Cookie.DurationSec": "86400",
            "GUI.TempFileMaxDurationDay": "10",
            "GUI.HTTPRequest.TimeoutSec": "604800",
            "GUI.HTTPRequest.RetryMax": "4",
            "GUI.HTTPRequest.RetryWaitMaxSec": "30",
            "GUI.HTTPRequest.RetryWaitMinSec": "1"
          }'
  Kafka:
    data: '{
            "KafkaLoader.ReplicaNumber": "5",
            "KafkaConnect.AllowedTaskPerCPU": "5"
          }'
  
  

#define the valid post the configs
invalid_post_configs:  
  invalid_key_1:
    data: '{
            "RESTPP.invalid_key": "123"
          }'
    status_code: 500
    error: True
    message: "ParameterErr (key: RESTPP.invalid_key does not exist in EntryMeta)"
  invalid_key_2:
    data: '{
            "RESTPP.Factory.DefaultQueryTimeoutSec": "1001",
            "RESTPP.invalid_key": "123"
          }'
    status_code: 500
    error: True
    message: "ParameterErr (key: RESTPP.invalid_key does not exist in EntryMeta)"
  invliad_value_1:
    data: '{
            "RESTPP.Factory.DefaultQueryTimeoutSec": "invalid_value"
          }'
    status_code: 500
    error: True
    message: "ParameterErr (invalid value \"invalid_value\" for entry RESTPP.Factory.DefaultQueryTimeoutSec;"
  invalid_value_2:
    data: '{
            "RESTPP.Factory.DefaultQueryTimeoutSec": "1001",
            "RESTPP.WorkLoadManager.MaxHeavyBuiltinQueries": "invalid_value"
          }'
    status_code: 500
    error: True
    message: "ParameterErr (invalid value \"invalid_value\" for entry RESTPP.WorkLoadManager.MaxHeavyBuiltinQueries;"
  no_privilege_key:
    data: '{"System.S3.AWSAccessKeyID": "123"}'
    status_code: 500
    error: True
    message: "ParameterErr (key: System.S3.AWSAccessKeyID does not exist in EntryMeta)"

#generate the Nginx cert
valid_generate_cert:
  valid_key_value:
    data: '{
            "country": "1",
            "province": "1",
            "locality": "1",
            "organization": "1",
            "organizationUnit": "1",
            "commonName": "1"
          }'

invalid_generate_cert:
  inalid_key:
    data: '{
            "country": "1",
            "province_invalid": "1",
            "locality": "1",
            "organization": "1",
            "organizationUnit": "1",
            "commonName": "1"
          }'
    status_code: 400
    error: True
    message: "Invalid payload"
  inalid_value:
    data: '{
            "country": "1",
            "locality": "1",
            "organization": "1",
            "organizationUnit": "1",
            "commonName": "1"
          }'
    status_code: 400
    error: True
    message: "Invalid payload"
  empty_value:
    data: '{
            "country": "",
            "province": "",
            "locality": "1",
            "organization": "1",
            "organizationUnit": "1",
            "commonName": "1"
          }'
    status_code: 400
    error: True
    message: "Invalid payload"

#stop/start services as success
valid_services:
  RESTPP:
    key: "RESTPP"
  DICT:
    key: "DICT"
  GSE:
    key: "GSE"
  GPE:
    key: "GPE"
  ALL:
    key: "GSE,RESTPP,GPE,IFM,DICT"

#stop services as failed
invalid_stop_services:
  invalid_key:
    key: "invalid_key"
    status_code: 400
    error: True
    message: "Failed to parse service names."
  empty_key:
    key: ""
    status_code: 400
    error: True
    message: "No services to stop."

#start services as failed
invalid_start_services:
  invalid_key:
    key: "invalid_key"
    status_code: 400
    error: True
    message: "Failed to parse service names."
  empty_key:
    key: ""
    status_code: 400
    error: True
    message: "No services to start."