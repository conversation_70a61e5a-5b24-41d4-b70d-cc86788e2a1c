## The test data of APIs graph-algorithm

# define positive case
test_get_all_algorithm:


# define negative cases
algorithms_invalid_cases:
  get_algorithms_without_cookie:
    cookie: ""
    status_code: 401
    error: True
    message: "You are not authorized to use this API"
  get_algorithms_with_error_cookie:
    cookie: "624e8a5d-4a42-42a5-b529-8eaf74361546"
    status_code: 401
    error: True
    message: "You are not authorized to use this API"

# define positive case of installing algorithm
install_algorithm_cases:
  install_single_algorithm_query:
    graph_name: "MyGraph"
    queries_name: [{algrotithm_name: "K-Core", query_name: "tg_kcore"}]
  install_multi_algorithm_query:
    graph_name: "MyGraph"
    queries_name: [{algrotithm_name: "<PERSON>rank", query_name: "tg_pagerank"}, {algrotithm_name: "Maxflow", query_name: "tg_maxflow"}]

# define negative cases of installing algorithm
install_algorithm_invalid_payload:
  install_algorithm_without_graph:
    graph_name: 
    algrotithm_name: "K-Core"
    is_login: True
    status_code: 200
    error: False
    message: "Error: Currently not using any graphs!"
  install_algorithm_with_nonexistent_graph:
    graph_name: "ldbc_sn"
    algrotithm_name: "K-Core"
    is_login: True
    status_code: 200
    error: False
    message: "Error: Currently not using any graphs!"
  install_algorithm_without_cookie:
    graph_name: "MyGraph"
    algrotithm_name: "K-Core"
    is_login: False
    status_code: 401
    error: True
    message: "You are not authorized to use this API"
  install_algorithm_invalid_algorithm_name:
    graph_name: "MyGraph"
    algrotithm_name: "KK-core"
    is_login: True
    status_code: 500
    error: True
    message: ""
  
  
  