## default proxy group payload
default_proxy_group:
  payload: {"groupName":"api_test_proxy_group","proxyRule":"test_api=test_api"}

# default payload for DELETE proxy groups
default_delete_proxy_group:
  payload: {"groupNames":["api_test_delete"]}

update_proxy_group:
  payload: {"name":"","rule":"test_api_1=test_api"}

post_proxy_group_invalid_payload:
  empty_name:
    payload: {"groupName":"","proxyRule":"test_api=test_api"}
    status_code: 400
    error: True
    message: "The username cannot contain any invisible character"
  empty_rule:
    payload: {"groupName":"empty_rule","proxyRule":""}
    status_code: 201
    error: false
    message: "Successfully created group"
  special_character_name:
    payload: {"groupName":"&**name?////","proxyRule":"test_api=test_api"}
    status_code: 201
    error: false
    message: "Successfully created group"
  incorrect_rule_format:
    payload: {"groupName":"incorrect_rule_format","proxyRule":"@test"}
    status_code: 201
    error: false
    message: "Successfully created group"

delete_proxy_group_invalid_payload:
  empty_name:
    payload: {"groupNames":[""]}
    status_code: 400
    error: True
    message: "could not be found"
  non_existent_name:
    payload: {"groupNames":["nonexistent_name"]}
    status_code: 400
    error: True
    message: "could not be found"
