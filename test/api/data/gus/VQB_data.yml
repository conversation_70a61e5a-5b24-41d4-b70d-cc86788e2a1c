## The test data of APIs VQB

get_VQB_patterns:
  invalid_cookies:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  existent_graph_name:
    graph_name: "MyGraph"
    draft: "draft"
    params: ""
    status_code: 200
    error: False
    message: ""
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    draft: "all_connection"
    params: ""
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  special_characters_draft_name:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: "jobName=load_job_popData___~*Sheet1_csv_1584998109163"
    status_code: 404
    error: False
    message: "Graph MyGraphjobName=load_job_popData___~*Sheet1_csv_1584998109163 does not exist or you don't have privileges on it"
  oversize_characters_draft_name:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: "jobNameload_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 404
    error: False
    message: "Graph"
  null_params:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: "?null"
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: '?id=null&KEY=””'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: "?key=jo’scar"
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: "?number=9999999999999999999999999"
    status_code: 200
    error: False
    message: ""
delete_VQB_patterns:
  invalid_cookies:
    graph_name: "MyGraph"
    pattern_name: "FindInfectedPatients"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    pattern_name: "FindInfectedPatients"
    params: ""
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  special_characters_name:
    graph_name: "MyGraph"
    pattern_name: "FindInfectedPatients"
    params: "load_job_popData___~*Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: "Successfully deleted visual pattern"
  oversize_characters_name:
    graph_name: "MyGraph"
    pattern_name: "FindInfectedPatients"
    params: "jobNameload_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 200
    error: False
    message: "Successfully deleted visual pattern"
  null_params:
    graph_name: "MyGraph"
    pattern_name: "FindInfectedPatients"
    params: "?null"
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    pattern_name: "FindInfectedPatients"
    params: '?id=null&KEY=””'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    pattern_name: "FindInfectedPatients"
    params: "?key=jo’scar"
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    pattern_name: "FindInfectedPatients"
    params: "?number=9999999999999999999999999"
    status_code: 200
    error: False
    message: ""