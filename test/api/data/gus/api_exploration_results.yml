## valid data for GET exploration result
get_exploration_result:
  valid_graph_name:
    graph_name: "MyGraph"
    file_name: "open_graph_ex"

## valid data for POST exploration result
post_exploration_result:
  save_exploration:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    data: '{
    "name": "one_vertex",
    "timestamp": 1698031291611,
    "username": "tigergraph",
    "schema": {},
    "previewImage": "data:image/png;base64,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",
    "data": {
        "explorationResult": {
            "latestGraph": {
                "nodes": [{"id": "Gimhae-si","type": "City","attrs": {"academy_ratio": 0,"city": "","elderly_alone_ratio": 0,"elderly_population_ratio": 0,"elementary_school_count": 0,"kindergarten_count": 0,"nursing_home_count": 0,"university_count": 0},
                        "styles": {},"others": {},"labels": {},"x": 766,"y": 276.5}],
                "links": []},
            "latestSelections": {
                "nodes": [{"type": "City","id": "Gimhae-si"}],
                "links": []},
            "latestLayout": "force"
        },
        "graphChartConfig": null,
        "graphExplorerConfig": {
            "vertexTypeFilter": {"City": true,"Province": false,"Country": false,"SearchStat": false,"WeatherStat": false,"InfectionCase": false,"Year_": false,"Month_": false,"Day_": false,"TravelEvent": false,"Patient": false},
            "randomPickVertexNumber": 1}},
    "description": ""
      }'
    
## valid data for DELETE exploration result
delete_exploration_result:
  delete_none_selections_result:
    graph_name: "MyGraph"
    file_name: "none_selected"

## valid data for PUT exploration result
put_exploration_result:
  add_a_vertex:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    change_data: '{
    "previewImage": "data:image/png;base64,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",
    "data": {
        "explorationResult": {
            "latestGraph": {
                "nodes": [{"id": "Gimhae-si","type": "City","attrs": {"academy_ratio": 0,"city": "","elderly_alone_ratio": 0,"elderly_population_ratio": 0,"elementary_school_count": 0,"kindergarten_count": 0,"nursing_home_count": 0,"university_count": 0},"styles": {},"others": {},"labels": {},"x": 166.89235127478744,"y": 93.7025495750708},
                    {"id": "Sejong","type": "Province","attrs": {"area": 0,"population": 0,"province": ""},"styles": {},"others": {},"labels": {},"x": 0,"y": 0}],
                "links": []
            },
            "latestSelections": {
                "nodes": [{"type": "City","id": "Gimhae-si"},
                    {"type": "Province","id": "Sejong"}],
                "links": []
            },
            "latestLayout": "force"
        },
        "graphChartConfig": null,
        "graphExplorerConfig": {
            "randomPickVertexNumber": 1,
            "vertexTypeFilter": {"City": false,"Country": false,"Day_": false,"InfectionCase": false,"Month_": false,"Patient": false,"Province": true,"SearchStat": false,"TravelEvent": false,"WeatherStat": false,"Year_": false}}}
      }'

## invalid data for GET all exploration results
invalid_get_all_exploration_results:
  empty_graph:
    graph_name: ''
    cookies: ''
    status_code: 404
    error: True
    message: 'Route /api/exploration-results/ not found.'
  nonexistent_graph:
    graph_name: "none_graph"
    cookies: ''
    status_code: 404
    error: false
    message: "Graph none_graph does not exist or you don't have privileges on it"
  special_character_graph:
    graph_name: '?1&1'
    cookies: ''
    status_code: 404
    error: True
    message: 'Route /api/exploration-results/?1&1 not found.'
  empty_cookies:
    graph_name: "MyGraph"
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies:
    graph_name: "MyGraph"
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''

## invalid data for GET specified exploration result
invalid_get_exploration_results:
  empty_graph:
    graph_name: ''
    file_name: 'open_graph_ex'
    cookies: ''
    status_code: 404
    error: False
    message: ''
  nonexistent_graph:
    graph_name: 'none_graph'
    file_name: 'open_graph_ex'
    cookies: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  special_character_graph:
    graph_name: '?1&1'
    file_name: 'open_graph_ex'
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/exploration-results/?1&1/open_graph_ex not found."
  empty_file_name:
    graph_name: 'MyGraph'
    file_name: ''
    cookies: ''
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  nonexistent_file_name:
    graph_name: 'MyGraph'
    file_name: 'none_file0402'
    cookies: ''
    status_code: 404
    error: True
    message: "Exploration result 'none_file0402' for graph 'MyGraph' cannot be found."
  special_character_file_name:
    graph_name: 'MyGraph'
    file_name: '?1&1'
    cookies: ''
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  empty_cookies:
    graph_name: 'MyGraph'
    file_name: 'open_graph_ex'
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies:
    graph_name: 'MyGraph'
    file_name: 'open_graph_ex'
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''
  

## invliad data for POST exploration result
invalid_post_exploration_result:
  empty_file_name_in_body:
    graph_name: "MyGraph"
    file_name: ""
    data: '{
    "name": "",
    "timestamp": 1698031291611,
    "username": "tigergraph",
    "schema": {},
    "previewImage": "data:image/png;base64,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",
    "data": {
        "explorationResult": {
            "latestGraph": {
                "nodes": [{"id": "Gimhae-si","type": "City","attrs": {"academy_ratio": 0,"city": "","elderly_alone_ratio": 0,"elderly_population_ratio": 0,"elementary_school_count": 0,"kindergarten_count": 0,"nursing_home_count": 0,"university_count": 0},
                        "styles": {},"others": {},"labels": {},"x": 766,"y": 276.5}],
                "links": []},
            "latestSelections": {
                "nodes": [{"type": "City","id": "Gimhae-si"}],
                "links": []},
            "latestLayout": "force"
        },
        "graphChartConfig": null,
        "graphExplorerConfig": {
            "vertexTypeFilter": {"City": true,"Province": false,"Country": false,"SearchStat": false,"WeatherStat": false,"InfectionCase": false,"Year_": false,"Month_": false,"Day_": false,"TravelEvent": false,"Patient": false},
            "randomPickVertexNumber": 1}},
    "description": ""
      }'
    cookies: ''
    status_code: 405
    error: True
    message: "Method not allowed."
  empty_username:
    graph_name: "MyGraph"
    file_name: "invalid_test"
    data: '{
    "name": "invalid_test",
    "timestamp": 1698031291611,
    "username": "",
    "schema": {},
    "previewImage": "data:image/png;base64,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",
    "data": {
        "explorationResult": {
            "latestGraph": {
                "nodes": [{"id": "Gimhae-si","type": "City","attrs": {"academy_ratio": 0,"city": "","elderly_alone_ratio": 0,"elderly_population_ratio": 0,"elementary_school_count": 0,"kindergarten_count": 0,"nursing_home_count": 0,"university_count": 0},
                        "styles": {},"others": {},"labels": {},"x": 766,"y": 276.5}],
                "links": []},
            "latestSelections": {
                "nodes": [{"type": "City","id": "Gimhae-si"}],
                "links": []},
            "latestLayout": "force"
        },
        "graphChartConfig": null,
        "graphExplorerConfig": {
            "vertexTypeFilter": {"City": true,"Province": false,"Country": false,"SearchStat": false,"WeatherStat": false,"InfectionCase": false,"Year_": false,"Month_": false,"Day_": false,"TravelEvent": false,"Patient": false},
            "randomPickVertexNumber": 1}},
    "description": ""
      }'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid payload."
  empty_previewImage:
    graph_name: "MyGraph"
    file_name: "invalid_test"
    data: '{
    "name": "invalid_test",
    "timestamp": 1698031291611,
    "username": "",
    "schema": {},
    "previewImage": "",
    "data": {
        "explorationResult": {
            "latestGraph": {
                "nodes": [{"id": "Gimhae-si","type": "City","attrs": {"academy_ratio": 0,"city": "","elderly_alone_ratio": 0,"elderly_population_ratio": 0,"elementary_school_count": 0,"kindergarten_count": 0,"nursing_home_count": 0,"university_count": 0},
                        "styles": {},"others": {},"labels": {},"x": 766,"y": 276.5}],
                "links": []},
            "latestSelections": {
                "nodes": [{"type": "City","id": "Gimhae-si"}],
                "links": []},
            "latestLayout": "force"
        },
        "graphChartConfig": null,
        "graphExplorerConfig": {
            "vertexTypeFilter": {"City": true,"Province": false,"Country": false,"SearchStat": false,"WeatherStat": false,"InfectionCase": false,"Year_": false,"Month_": false,"Day_": false,"TravelEvent": false,"Patient": false},
            "randomPickVertexNumber": 1}},
    "description": ""
      }'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid payload."
  empty_cookies:
    graph_name: "MyGraph"
    file_name: "invalid_test"
    data: ''
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    data: ''
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''
  existing_file_name:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    data: ''
    cookies: ''
    status_code: 409
    error: True
    message: "Exploration result 'one_vertex' already exists on graph 'MyGraph'."

## invalid data for delete exploration result
invalid_delete_exploration_result:
  nonexistent_file:
    graph_name: "MyGraph"
    file_name: "none_file"
    cookies: ''
    status_code: 200
    error: False
    message: "Successfully deleted exploration result 'none_file' from graph 'MyGraph'."
  nonexistent_graph:
    graph_name: "none_graph"
    file_name:  "one_vertex"
    cookies: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  empty_file_name:
    graph_name: "MyGraph"
    file_name: ''
    cookies: ''
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  empty_cookies:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''
  
## invalid data for PUT exploration result
invalid_put_exploration_result:
  empty_payload:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    data: '{}'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid payload."
  nonexistent_graph:
    graph_name: "none_graph"
    file_name: "one_vertex"
    data: '{
    "name": "one_vertex",
    "timestamp": 1698031291611,
    "username": "tigergraph",
    "schema": {},
    "previewImage": "data:image/png;base64,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",
    "data": {
        "explorationResult": {
            "latestGraph": {
                "nodes": [{"id": "Gimhae-si","type": "City","attrs": {"academy_ratio": 0,"city": "","elderly_alone_ratio": 0,"elderly_population_ratio": 0,"elementary_school_count": 0,"kindergarten_count": 0,"nursing_home_count": 0,"university_count": 0},
                        "styles": {},"others": {},"labels": {},"x": 766,"y": 276.5}],
                "links": []},
            "latestSelections": {
                "nodes": [{"type": "City","id": "Gimhae-si"}],
                "links": []},
            "latestLayout": "force"
        },
        "graphChartConfig": null,
        "graphExplorerConfig": {
            "vertexTypeFilter": {"City": true,"Province": false,"Country": false,"SearchStat": false,"WeatherStat": false,"InfectionCase": false,"Year_": false,"Month_": false,"Day_": false,"TravelEvent": false,"Patient": false},
            "randomPickVertexNumber": 1}},
    "description": ""
      }'
    cookies: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  nonexistent_file:
    graph_name: "MyGraph"
    file_name: "none_file"
    data: '{
    "name": "one_vertex",
    "timestamp": 1698031291611,
    "username": "tigergraph",
    "schema": {},
    "previewImage": "data:image/png;base64,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",
    "data": {
        "explorationResult": {
            "latestGraph": {
                "nodes": [{"id": "Gimhae-si","type": "City","attrs": {"academy_ratio": 0,"city": "","elderly_alone_ratio": 0,"elderly_population_ratio": 0,"elementary_school_count": 0,"kindergarten_count": 0,"nursing_home_count": 0,"university_count": 0},
                        "styles": {},"others": {},"labels": {},"x": 766,"y": 276.5}],
                "links": []},
            "latestSelections": {
                "nodes": [{"type": "City","id": "Gimhae-si"}],
                "links": []},
            "latestLayout": "force"
        },
        "graphChartConfig": null,
        "graphExplorerConfig": {
            "vertexTypeFilter": {"City": true,"Province": false,"Country": false,"SearchStat": false,"WeatherStat": false,"InfectionCase": false,"Year_": false,"Month_": false,"Day_": false,"TravelEvent": false,"Patient": false},
            "randomPickVertexNumber": 1}},
    "description": ""
      }'
    cookies: ''
    status_code: 200
    error: False
    message: "Successfully upserted exploration result"
  inconsistent_filename:
    graph_name: "MyGraph"
    file_name: "other_file"
    data: '{
    "name": "one_vertex",
    "timestamp": 1698031291611,
    "username": "tigergraph",
    "schema": {},
    "previewImage": "data:image/png;base64,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",
    "data": {
        "explorationResult": {
            "latestGraph": {
                "nodes": [{"id": "Gimhae-si","type": "City","attrs": {"academy_ratio": 0,"city": "","elderly_alone_ratio": 0,"elderly_population_ratio": 0,"elementary_school_count": 0,"kindergarten_count": 0,"nursing_home_count": 0,"university_count": 0},
                        "styles": {},"others": {},"labels": {},"x": 766,"y": 276.5}],
                "links": []},
            "latestSelections": {
                "nodes": [{"type": "City","id": "Gimhae-si"}],
                "links": []},
            "latestLayout": "force"
        },
        "graphChartConfig": null,
        "graphExplorerConfig": {
            "vertexTypeFilter": {"City": true,"Province": false,"Country": false,"SearchStat": false,"WeatherStat": false,"InfectionCase": false,"Year_": false,"Month_": false,"Day_": false,"TravelEvent": false,"Patient": false},
            "randomPickVertexNumber": 1}},
    "description": ""
      }'
    cookies: ''
    status_code: 200
    error: False
    message: "Successfully upserted exploration result 'other_file' for graph 'MyGraph'."
  invalid_data:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    data: '{
      "no_graph": "sssssssssssss&*@4",
      "user": "invalid user"
    }'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid payload."
  empty_cookies:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    data: ''
    cookies: ''
    error: ''
    message: ''
  invalid_cookies:
    graph_name: "MyGraph"
    file_name: "one_vertex"
    data: ''
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    error: ''
    message: ''
  
  
  
  
  

  


  
  
  
