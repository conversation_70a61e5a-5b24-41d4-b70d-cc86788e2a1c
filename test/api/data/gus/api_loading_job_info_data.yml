## valid data for GET loading job info
valid_get_loading_job:
  get_loading_job:
    graph_name: 'MyGraph'
    loading_job_name: 'load_job_Case_csv'

## invalid data for GET loading job info
invalid_get_loading_job_info:
  empty_graph_name:
    graph_name: ''
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/loading-job-info/ not found."
  nonexistent_graph_name:
    graph_name: 'none_graph'
    cookies: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  invalid_graph_name:
    graph_name: '?1&1'
    cookies: ''
    status_code: 404
    error: True
    message: 'Route /api/loading-job-info/?1&1 not found.'
  empty_cookies:
    graph_name: "MyGraph"
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies:
    graph_name: "MyGraph"
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''

## valid data for PUT loading job info
valid_put_loading_job:
  change_loadingStatementsStyle:
    graph_name: "MyGraph"

## invalid data for PUT laoding job info
invalid_put_loading_job:
  empty_graph_name:
    graph_name: ''
    cookies: ''
    payload: ''
    status_code: 404
    error: True
    message: "Route /api/loading-job-info/ not found."
  nonexistent_graph_name:
    graph_name: "none_graph"
    cookies: ''
    payload: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  invalid_graph_name:
    graph_name: "@/1&"
    cookies: ''
    payload: ''
    status_code: 404
    error: True
    message: 'Route /api/loading-job-info/@/1& not found.'
  blank_payload:
    graph_name: "MyGraph"
    cookies: ''
    payload: ''
    status_code: 400
    error: True
    message: 'Invalid payload.'
  invalid_payload:
    graph_name: "MyGraph"
    cookies: ''
    payload: '[{"loadingJobName":"load_job_popData___Sheet1_csv_1584998109163","dataSourceJson":{"type":"file","dataSourceName":"","uri":"popData - Sheet1.csv","options":{"format":"none","separator":",","eol":"\\n","header":true,"quote":""},"position":{"x":-457.409190417981,"y":-643.8690659374646}},"dataSetJson":{"dataFormat":"csv","dataSchema":[]},"header":["province","population","area"],"sampleData":[["Seoul","9904312","605.2"],["Busan","3448737","769.6"],["Daegu","2466052","883.6"],["Gwangju","1502881","501.2"]]}]'
    status_code: 400
    error: True
    message: 'Invalid payload.'
  empty_cookies:
    graph_name: "MyGraph"
    cookies: ''
    payload: ''
    status_code: ''
    error: ''
    message: ''

## invalid data for DELETE loading job info
invalid_delete_loading_job_info:
  empty_graph_name:
    graph_name: ''
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/loading-job-info/ not found."
  nonexistent_graph_name:
    graph_name: "none_graph"
    cookies: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  invalid_graph_name:
    graph_name: "@/1&"
    cookies: ''
    status_code: 404
    error: True
    message: 'Route /api/loading-job-info/@/1& not found.'
  empty_cookies:
    graph_name: "MyGraph"
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies: 
    graph_name: "MyGraph"
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''

