get_privilege_invalid_param:
  none_existent_role:
    role: "none_existent_role"
    state_code: 400
    error: True
    message: "Role 'none_existent_role' does not exist"
  special_character:
    role: "?=superadmin"
    state_code: 400
    error: True
    message: "Role '?=superadmin' does not exist"
  empty_role:
    role: ""
    state_code: 200
    error: False
    message: ""
post_privileges:
  single_privilege:
    payload: {"privileges":["WRITE_SCHEMA"],"roles":["role_test"]}
  multi_privileges:
    payload: {"privileges":["WRITE_SCHEMA", "READ_SCHEMA"],"roles":["role_test"]}

post_privileges_invalid_payload:
  empty_privileges:
    payload: {"privileges":[""],"roles":[""]}
    state_code: 400
    error: True
    message: "Failed to grant privilege"
  none_existent_privilege:
    payload: {"privileges":["WRITE_DATA"],"roles":[""]}
    state_code: 400
    error: True
    message: "Failed to grant privilege"
  none_existent_roles:
    payload: {"privileges":["WRITE_SCHEMA"],"roles":["none_existent_role"]}
    state_code: 400
    error: True
    message: "Failed to grant privilege"
  special_character:
    payload: {"privileges":["WRITE_SCHEMA**&&"],"roles":[""]}
    state_code: 400
    error: True
    message: "Failed to grant privilege"

revoke_privileges_invalid_payload:
  empty_privileges:
    payload: {"privileges":[""],"roles":[""]}
    state_code: 400
    error: True
    message: "Failed to revoke privilege"
  none_existent_privilege:
    payload: {"privileges":["WRITE_DATA"],"roles":[""]}
    state_code: 400
    error: True
    message: "Failed to revoke privilege"
  none_existent_roles:
    payload: {"privileges":["WRITE_SCHEMA"],"roles":["none_existent_role"]}
    state_code: 400
    error: True
    message: "Failed to revoke privilege"
  special_character:
    payload: {"privileges":["WRITE_SCHEMA**&&"],"roles":[""]}
    state_code: 400
    error: True
    message: "Failed to revoke privilege"
  no_assigned_privilege:
    payload: {"privileges":["EXECUTE_LOADINGJOB"],"roles":[""]}
    state_code: 400
    error: True
    message: "Failed to revoke privilege"
  