## valid data for GET data source
valid_get_data_source:
  s3_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    response_result: "dev"
  gcs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "gcs"
    response_result: "qe"
  abs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "abs"
    response_result: "haocheng"

## valid data for PUT data source
valid_put_data_source:
  s3_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "new_s3"
    setting: '{"file.reader.settings.fs.s3a.access.key":"********************","file.reader.settings.fs.s3a.secret.key":"8yWfA54skmiORZVDTafLPL6O3jfo07YsvwBMRYbx"}'
    sample_data: {"graphName":"MyMultiEdge","dataSource":"new_s3","type":"s3","path":"s3a://gsql-sample-data/test-json/test.json","dataFormat":"csv","parsing":{"fileFormat":"none","separator":",","eol":"\\n","header":true,"quote":""},"filling":"N/A","size":10}
  gcs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "gcs"
    alias: "qe"
    setting: '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    sample_data: {"graphName":"MyMultiEdge","dataSource":"qe","type":"gcs","path":"gs://qe-test-data/person.csv","dataFormat":"json","parsing":{"fileFormat":"none","eol":"\\n"},"filling":"N/A","size":10}
  abs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "abs"
    alias: "new_abs"
    setting: '{"accountName":"abstest1","accountKey":"****************************************************************************************"}'
    sample_data: ""

## valid data for POST data source format check
valid_post_datasource_format_check:
  gcs_format_check:
    dataSource_type: "gcs"
    payload: '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

## valid data for DELETE data source
valid_delete_datasource:
  s3_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "s3_delete_test"
  gcs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "gcs"
    alias: "gcs_delete_test"
  abs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "abs"
    alias: "abs_delete_test"

## invalid data for GET data source
invalid_get_datasource:
  invalid_dataSource_type:
    graph_name: "MyMultiEdge"
    dataSource_type: "?&=1gcs_1"
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/data-sources/MyMultiEdge?&=1gcs_1/names not found."
  empty_dataSource_type:
    graph_name: "MyMultiEdge"
    dataSource_type: ""
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/data-sources/MyMultiEdge/names not found."
  invalid_graph_name:
    graph_name: "?&=MyMultiEdge"
    dataSource_type: "s3"
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/data-sources/?&=MyMultiEdge/s3/names not found."
  nonexistent_graph_name:
    graph_name: "none_graph"
    dataSource_type: "s3"
    cookies: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  empty_graph_name:
    graph_name: ""
    dataSource_type: "s3"
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/data-sources/s3/names not found."
  empty_cookies:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''

## invalid data for POST data source
invalid_put_data_source:
  incorrect_key_s3_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "s3_incorrect_key"
    setting: '{"file.reader.settings.fs.s3a.access.key":"6B6T6R52UU7XJ2NL","file.reader.settings.fs.s3a.secret.key":"8yWfA54skmiORZVDTafLPL6O3jfo07YsvwBMRYbx"}'
    cookies: ''
    status_code: 500
    error: True
    message: ''
  empty_key_s3_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "s3_empty_key"
    settin: '{"file.reader.settings.fs.s3a.access.key":"","file.reader.settings.fs.s3a.secret.key":"8yWfA54skmiORZVDTafLPL6O3jfo07YsvwBMRYbx"}'
    cookies: ''
    status_code: 400 
    error: True
    message: "Invalid data source credentials format for data source of type: s3"
  incorrect_key_id_gcs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "gcs"
    alias: "invalid_key_id"
    setting: '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    cookies: ''
    status_code: 400
    error: True
    message: ''
  empty_key_gcs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "gcs"
    alias: "invalid_key"
    setting: '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid data source credentials format for data source of type: gcs"
  incorrect_graphName_abs_data_source:
    graph_name: "none_graph"
    dataSource_type: "abs"
    alias: "new_abs"
    setting: '{"accountName":"dddabstest1","accountKey":"****************************************************************************************"}'
    cookies: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  empty_account_key_abs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "abs"
    alias: "empty_accountkey"
    setting: '{"accountName":"abstest1","accountKey":""}'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid data source credentials format for data source of type: abs"
  empty_accountName_abs_data_source:
    graph_name: "MyMultiEdge"
    dataSource_type: "abs"
    alias: "empty_accountName"
    setting: '{"accountName":"","accountKey":"****************************************************************************************"}'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid data source credentials format for data source of type: abs"
  same_alias_name:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "dev"
    setting: '{"file.reader.settings.fs.s3a.access.key":"********************","file.reader.settings.fs.s3a.secret.key":"8yWfA54skmiORZVDTafLPL6O3jfo07YsvwBMRYbx"}'
    cookies: ''
    status_code: 409
    error: True
    message: "Semantic Check Fails: The data source name conflicts with another type or existing data source names"
  empty_cookies:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "new_s3"
    setting: '{"file.reader.settings.fs.s3a.access.key":"********************","file.reader.settings.fs.s3a.secret.key":"8yWfA54skmiORZVDTafLPL6O3jfo07YsvwBMRYbx"}'
    cookies: ''
    status_code:
    error:
    message:
  invalid_cookies:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "new_s3"
    setting: '{"file.reader.settings.fs.s3a.access.key":"********************","file.reader.settings.fs.s3a.secret.key":"8yWfA54skmiORZVDTafLPL6O3jfo07YsvwBMRYbx"}'
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code:
    error:
    message:

## invalid for POST data source format check
invalid_post_datasource_format_check:
  lack_project_id:
    dataSource_type: "gcs"
    payload: '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid data source credentials format for data source of type: gcs"
  lack_private_key_id:
    dataSource_type: "gcs"
    payload: '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid data source credentials format for data source of type: gcs"
  lack_private_key:
    dataSource_type: "gcs"
    payload: '*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid data source credentials format for data source of type: gcs"
  lack_client_id:
    dataSource_type: "gcs"
    payload: '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid data source credentials format for data source of type: gcs"
  incorrect_datasource_type:
    dataSource_type: "s3"
    payload: '{
          "type": "service_account",
          "project_id": "tigergraph-qe",
          "private_key_id": "b32d1b71215be508343ec5b3ffc987ca6c0a93d9",
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          "client_email": "<EMAIL>",
          "client_id": "109283814914819679199",
          "auth_uri": "https://accounts.google.com/o/oauth2/auth",
          "token_uri": "https://oauth2.googleapis.com/token",
          "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
          "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/************-compute%40developer.gserviceaccount.com"
        }'
    cookies: ''
    status_code: 400
    error: True
    message: "Invalid data source credentials format for data source of type: s3"
  empty_cookies:
    dataSource_type: "gcs"
    payload: '{
              "type": "service_account",
              "project_id": "tigergraph-qe",
              "private_key_id": "b32d1b71215be508343ec5b3ffc987ca6c0a93d9",
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
              "client_email": "<EMAIL>",
              "client_id": "109283814914819679199",
              "auth_uri": "https://accounts.google.com/o/oauth2/auth",
              "token_uri": "https://oauth2.googleapis.com/token",
              "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
              "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/************-compute%40developer.gserviceaccount.com"
            }'
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies:
    dataSource_type: "gcs"
    payload: '{
              "type": "service_account",
              "project_id": "tigergraph-qe",
              "private_key_id": "b32d1b71215be508343ec5b3ffc987ca6c0a93d9",
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
              "client_email": "<EMAIL>",
              "client_id": "109283814914819679199",
              "auth_uri": "https://accounts.google.com/o/oauth2/auth",
              "token_uri": "https://oauth2.googleapis.com/token",
              "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
              "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/************-compute%40developer.gserviceaccount.com"
            }'
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''



## invalid data for DELETE data source
invalid_delete_datasource:
  nonexistent_graph_name:
    graph_name: "none_graph"
    dataSource_type: "s3"
    alias: "dev"
    cookies: ''
    status_code: 404
    error: False
    message: "Graph none_graph does not exist or you don't have privileges on it"
  empty_graph_name:
    graph_name: ""
    dataSource_type: "s3"
    alias: "dev"
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/data-sources/s3/dev not found."
  nonexistent_dataSource_type:
    graph_name: "MyMultiEdge"
    dataSource_type: "gcs3"
    alias: "qe"
    cookies: ''
    status_code: 200
    error: False
    message: "Successfully deleted data source 'qe' from graph 'MyMultiEdge'."
  empty_dataSource_type:
    graph_name: "MyMultiEdge"
    dataSource_type: ""
    alias: "qe"
    cookies: ''
    status_code: 404
    error: True
    message: "Route /api/data-sources/MyMultiEdge/qe not found."
  nonexistent_alias:
    graph_name: "MyMultiEdge"
    dataSource_type: "gcs"
    alias: "qe_none"
    cookies: ''
    status_code: 404
    error: True
    message: "Data source 'qe_none' not found in graph 'MyMultiEdge'."
  empty_alias:
    graph_name: "MyMultiEdge"
    dataSource_type: "gcs"
    alias: ""
    cookies: ''
    status_code: 404
    error: True
    message:  "Route /api/data-sources/MyMultiEdge/gcs not found."
  empty_cookies:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "dev"
    cookies: ''
    status_code: ''
    error: ''
    message: ''
  invalid_cookies:
    graph_name: "MyMultiEdge"
    dataSource_type: "s3"
    alias: "dev"
    cookies: 'Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a'
    status_code: ''
    error: ''
    message: ''

get_sample_data:
  payload: {"graphName":"MyMultiEdge","dataSource":"new_s3","type":"s3","path":"s3a://gsql-sample-data/test-json/test.json","dataFormat":"csv","parsing":{"fileFormat":"none","separator":",","eol":"\\n","header":true,"quote":""},"filling":"N/A","size":10}
