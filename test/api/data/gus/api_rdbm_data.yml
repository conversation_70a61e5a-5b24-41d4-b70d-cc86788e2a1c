# define data for rdbms test cases 
post_rdbm:
  mysql:
    type: "mysql"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling"
    payload: {"username":"root","password":"123456"}
    dump_payload: {"username":"root","password":"123456","tableNames":["tb_emp1"]}
  postgresql:
    type: "postgresql"
    server: "52.205.140.166"
    port: "9092"
    database: "yuling"
    payload: {"username":"postgres","password":"123456"}
    dump_payload: {"username":"postgres","password":"123456","tableNames":["teachers"]}

post_rdb_invalid_payload:
  unspport_type:
    type: "Oracle"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling"
    payload: {"username":"root","password":"123456"}
    status_code: 500
    error: True
    message: "No enum constant com.tigergraph.schema.controller.RdbmsController.SupportedJDBCDrivers.ORACLE"
  incorrect_server:
    type: "mysql"
    server: "52.205.140.133"
    port: "30008"
    database: "yuling"
    payload: {"username":"root","password":"123456"}
    status_code: 500
    error: True
    message: "Connect timed out"
  incorrect_database_name:
    type: "mysql"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling_2"
    payload: {"username":"root","password":"123456"}
    status_code: 500
    error: True
    message: "Unknown database"
  incorrect_port:
    type: "mysql"
    server: "52.205.140.166"
    port: "3206"
    database: "yuling_2"
    payload: {"username":"root","password":"123456"}
    status_code: 500
    error: True
    message: "Socket fail to connect to"
  incorrect_username:
    type: "mysql"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling"
    payload: {"username":"root2","password":"123456"}
    status_code: 500
    error: True
    message: "Access denied for user"

post_rdbm_dump_invalid_payload:
  unspport_type:
    type: "Oracle"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling"
    payload: {"username":"root","password":"123456","tableNames":["tb_emp1"]}
    status_code: 400
    error: True
    message: "Database type 'Oracle' is not supported"
  incorrect_server:
    type: "mysql"
    server: "52.205.140.133"
    port: "30008"
    database: "yuling"
    payload: {"username":"root","password":"123456","tableNames":["tb_emp1"]}
    status_code: 500
    error: True
    message: "Connect timed out"
  incorrect_database_name:
    type: "mysql"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling_2"
    payload: {"username":"root","password":"123456","tableNames":["tb_emp1"]}
    status_code: 500
    error: True
    message: "Unknown database"
  incorrect_port:
    type: "mysql"
    server: "52.205.140.166"
    port: "3206"
    database: "yuling"
    payload: {"username":"root","password":"123456","tableNames":["tb_emp1"]}
    status_code: 500
    error: True
    message: "Socket fail to connect to"
  incorrect_username:
    type: "mysql"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling"
    payload: {"username":"root2","password":"123456","tableNames":["tb_emp1"]}
    status_code: 500
    error: True
    message: "Access denied for user"
  incorrect_tableNames:
    type: "mysql"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling"
    payload: {"username":"root","password":"123456","tableNames":["tb_emp_2"]}
    status_code: 200
    error: False
    message: ""
  empty_tableNames:
    type: "mysql"
    server: "52.205.140.166"
    port: "30008"
    database: "yuling"
    payload: {"username":"root","password":"123456","tableNames":[""]}
    status_code: 200
    error: False
    message: ""
  

  