#define request the api with the invalid cookies
invalid_cookie:
  expire_cookie:
    cookie: "TigerGraphApp=c3e64783-b529-423e-8791-3117c7347a2e"
  empty_cookie:
    cookie: ""
  invliad_cookie:
    cookie: "invalid_cookie"
  specific_cookie:
    cookie: "invalid_123_@#$"

#define delete file with the invalid conditions
invalid_files:
  empty_files:
    filename: ""
    message: "Successfully removed path"
  not_wholename_files:
    filename: "upload_data"
    message: "Successfully removed path"
  end_format_files:
    filename: "*.csv"
    message: "Successfully removed path"
  invalid_files:
    filename: "invalid.csv"
    message: "Successfully removed path"
  specific_files:
    filename: "invalid_&&#12.tar"
    message: "Successfully removed path"

## batch upload file 
batch_upload_file:
  upload_to_MyGraph:
    file_list: ['Case.csv', 'PatientInfo.csv', 'PatientRoute.csv', 'Region.csv', 'SearchTrend.csv', 'Weather.csv']
