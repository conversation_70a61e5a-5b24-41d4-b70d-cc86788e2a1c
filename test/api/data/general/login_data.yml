default_user:
  username: "tigergraph"
  password: "tigergraph"

#define the valid login cases
valid_login:  
  default_user:
    username: "tigergraph"
    password: "tigergraph"
    status_code: 200
    error: False
    message: ""

#define the invalid login cases
invalid_login:  
  invalid_user:
    username: "invalid"
    password: "tigergraph"
    status_code: 500
    error: True
    message: "GSQL authentication failed."
  invalid_password:
    username: "tigergraph"
    password: "invalid"
    status_code: 500
    error: True
    message: "GSQL authentication failed."
  empty_username:
    username: ""
    password: "tigergraph"
    status_code: 400
    error: True
    message: "Invalid payload."
  empty_password:
    username: "tigergraph"
    password: ""
    status_code: 400
    error: True
    message: "Invalid payload."
  empty_all:
    username: ""
    password: ""
    status_code: 400
    error: True
    message: "Invalid payload."
  not_exists_user:
    username: "notexistsuser"
    password: "123"
    status_code: 500
    error: True
    message: "GSQL authentication failed."

#define logout with the invalid cookies
invalid_cookie:
  empty_cookie:
    cookie: ""
  invliad_cookie:
    cookie: "invalid_cookie"
  specific_cookie:
    cookie: "invalid_123_@#$"

#define cloud login data
cloud_login_data_dev:
  authServerName: auth.tgcloud-dev.com
  client: eyJuYW1lIjoiYXV0aDAtcmVhY3QiLCJ2ZXJzaW9uIjoiMS4xMC4wIn0
  client_id: PBBtD7unto4oYb0eNc6KuJbUNyZQXzka
  cloud_tools_domain: https://tools-v390.tgcloud-dev.com
  code_challenge: KJP8QWvzESsCMa4jdBliNUNfwyEPz3IZJu6TbgO49ds
  code_verifier: 1WQ1nUEODLFqOUIlFfGHX3pBg5RbUCF8um9Jq.l_PGZ
  init_nonce: blRrUy1hdmF2UzgxV1lZMmZMSUZCUzNwWkRkUmtieGF5ZmVzaHVQT09uMA
  init_state: Ny02eUNjYmtxandSaUpwYUxXNmpoRzBMY1Y3Smkzc0dZQUlrSEt4RE5VLg
