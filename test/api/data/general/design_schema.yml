#define the invalid export cases
valid_export:
  null_params:
    params: ""
    status_code: 200
invalid_export:
  null_params:
    params: "?null"
    status_code: 200
  special_characters:
    params: "?id=null&KEY=””"
    status_code: 200
  embedded_single_quote:
    params: "?key=jo’scar"
    status_code: 200
  field_size_test:
    params: "?number=9999999999999999999999999"
    status_code: 200
invalid_gui_store:
  valid_params:
    params: ""
    status_code: 200
  null_params:
    params: "?null"
    status_code: 200
#  special_characters:
#    params: "?id=null&KEY=””"
#    status_code: 200
#  embedded_single_quote:
#    params: "?key=jo’scar"
#    status_code: 200
#  field_size_test:
#    params: "?number=9999999999999999999999999"
#    status_code: 200
