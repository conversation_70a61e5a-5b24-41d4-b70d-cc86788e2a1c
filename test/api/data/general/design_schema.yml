#define the invalid export cases
valid_export:
  null_params:
    params: ""
    status_code: 200
invalid_export:
  null_params:
    params: "?null"
    status_code: 200
  special_characters:
    params: "?id=null&KEY=””"
    status_code: 200
  embedded_single_quote:
    params: "?key=jo’scar"
    status_code: 200
  field_size_test:
    params: "?number=9999999999999999999999999"
    status_code: 200
invalid_gui_store:
  valid_params:
    params: ""
    status_code: 200
  null_params:
    params: "?null"
    status_code: 200
#  special_characters:
#    params: "?id=null&KEY=””"
#    status_code: 200
#  embedded_single_quote:
#    params: "?key=jo’scar"
#    status_code: 200
#  field_size_test:
#    params: "?number=9999999999999999999999999"
#    status_code: 200

create_graph:
  empty_graph:
    graph_name: "empty_graph"
    payload: {"VertexTypes":[],"EdgeTypes":[]}
  graph_with_vertex:
    graph_name: "graph_with_vertex"
    payload: {"VertexTypes":["City","Country"],"EdgeTypes":[]}

create_graph_invalid:
  empty_graph_name:
    graph_name: ""
    paylaod: {"VertexTypes":[],"EdgeTypes":[]}
    state_code: 400
    error: True
    message: "Required request body is missing"
  empty_payload:
    graph_name: "empty_payload"
    paylaod: {}
    state_code: 400
    error: True
    message: "Required request body is missing"
  incorrect_keyword:
    graph_name: "incorrect_keyword"
    paylaod: {"Vertex":[],"EdgeTypes":[]}
    state_code: 400
    error: True
    message: "Required request body is missing"
  duplicated_graph_name:
    graph_name: "graph_with_vertex"
    payload: {"VertexTypes":["Post","University"],"EdgeTypes":[]}
    state_code: 500
    error: True
    message: "The graph name conflicts with another type"
  none_existetnt_vertex:
    graph_name: "none_existetnt_vertex"
    payload: {"VertexTypes":["Jing","API"],"EdgeTypes":[]}
    state_code: 500
    error: True
    message: "The name Jing does not match any vertex type"


change_graph:
  add_vertex:
    graph_name: "empty_graph"
    payload: {"VertexTypes":[],"EdgeTypes":[]}
    change_payload: {"alterEdgeTypes":[],"alterVertexTypes":[],"dropEdgeTypes":[],"dropVertexTypes":[],"addVertexTypes":[{"Name":"vertex_1","PrimaryId":{"AttributeName":"id","AttributeType":{"Name":"STRING"}},"Attributes":[],"Config":{"STATS":"OUTDEGREE_BY_EDGETYPE"},"IsLocal":true},{"Name":"vertex_2","PrimaryId":{"AttributeName":"id","AttributeType":{"Name":"STRING"}},"Attributes":[],"Config":{"STATS":"OUTDEGREE_BY_EDGETYPE"},"IsLocal":true}],"addEdgeTypes":[]}
  # add_eges:
  #   graph_name: "graph_with_vertex"
  #   payload: {"VertexTypes":["City","Country"],"EdgeTypes":[]}
  #   change_payload: {"alterEdgeTypes":[],"alterVertexTypes":[],"dropEdgeTypes":[],"dropVertexTypes":[],"addVertexTypes":[],"addEdgeTypes":[{"Name":"edge_1","FromVertexTypeName":"City","ToVertexTypeName":"Country","IsDirected":false,"Config":{},"Attributes":[],"DiscriminatorCount":0,"IsLocal":true}]}

change_graph_invalid:
  existent_vertex:
    graph_name: "empty_graph"
    payload: {"VertexTypes":[],"EdgeTypes":[]}
    change_payload: {"alterEdgeTypes":[],"alterVertexTypes":[],"dropEdgeTypes":[],"dropVertexTypes":[],"addVertexTypes":[{"Name":"City","PrimaryId":{"AttributeName":"id","AttributeType":{"Name":"STRING"}},"Attributes":[],"Config":{"STATS":"OUTDEGREE_BY_EDGETYPE"},"IsLocal":true},{"Name":"Country","PrimaryId":{"AttributeName":"id","AttributeType":{"Name":"STRING"}},"Attributes":[],"Config":{"STATS":"OUTDEGREE_BY_EDGETYPE"},"IsLocal":true}],"addEdgeTypes":[]}
    state_code: 422
    error: True
    message: "Please use a different name"
  nonexistent_vertex_in_edges:
    graph_name: "empty_graph"
    payload: {"VertexTypes":[],"EdgeTypes":[]}
    change_payload: {"alterEdgeTypes":[],"alterVertexTypes":[],"dropEdgeTypes":[],"dropVertexTypes":[],"addVertexTypes":[{"Name":"edge_1","PrimaryId":{"AttributeName":"id","AttributeType":{"Name":"STRING"}},"Attributes":[],"Config":{"STATS":"OUTDEGREE_BY_EDGETYPE"},"IsLocal":true},{"Name":"edge_2","PrimaryId":{"AttributeName":"id","AttributeType":{"Name":"STRING"}},"Attributes":[],"Config":{"STATS":"OUTDEGREE_BY_EDGETYPE"},"IsLocal":true}],"addEdgeTypes":[]}
    state_code: 422
    error: True
    message: "Please use a different name"
  no_vertex_in_edge:
    graph_name: "empty_graph"
    payload: {"VertexTypes":[],"EdgeTypes":[]}
    change_payload: {"alterEdgeTypes":[],"alterVertexTypes":[],"dropEdgeTypes":[],"dropVertexTypes":[],"addVertexTypes":[],"addEdgeTypes":[{"Name":"add_edge_2","FromVertexTypeName":"","ToVertexTypeName":"","IsDirected":false,"Config":{},"Attributes":[],"DiscriminatorCount":0,"IsLocal":true}]}
    state_code: 422
    error: True
    message: "FROM vertex type  does not exist"
  no_change:
    graph_name: "empty_graph"
    payload: {"VertexTypes":[],"EdgeTypes":[]}
    hange_payload: {"alterEdgeTypes":[],"alterVertexTypes":[],"dropEdgeTypes":[],"dropVertexTypes":[],"addVertexTypes":[],"addEdgeTypes":[]}
    state_code: 400
    error: True
    message: "Required request body is missing"
