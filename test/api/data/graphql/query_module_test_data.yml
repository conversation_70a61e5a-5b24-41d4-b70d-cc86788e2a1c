# Test data for query module
test_blank_query:
  graph_name: "ldbc_snb"
  query: null
  variables: null
  verify_level: 2
  except_data: null
  except_errors: ["operation  is not found"]

test_sample_query:
  graph_name: "ldbc_snb"
  query: "query{\n  ldbc_snb{\n    Person(order_by:\n      {lastName: asc},\n    limit:10){\n      lastName\n      firstName\n      browserUsed\n    }\n  }\n}\n"
  variables: null
  verify_level: 1
  except_errors: null
  except_data: {"ldbc_snb":{Person":[{"browserUsed":"Firefox","firstName":"Wim","lastName":"Aa"},{"browserUsed":"Internet Explorer","firstName":"Henri","lastName":"Aa"},{"browserUsed":"Internet Explorer","firstName":"Jan","lastName":"Aa"},{"browserUsed":"Firefox","firstName":"<PERSON>","lastName":"Aa"},{"browserUsed":"Firefox","firstName":"<PERSON>","lastName":"Aa"},{"browserUsed":"Firefox","firstName":"<PERSON>","lastName":"Aa"},{"browserUsed":"Opera","firstName":"Jan","lastName":"Aa"},{"browserUsed":"Firefox","firstName":"Patrick","lastName":"Aa"},{"browserUsed":"Firefox","firstName":"Dilnaz","lastName":"Aab"},{"browserUsed":"Internet Explorer","firstName":"Alfina","lastName":"Aab"}]}}

test_translation_check:
  graph_name: "MyGraph"
  query: "query{\n  MyGraph{\n    Account{\n      Industry\n      Phone\n    }\n    Industry{\n      reverse_is_for_the{\n        to{\n          StageName\n        }\n      }\n    }\n  }\n  \n    \n  }\n"
  variables: null
  except_attribute_type: "<class 'str'>"
  except_vertex_type: "<class 'list'>"
  except_errors: null

test_wrong_query:
  graph_name: "MyGraph"
  query: "query{\n  MyGraph{\n    Account{\n      Industry\n      Phone\n    }\n    Industry{\n      reverse_is_for_the{\n        to{\n          StageName\n        }\n      }\n    }\n  }\n  \n    \n  }\n"
  variables: null
  except_data: null
  except_errors: "\nType Check Error in query  (TYP-523): line 6, col 6\nAn undefined variable vertex_set_1 in current scope\n"

test_sort_single_column:
  graph_name: "MyGraph"
  field_name: "Campaign"
  query_asc: "query{\n  MyGraph{\n   Campaign(order_by:{ActualCost : asc}){\n    BudgetedCost\n    Campaign_Type\n    CreatedDate\n    ActualCost\n    Status\n    reverse_is_part_of{\n      to{\n        Status\n        SystemModstamp\n        IsPrimary\n        IsDeleted\n      }\n    }\n  }\n  }\n}"
  query_desc: "query{\n  MyGraph{\n   Campaign(order_by:{ActualCost : desc}){\n    BudgetedCost\n    Campaign_Type\n    CreatedDate\n    ActualCost\n    Status\n    reverse_is_part_of{\n      to{\n        Status\n        SystemModstamp\n        IsPrimary\n        IsDeleted\n      }\n    }\n  }\n  }\n}"
  variables: null
  except_asc_actualcost_1: 0
  except_asc_actualcost_end: 25000
  except_attribute_type: "<class 'str'>"
  except_vertex_type: "<class 'list'>"
  except_errors: null

test_sort_multiple_column:
  graph_name: "MyGraph"
  field_name: "Campaign"
  query: "query{\n  \t\tMyGraph{\n        Campaign(order_by:[\n          {ActualCost : asc},\n        {Status :desc},\n        {CreatedDate:asc},\n        {Number_Of_Leads: desc}]){\n          BudgetedCost\n          Campaign_Type\n          CreatedDate\n          ActualCost\n          Number_Of_Leads\n          Status\n          \n        }\n      }\n}\n\n"
  variables: null
  except_errors: null
  except_data_status1: "Planned"
  except_data_status2: "In Progress"

test_sort_nlop:
  graph_name: "MyGraph"
  field_name: "Campaign"
  query: "query{\n  \t\tMyGraph{\n        Campaign(order_by:[\n          {ActualCost : asc},\n        {Status :desc},\n        {CreatedDate:asc},\n        {Number_Of_Leads: desc}]){\n          BudgetedCost\n          Campaign_Type\n          CreatedDate\n          ActualCost\n          Number_Of_Leads\n          Status\n          reverse_Is_Driven_By{\n            to(order_by:{IsWon : asc}){\n              StageName\n              IsWon\n            }\n          }\n        }\n      }\n}\n\n"
  variables: null
  except_errors: "Unknown argument \"order_by\" on field \"to\" of type \"reverse_Is_Driven_By\"."

test_sort_multiple_vertex:
  graph_name: "MyGraph"
  query: "query{\n  \t\tMyGraph{\n        Campaign(order_by:[{ActualCost : asc},\n        {Number_Of_Leads: desc}]\n          ){\n          BudgetedCost\n          Campaign_Type\n          CreatedDate\n          ActualCost\n          Number_Of_Leads\n          Status\n          \n          }\n\n        Account(order_by:{\n          CreatedDate:asc\n        }){\n          Description\n          Account_id\n          BillingCity\n          AccountSource\n          CreatedDate\n        }\n        \n        Contact(\n          order_by:{\n            LastName: desc\n          }\n        ){\n          LastName\n          Free_Trial_Start_date\n          Description\n        }\n        \n        Lead(order_by:{\n          LastModifiedById:asc\n        }){\n          State\n          Status\n          LastModifiedById\n        }\n      }\n  \t\n\n      }"
  variables: ""
  except_errors: null

