from email import header
import os
from src.api.tools_api import tools
from src.api.gsql_server_api import gsqlserver
from src.common.read_data import data
from src.common.logger import logger
from src.common.result_base import ResultBase
from pathlib import Path
import json
import mimetypes
import requests
import urllib.parse
from codecs import encode
from datetime import datetime

RESOURCES_PATH = Path(__file__).resolve().parents[2].joinpath("data/gus")
LOCALFILES_PATH = Path(__file__).resolve().parents[3].joinpath("data/localFiles")


class GSQLServer():
    def give_headers(self, cookie):
        header = {
            "Cookie": cookie,
            "Content-Type": "application/json"
        }
        return header

    def get_type_names(self, cookies):
        logger.info("check the type names")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.get_type_names(headers=header))


    # Queries Module
    def run_query_codecheck(self, cookies, graph_name, query):
        logger.info("check the query code check ...")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.query_codecheck(graph_name, query, headers=header))

    def run_interpret_query(self, cookies, graph_name, query):
        logger.info("check POST /api/gsql-server/gsql/v1/queries/interpret?graph={}".format(graph_name))
        header = self.give_headers(cookies)
        header["Content-Type"] = "text/plain"
        return ResultBase(gsqlserver.post_interpret_query(graph_name, query, headers=header))

    def check_gsql_output_file(self, path, cookies):
        logger.info("Try to check if the gsql file `{}` match file policy".format(path))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.check_file_policy(path, headers = header))

    def get_queries_info(self, graph_name, query, cookie):
        logger.info("Check queries info")
        header = self.give_headers(cookie)
        header["Content-Type"] = "text/plain"
        return ResultBase(gsqlserver.get_queries_info(graph_name, query, headers = header))

    # Auth Module
    def get_authorization_info(self, cookies):
        logger.info("check GET /api/gsql-server/gsql/v1/auth/simple ...")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.get_authorization_info(headers=header))

    def get_oidc_auth_info(self, cookies):
        logger.info("check GET /api/gsql-server/gsql/v1/oidc/authnrequest")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.get_oidc_auth(headers = header))

    def get_saml_auth_info(self, cookies):
        logger.info("check GET /api/gsql-server/gsql/v1/saml/authnrequest")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.get_saml_auth(headers = header))

    # RDBMs Module
    def create_rdbm_meta(self, type, server, port, database, payload, cookies):
        logger.info("check POST /api/gsql-server/gsql/v1/rdbms/meta with type {}".format(type))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.post_rdbms(type, server, port, database, json.dumps(payload), headers=header))

    def post_rdbm_dumps(self, type, server, port, database, payload, cookies):
        logger.info("check POST /api/gsql-server/gsql/v1/rdbms/dump with type {}".format(type))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.post_rdbm_dumps(type, server, port, database, json.dumps(payload), headers=header))

    # Users Module
    def list_proxy_groups(self, cookies):
        logger.info("Try to list all proxy groups")
        header = self.give_headers(cookies)
        param = "gsqlFormat=true"
        return ResultBase(gsqlserver.get_proxy_group(param, headers = header))

    def create_proxy_group(self, payload, cookies):
        logger.info("Try to create a new proxy group ")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.post_proxy_group(json.dumps(payload), headers = header))

    def delete_proxy_group(self, payload, cookies):
        logger.info("Try to delete the proxy group `{}`".format(payload["groupNames"]))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.delete_proxy_group(json.dumps(payload), headers=header))

    def check_if_the_proxy_Group_exist(self, name, cookies):
        logger.info("Check if the proxy group `{}` exist".format(name))
        is_exist = False
        proxy_group_lists = self.list_proxy_groups(cookies).results
        for pg in proxy_group_lists:
            logger.info("The current proxy group name is `{}`, the desired proxy group name is `{}`".format(pg["name"], name))
            if name == pg["name"]:
                is_exist = True
                logger.info("The proxy group `{}` already exists".format(name))
        return is_exist

    def update_proxy_group(self, payload, cookies):
        logger.info("Try to update proxy group {}".format(payload["name"]))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.put_proxy_group(json.dumps(payload), headers = header))

    def get_roles_privileges(self, roles, cookies):
        logger.info("Try to get these role's privileges: `{}`".format(roles))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.get_role_privilege_info(roles, headers = header))

    def list_all_roles(self, cookies):
        logger.info("Try to list all roles")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.get_roles(headers = header))

    def create_new_role(self, payload, cookies):
        logger.info("Try to create these role `{}`".format(payload))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.post_roles(json.dumps(payload), headers = header))

    def delete_a_role(self, role, cookies):
        logger.info("Try to delete the role `{}`".format(role))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.delete_roles(role, headers = header))

    def check_if_role_exist(self, role_name, cookies):
        logger.info("Check if the role `{}` exists".format(role_name))
        roles = self.list_all_roles(cookies).results["userDefinedRoles"]
        if "1" in roles:
            userdefined_roles = roles["1"]
            for role in userdefined_roles:
                if role_name == role:
                    return True
        return False

    def get_roles_by_rolesName(self, role_name, cookies):
        logger.info("Check the role `{}`'s privileges".format(role_name))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.get_roles_by_rolename(role_name, headers = header))

    def grant_roles(self, payload, cookies):
        logger.info("Check if can grant roles for the user `{}`".format(payload["users"][0]))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.grant_roles(json.dumps(payload), headers = header))

    def revoke_roles(self, payload, cookies):
        logger.info("Check if can revoke roles for the user `{}`".format(payload["users"][0]))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.revoke_roles(json.dumps(payload), headers = header))

    def grant_privileges(self, payload, cookies):
        logger.info("Check if can grant privileges for role")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.post_privilege(json.dumps(payload), headers = header))

    def revoke_privileges(self, payload, cookies):
        logger.info("Check if can revoke privileges")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.revoke_privilege(json.dumps(payload), headers = header))

    def list_users(self, cookies):
        logger.info("Check if can list all users")
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.get_users(headers = header))

    def create_users(self, payload, cookies):
        logger.info("Create a user `{}`".format(payload["username"]))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.post_users(json.dumps(payload), headers = header))

    def delete_user(self, user, cookies):
        logger.info("Check if can delete the user `{}`".format(user))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.delete_user(user, headers = header))

    def change_password(self, user, payload, cookies):
        logger.info("Check if can change password for the user `{}`".format(user))
        header = self.give_headers(cookies)
        return ResultBase(gsqlserver.put_users(user, json.dumps(payload), headers = header))

    def check_if_user_exist(self, user, cookie):
        logger.info("Check if the user `{}` exists".format(user))
        users = self.list_users(cookie).results
        is_exist = False
        for i in users:
            if i["name"] == user:
                is_exist = True
        return is_exist

    def create_secrets(self, alias, cookie):
        logger.info("Check if can create a new secret with alias `{}`".format(alias))
        header = self.give_headers(cookie)
        return ResultBase(gsqlserver.create_secret(alias, headers = header))

    def check_if_secret_exists(self, alias, cookie):
        alias_info = self.get_authorization_info(cookie).results["secrets"]
        if alias_info:
            for a in alias_info:
                if a["alias"] == alias:
                    return True
        return False

    def delete_secret(self, payload, cookie):
        logger.info("check if can delete the secret `{}`".format(payload["secrets"]))
        header = self.give_headers(cookie)
        return ResultBase(gsqlserver.delete_secret(json.dumps(payload), headers = header))

    ## Graph related APIs
    def get_udf_tuples(self, graph_name, cookie):
        logger.info("Check if can get udf tuples")
        header = self.give_headers(cookie)
        return ResultBase(gsqlserver.get_udt_tuples(graph_name, headers = header))

    def get_first_graph(self, cookie):
        graph_info = self.get_authorization_info(cookie).results["creators"]
        graph_list = list(graph_info.keys())
        return graph_list[0]
            
    def get_token_functions(self, cookie):
        logger.info("Check if can get token functions")
        header = self.give_headers(cookie)
        return ResultBase(gsqlserver.get_token_functions(headers = header))

    def get_sample_data(self, payload, cookie):
        header = self.give_headers(cookie)
        return ResultBase(gsqlserver.get_sample_data(json.dumps(payload), headers = header))

    def create_graph(self, graph_name, payload, cookie):
        header = self.give_headers(cookie)
        return ResultBase(gsqlserver.post_schema(graph_name, json.dumps(payload), headers = header))

    def delete_graph(self, graph_name, cookie):
        header = self.give_headers(cookie)
        return ResultBase(gsqlserver.delete_schema(graph_name, headers = header))

    def check_if_graph_exist(self, graph_name, cookie):
        graph_info = self.get_authorization_info(cookie).results["creators"]
        graph_list = list(graph_info.keys())
        for graph in graph_list:
            if graph_name == graph:
                return True
        return False

    def change_schema(self, graph_name, payload, cookie):
        header = self.give_headers(cookie)
        return ResultBase(gsqlserver.change_schema(graph_name, json.dumps(payload), headers = header))
    
    def schema_change_payload_prepare(self, payload):
        if payload["addVertexTypes"]:
            for add_vertex in payload["addVertexTypes"]:
                new_name = add_vertex["Name"] + str(int(datetime.now().timestamp()))
                add_vertex["Name"] = new_name

        if payload["addEdgeTypes"]:
            for add_edges in payload["addEdgeTypes"]:
                new_edge_name = add_edges["Name"] + str(int(datetime.now().timestamp()))
                add_edges["Name"] = new_edge_name

        return payload

