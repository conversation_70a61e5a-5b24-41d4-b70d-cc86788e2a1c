import os
from api.tools_api import tools
from common.logger import logger
from common.result_base import ResultBase
import json


def graphql_login(cookies, graph_name):
    """
    Acccess to GraphQL client
    :param cookies: Cookies gotten from GST
    :param graph_name: graph name that you want to query
    :return: return the response of api with custom keywords
    """

    logger.info("start to connect GraphQL client....")
    result = ResultBase()
    header = {
        "Cookie": cookies,
        "Upgrade-Insecure-Requests": "1",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"
    }
    res = tools.connect_graphql(graph_name, headers=header)
    logger.info("the response of graphql api is {}".format(res.status_code))
    result.success = False
    if res.status_code == 200:
        result.success = True

    else:
        result.error = "response's status code is {},the wrong messages is {}".format(res.status_code, res.json()["message"])
        logger.info(result.error)
        result.message = res.json()["message"]

    result.response = res
    return result


def gst_logout(cookies):
    """
    Logout GraphStudio
    :param cookies: Cookies gotten from GST
    :return: return the response of api with custom keywords
    """

    logger.info("start to logout GraphStudio....")
    result = ResultBase()
    header = {
        "Cookie": cookies,
        "Content-Type": "application/json"
    }
    res = tools.logout_gst(headers=header)
    result.success = False
    if res.status_code == 200:
        result.success = True

    else:
        result.error = "response's status code is {},the wrong messages is {}".format(res.status_code, res.json()["message"])
    result.error = res.json()["error"]
    result.message = res.json()["message"]
    result.response = res.json()
    logger.info("{}!  Cookies is invalid".format(res.json()["message"]))
    result.cookies = res.headers["Set-Cookie"]
    return result


def graphql_query(cookies, graph_name, query=None, variables=None):
    """
    Execute query with Graphql language
    :param graph_name: graph name that you want to query
    :param cookies: Cookies gotten from GST
    :param query: queries ，the format is string
    :param variables: query variables，format is json
    :return: return the response of api with custom keywords
    """

    logger.info("execute the query now")
    result = ResultBase()
    header = {
        "cookie": cookies,
        "Content-Type": "application/json"
    }
    query_data = {
        "query": query,
        "variables": variables
    }
    res = tools.exccute_qury(graph_name, json=json.dumps(query_data), headers=header)
    logger.info("response is {}".format(res.json()))
    result.success = False
    if res.status_code == 200:
        result.success = True

    else:
        result.error = "response's status code is {},the wrong messages is {}".format(res.status_code, res.json()["errors"])
    result.error = res.json()["errors"]
    result.data = res.json()["data"]
    return result









