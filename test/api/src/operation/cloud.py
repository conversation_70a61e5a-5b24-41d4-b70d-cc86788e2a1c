import os
import re
import base64
import retrying
import requests
from src.api.cloud_api import cloudAuth, cloud, CloudTools
from src.common.result_base import ResultBase
from src.common.cookies_base import CookiesBase
from src.common.read_data import data
from src.common.update_data import updateData
from src.common.logger import logger
import json
import time
import sys
from pathlib import Path
from urllib import parse
utils_path = Path(__file__).resolve().parents[3].joinpath("src")
sys.path.append(utils_path)
from utils.data_util.data_resolver import read_test_data


class Cloud():
    login_user_data = data.get_yaml_data("general/login_data.yml")
    test_data = read_test_data(file="tools_test_data.json")
    test_env = test_data.get("test_env")
    test_domain = test_data.get("domain")
    org = test_data.get("org")
    user_name = test_data.get("user")
    pwd = test_data.get("password")
    cluster_name = test_data.get("cluster_name")
    # parameter for cloud env
    cloud_test_data = login_user_data.get("cloud_login_data_dev")
    client_id = cloud_test_data.get("client_id")
    client = cloud_test_data.get("client")
    code_verifier = cloud_test_data.get("code_verifier")
    init_state = cloud_test_data.get("init_state")
    init_nonce =  cloud_test_data.get("init_nonce")
    code_challenge =  cloud_test_data.get("code_challenge")
    cloud_tools_domain = cloud_test_data.get("cloud_tools_domain")
    cookies = login_user_data.get("cookies")

    @retrying.retry(stop_max_attempt_number=3, wait_fixed=20)
    def get_cloud_id_token(self, 
                            org,
                            username,
                            pwd,
                            test_env=test_env,
                            client_id=client_id, 
                            client=client, 
                            code_verifier=code_verifier, 
                            init_state=init_state, 
                            init_nonce=init_nonce, 
                            code_challenge=code_challenge, 
                            ):
        """
        Get cloud id token from auth0
        """
        logger.info("Try to get org state")
        header = {
            "Content-Type": ""
        }
        form_content_type = "application/x-www-form-urlencoded"
        # Get first state code
        auth_response = cloudAuth.get_state(client_id, init_state, init_nonce, code_challenge, client, headers=header, allow_redirects=False)
        tmp_state = self.update_state(auth_response.text)
        logger.info("The temp state is {}".format(tmp_state))

        # update auth cookie
        cloudAuth.get_auth_cookie(tmp_state, headers=header)

        # Verify org name
        header["Content-Type"]= form_content_type
        request_body = {
            "state": tmp_state,
            "organizationName": org,
            "action": "default"
        }
        verify_response = cloudAuth.check_org_state(tmp_state, parse.urlencode(request_body), headers=header, allow_redirects=False)
        # update state
        tmp_state =self.update_state(verify_response.text)

        # call api to update cookie
        cloudAuth.get_cloud_cookie(tmp_state, headers=header) 

        # login user with pwd
        login_body = {
            "state": tmp_state,
            "username": username,
            "password": pwd,
            "action": "default"
        }
        login_response = cloudAuth.login_user(tmp_state, parse.urlencode(login_body), headers=header, allow_redirects=False)

        # update state
        resume_state = self.update_state(login_response.text)

        # get code
        time.sleep(5)
        redirect_uri =cloudAuth.get_login_code(resume_state, headers=header, allow_redirects=False)
        code = re.search(r'code=(.*?)\&state', redirect_uri.text).group(1)

        # update cloud cookie
        cloud.get_cloud_cookie(code, init_state, headers=header)

        # get id token
        header["Content-Type"]= "application/json"
        response_body = {
            "client_id": client_id,
            "code_verifier": code_verifier,
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": test_env
        }
        id_token = cloudAuth.get_id_token(json.dumps(response_body), headers=header).json()
        return id_token
    
    def update_state(self, response):
        return re.search(r'state=(\w+)', response).group(1)

    def get_cluster_domain(self, id_token, cluster_name=cluster_name):
        header = {
            "Authorization" : "Bearer " + id_token["id_token"]
        }
        clusters =cloud.get_cluster_info(headers=header)
        for i in clusters.json()["Result"]:
            if i["Name"] == cluster_name and i["State"] == "ready":
                self.save_cloud_login_info(id_token, i)
                return i

    def update_domain_to_file(self, domain, filepath, key="domain"):
        domain = "https://{}".format(domain)
        updateData.update_json_signle_data(key, domain, filepath)

    def access_tools(self, 
                    token, 
                    domain, 
                    cluster_id, 
                    cloud_tools_domain=cloud_tools_domain,
                    org=org, 
                    cookies=cookies):
        cloud_tools = CloudTools("https://" + domain)
        if not cookies and token:
            header={
                    "Cookie": token["id_token"],
                    "Content-Type": ""
                    }
            # get redirect url
            redirect_url = ResultBase(cloud_tools.login_oidc_user(headers=header))
            redirect_url.assert_no_error()
            requests.get(redirect_url.results["OIDCRequest"], headers=header, allow_redirects=False)

            # obtain state
            s_domain = re.search(r"(.*?)\.tgcloud-dev\.com", domain).group(1)
            state = cloud_tools_domain + \
            "/studio/#/home?user_opt_in=true&domain={}&clusterid={}&orgName={}".format(s_domain, cluster_id, org)
            encoded_state = base64.b64encode(state.encode('utf-8')).decode('utf-8')
                    
            request_body={
                "access_token": token["access_token"],
                "scope":"openid profile email",
                "expires_in":7200,
                "token_type":"Bearer",
                "id_token": token["id_token"],
                "state": encoded_state
                }
                    
            header["Content-Type"] = "application/x-www-form-urlencoded"
            res = cloud_tools.login_callback(parse.urlencode(request_body), headers=header)
            logger.info("Access tools successfully")
        return cloud_tools

    def init_cloud_env(self, org=org, user=user_name, pwd=pwd, test_domain=test_domain):
        # obtain cloud id_token
        token = self.get_cloud_id_token(org, user, pwd)
        # obtain cluster info
        cluster_info = self.get_cluster_domain(token)
        cluster_id = cluster_info["ID"]
        domain = cluster_info["Domain"]
        # update domain to config file
        if test_domain != "https://{}".format(domain):
            self.update_domain_to_file(domain, "config/tools_test_data.json")
        return token, domain, cluster_id

    def save_cloud_login_info(self, token, cluster_info, filepath="general/cloud_data.json"):
        logger.info("Get cluster info successfully, try to save these info to file")
        login_info={
            "id_token": token["id_token"],
            "access_token": token["access_token"],
            "cluster_id": cluster_info["ID"],
            "domain": cluster_info["Domain"]
        }
        updateData.update_json_data(login_info, filepath)
        logger.info("Save `id_token`:{}, `access_token`:{}, `cluster_id`:{} successfully".format(token["id_token"],token["access_token"],cluster_info["ID"]))