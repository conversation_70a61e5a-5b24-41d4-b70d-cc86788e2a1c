import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
from src.common.api_basecase import APIBaseCase
import allure


class TestLoadingJobInfo(APIBaseCase):
    loading_job_data = data.get_yaml_data("gus/api_loading_job_info_data.yml")
    get_loading_job_valid = loading_job_data.get("valid_get_loading_job")
    get_loading_job_invalid = loading_job_data.get("invalid_get_loading_job_info")
    put_loading_job_valid = loading_job_data.get("valid_put_loading_job")
    put_loading_job_invalid = loading_job_data.get("invalid_put_loading_job")
    delete_loading_job_invalid = loading_job_data.get("invalid_delete_loading_job_info")
    test_get_loading_job = []
    test_get_loading_job_invalid = []
    test_put_loading_job_info = []
    test_put_loading_job_info_invalid_data = []
    test_delete_loading_job_invalid = []

    for case in get_loading_job_valid:
        test_get_loading_job.append((
            case,
            get_loading_job_valid.get(case).get("graph_name"),
            get_loading_job_valid.get(case).get("loading_job_name")
        ))

    for case in get_loading_job_invalid:
        test_get_loading_job_invalid.append((
            case,
            get_loading_job_invalid.get(case).get("graph_name"),
            get_loading_job_invalid.get(case).get("cookies"),
            get_loading_job_invalid.get(case).get("status_code"),
            get_loading_job_invalid.get(case).get("error"),
            get_loading_job_invalid.get(case).get("message")
        ))

    for case in put_loading_job_valid:
        test_put_loading_job_info.append((
            case,
            put_loading_job_valid.get(case).get("graph_name")
        ))

    for case in put_loading_job_invalid:
        test_put_loading_job_info_invalid_data.append((
            case,
            put_loading_job_invalid.get(case).get("graph_name"),
            put_loading_job_invalid.get(case).get("cookies"),
            put_loading_job_invalid.get(case).get("payload"),
            put_loading_job_invalid.get(case).get("status_code"),
            put_loading_job_invalid.get(case).get("error"),
            put_loading_job_invalid.get(case).get("message")
        ))

    for case in delete_loading_job_invalid:
        test_delete_loading_job_invalid.append((
            case,
            delete_loading_job_invalid.get(case).get("graph_name"),
            delete_loading_job_invalid.get(case).get("cookies"),
            delete_loading_job_invalid.get(case).get("status_code"),
            delete_loading_job_invalid.get(case).get("error"),
            delete_loading_job_invalid.get(case).get("message")
        ))

    @allure.title("Get loading job info")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Get graph loading job \n"
        "Description: Try to get graph loading job \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_get_loading_job, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiloadingjobinfo
    @pytest.mark.run(order=1)
    def test_get_loading_job_info(self, case, graph_name, loading_job_name):
        login = Login()
        gus = GUS()
        login_result = login.login()
        loading_job_info = gus.get_graph_loading_job_info(login_result.get_cookies(), graph_name)
        loading_job_info.assert_no_error()
        assert loading_job_name in str(loading_job_info.results)

    @allure.title("Get loading job info")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Get graph loading job with invalid payload \n"
        "Description: Try to get graph loading job with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_get_loading_job_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiloadingjobinfo
    @pytest.mark.run(order=1)
    def test_get_loading_job_info_invalid(self, case, graph_name, cookies, status_code, error, message):
        login = Login()
        gus = GUS()
        login_result = login.login()
        if 'cookies' in case:
            loading_job_info = gus.get_graph_loading_job_info(cookies, graph_name)
            loading_job_info.assert_api_no_authorized()
        else:
            loading_job_info = gus.get_graph_loading_job_info(login_result.get_cookies(), graph_name)
            loading_job_info.assert_result(status_code, error, message)


    @allure.title("Put loading job info")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Change loading job info and save it \n"
        "Description: Try to change loading job StatementsStyle and check if can save it successfully \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_put_loading_job_info, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiloadingjobinfo
    @pytest.mark.run(order=1)
    def test_put_loading_job_info(self, case, graph_name):
        login = Login()
        gus = GUS()
        login_res = login.login()

        # get loading job info
        origin_loading_job_info = gus.get_graph_loading_job_info(login_res.get_cookies(), graph_name).results

        # change loading job info
        loading_job_data = origin_loading_job_info
        loading_job_data[0]["loadingStatementsStyle"] = []

        # change loading job and save changes
        res = gus.put_graph_loading_job_info(login_res.get_cookies(), graph_name, loading_job_data)
        res.assert_no_error()

        # check if loading job changed successfully
        loading_job_res = gus.get_graph_loading_job_info(login_res.get_cookies(), graph_name)
        assert loading_job_data[0]["loadingStatementsStyle"] == []

        # set loading job info back
        res = gus.put_graph_loading_job_info(login_res.get_cookies(), graph_name, origin_loading_job_info)
        res.assert_no_error()


    @allure.title("Put loading job info")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Change graph loading job with invalid payload \n"
        "Description: Try to change graph loading job with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_put_loading_job_info_invalid_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiloadingjobinfo
    @pytest.mark.run(order=1)
    def test_put_loading_job_info_invalid(self, 
                                        case, 
                                        graph_name, 
                                        cookies, 
                                        payload, 
                                        status_code,
                                        error,
                                        message):
        login = Login()
        gus = GUS()
        login_res = login.login()

        # get loading job info
        origin_loading_job_info = gus.get_graph_loading_job_info(login_res.get_cookies(), graph_name).results

        if 'cookies' in case:
            res = gus.put_graph_loading_job_info(cookies, graph_name, origin_loading_job_info)
            res.assert_api_no_authorized()
        elif 'payload' in case:
            res = gus.put_graph_loading_job_info(login_res.get_cookies(), graph_name, payload)
            res.assert_result(status_code, error, message)
        else:
            res = gus.put_graph_loading_job_info(login_res.get_cookies(), graph_name, origin_loading_job_info)
            res.assert_result(status_code, error, message)


    @allure.title("Delete loading job info")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can delete loading job info successfully \n"
        "Description: Try to delete loading job info and set it back \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiloadingjobinfo
    @pytest.mark.run(order=1)
    def test_delete_loading_job_info(self, graph_name="MyGraph"):
        login = Login()
        gus = GUS()
        login_res = login.login()

        # get loading job info
        origin_loading_job_info = gus.get_graph_loading_job_info(login_res.get_cookies(), graph_name).results

        # change loading job info
        delete_res = gus.delete_graph_loading_job_info(login_res.get_cookies(), graph_name)
        delete_res.assert_no_error()

        delete_check = gus.get_graph_loading_job_info(login_res.get_cookies(), graph_name)
        delete_check.assert_result(404, True, "Loading job info for graph 'MyGraph' cannot be found.")

        # set loading job back
        res = gus.put_graph_loading_job_info(login_res.get_cookies(), graph_name, origin_loading_job_info)
        res.assert_no_error()

    @allure.title("Delete loading job info")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can delete loading job info with invalid payload \n"
        "Description: Try to delete loading job info with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_delete_loading_job_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiloadingjobinfo
    @pytest.mark.run(order=1)
    def test_delete_loading_job_info_invalid(self, case, graph_name, cookies, status_code, error, message):
        login = Login()
        gus = GUS()
        login_res = login.login()

        if 'cookies' in case:
            res = gus.delete_graph_loading_job_info(cookies, graph_name)
            res.assert_api_no_authorized()
        else:
            res = gus.delete_graph_loading_job_info(login_res.get_cookies(), graph_name)
            res.assert_result(status_code, error, message)