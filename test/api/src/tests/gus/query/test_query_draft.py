import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestQueriesDraft():

    gsql_queries_data = data.get_yaml_data("gus/queries_data.yml")
    delete_query_draft_invalid = gsql_queries_data.get("delete_query_draft_invalid")
    delete_all_query_draft_invalid = gsql_queries_data.get("delete_all_query_draft_invalid")
    post_add_query_draft_invalid = gsql_queries_data.get("post_add_query_draft_invalid")
    put_update_query_draft_invalid = gsql_queries_data.get("put_update_query_draft_invalid")
    post_install_query_invalid = gsql_queries_data.get("post_install_query_invalid")
    post_install_query_valid = gsql_queries_data.get("post_install_query_valid")
    delete_query_draft_invalid_list = []
    delete_all_query_draft_invalid_list = []
    post_add_query_draft_invalid_list = []
    put_update_query_draft_invalid_list = []
    post_install_query_valid_list = []
    post_install_query_invalid_list = []
    for case in delete_all_query_draft_invalid:
        delete_all_query_draft_invalid_list.append(
            (
                case,
                delete_all_query_draft_invalid.get(case).get("graph_name"),
                delete_all_query_draft_invalid.get(case).get("draft"),
                delete_all_query_draft_invalid.get(case).get("params"),
                delete_all_query_draft_invalid.get(case).get("status_code"),
                delete_all_query_draft_invalid.get(case).get("error"),
                delete_all_query_draft_invalid.get(case).get("message")
            )
        )
    for case in delete_query_draft_invalid:
        delete_query_draft_invalid_list.append(
            (
                case,
                delete_query_draft_invalid.get(case).get("graph_name"),
                delete_query_draft_invalid.get(case).get("draft"),
                delete_query_draft_invalid.get(case).get("params"),
                delete_query_draft_invalid.get(case).get("status_code"),
                delete_query_draft_invalid.get(case).get("error"),
                delete_query_draft_invalid.get(case).get("message")
            )
        )

    for case in post_add_query_draft_invalid:
        post_add_query_draft_invalid_list.append(
            (
                case,
                post_add_query_draft_invalid.get(case).get("graph_name"),
                post_add_query_draft_invalid.get(case).get("query_name"),
                post_add_query_draft_invalid.get(case).get("params"),
                post_add_query_draft_invalid.get(case).get("json_data"),
                post_add_query_draft_invalid.get(case).get("status_code"),
                post_add_query_draft_invalid.get(case).get("error"),
                post_add_query_draft_invalid.get(case).get("message")
            )
        )
    for case in put_update_query_draft_invalid:
        put_update_query_draft_invalid_list.append(
            (
                case,
                put_update_query_draft_invalid.get(case).get("graph_name"),
                put_update_query_draft_invalid.get(case).get("query_name"),
                put_update_query_draft_invalid.get(case).get("params"),
                put_update_query_draft_invalid.get(case).get("json_data"),
                put_update_query_draft_invalid.get(case).get("status_code"),
                put_update_query_draft_invalid.get(case).get("error"),
                put_update_query_draft_invalid.get(case).get("message")
            )
        )
    for case in post_install_query_valid:
        post_install_query_valid_list.append(
            (
                case,
                post_install_query_valid.get(case).get("graph_name"),
                post_install_query_valid.get(case).get("params"),
                post_install_query_valid.get(case).get("status_code"),
                post_install_query_valid.get(case).get("error"),
                post_install_query_valid.get(case).get("message")
            )
        )
    for case in post_install_query_invalid:
        post_install_query_invalid_list.append(
            (
                case,
                post_install_query_invalid.get(case).get("graph_name"),
                post_install_query_invalid.get(case).get("params"),
                post_install_query_invalid.get(case).get("status_code"),
                post_install_query_invalid.get(case).get("error"),
                post_install_query_invalid.get(case).get("message")
            )
        )
    @allure.title("delete all query draft with params")
    @allure.description(
        "TestType: nagetive \n"
        "Target: Check we can delete all query draft with params \n"
        "Description: delete all query draft with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(delete_all_query_draft_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=210)
    def test_delete_all_query_draft_with_params(self, case, graph_name, draft, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.delete_all_query_drafts("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.delete_all_query_drafts(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("delete query draft with params")
    @allure.description(
        "TestType: nagetive \n"
        "Target: Check we can delete query draft with params \n"
        "Description: delete query draft with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(delete_query_draft_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=200)
    def test_delete_query_draft_with_params(self, case, graph_name, draft, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.delete_query_draft_with_para("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, draft, "")
        else:
            result = gus.delete_query_draft_with_para(cookie, graph_name, draft, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("post add query draft with params")
    @allure.description(
        "TestType: nagetive \n"
        "Target: Check we can add query draft with params \n"
        "Description: add query draft with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(post_add_query_draft_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=111)
    def test_post_add_query_draft_with_params(self, case, graph_name, query_name, params, json_data, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()
        gus.delete_query_draft(cookie, graph_name, query_name)

        if params == "invalid_cookies":
            result = gus.post_add_query_draft_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, query_name, "", json_data)
        else:
            result = gus.post_add_query_draft_info(cookie, graph_name, query_name, params, json_data)
        # check error message
        result.assert_result(status_code, error, message)


    @allure.title("update query draft cases with params")
    @allure.description(
        "TestType: nagetive \n"
        "Target: Check we can update query draft with params \n"
        "Description: update query draft with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(put_update_query_draft_invalid_list, skip_on_empty=True)
    @pytest.mark.run(order=11)
    def test_put_update_query_draft(self, case, graph_name, query_name, params, json_data, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()
        gus.delete_query_draft(cookie, graph_name, query_name)
        if params == "invalid_cookies":
            result = gus.put_update_query_draft_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, query_name, "", json_data)
        else:
            result = gus.put_update_query_draft_info(cookie, graph_name, query_name, params, json_data)
        # check error message
        result.assert_result(status_code, error, message)


    @allure.title("install query draft with params")
    @allure.description(
        "TestType: nagetive \n"
        "Target: Check we can't install query draft with invalid params \n"
        "Description: install query draft with invalid params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(post_install_query_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=1)
    def test_install_query_draft_with_invalid_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.install_query_draft("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.install_query_draft(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("install query draft with valid params")
    @allure.description(
        "TestType: positive \n"
        "Target: Check we can install query draft with valid params \n"
        "Description: install query draft with valid params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(post_install_query_valid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.preexec
    @pytest.mark.apiqueries
    @pytest.mark.run(order=10)
    def test_install_query_draft_with_valid_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.install_query_draft("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.install_query_draft(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)