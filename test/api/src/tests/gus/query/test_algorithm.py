import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestAlgorithm():

    gsql_algorithm_data = data.get_yaml_data("gus/get_gsql_algorithm_data.yml")
    algorithm_test_data = gsql_algorithm_data.get("algorithms_invalid_cases")
    test_data = []
    for i in algorithm_test_data:
        test_data.append(
            (
                algorithm_test_data.get(i).get("cookie"),
                algorithm_test_data.get(i).get("status_code"),
                algorithm_test_data.get(i).get("error"),
                algorithm_test_data.get(i).get("message")
            )
        )

    install_algorithm_test_data = gsql_algorithm_data.get("install_algorithm_cases")
    install_data = []
    for i in install_algorithm_test_data:
        install_data.append(
            (
                install_algorithm_test_data.get(i).get("graph_name"),
                install_algorithm_test_data.get(i).get("queries_name")
            )
        )

    invalid_install_algorithm_test_data = gsql_algorithm_data.get("install_algorithm_invalid_payload")
    invalid_install_data = []
    for i in invalid_install_algorithm_test_data:
        invalid_install_data.append((
            invalid_install_algorithm_test_data.get(i).get("graph_name"),
            invalid_install_algorithm_test_data.get(i).get("algrotithm_name"),
            invalid_install_algorithm_test_data.get(i).get("is_login"),
            invalid_install_algorithm_test_data.get(i).get("status_code"),
            invalid_install_algorithm_test_data.get(i).get("error"),
            invalid_install_algorithm_test_data.get(i).get("message")
        ))


    @allure.title("Get all algorithm info")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Get all algorithm info \n"
        "Description: login tools with default user \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apialgorithm1
    @pytest.mark.run(order=1)
    def test_get_algorithm_info(self):
        login = Login()
        gus = GUS()
        login_result = login.login()
        result = gus.get_algorithm_info(login_result.get_cookies())
        result.assert_result(200, False, "")

    @allure.title("Failed to get all algorithm info")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Failed to get all algorithm info \n"
        "Description: Try to get algorithm info without cookies/error cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apialgorithm
    @pytest.mark.run(order=1)
    def test_get_algorithm_info_with_invalid_cookie(self, cookie, status_code, error, message):
        gus = GUS()
        logger.info(f"cookie is {cookie}")
        result = gus.get_algorithm_info(cookie)
        result.assert_result(status_code, error, message) 

    @allure.title("Install algorithm")
    @allure.description(
        "TestType: positive \n" 
        "Target: Install algorithm successfully \n"
        "Description: Try to install algorithm  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(install_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.preexec
    @pytest.mark.apialgorithm
    @pytest.mark.run(order=1)
    def test_install_algorithm(self, graph_name, queries_name):
        login = Login()
        gus = GUS()
        # get cookie from login response
        cookie = login.login().get_cookies()
        algorithms = []

        # delete algorithm if it was installed
        for query in queries_name:
            query_info = gus.get_queries_info(cookie, graph_name, query["query_name"])
            if query_info.results:
                logger.info("The query {} was already installed".format(query["query_name"]))
                deleted=gus.delete_query(cookie, graph_name, query["query_name"])
                deleted.assert_result(200, False, "")
                logger.info("The query {} was deleted successfully".format(query["query_name"]))
            algorithms.append(query["algrotithm_name"])

        # install algorithm
        algorithms_param = "&".join(algorithms)
        logger.info("Start to install query {}".format(algorithms_param))
        result = gus.install_algorithm_query(cookie, graph_name, algorithms_param)
        result.assert_result(200, False, "") 
        assert result.results["failed"] == [], f"The query {algorithms_param} has beem installed yet"

    @allure.title("Install algorithm -Negative")
    @allure.description(
        "TestType: negative \n" 
        "Target: Install algorithm with invalid payload \n"
        "Description: Try to install algorithm with invalid paypload and check error message \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(invalid_install_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apialgorithm1
    @pytest.mark.run(order=1)
    def test_install_algorithm_with_invalid_payload(self, graph_name, algrotithm_name, is_login, status_code, error, message):
        # get cookies
        login = Login()
        cookie = login.login().get_cookies()
        gus = GUS()
        if not is_login:
            cookie = ""
        result = gus.install_algorithm_query(cookie, graph_name, algrotithm_name)

        # check actual failed message
        if status_code==200:
            result.assert_result(status_code, error, "")
            assert message in str(result.results["failed"]), "The actual response is `{}`".format(result.results["failed"])
        else:
            result.assert_result(status_code, error, message)
