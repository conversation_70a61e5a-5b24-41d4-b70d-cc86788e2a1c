import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.operation.gsql_server import GSQLServer
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestQueries():

    gsql_queries_data = data.get_yaml_data("gus/queries_data.yml")
    queries_info_test_data = gsql_queries_data.get("get_queries_info_cases")
    invalid_queries_info_test_data = gsql_queries_data.get("get_queries_info_negative_cases")
    delete_query_test_data = gsql_queries_data.get("delete_query_cases")
    invalid_delete_query_test_data = gsql_queries_data.get("delete_query_negative_cases")
    valid_code_check = gsql_queries_data.get("post_code_check_valid")
    general_data = data.get_yaml_data("gus/loading_data.yml")
    invalid_cookie = general_data.get("invalid_cookie")
    query_data = []
    invalid_query_info_data = []
    delete_query_data = []
    invalid_delete_query_data = []
    valid_code_check_data = [] 
    test_invalid_cookie_data = []

    for i in queries_info_test_data:
        query_data.append(
            (
                queries_info_test_data.get(i).get("graph_name"),
                queries_info_test_data.get(i).get("query_name")
            )
        )
    
    for i in invalid_queries_info_test_data:
        invalid_query_info_data.append((
            invalid_queries_info_test_data.get(i).get("graph_name"),
            invalid_queries_info_test_data.get(i).get("query_name"),
            invalid_queries_info_test_data.get(i).get("is_login"),
            invalid_queries_info_test_data.get(i).get("status_code"),
            invalid_queries_info_test_data.get(i).get("error"),
            invalid_queries_info_test_data.get(i).get("message")
        ))

    for i in delete_query_test_data:
        delete_query_data.append((
            delete_query_test_data.get(i).get("graph_name"),
            delete_query_test_data.get(i).get("query")
        ))

    for i in invalid_delete_query_test_data:
        invalid_delete_query_data.append((
            invalid_delete_query_test_data.get(i).get("graph_name"),
            invalid_delete_query_test_data.get(i).get("query_name"),
            invalid_delete_query_test_data.get(i).get("is_login"),
            invalid_delete_query_test_data.get(i).get("status_code"),
            invalid_delete_query_test_data.get(i).get("error"),
            invalid_delete_query_test_data.get(i).get("message")
        ))

    for case in valid_code_check:
        valid_code_check_data.append((
            case,
            valid_code_check.get(case).get("graph_name"),
            valid_code_check.get(case).get("query"),
            valid_code_check.get(case).get("errors"),
            valid_code_check.get(case).get("warnings")
        ))

    for case in invalid_cookie:
        test_invalid_cookie_data.append((
            case,
            invalid_cookie.get(case).get("cookie")
        ))

    @allure.title("Get all queries info for specified graph")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Get all queries info \n"
        "Description: Get single query/all queries info \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(query_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=1)
    def test_get_queries_info(self, graph_name, query_name):
        login = Login()
        gus = GUS()
        cookie = login.login().get_cookies()
        result = gus.get_queries_info(cookie, graph_name, query_name)
        result.assert_result(200, False, "")

    @allure.title("Get all queries info - negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Get all queries info with invalid payload \n"
        "Description: Get single query/all queries info with invalid payload\n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(invalid_query_info_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=1)
    def test_get_queries_info_invalid_paylaod(self, graph_name, query_name, is_login, status_code, error, message):
        login = Login()
        cookie = login.login().get_cookies()
        gus = GUS()
        if not is_login:
            cookie = ""
        result = gus.get_queries_info(cookie, graph_name, query_name)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("Delete installed query")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Delete query with invalid payload \n"
        "Description: Check if the query exist and then delete the query \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(delete_query_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=100)
    def test_delete_queries(self, graph_name, query):
        login = Login()
        cookie = login.login().get_cookies()
        gus = GUS()
        # install algorithm query if the query doesn't exist in list
        query_info = gus.get_queries_info(cookie, graph_name, query["query_name"])
        if not query_info.results and query["algrotithm_name"]:
            gus.install_algorithm_query(cookie, graph_name, query["algrotithm_name"])
        result = gus.delete_query(cookie, graph_name,  query["query_name"])
        result.assert_result(200, False, "")

    
    @allure.title("Delete installed query - negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if installed query can be deleted successfully \n"
        "Description: Delete query with invalid cookie/graph/query \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(invalid_delete_query_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=19)
    def test_delete_queries_invalid_payload(self, graph_name, query_name, is_login, status_code, error, message):
        login = Login()
        cookie = login.login().get_cookies()
        gus = GUS()
        if not is_login:
            cookie = ""
        result = gus.delete_query(cookie, graph_name,  query_name)
        result.assert_result(status_code, error, message)   
        
    @allure.title("Code check for queries")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can run code check for each query \n"
        "Description: Run code check for queries \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(valid_code_check_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_post_code_check(self, case, graph_name, query, errors, warnings):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.run_query_codecheck(cookies=cookie, graph_name=graph_name, query=query)
        check_res.assert_no_error()
        assert errors in str(check_res.results["errors"]), "The actual errors is {}".format(check_res.results["errors"])
        assert warnings in str(check_res.results["warnings"]), "The actual warnings is {}".format(check_res.results["warnings"])

    @allure.title("Code check for queries with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can run code check with invalid cookies \n"
        "Description: Run code check with invalid cookies and check result \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_post_code_check_invalid(self, case, cookie):
        #check the existing local files
        gsql = GSQLServer()
        graph_name = "MyGraph"
        query = '{"code":"CREATE QUERY query_for_output(/* Parameters here */) FOR GRAPH MyGraph {\n  FILE f (\"/home/<USER>/tigergraph/data/gsql_output/query_output.csv\");\n  f.println(\"Vertex_ID\", \"query_output_test\");\n}"}'
        check_res= gsql.run_query_codecheck(cookies=cookie, graph_name=graph_name, query=query)
        check_res.assert_error()
        check_res.assert_api_no_authorized()

    @allure.title("Check gsql output file policy")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if the output file match file policy \n"
        "Description: Run file check for gsql output file \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_file_policy_check(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        path = "/home/<USER>/tigergraph/data/gsql_output/query_output.csv"
        check_res= gsql.check_gsql_output_file(path, cookies=cookie)
        check_res.assert_no_error()

    @allure.title("Check gsql output file policy with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can check output file policy with invalid cookies \n"
        "Description: Check gsql output file policy with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_post_code_check_invalid(self, case, cookie):
        #check the existing local files
        gsql = GSQLServer()
        path = "/home/<USER>/tigergraph/data/gsql_output/query_output.csv"
        check_res= gsql.check_gsql_output_file(path, cookies=cookie)
        check_res.assert_error()
        check_res.assert_api_no_authorized()
