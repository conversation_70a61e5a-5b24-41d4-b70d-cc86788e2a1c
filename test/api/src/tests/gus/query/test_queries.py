import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestQueries():

    gsql_queries_data = data.get_yaml_data("gus/queries_data.yml")
    queries_info_test_data = gsql_queries_data.get("get_queries_info_cases")
    invalid_queries_info_test_data = gsql_queries_data.get("get_queries_info_negative_cases")
    delete_query_test_data = gsql_queries_data.get("delete_query_cases")
    invalid_delete_query_test_data = gsql_queries_data.get("delete_query_negative_cases")
    query_data = []
    invalid_query_info_data = []
    delete_query_data = []
    invalid_delete_query_data = []
    for i in queries_info_test_data:
        query_data.append(
            (
                queries_info_test_data.get(i).get("graph_name"),
                queries_info_test_data.get(i).get("query_name")
            )
        )
    
    for i in invalid_queries_info_test_data:
        invalid_query_info_data.append((
            invalid_queries_info_test_data.get(i).get("graph_name"),
            invalid_queries_info_test_data.get(i).get("query_name"),
            invalid_queries_info_test_data.get(i).get("is_login"),
            invalid_queries_info_test_data.get(i).get("status_code"),
            invalid_queries_info_test_data.get(i).get("error"),
            invalid_queries_info_test_data.get(i).get("message")
        ))

    for i in delete_query_test_data:
        delete_query_data.append((
            delete_query_test_data.get(i).get("graph_name"),
            delete_query_test_data.get(i).get("query")
        ))

    for i in invalid_delete_query_test_data:
        invalid_delete_query_data.append((
            invalid_delete_query_test_data.get(i).get("graph_name"),
            invalid_delete_query_test_data.get(i).get("query_name"),
            invalid_delete_query_test_data.get(i).get("is_login"),
            invalid_delete_query_test_data.get(i).get("status_code"),
            invalid_delete_query_test_data.get(i).get("error"),
            invalid_delete_query_test_data.get(i).get("message")
        ))

    @allure.title("Get all queries info for specified graph")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Get all queries info \n"
        "Description: Get single query/all queries info \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(query_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=1)
    def test_get_queries_info(self, graph_name, query_name):
        login = Login()
        gus = GUS()
        cookie = login.login().get_cookies()
        result = gus.get_queries_info(cookie, graph_name, query_name)
        result.assert_result(200, False, "")

    @allure.title("Get all queries info - negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Get all queries info with invalid payload \n"
        "Description: Get single query/all queries info with invalid payload\n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(invalid_query_info_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=1)
    def test_get_queries_info_invalid_paylaod(self, graph_name, query_name, is_login, status_code, error, message):
        login = Login()
        cookie = login.login().get_cookies()
        gus = GUS()
        if not is_login:
            cookie = ""
        result = gus.get_queries_info(cookie, graph_name, query_name)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("Delete installed query")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Delete query with invalid payload \n"
        "Description: Check if the query exist and then delete the query \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(delete_query_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=100)
    def test_delete_queries(self, graph_name, query):
        login = Login()
        cookie = login.login().get_cookies()
        gus = GUS()
        # install algorithm query if the query doesn't exist in list
        query_info = gus.get_queries_info(cookie, graph_name, query["query_name"])
        if not query_info.results and query["algrotithm_name"]:
            gus.install_algorithm_query(cookie, graph_name, query["algrotithm_name"])
        result = gus.delete_query(cookie, graph_name,  query["query_name"])
        result.assert_result(200, False, "")

    
    @allure.title("Delete installed query - negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if installed query can be deleted successfully \n"
        "Description: Delete query with invalid cookie/graph/query \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(invalid_delete_query_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=19)
    def test_delete_queries_invalid_payload(self, graph_name, query_name, is_login, status_code, error, message):
        login = Login()
        cookie = login.login().get_cookies()
        gus = GUS()
        if not is_login:
            cookie = ""
        result = gus.delete_query(cookie, graph_name,  query_name)
        result.assert_result(status_code, error, message)   
        
