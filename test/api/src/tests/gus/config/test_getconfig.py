import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.api.tools_api import tools
from src.operation.gus import GUS
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure


class TestConfig(APIBaseCase):

    configs_data = data.get_yaml_data("gus/apiconfig_data.yml")
    apiconfig_data_valid = configs_data.get("valid_configs")
    apiconfig_data_invalid = configs_data.get("invalid_configs")
    test_data_valid = []
    test_data_invalid = []
    for case in apiconfig_data_valid:
        test_data_valid.append(
            (
                case,
                apiconfig_data_valid.get(case).get("key")
            )
        )

    for case in apiconfig_data_invalid:
        test_data_invalid.append(
            (
                case,
                apiconfig_data_invalid.get(case).get("key"),
                apiconfig_data_invalid.get(case).get("status_code"),
                apiconfig_data_invalid.get(case).get("error"),
                apiconfig_data_invalid.get(case).get("message")
            )
        )



    @allure.title("get api configs with valid key")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the configs can be retrive with valid key \n"
        "Description: Get api configs \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-08 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.getconfig
    @pytest.mark.run(order=1)
    def test_getconfig_valid(self, case, key):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the api config can be retrive with valid key
        gus = GUS()
        config_res = gus.get_configs(cookie, key)
        config_res.assert_no_error()
        config_res.assert_keys(key)

    @allure.title("get api configs with valid key but without cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the configs can't be retrive with valid key if no cookie \n"
        "Description: Unable to get api configs if without cookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-08 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.getconfig
    @pytest.mark.run(order=1)
    def test_getconfig_without_cookie(self, case, key):
        cookie = ""
        #check the api config can't be retrive without cookie
        gus = GUS()
        config_res = gus.get_configs(cookie, key)
        config_res.assert_error()
        config_res.assert_api_no_authorized()

    @allure.title("get api configs with valid key but with expired cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the configs can't be retrive with valid key if use expired cookie \n"
        "Description: Unable to get api configs if use expired cookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-08 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.getconfig
    @pytest.mark.run(order=1)
    def test_getconfig_with_expired_cookie(self, case, key):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        assert cookie != "", "the cookie is null"

         #logout with the cookie
        logout_res = login.logout(cookie)
        logout_res.assert_logout_success()

        #check the api config can't be retrive without cookie
        gus = GUS()
        config_res = gus.get_configs(cookie, key)
        config_res.assert_error()
        config_res.assert_api_no_authorized()

    @allure.title("get api configs with invalid key")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check system will return error if get configs with invalid key \n"
        "Description: Get api configs with invalid key \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-08 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.getconfig
    @pytest.mark.run(order=1)
    def test_getconfig_invalid_key(self, case, key, status_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the api config can be retrive with valid key
        gus = GUS()
        config_res = gus.get_configs(cookie, key)
        config_res.assert_result(status_code, error, message)



