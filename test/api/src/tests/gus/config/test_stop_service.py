import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.api.tools_api import tools
from src.operation.gus import GUS
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure


class TestConfig(APIBaseCase):

    configs_data = data.get_yaml_data("gus/apiconfig_data.yml")
    apiconfig_data_valid = configs_data.get("valid_services")
    apiconfig_data_invalid = configs_data.get("invalid_stop_services")
    test_data_valid = []
    test_data_invalid = []
    for case in apiconfig_data_valid:
        test_data_valid.append(
            (
                case,
                apiconfig_data_valid.get(case).get("key")
            )
        )

    for case in apiconfig_data_invalid:
        test_data_invalid.append(
            (
                case,
                apiconfig_data_invalid.get(case).get("key"),
                apiconfig_data_invalid.get(case).get("status_code"),
                apiconfig_data_invalid.get(case).get("error"),
                apiconfig_data_invalid.get(case).get("message")
            )
        )



    @allure.title("successfully stop the services")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the services can be stopped via API \n"
        "Description: Stop the services as valid \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.dependency10
    @pytest.mark.postconfig
    @pytest.mark.run(order=1)
    def test_stop_service_valid(self, case, data):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

         #check the services can be stopped successful
        gus = GUS()
        config_res = gus.stop_service(cookie, data)
        config_res.assert_result(message="Successfully stopped services.")
    

    @allure.title("stop the services but without cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check system should blocked stop service if no cookie \n"
        "Description: Unable to stop services if without cookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.dependency10
    @pytest.mark.postconfig
    @pytest.mark.run(order=1)
    def test_stopservice_without_cookie(self, case, data):
        cookie = ""
        #check the api config can't be stop service without cookie
        gus = GUS()
        config_res = gus.stop_service(cookie, data)
        config_res.assert_error()
        config_res.assert_api_no_authorized()


    @allure.title("stop the services with invalid key/value")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check system will return error if stop the services with invalid key \n"
        "Description: Stop the services with invalid key \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.dependency10
    @pytest.mark.postconfig2
    @pytest.mark.run(order=1)
    def test_stopservice_invalid_key(self, case, data, status_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the service unable to stop with invalid key
        gus = GUS()
        config_res = gus.stop_service(cookie, data)
        config_res.assert_result(status_code, error, message)



