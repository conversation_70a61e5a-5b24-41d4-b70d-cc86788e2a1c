import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
from src.common.api_basecase import APIBaseCase
import allure


class TestLocalGraphStyle(APIBaseCase):
    graph_style_data = data.get_yaml_data("gus/apigraph_style_data.yml")
    get_local_graph_style_valid = graph_style_data.get("valid_get_local_graph_style")
    get_local_graph_style_invalid = graph_style_data.get("invalid_get_local_graph_style")
    put_local_graph_style_valid = graph_style_data.get("valid_put_local_graph_style")
    put_local_graph_style_invalid = graph_style_data.get("invalid_put_local_graph_style")
    delete_lcoal_graph_style_valid = graph_style_data.get("valid_delete_local_graph_style")
    delete_lcoal_graph_style_invalid = graph_style_data.get("invalid_delete_local_graph_style")
    test_local_gs_valid = []
    test_local_gs_invalid = []
    test_put_local_gs_valid = []
    test_put_local_gs_invalid = []
    test_delete_local_gs_valid = []
    test_delete_local_gs_invalid = []

    for case in get_local_graph_style_valid:
        test_local_gs_valid.append((
            case,
            get_local_graph_style_valid.get(case).get("graphName")
        ))

    for case in get_local_graph_style_invalid:
        test_local_gs_invalid.append((
            case,
            get_local_graph_style_invalid.get(case).get("graphName"),
            get_local_graph_style_invalid.get(case).get("cookies"),
            get_local_graph_style_invalid.get(case).get("status_code"),
            get_local_graph_style_invalid.get(case).get("error"),
            get_local_graph_style_invalid.get(case).get("message")
        ))

    for case in put_local_graph_style_valid:
        test_put_local_gs_valid.append((
            case,
            put_local_graph_style_valid.get(case).get("color_code"),
            put_local_graph_style_valid.get(case).get("graph_name")
        ))

    for case in put_local_graph_style_invalid:
        test_put_local_gs_invalid.append((
            case,
            put_local_graph_style_invalid.get(case).get("graph_name"),
            put_local_graph_style_invalid.get(case).get("style_name"),
            put_local_graph_style_invalid.get(case).get("color_code"),
            put_local_graph_style_invalid.get(case).get("cookies"),
            put_local_graph_style_invalid.get(case).get("status_code"),
            put_local_graph_style_invalid.get(case).get("error"),
            put_local_graph_style_invalid.get(case).get("message")
        ))

    for case in delete_lcoal_graph_style_valid:
        test_delete_local_gs_valid.append((
            case,
            delete_lcoal_graph_style_valid.get(case).get("graph_name")
        ))

    for case in delete_lcoal_graph_style_invalid:
        test_delete_local_gs_invalid.append((
            case,
            delete_lcoal_graph_style_invalid.get(case).get("graph_name"),
            delete_lcoal_graph_style_invalid.get(case).get("cookies"),
            delete_lcoal_graph_style_invalid.get(case).get("status_code"),
            delete_lcoal_graph_style_invalid.get(case).get("error"),
            delete_lcoal_graph_style_invalid.get(case).get("message")
        ))

    @allure.title("Get local graph style")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Get local graph style \n"
        "Description: Try to get local graph style \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_local_gs_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_get_local_graph_style(self, case, graphName):
        login = Login()
        gus = GUS()
        login_result = login.login()
        result = gus.get_graph_style(login_result.get_cookies(), graphName, 'local')
        result.assert_no_error()
        assert 'fillColor' in str(result.results)
        assert 'WEATHER_STAMP' in str(result.results)


    @allure.title("Get local graph style")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Get local graph style with invalid payload\n"
        "Description: Try to get local graph style with invalid graph name or cookies\n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_local_gs_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle2
    @pytest.mark.run(order=1)
    def test_get_local_graph_style_invalid_payload(
        self, 
        case,
        graphName, 
        cookies,
        status_code,
        error,
        message
        ):
        login = Login()
        gus = GUS()
        login_result = login.login()
        scope='local'
        if 'cookies' in case :
            result = gus.get_graph_style(cookies, graphName, scope)
            result.assert_api_no_authorized()
        else:
            result = gus.get_graph_style(login_result.get_cookies(), graphName, scope)
            result.assert_result(status_code, error, message)

    @allure.title("Save local graph style changes")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can save local graph style changes successfully\n"
        "Description: Change local vertex fillcolor and save changes \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_put_local_gs_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_put_local_graph_style(self, case, color_code, graph_name):
        login = Login()
        gus = GUS()
        login_result = login.login()
        # get local graph style info
        scope='local'
        origin_global_gs_info = gus.get_graph_style(login_result.get_cookies(), graph_name, scope)
        legacy_color=origin_global_gs_info.results["vertexStyles"]["City"]["fillColor"]
        origin_global_gs_info.results["vertexStyles"]["City"]["fillColor"] = color_code

        # change local graph style
        graph_style_changed = gus.put_graph_style(login_result.get_cookies(), origin_global_gs_info.results, graph_name, scope)
        graph_style_changed.assert_no_error()

        # check local graph style
        new_global_gs_info = gus.get_graph_style(login_result.get_cookies(), graph_name, scope)
        assert color_code in new_global_gs_info.results["vertexStyles"]["City"]["fillColor"]

        # change color back
        new_global_gs_info.results["vertexStyles"]["City"]["fillColor"]=legacy_color
        graph_style_changed = gus.put_graph_style(login_result.get_cookies(), new_global_gs_info.results, graph_name, scope)
        graph_style_changed.assert_no_error()

    @allure.title("Save local graph style changes")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can save local graph style changes with invalid payload\n"
        "Description: Change local vertex fillcolor/location with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand( test_put_local_gs_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_put_lcoal_graph_style_invalid(self, 
    case, 
    graph_name,
    style_name,
    color_code,
    cookies,
    status_code,
    error,
    message
    ):
        login = Login()
        gus = GUS()
        login_result = login.login()
        # get local graph style info
        scope='local'
        origin_global_gs_info = gus.get_graph_style(login_result.get_cookies(), "MyMultiEdge", scope)
        if 'edge' in case:
            styles_type = 'edgeStyles'
        else:
            styles_type = 'vertexStyles'

        origin_global_gs_info.results[styles_type][style_name]["fillColor"] = color_code

        if 'cookies' in case:
            graph_style_changed = gus.put_graph_style(cookies, origin_global_gs_info.results, graph_name, scope)
            graph_style_changed.assert_api_no_authorized()
        else:
            # change global graph style
            graph_style_changed = gus.put_graph_style(login_result.get_cookies(), origin_global_gs_info.results, graph_name, scope)
            graph_style_changed.assert_result(status_code, error, message)

    @allure.title("Delete local graph style")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can delete local graph style successfully\n"
        "Description: Delete all local style and check default local style, then set all style back \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-17 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_delete_local_gs_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=100)
    def test_delete_local_graph_style(self, case, graph_name):
        login = Login()
        gus = GUS()
        login_result = login.login()
        # get local graph style
        scope = 'local'
        origin_global_graph_style = gus.get_graph_style(login_result.get_cookies(), graph_name, scope).results
        # delete global graph style info
        result = gus.delete_graph_style(login_result.get_cookies(), graph_name, scope)
        result.assert_no_error()

        # check if deleting is successfully
        current_global_graph_style = gus.get_graph_style(login_result.get_cookies(), graph_name, scope)
        current_global_graph_style.assert_result(404, True, "Local graph style for graph 'MyMultiEdge' cannot be found.")

        # set global graph style back
        gus.put_graph_style(login_result.get_cookies(), origin_global_graph_style, graph_name, scope)
        result = gus.get_graph_style(login_result.get_cookies(), graph_name, scope)
        result.assert_no_error()


    @allure.title("Delete local graph style")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can delete local graph style with invalid payload\n"
        "Description: Delete all local style with invalid payload and check default global style, then set all global style back \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_delete_local_gs_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_delete_local_graph_style_invalid(self, case, graph_name, cookies, status_code, error, message):
        login = Login()
        gus = GUS()
        login_result = login.login()
        scope='local'
        if 'cookies' in case: 
            result = gus.delete_graph_style(cookies, graph_name, scope)
            result.assert_api_no_authorized()
        else:
            result = gus.delete_graph_style(login_result.get_cookies(), graph_name, scope)
            result.assert_result(status_code, error, message)
    