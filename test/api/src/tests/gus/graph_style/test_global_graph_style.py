import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
from src.common.api_basecase import APIBaseCase
import allure


class TestGlobalGraphStyle(APIBaseCase):
    graph_style_data = data.get_yaml_data("gus/apigraph_style_data.yml")
    get_graph_style_invalid = graph_style_data.get("invalid_cookies_get_graph_style")
    put_graph_style = graph_style_data.get("valid_put_global_graph_style")
    put_graph_style_invalid = graph_style_data.get("invalid_put_global_graph_style")
    delete_graph_style_invalid = graph_style_data.get("invalid_delete_global_graph_style")
    test_get_global_gs_invalid = []
    test_put_global_gs_valid = []
    test_put_global_gs_invalid = []
    test_delete_global_gs_invalid = []
    for case in get_graph_style_invalid:
        test_get_global_gs_invalid.append((
            case,
            get_graph_style_invalid.get(case).get("cookies")
        ))
    
    for case in put_graph_style:
        test_put_global_gs_valid.append((
            case,
            put_graph_style.get(case).get("color_code")
        ))

    for case in put_graph_style_invalid:
        test_put_global_gs_invalid.append((
            case,
            put_graph_style_invalid.get(case).get("style_name"),
            put_graph_style_invalid.get(case).get("color_code"),
            put_graph_style_invalid.get(case).get("cookies"),
            put_graph_style_invalid.get(case).get("status_code"),
            put_graph_style_invalid.get(case).get("error"),
            put_graph_style_invalid.get(case).get("message")
        ))

    for case in delete_graph_style_invalid:
        test_delete_global_gs_invalid.append((
            case,
            delete_graph_style_invalid.get(case).get("cookies")
        ))
    

    @allure.title("Get global graph style")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Get global graph style \n"
        "Description: Try to get global graph style \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_get_global_graph_style(self):
        login = Login()
        gus = GUS()
        login_result = login.login()
        result = gus.get_graph_style(login_result.get_cookies())
        result.assert_no_error()
        assert 'fillColor' in str(result.results)
        assert 'WEATHER_STAMP' in str(result.results)

    @allure.title("Get global graph style")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Failed to get global graph style with invalid cookies \n"
        "Description: Failed to get global graph style with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_get_global_gs_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_get_global_graph_style_invalid_cookies(self, case, cookies):
        gus = GUS()
        result = gus.get_graph_style(cookies)
        result.assert_api_no_authorized()

    @allure.title("Save global graph style changes")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can save global graph style changes successfully\n"
        "Description: Change global vertex fillcolor and save changes \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_put_global_gs_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_put_global_graph_style(self, case, color_code):
        login = Login()
        gus = GUS()
        login_result = login.login()
        # get global graph style info
        origin_global_gs_info = gus.get_graph_style(login_result.get_cookies())
        legacy_color=origin_global_gs_info.results["vertexStyles"]["City"]["fillColor"]
        origin_global_gs_info.results["vertexStyles"]["City"]["fillColor"] = color_code

        # change global graph style
        graph_style_changed = gus.put_graph_style(login_result.get_cookies(), origin_global_gs_info.results)
        graph_style_changed.assert_no_error()

        # check global graph style
        new_global_gs_info = gus.get_graph_style(login_result.get_cookies())
        assert color_code in new_global_gs_info.results["vertexStyles"]["City"]["fillColor"]

        # change color back
        new_global_gs_info.results["vertexStyles"]["City"]["fillColor"]=legacy_color
        graph_style_changed = gus.put_graph_style(login_result.get_cookies(), new_global_gs_info.results)
        graph_style_changed.assert_no_error()

    @allure.title("Save global graph style changes")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can save global graph style changes with invalid payload\n"
        "Description: Change global vertex fillcolor/location with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_put_global_gs_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_put_global_graph_style_invalid(self, 
    case, 
    style_name,
    color_code,
    cookies,
    status_code,
    error,
    message
    ):
        login = Login()
        gus = GUS()
        login_result = login.login()
        # get global graph style info
        origin_global_gs_info = gus.get_graph_style(login_result.get_cookies())
        if 'edge' in case:
            styles_type = 'edgeStyles'
        else:
            styles_type = 'vertexStyles'

        origin_global_gs_info.results[styles_type][style_name]["fillColor"] = color_code

        if 'cookies' in case:
            graph_style_changed = gus.put_graph_style(cookies, origin_global_gs_info.results)
            graph_style_changed.assert_api_no_authorized()
        else:
            # change global graph style
            graph_style_changed = gus.put_graph_style(login_result.get_cookies(), origin_global_gs_info.results)
            graph_style_changed.assert_result(status_code, error, message)


    @allure.title("Delete global graph style")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can delete global graph style successfully\n"
        "Description: Delete all global style and check default global style, then set all global style back \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.teardown
    @pytest.mark.run(order=1)
    def test_delete_global_graph_style(self):
        login = Login()
        gus = GUS()
        login_result = login.login()
        # get global graph style
        origin_global_graph_style = gus.get_graph_style(login_result.get_cookies()).results
        # delete global graph style info
        result = gus.delete_graph_style(login_result.get_cookies())
        result.assert_no_error()

        # check if deleting is successfully
        current_global_graph_style = gus.get_graph_style(login_result.get_cookies())
        current_global_graph_style.assert_result(404, True, 'Global graph style cannot be found.')

        # set global graph style back
        gus.put_graph_style(login_result.get_cookies(), origin_global_graph_style)
        result = gus.get_graph_style(login_result.get_cookies())
        result.assert_no_error()

    @allure.title("Delete global graph style")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can delete global graph style with invalid payload\n"
        "Description: Delete all global style with invalid payload and check default global style, then set all global style back \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-12 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2685"
    )
    @parameterized.expand(test_delete_global_gs_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apigraphstyle
    @pytest.mark.run(order=1)
    def test_delete_global_graph_style_invalid(self, case, cookies):
        login = Login()
        gus = GUS()
        login_result = login.login()
        if 'cookies' in case: 
            result = gus.delete_graph_style(cookies)
            result.assert_api_no_authorized()
        else:
            result = gus.delete_graph_style(login_result.get_cookies())