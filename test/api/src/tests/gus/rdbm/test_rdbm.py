import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.operation.gsql_server import GSQLServer
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestQueries():
    rdbm_data = data.get_yaml_data("gus/api_rdbm_data.yml")
    post_rdbm_data = rdbm_data.get("post_rdbm")
    post_rdbm_invalid_payload = rdbm_data.get("post_rdb_invalid_payload")
    post_rdbm_dump_invalid_payload = rdbm_data.get("post_rdbm_dump_invalid_payload")
    general_data = data.get_yaml_data("gus/loading_data.yml")
    invalid_cookie = general_data.get("invalid_cookie")

    post_rdbm_test_data = []
    test_invalid_cookie_data = []
    post_rdbm_invalid_payload_test_data = []
    post_rdbm_dump_invalid_payload_test_data = []

    for case in post_rdbm_data:
        post_rdbm_test_data.append((
            case,
            post_rdbm_data.get(case).get("type"),
            post_rdbm_data.get(case).get("server"),
            post_rdbm_data.get(case).get("port"),
            post_rdbm_data.get(case).get("database"),
            post_rdbm_data.get(case).get("payload"),
            post_rdbm_data.get(case).get("dump_payload")
        ))

    for case in invalid_cookie:
        test_invalid_cookie_data.append((
            case,
            invalid_cookie.get(case).get("cookie"),
            post_rdbm_data.get("mysql").get("type"),
            post_rdbm_data.get("mysql").get("server"),
            post_rdbm_data.get("mysql").get("port"),
            post_rdbm_data.get("mysql").get("database"),
            post_rdbm_data.get("mysql").get("payload")
        ))

    for case in post_rdbm_invalid_payload:
        post_rdbm_invalid_payload_test_data.append((
            case,
            post_rdbm_invalid_payload.get(case).get("type"),
            post_rdbm_invalid_payload.get(case).get("server"),
            post_rdbm_invalid_payload.get(case).get("port"),
            post_rdbm_invalid_payload.get(case).get("database"),
            post_rdbm_invalid_payload.get(case).get("payload"),
            post_rdbm_invalid_payload.get(case).get("status_code"),
            post_rdbm_invalid_payload.get(case).get("error"),
            post_rdbm_invalid_payload.get(case).get("message")
        ))

    for case in post_rdbm_dump_invalid_payload:
        post_rdbm_dump_invalid_payload_test_data.append((
            case,
            post_rdbm_dump_invalid_payload.get(case).get("type"),
            post_rdbm_dump_invalid_payload.get(case).get("server"),
            post_rdbm_dump_invalid_payload.get(case).get("port"),
            post_rdbm_dump_invalid_payload.get(case).get("database"),
            post_rdbm_dump_invalid_payload.get(case).get("payload"),
            post_rdbm_dump_invalid_payload.get(case).get("status_code"),
            post_rdbm_dump_invalid_payload.get(case).get("error"),
            post_rdbm_dump_invalid_payload.get(case).get("message")
        ))

    @allure.title("Migrate From Relational Database ")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can migrate from MySQL/PostgreSQL  \n"
        "Description: create rdbm connect with MySQL/PostgreSQL \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(post_rdbm_test_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_rdbm_meta(self, case, type, server, port, database, payload, dump_payload):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.create_rdbm_meta(type, server, port, database, payload, cookie)
        check_res.assert_no_error()
        assert check_res.results[0] is not None, "Failed to connect with database {}".format(type)

    @allure.title("Migrate From Relational Database with invalid cookies ")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can migrate from MySQL with invalid cookies \n"
        "Description: create rdbm connect with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_rdbm_meta_invalid_cookies(self, case, cookie, type, server, port, database, payload):

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.create_rdbm_meta(type, server, port, database, payload, cookie)
        check_res.assert_api_no_authorized()
        check_res.assert_error()

    @allure.title("Migrate From Relational Database with invalid payload ")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can migrate from MySQL with invalid payload \n"
        "Description: create rdbm connect with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(post_rdbm_invalid_payload_test_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_rdbm_meta_invalid_payload(self, case, type, server, port, database, payload, status_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.create_rdbm_meta(type, server, port, database, payload, cookie)
        check_res.assert_result(status_code, error, message)


    @allure.title("dump data From Relational Database with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can dump data from MySQL with invalid cookies \n"
        "Description: dump data from MySQL with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_rdbm_dump_invalid_cookies(self, case, cookie, type, server, port, database, payload):

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.post_rdbm_dumps(type, server, port, database, payload, cookie)
        check_res.assert_api_no_authorized()

    @allure.title("dump data From Relational Database ")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can dump data from MySQL/PostgreSQL  \n"
        "Description: dump data from MySQL/PostgreSQL \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(post_rdbm_test_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_rdbm_dump(self, case, type, server, port, database, payload, dump_payload):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.post_rdbm_dumps(type, server, port, database, dump_payload, cookie)
        check_res.assert_no_error()
        table_name = dump_payload["tableNames"][0]
        assert "/data/gui/loading_data" in check_res.results[table_name], "Dump data fails"

    @allure.title("dump data From Relational Database with invalid payload")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can dump data from MySQL/PostgreSQL  with invalid payload\n"
        "Description: dump data from MySQL/PostgreSQL with invalid payload\n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(post_rdbm_dump_invalid_payload_test_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_rdbm_dump_invalid_payload(self, case, type, server, port, database, dump_payload, status_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.post_rdbm_dumps(type, server, port, database, dump_payload, cookie)
        check_res.assert_result(status_code, error, message)