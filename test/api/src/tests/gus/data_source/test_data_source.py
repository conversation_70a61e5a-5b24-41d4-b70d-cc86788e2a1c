import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
from src.operation.gsql_server import GSQLServer
from src.common.api_basecase import APIBaseCase
import allure


class TestDataSource(APIBaseCase):
    dataSource_data = data.get_yaml_data("gus/api_data_source.yml")
    get_datasource_info = dataSource_data.get("valid_get_data_source")
    put_datasource_data = dataSource_data.get("valid_put_data_source")
    post_datasource_format_check = dataSource_data.get("valid_post_datasource_format_check")
    delete_datasource_data = dataSource_data.get("valid_delete_datasource")
    s3_setting = put_datasource_data.get("s3_data_source").get("setting")
    gcs_setting = put_datasource_data.get("gcs_data_source").get("setting")
    abs_setting = put_datasource_data.get("abs_data_source").get("setting")

    ## invalid data
    invalid_get_datasource_info = dataSource_data.get("invalid_get_datasource")
    invalid_put_datasource = dataSource_data.get("invalid_put_data_source")
    invalid_post_datasource = dataSource_data.get("invalid_post_datasource_format_check")
    invalid_delete_datasource = dataSource_data.get("invalid_delete_datasource")

    test_get_datasource_info = []
    test_put_datasource = []
    test_post_datasource_format_check = []
    test_delete_datasource = []
    test_get_datasource_invalid = []
    test_put_datasource_invalid = []
    test_post_datasource_format_check_invalid = []
    test_delete_datasource_invalid = []

    for case in get_datasource_info:
        test_get_datasource_info.append((
            case,
            get_datasource_info.get(case).get("graph_name"),
            get_datasource_info.get(case).get("dataSource_type"),
            get_datasource_info.get(case).get("response_result")
        ))

    for case in put_datasource_data:
        test_put_datasource.append((
            case,
            put_datasource_data.get(case).get("graph_name"),
            put_datasource_data.get(case).get("dataSource_type"),
            put_datasource_data.get(case).get("alias"),
            put_datasource_data.get(case).get("setting"),
            put_datasource_data.get(case).get("sample_data")
        ))

    for case in post_datasource_format_check:
        test_post_datasource_format_check.append((
            case,
            post_datasource_format_check.get(case).get("dataSource_type"),
            post_datasource_format_check.get(case).get("payload")
        ))

    for case in delete_datasource_data:
        test_delete_datasource.append((
            case,
            delete_datasource_data.get(case).get("graph_name"),
            delete_datasource_data.get(case).get("dataSource_type"),
            delete_datasource_data.get(case).get("alias"),
            put_datasource_data.get(case).get("setting")
        ))

    for case in invalid_get_datasource_info:
        test_get_datasource_invalid.append((
            case,
            invalid_get_datasource_info.get(case).get("graph_name"),
            invalid_get_datasource_info.get(case).get("dataSource_type"),
            invalid_get_datasource_info.get(case).get("cookies"),
            invalid_get_datasource_info.get(case).get("status_code"),
            invalid_get_datasource_info.get(case).get("error"),
            invalid_get_datasource_info.get(case).get("message")
        ))

    for case in invalid_put_datasource:
        test_put_datasource_invalid.append((
            case,
            invalid_put_datasource.get(case).get("graph_name"),
            invalid_put_datasource.get(case).get("dataSource_type"),
            invalid_put_datasource.get(case).get("alias"),
            invalid_put_datasource.get(case).get("setting"),
            invalid_put_datasource.get(case).get("cookies"),
            invalid_put_datasource.get(case).get("status_code"),
            invalid_put_datasource.get(case).get("error"),
            invalid_put_datasource.get(case).get("message")
        ))

    for case in invalid_post_datasource:
        test_post_datasource_format_check_invalid.append((
            case,
            invalid_post_datasource.get(case).get("dataSource_type"),
            invalid_post_datasource.get(case).get("payload"),
            invalid_post_datasource.get(case).get("cookies"),
            invalid_post_datasource.get(case).get("status_code"),
            invalid_post_datasource.get(case).get("error"),
            invalid_post_datasource.get(case).get("message")
        ))

    for case in invalid_delete_datasource:
        test_delete_datasource_invalid.append((
            case,
            invalid_delete_datasource.get(case).get("graph_name"),
            invalid_delete_datasource.get(case).get("dataSource_type"),
            invalid_delete_datasource.get(case).get("alias"),
            invalid_delete_datasource.get(case).get("cookies"),
            invalid_delete_datasource.get(case).get("status_code"),
            invalid_delete_datasource.get(case).get("error"),
            invalid_delete_datasource.get(case).get("message")
        ))

    @allure.title("Get data source info")
    @allure.description(
        "TestType: Positive \n"
        "Target: Get data source info for S3/GCS3/ABS \n"
        "Description: Try to get data source info for S3/GCS3/ABS \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_get_datasource_info, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apidatasource
    @pytest.mark.run(order=3)
    def test_get_data_source(self, case, graph_name, dataSource_type, response_result):
        login = Login()
        gus = GUS()
        login_result = login.login()
        result = gus.get_dataSource_name_info(login_result.get_cookies(), graph_name, dataSource_type)
        result.assert_no_error()

    @allure.title("Save a new data source info")
    @allure.description(
        "TestType: Positive \n"
        "Target: Add a new data source and save it successfully\n"
        "Description: Try to save a new datasource and check response  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_put_datasource, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apidatasource
    @pytest.mark.run(order=1)
    def test_add_data_source(self, case, graph_name, dataSource_type, alias, setting, sample_data):
        login = Login()
        gus = GUS()
        login_result = login.login()

        datasource_list = gus.get_dataSource_name_info(login_result.get_cookies(), graph_name, dataSource_type).results

        # delete the datasource if it's already in data source list
        if alias in datasource_list:
            res = gus.delete_dataSource(login_result.get_cookies(), graph_name, dataSource_type, alias)
            res.assert_no_error()
        result = gus.put_dataSource(login_result.get_cookies(), graph_name, dataSource_type, alias, setting)
        result.assert_result(201, False, '')
        res = gus.get_dataSource_name_info(login_result.get_cookies(), graph_name, dataSource_type).results
        assert alias in res

    @allure.title("Data source format check")
    @allure.description(
        "TestType: Positive \n"
        "Target: Add a gcs data source and save it successfully\n"
        "Description: Try to save a new datasource and check response  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_post_datasource_format_check, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apidatasource
    @pytest.mark.run(order=3)
    def test_data_source_format_check(self, case, dataSource_type, payload):
        login = Login()
        gus = GUS()
        login_result = login.login()
        result = gus.post_dataSource_format_check(login_result.get_cookies(), dataSource_type, payload)
        result.assert_no_error()

    @allure.title("Delete Data source")
    @allure.description(
        "TestType: Positive \n"
        "Target: Delete data source and check if it works\n"
        "Description: Try to delete a existing datasource and check if it's still in datasource list  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_delete_datasource, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apidatasource
    @pytest.mark.run(order=2)
    def test_delete_data_source(self, case, graph_name, dataSource_type, alias, setting):
        login = Login()
        gus = GUS()
        login_result = login.login()
        datasource_list = gus.get_dataSource_name_info(login_result.get_cookies(), graph_name, dataSource_type).results

        # add a datasource if there's no exist
        if alias not in datasource_list:
            res = gus.put_dataSource(login_result.get_cookies(), graph_name, dataSource_type, alias, setting)
            res.assert_result(201, False, '')
        result = gus.delete_dataSource(login_result.get_cookies(), graph_name, dataSource_type, alias)
        result.assert_no_error()

        ## check if it's in data source list
        datasource_list = gus.get_dataSource_name_info(login_result.get_cookies(), graph_name, dataSource_type).results
        assert alias not in datasource_list, "The data source {} wasn't deleted successfully"

    @allure.title("Get data source info")
    @allure.description(
        "TestType:  Negative \n"
        "Target: Get data source info for S3/GCS3/ABS with invalid payload \n"
        "Description: Check if can get data source info with invalid payload  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_get_datasource_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apidatasource
    @pytest.mark.run(order=300)
    def test_get_data_source_invalid(self,
                                     case,
                                     graph_name,
                                     dataSource_type,
                                     cookies,
                                     status_code,
                                     error,
                                     message):
        login = Login()
        gus = GUS()
        login_result = login.login()
        if "cookies" in case:
            res = gus.get_dataSource_name_info(cookies, graph_name, dataSource_type)
            res.assert_api_no_authorized()
        else:
            res = gus.get_dataSource_name_info(login_result.get_cookies(), graph_name, dataSource_type)
            res.assert_result(status_code, error, message)

    @allure.title("Save a new data source info")
    @allure.description(
        "TestType: Negative \n"
        "Target: Add a new data source with invalid payload and check if can save it successfully\n"
        "Description: Try to save a new datasource with invalid payload and check if can save it.  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_put_datasource_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apidatasource
    @pytest.mark.run(order=1)
    def test_add_data_source_invalid(self,
                                     case,
                                     graph_name,
                                     dataSource_type,
                                     alias,
                                     setting,
                                     cookies,
                                     status_code,
                                     error,
                                     message):
        login = Login()
        gus = GUS()
        login_result = login.login()

        if "cookies" in case:
            res = gus.put_dataSource(cookies, graph_name, dataSource_type, alias, setting)
            res.assert_api_no_authorized()
        else:
            res = gus.put_dataSource(login_result.get_cookies(), graph_name, dataSource_type, alias, setting)
            res.assert_result(status_code, error, message)

    @allure.title("Data source format check")
    @allure.description(
        "TestType: Negative \n"
        "Target: Check data source format with invalid payload\n"
        "Description: Try to check data source format with invalid payload to check if it can pass check  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_post_datasource_format_check_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apidatasource
    @pytest.mark.run(order=3)
    def test_data_source_format_check_invalid(self, case, dataSource_type, payload, cookies, status_code, error, message):
        login = Login()
        gus = GUS()
        login_result = login.login()
        if "cookies" in case:
            res = gus.post_dataSource_format_check(cookies, dataSource_type, payload)
            res.assert_api_no_authorized()
        else:
            result = gus.post_dataSource_format_check(login_result.get_cookies(), dataSource_type, payload)
            result.assert_result(status_code, error, message)

    @allure.title("Delete Data source")
    @allure.description(
        "TestType: Negative \n"
        "Target: Delete data source with invalid payload and check if it works\n"
        "Description: Try to a existing datasource with invalid payload and check if it can works \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_delete_datasource_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apidatasource
    @pytest.mark.run(order=2)
    def test_delete_data_source_invalid(self,
                                        case,
                                        graph_name,
                                        dataSource_type,
                                        alias,
                                        cookies,
                                        status_code,
                                        error,
                                        message):
        login = Login()
        gus = GUS()
        login_result = login.login()

        if "cookies" in case:
            res = gus.delete_dataSource(cookies, graph_name, dataSource_type, alias)
            res.assert_api_no_authorized()
        else:
            result = gus.delete_dataSource(login_result.get_cookies(), graph_name, dataSource_type, alias)
            result.assert_result(status_code, error, message)

    @allure.title("Get udt tuples info")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can get udt tuples info \n"
        "Description: Can get udt tuples info \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_udf_tuples(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        graph_name = gsql.get_first_graph(cookie)
        check_res = gsql.get_udf_tuples(graph_name, cookie)
        check_res.assert_no_error()

    @allure.title("List token function")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can list token function \n"
        "Description: Can list token function \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_token_functions(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.get_token_functions(cookie)
        check_res.assert_no_error()

    @allure.title("Get sample data ")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can get s3/gcs sample data \n"
        "Description: Can can get s3/gcs sample data \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_put_datasource, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_sample_data(self, case, graph_name, dataSource_type, alias, setting, sample_data):
        login = Login()
        result = login.login()
        gus = GUS()
        cookie = result.get_cookies()

        datasource_list = gus.get_dataSource_name_info(cookie, graph_name, dataSource_type).results

        # Check if data source is connected
        if alias not in datasource_list:
            result = gus.put_dataSource(cookie, graph_name, dataSource_type, alias, setting)
            result.assert_result(201, False, '')

        #check the existing local files
        gsql = GSQLServer()
        if sample_data:
            check_res = gsql.get_sample_data(sample_data, cookie)
            check_res.assert_no_error()