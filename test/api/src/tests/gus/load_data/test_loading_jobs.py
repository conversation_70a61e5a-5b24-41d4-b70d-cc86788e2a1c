import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure
from src.common.logger import logger

from src.operation.gus import GUS

from src.api.tools_api import tools

from src.operation.design_schema import Design_Schema


class TestLoadingJobs(APIBaseCase):

    login_user_data = data.get_yaml_data("general/loading_jobs.yml")
    loading_test_data_invalid = login_user_data.get("get_loading_jobs_invalid")
    delete_loading_jobs_invalid = login_user_data.get("delete_all_loading_jobs_invalid")
    get_loading_jobs_name_invalid = login_user_data.get("get_loading_jobs_name_invalid")
    post_add_loading_jobs_name_invalid = login_user_data.get("post_add_loading_jobs_name_invalid")
    delete_loading_jobs_name_invalid = login_user_data.get("delete_loading_jobs_name_invalid")
    get_loading_jobs_progress_invalid = login_user_data.get("get_loading_jobs_progress_invalid")
    post_start_loading_jobs_invalid = login_user_data.get("post_start_loading_jobs_invalid")
    pause_loading_jobs_invalid = login_user_data.get("pause_loading_jobs_invalid")
    resume_loading_jobs_invalid = login_user_data.get("resume_loading_jobs_invalid")
    stop_loading_jobs_invalid = login_user_data.get("stop_loading_jobs_invalid")
    start_all_loading_job = login_user_data.get("start_all_loading_job")
    loading_jobs_invalid = []
    delete_all_loading_jobs_invalid_list = []
    get_loading_jobs_name_invalid_list = []
    post_add_loading_jobs_name_invalid_list = []
    delete_loading_jobs_name_invalid_list = []
    get_loading_jobs_progress_invalid_list = []
    post_start_loading_jobs_invalid_list = []
    pause_loading_jobs_invalid_list = []
    resume_loading_jobs_invalid_list = []
    stop_loading_jobs_invalid_list = []
    test_start_all_loading_job = []
    for case in loading_test_data_invalid:
        loading_jobs_invalid.append(
            (
                case,
                loading_test_data_invalid.get(case).get("graph_name"),
                loading_test_data_invalid.get(case).get("params"),
                loading_test_data_invalid.get(case).get("status_code"),
                loading_test_data_invalid.get(case).get("error"),
                loading_test_data_invalid.get(case).get("message")
            )
        )
    for case in delete_loading_jobs_invalid:
        delete_all_loading_jobs_invalid_list.append(
            (
                case,
                delete_loading_jobs_invalid.get(case).get("graph_name"),
                delete_loading_jobs_invalid.get(case).get("params"),
                delete_loading_jobs_invalid.get(case).get("status_code"),
                delete_loading_jobs_invalid.get(case).get("error"),
                delete_loading_jobs_invalid.get(case).get("message"),
            )
        )
    for case in get_loading_jobs_name_invalid:
        get_loading_jobs_name_invalid_list.append(
            (
                case,
                get_loading_jobs_name_invalid.get(case).get("graph_name"),
                get_loading_jobs_name_invalid.get(case).get("params"),
                get_loading_jobs_name_invalid.get(case).get("status_code"),
                get_loading_jobs_name_invalid.get(case).get("error"),
                get_loading_jobs_name_invalid.get(case).get("message"),
            )
        )
    for case in post_add_loading_jobs_name_invalid:
        post_add_loading_jobs_name_invalid_list.append(
            (
                case,
                post_add_loading_jobs_name_invalid.get(case).get("graph_name"),
                post_add_loading_jobs_name_invalid.get(case).get("params"),
                post_add_loading_jobs_name_invalid.get(case).get("json_data"),
                post_add_loading_jobs_name_invalid.get(case).get("status_code"),
                post_add_loading_jobs_name_invalid.get(case).get("error"),
                post_add_loading_jobs_name_invalid.get(case).get("message"),
            )
        )
    for case in delete_loading_jobs_name_invalid:
        delete_loading_jobs_name_invalid_list.append(
            (
                case,
                delete_loading_jobs_name_invalid.get(case).get("graph_name"),
                delete_loading_jobs_name_invalid.get(case).get("params"),
                delete_loading_jobs_name_invalid.get(case).get("status_code"),
                delete_loading_jobs_name_invalid.get(case).get("error"),
                delete_loading_jobs_name_invalid.get(case).get("message"),
            )
        )
    for case in get_loading_jobs_progress_invalid:
        get_loading_jobs_progress_invalid_list.append(
            (
                case,
                get_loading_jobs_progress_invalid.get(case).get("graph_name"),
                get_loading_jobs_progress_invalid.get(case).get("params"),
                get_loading_jobs_progress_invalid.get(case).get("status_code"),
                get_loading_jobs_progress_invalid.get(case).get("error"),
                get_loading_jobs_progress_invalid.get(case).get("message"),
            )
        )
    for case in post_start_loading_jobs_invalid:
        post_start_loading_jobs_invalid_list.append(
            (
                case,
                post_start_loading_jobs_invalid.get(case).get("graph_name"),
                post_start_loading_jobs_invalid.get(case).get("params"),
                post_start_loading_jobs_invalid.get(case).get("json_data"),
                post_start_loading_jobs_invalid.get(case).get("status_code"),
                post_start_loading_jobs_invalid.get(case).get("error"),
                post_start_loading_jobs_invalid.get(case).get("message"),
            )
        )
    for case in pause_loading_jobs_invalid:
        pause_loading_jobs_invalid_list.append(
            (
                case,
                pause_loading_jobs_invalid.get(case).get("graph_name"),
                pause_loading_jobs_invalid.get(case).get("params"),
                pause_loading_jobs_invalid.get(case).get("status_code"),
                pause_loading_jobs_invalid.get(case).get("error"),
                pause_loading_jobs_invalid.get(case).get("message"),
            )
        )
    for case in resume_loading_jobs_invalid:
        resume_loading_jobs_invalid_list.append(
            (
                case,
                resume_loading_jobs_invalid.get(case).get("graph_name"),
                resume_loading_jobs_invalid.get(case).get("params"),
                resume_loading_jobs_invalid.get(case).get("status_code"),
                resume_loading_jobs_invalid.get(case).get("error"),
                resume_loading_jobs_invalid.get(case).get("message"),
            )
        )
    for case in stop_loading_jobs_invalid:
        stop_loading_jobs_invalid_list.append(
            (
                case,
                stop_loading_jobs_invalid.get(case).get("graph_name"),
                stop_loading_jobs_invalid.get(case).get("params"),
                stop_loading_jobs_invalid.get(case).get("status_code"),
                stop_loading_jobs_invalid.get(case).get("error"),
                stop_loading_jobs_invalid.get(case).get("message"),
            )
        )

    for case in start_all_loading_job:
        test_start_all_loading_job.append((
            case,
            start_all_loading_job.get(case).get("graph_name"),
            start_all_loading_job.get(case).get("json_data")
        ))


    @allure.title("get loading jobs with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can get loading jobs with params \n"
        "Description: get loading jobs with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(loading_jobs_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_get_loading_jobs_with_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.get_loading_jobs_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.get_loading_jobs_info(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("delete all loading jobs with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can delete all loading jobs with params \n"
        "Description: delete all loading jobs with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(delete_all_loading_jobs_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.teardown
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_delete_all_loading_jobs_with_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.delete_loading_jobs_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "?load_job_popData___Sheet1_csv_1584998109163")
        else:
            result = gus.delete_loading_jobs_info(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("get loading jobs name with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can get loading jobs name with params \n"
        "Description: get loading jobs name with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(get_loading_jobs_name_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_get_loading_jobs_name_with_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.get_loading_jobs_name_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.get_loading_jobs_name_info(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("post add loading jobs name with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can add loading jobs name with params \n"
        "Description: add loading jobs name with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(post_add_loading_jobs_name_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_post_add_loading_jobs_name_with_params(self, case, graph_name, params, json_data, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()
        if params == "add_new":
            # delete_loading job
            params = "load_job_SearchTrend_csv_1586467063020"
            gus.delete_loading_jobs_name_info(cookie, graph_name, params)

        if params == "invalid_cookies":
            result = gus.post_add_loading_jobs_name_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "load_job_popData___Sheet1_csv_1584998109163", json_data)
        else:
            result = gus.post_add_loading_jobs_name_info(cookie, graph_name, params, json_data)
        # check error message
        result.assert_result(status_code, error, message)


    @allure.title("delete loading jobs name with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can delete loading jobs name with params \n"
        "Description: delete loading jobs name with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(delete_loading_jobs_name_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.teardown
    @pytest.mark.run(order=4000)
    def test_delete_loading_jobs_name_with_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()
        if params == "delete_loading_jobs":
            params = "load_job_SearchTrend_csv_1586467063020"
            payload = "{\"FileNames\":{\"MyDataSource\":\"\"},\"Type\":\"Offline\",\"JobName\":\"load_job_SearchTrend_csv_1586467063020\",\"GraphName\":\"MyGraph\",\"Headers\":{},\"Filters\":[],\"LoadingStatements\":[{\"Type\":\"Vertex\",\"TargetName\":\"SearchStat\",\"DataSource\":{\"Type\":\"FileVar\",\"Value\":\"MyDataSource\"},\"Mappings\":[{\"Type\":\"SrcColIndex\",\"Value\":0},{\"Type\":\"SrcColIndex\",\"Value\":1},{\"Type\":\"SrcColIndex\",\"Value\":2},{\"Type\":\"SrcColIndex\",\"Value\":3},{\"Type\":\"SrcColIndex\",\"Value\":4}],\"UsingClauses\":{\"EOL\":\"\\\\n\",\"HEADER\":\"true\",\"SEPARATOR\":\",\"}},{\"Type\":\"Edge\",\"TargetName\":\"WEATHER_STAMP\",\"FromVertexType\":\"Day_\",\"ToVertexType\":\"WeatherStat\",\"DataSource\":{\"Type\":\"FileVar\",\"Value\":\"MyDataSource\"},\"Mappings\":[{\"Type\":\"SrcColIndex\",\"Value\":0},{\"Type\":\"SrcColIndex\",\"Value\":3}],\"UsingClauses\":{\"EOL\":\"\\\\n\",\"SEPARATOR\":\",\",\"HEADER\":\"true\"},\"WhereClause\":\"($1 == \\\"20052677\\\")\"}]}"
            gus.post_add_loading_jobs_name_info(cookie, graph_name, params, payload)

        if params == "invalid_cookies":
            result = gus.delete_loading_jobs_name_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "load_job_popData___Sheet1_csv_1584998109163")
        else:
            result = gus.delete_loading_jobs_name_info(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("get loading jobs progress with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can get loading jobs progress with params \n"
        "Description: get loading jobs progress with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(get_loading_jobs_progress_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_get_loading_jobs_progress_with_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.get_loading_jobs_progress_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.get_loading_jobs_progress_info(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)


    @allure.title("post start loading jobs name with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can start loading jobs name with params \n"
        "Description: start loading jobs name with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(post_start_loading_jobs_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_post_start_loading_jobs_with_params(self, case, graph_name, params, json_data, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.post_start_loading_jobs_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "?load_job_popData___Sheet1_csv_1584998109163", json_data)
        else:
            result = gus.post_start_loading_jobs_info(cookie, graph_name, params, json_data)
        # check error message
        result.assert_result(status_code, error, message)


    @allure.title("pause loading jobs progress with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can pause loading jobs progress with params \n"
        "Description: pause loading jobs progress with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(pause_loading_jobs_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_pause_loading_jobs_progress_with_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.pause_loading_jobs_progress_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.pause_loading_jobs_progress_info(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("resume loading jobs progress with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can resume loading jobs progress with params \n"
        "Description: resume loading jobs progress with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(resume_loading_jobs_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_resume_loading_jobs_progress_with_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.resume_loading_jobs_progress_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.resume_loading_jobs_progress_info(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("stop loading jobs progress with params")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can stop loading jobs progress with params \n"
        "Description: stop loading jobs progress with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(stop_loading_jobs_invalid_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=4)
    def test_stop_loading_jobs_progress_with_params(self, case, graph_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.stop_loading_jobs_progress_info("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, "")
        else:
            result = gus.stop_loading_jobs_progress_info(cookie, graph_name, params)
        # check error message
        result.assert_result(status_code, error, message)

    @allure.title("start all loading jobs ")
    @allure.description(
        "TestType: nagetive \n" 
        "Target: Check we can start loading jobs name with params \n"
        "Description: start loading jobs name with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(test_start_all_loading_job, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.preexec
    @pytest.mark.runloadingjob
    @pytest.mark.solution
    @pytest.mark.run(order=0.6)
    def test_post_start_loading_jobs(self, case, graph_name, json_data):
        login = Login()
        gus = GUS()
        cookie = login.login().get_cookies()
        result = gus.post_start_loading_jobs_info(cookie, graph_name, "", json_data)
        # check error message
        result.assert_no_error()