import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure
from src.common.logger import logger
from src.operation.gus import GUS
from src.api.tools_api import tools
from src.operation.design_schema import Design_Schema
from src.operation.gsql_server import GSQLServer
from datetime import datetime


class TestDesignSchema(APIBaseCase):

    design_schema_data = data.get_yaml_data("general/design_schema.yml")
    general_data = data.get_yaml_data("gus/loading_data.yml")
    invalid_cookie = general_data.get("invalid_cookie")
    export_test_data_invalid = design_schema_data.get("invalid_export")
    export_test_data_valid = design_schema_data.get("valid_export")
    invalid_gui_store = design_schema_data.get("invalid_gui_store")
    create_graph_data = design_schema_data.get("create_graph")
    create_graph_invalid_data = design_schema_data.get("create_graph_invalid")
    schema_change_data = design_schema_data.get("change_graph")
    schema_change_invalid_data = design_schema_data.get("change_graph_invalid")
    default_graph_payload = create_graph_data.get("empty_graph").get("payload")
    test_data_valid = []
    test_data_invalid = []
    test_gui_store_invalid = []
    test_invalid_cookie_data = []
    test_create_schema_data = []
    test_create_schema_invalid_data = []
    test_schema_change_data = []
    test_schema_change_invalid_data = []

    for case in export_test_data_valid:
        test_data_valid.append(
            (
                case,
                export_test_data_valid.get(case).get("params"),
                export_test_data_valid.get(case).get("status_code"),
            )
        )
    for case in export_test_data_invalid:
        test_data_invalid.append(
            (
                case,
                export_test_data_invalid.get(case).get("params"),
                export_test_data_invalid.get(case).get("status_code"),
            )
        )
    for case in invalid_gui_store:
        test_gui_store_invalid.append(
            (
                case,
                invalid_gui_store.get(case).get("params"),
                invalid_gui_store.get(case).get("status_code"),
            )
        )

    for case in invalid_cookie:
        test_invalid_cookie_data.append((
            case,
            invalid_cookie.get(case).get("cookie")
        ))

    for case in create_graph_data:
        test_create_schema_data.append((
            case, 
            create_graph_data.get(case).get("graph_name"),
            create_graph_data.get(case).get("payload"),
            create_graph_data.get(case).get("change_payload")
        ))

    for case in create_graph_invalid_data:
        test_create_schema_invalid_data.append((
            case,
            create_graph_invalid_data.get(case).get("graph_name"),
            create_graph_invalid_data.get(case).get("payload"),
            create_graph_invalid_data.get(case).get("state_code"),
            create_graph_invalid_data.get(case).get("error"),
            create_graph_invalid_data.get(case).get("message")
        ))

    for case in schema_change_data:
        test_schema_change_data.append((
            case, 
            schema_change_data.get(case).get("graph_name"),
            schema_change_data.get(case).get("payload"),
            schema_change_data.get(case).get("change_payload")
        ))

    for case in schema_change_invalid_data:
        test_schema_change_invalid_data.append((
            case,
            schema_change_invalid_data.get(case).get("graph_name"),
            schema_change_invalid_data.get(case).get("payload"),
            schema_change_invalid_data.get(case).get("change_payload"),
            schema_change_invalid_data.get(case).get("state_code"),
            schema_change_invalid_data.get(case).get("error"),
            schema_change_invalid_data.get(case).get("message")
        ))

    @allure.title("export with valid params")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check we can export solution with invalid params \n"
        "Description: export solution with valid params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(test_data_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=1)
    def test_export_valid(self, case, params, status_code):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        header = {
            "Cookie": cookie
        }
        
        res = tools.export_solution(paras=params, headers=header)
        logger.info("the response of api is {}".format(res.status_code))
        assert res.status_code == status_code, "the returned status code is: {}, expected {}".format(res.status_code, status_code)

    @allure.title("export with invalid params")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we unable export solution with invalid params \n"
        "Description: export solution with invalid params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=1)
    def test_export_invalid(self, case, params, status_code):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        header = {
            "Cookie": cookie
        }
        
        res = tools.export_solution(paras=params, headers=header)
        logger.info("the response of api is {}".format(res.status_code))
        assert res.status_code == status_code, "the returned status code is: {}, expected {}".format(res.status_code, status_code)

    # skip this cases because need auth token.
    @allure.title("gui-store with valid and invalid params")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we unable call api with invalid params \n"
        "Description: check api with invalid params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )

    # @parameterized.expand(test_gui_store_invalid, skip_on_empty=True)
    # @pytest.mark.high
    # @pytest.mark.smoke
    # @pytest.mark.api
    # @pytest.mark.gus
    # @pytest.mark.solution
    # @pytest.mark.run(order=1)
    def test_gui_store(self, case, params, status_code):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        header = {
            "Cookie": cookie
        }

        res = tools.gui_store(paras=params, headers=header)
        logger.info("the response of api is {}".format(res.message))
        logger.info("the response of api is {}".format(res.status_code))
        assert res.status_code == status_code, "the returned status code is: {}, expected {}".format(res.status_code, status_code)


    @allure.title("login tools and import solution negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we can login tools and import solution failed with wrong payload  \n"
        "Description: login tools and import solution failed with wrong payload \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=2)
    def test_login_gst_import_solution_with_special_characters(self):
        login = Login()
        result = login.login()
        my_cookies = result.get_cookies()
        ds = Design_Schema()
        header = {
                'Cookie': my_cookies,
                'Content-Type': 'text/csv',
                "Content-Disposition": 'form-data; name="file!<>?*"; filename="Contact.csv"'
            }
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import-check", cookies="", header=header))
        result.assert_result(state_code=400, error_state=True, message="Invalid payload")

    @allure.title("login tools and import solution negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we can login tools and import solution failed with wrong payload  \n"
        "Description: login tools and import solution failed with wrong payload \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=3)
    def test_login_gst_import_solution_with_embedded_single_quote(self):
        login = Login()
        result = login.login()
        my_cookies = result.get_cookies()
        ds = Design_Schema()
        header = {
                'Cookie': my_cookies,
                'Content-Type': 'text/csv',
                'Content-Disposition': 'form-data; name="file\'s"; filename="Contact.csv"'
            }
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import-check", cookies="", header=header))
        result.assert_result(state_code=400, error_state=True, message="Invalid payload")

    @allure.title("login tools and import solution negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we can login tools and import solution failed with wrong payload  \n"
        "Description: login tools and import solution failed with wrong payload, update import-check API \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=2)
    def test_login_gst_import_solution_with_field_size_test(self):
        login = Login()
        result = login.login()
        my_cookies = result.get_cookies()
        ds = Design_Schema()
        header = {
                'Cookie': my_cookies,
                'Content-Type': 'text/csv',
                "Content-Disposition": 'form-data; name="file99999999999999999999999999"; filename="Contact.csv"'
            }
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import-check", cookies="", header=header))
        result.assert_result(state_code=400, error_state=True, message="Invalid payload")

    @allure.title("Get schema types names")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can get all types names \n"
        "Description: Get type names for all schema \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_schema_types_valid(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.get_type_names(cookies=cookie)
        check_res.assert_no_error()

    @allure.title("Get schema types names with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can get all types names with invalid cookies\n"
        "Description: Get type names with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_schema_types_invalid(self, case, cookie):
        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.get_type_names(cookies=cookie)
        check_res.assert_error()
        check_res.assert_api_no_authorized()

    @allure.title("Create a graph")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can create a empty graph with valid payload \n"
        "Description: Can create graph successfully with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_create_schema_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    @pytest.mark.preexec
    @pytest.mark.run(order=2)
    def test_create_schema(self, case, graph_name, payload, change_payload):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        if gsql.check_if_graph_exist(graph_name, cookie):
            res = gsql.delete_graph(graph_name, cookie)
            res.assert_no_error()
        check_res= gsql.create_graph(graph_name, payload, cookie)
        check_res.assert_no_error()


    @allure.title("Create a graph with invalid payload")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can create a empty graph with invalid payload \n"
        "Description: Cannot create graph with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_create_schema_invalid_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    @pytest.mark.teardown
    @pytest.mark.run(order=102)
    def test_create_schema_invalid(self, case, graph_name, payload, state_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res= gsql.create_graph(graph_name, payload, cookie)
        check_res.assert_result(state_code, error, message)

    
    @allure.title("Do schema change for the graph")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if do schema change for the graph \n"
        "Description: Can change schema successfully with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_schema_change_data, skip_on_empty=True)
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    @pytest.mark.preexec
    @pytest.mark.run(order=6)
    def test_change_schema(self, case, graph_name, payload, change_payload):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        schema_payload = payload
        if not gsql.check_if_graph_exist(graph_name, cookie):
            res = gsql.create_graph(graph_name, schema_payload, cookie)
            res.assert_no_error()
        check_res= gsql.change_schema(graph_name, gsql.schema_change_payload_prepare(change_payload), cookie)
        check_res.assert_no_error()

    @allure.title("Do schema change for the graph with invalid payload")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if do schema change for the graph with invalid payload\n"
        "Description: Can change schema successfully with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_schema_change_invalid_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    @pytest.mark.preexec
    @pytest.mark.run(order=101)
    def test_change_schema_invalid(self, case, graph_name, payload, change_payload, state_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        if not gsql.check_if_graph_exist(graph_name, cookie):
            res = gsql.create_graph(graph_name, payload, cookie)
            res.assert_no_error()
        check_res= gsql.change_schema(graph_name, change_payload, cookie)
        check_res.assert_result(state_code, error, message)

    @allure.title("Delete a graph")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can delete a graph with valid payload \n"
        "Description: Can delete graph successfully with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    @pytest.mark.teardown
    @pytest.mark.run(order=5)
    def test_delete_schema(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        graph_name = "delete_graph_test"
        payload = self.default_graph_payload
        if not gsql.check_if_graph_exist(graph_name, cookie):
            check_res= gsql.create_graph(graph_name, payload, cookie)
            check_res.assert_no_error()
        res = gsql.delete_graph(graph_name, cookie)
        res.assert_no_error()

    @allure.title("Delete a graph with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can delete a graph with invalid cookies \n"
        "Description: Cannot delete graph successfully with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-03 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_delete_schema_invalid(self, case, cookie):
        login = Login()
        result = login.login()

        #check the existing local files
        gsql = GSQLServer()
        graph_name = "delete_graph_test"
        res = gsql.delete_graph(graph_name, cookie)
        res.assert_api_no_authorized()