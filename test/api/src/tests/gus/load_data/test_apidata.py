import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.api.tools_api import tools
from src.operation.gus import GUS
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure
import time


class TestAPIData(APIBaseCase):

    configs_data = data.get_yaml_data("gus/loading_data.yml")
    deletefile_invalid_conditions = configs_data.get("invalid_files")
    apiconfig_data_invalid = configs_data.get("invalid_cookie")
    batch_upload_file_data = configs_data.get("batch_upload_file")
    upload_filename = "upload_data.csv"
    files_invalid = []
    test_data_invalid = []
    test_batch_file = []
    for case in deletefile_invalid_conditions:
        files_invalid.append(
            (
                case,
                deletefile_invalid_conditions.get(case).get("filename"),
                deletefile_invalid_conditions.get(case).get("message")
            )
        )

    for case in apiconfig_data_invalid:
        test_data_invalid.append(
            (
                case,
                apiconfig_data_invalid.get(case).get("cookie")
            )
        )
    
    for case in batch_upload_file_data:
        test_batch_file.append((
            case,
            batch_upload_file_data.get(case).get("file_list")
        ))

    @allure.title("upload the local files")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the update local files into tigergraph \n"
        "Description: Get local files list \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-19 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.preexec
    @pytest.mark.getloglist
    @pytest.mark.run(order=100)
    def test_get_loglist_valid(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gus = GUS()
        check_res= gus.get_localfiles(cookies=cookie)
        #will ignore upload file if it already existing in the system
        if self.upload_filename not in str(check_res.results):
            check_res.assert_no_error()
            chunk_res = gus.upload_chunk(cookies=cookie, file_name=self.upload_filename)
            chunk_res.assert_result(state_code=201, error_state=False, message="Successfully uploaded chunk 1 for file")

            assemble_res = gus.upload_assemble(cookies=cookie, file_name=self.upload_filename)
            assemble_res.assert_result(state_code=201, error_state=False, message="Successfully assembled file")

            #check the local files already exists in the system
            check_res= gus.get_localfiles(cookies=cookie)
            assert self.upload_filename in str(check_res.results)

    @allure.title("delete the local files")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the delete local files in tigergraph system\n"
        "Description: Delete local files list \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-19 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.teardown
    @pytest.mark.deletelocalfiles
    @pytest.mark.run(order=100)
    def test_deletelocalfiles_valid(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gus = GUS()
        check_res= gus.get_localfiles(cookies=cookie)
        #will ignore delete file if it not existing in the system
        if self.upload_filename in str(check_res.results):
            check_res.assert_no_error()
            check_res= gus.delete_localfiles(cookies=cookie, file_name=self.upload_filename)
            check_res.assert_no_error()
            check_res.assert_result(state_code=200, error_state=False, message="Successfully removed path")

            #check the local files will delete in the system
            check_res= gus.get_localfiles(cookies=cookie)
            assert self.upload_filename not in str(check_res.results)

    @allure.title("get the local files but with invalid cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the local files can't be retrive with valid key if no cookie \n"
        "Description: Unable to get the local files if without cookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-19 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.getlocalfiles
    @pytest.mark.run(order=1)
    def test_getlocalfile_with_invalid_cookie(self, case, cookie):
        #check the api can't be retrive with invalid cookie
        gus = GUS()
        check_res= gus.get_localfiles(cookies=cookie)
        check_res.assert_error()
        check_res.assert_api_no_authorized()

    @allure.title("upload the trunk but without cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the upload the trunk will failed if invalid cookie \n"
        "Description: Unable to upload the trunk if with invalidcookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-19 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.uploadtrunk
    @pytest.mark.run(order=1)
    def test_uploadtrunk_with_invalid_cookie(self, case, cookie):
        #check the api can't be retrive with invalid cookie
        gus = GUS()
        check_res= gus.upload_chunk(cookies=cookie)
        check_res.assert_error()
        check_res.assert_api_no_authorized()

    @allure.title("upload the assemble but without cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the upload the assemble will failed if invalid cookie \n"
        "Description: Unable to upload the assemble if with invalidcookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-19 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.uploadassemble
    @pytest.mark.run(order=1)
    def test_upload_assemble_with_invalid_cookie(self, case, cookie):
        #check the api can't be retrive with invalid cookie
        gus = GUS()
        check_res= gus.upload_assemble(cookies=cookie)
        check_res.assert_error()
        check_res.assert_api_no_authorized()

    @allure.title("delete the local files but without cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the files will delete failed if invalid cookie \n"
        "Description: Unable to delete the files if with invalidcookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-19 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.deletelocalfiles
    @pytest.mark.run(order=1)
    def test_deletelocalfiles_with_invalid_cookie(self, case, cookie):
        #check the api can't be retrive with invalid cookie
        gus = GUS()
        check_res= gus.delete_localfiles(cookies=cookie, file_name=self.upload_filename)
        check_res.assert_error()
        check_res.assert_api_no_authorized()

    @allure.title("delete the local files with invliad conditions")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the files will delete failed if with invliad conditions \n"
        "Description: Unable to delete the files if with invliad conditions \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-19 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(files_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.deletelocalfilesinvalid
    @pytest.mark.run(order=1)
    def test_deletelocalfiles_with_invalid_conditions(self, case, filename, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        
        #check the file can't be delete if with invalid conditions
        #there is a bug for delete files: https://graphsql.atlassian.net/browse/TOOLS-2741
        gus = GUS()
        check_res= gus.delete_localfiles(cookies=cookie, file_name=filename)
        check_res.assert_result(message=message)


    @allure.title("upload the local files")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Upload all test data file to tigergraph \n"
        "Description: Upload all test data file to MyGraph \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_batch_file, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.preexec
    @pytest.mark.uploadbatchfiles
    @pytest.mark.run(order=0.5)
    def test_uploadlocalfiles_batch(self, case, upload_file_list):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gus = GUS()
        check_res= gus.get_localfiles(cookies=cookie)
        #will ignore upload file if it already existing in the system
        for upload_filename in upload_file_list:
            if upload_filename not in str(check_res.results):
                check_res.assert_no_error()
                chunk_res = gus.upload_chunk(cookies=cookie, file_name=upload_filename)
                chunk_res.assert_result(state_code=201, error_state=False, message="Successfully uploaded chunk 1 for file")

                assemble_res = gus.upload_assemble(cookies=cookie, file_name=upload_filename)
                assemble_res.assert_result(state_code=201, error_state=False, message="Successfully assembled file")

                #check the local files already exists in the system
                check_res= gus.get_localfiles(cookies=cookie)
                assert upload_filename in str(check_res.results)

