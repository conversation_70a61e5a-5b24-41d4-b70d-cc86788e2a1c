import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.operation.gsql_server import GSQLServer
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestPrivileges:
    general_data = data.get_yaml_data("gus/loading_data.yml")
    roles_data = data.get_yaml_data("gus/api_roles.yml")
    privileges_data = data.get_yaml_data("gus/api_privileges.yml")
    invalid_cookie = general_data.get("invalid_cookie")
    default_roles = roles_data.get("post_roles").get("default_roles").get("payload")
    get_privileges_invalid = privileges_data.get("get_privilege_invalid_param")
    post_privileges = privileges_data.get("post_privileges")
    post_privileges_invalid_paylaod = privileges_data.get("post_privileges_invalid_payload")
    revoke_privileges_invalid_payload = privileges_data.get("revoke_privileges_invalid_payload")

    test_invalid_cookie_data = []
    test_get_privileges_invalid_param_data = []
    test_post_privileges_data = []
    test_post_privileges_invalid_data = []
    test_revoke_privileges_invalid_data = []

    for case in invalid_cookie:
        test_invalid_cookie_data.append((
            case,
            invalid_cookie.get(case).get("cookie")
        ))

    for case in get_privileges_invalid:
        test_get_privileges_invalid_param_data.append((
            case,
            get_privileges_invalid.get(case).get("role"),
            get_privileges_invalid.get(case).get("state_code"),
            get_privileges_invalid.get(case).get("error"),
            get_privileges_invalid.get(case).get("message")
        ))

    for case in post_privileges:
        test_post_privileges_data.append((
            case,
            post_privileges.get(case).get("payload")
        ))

    for case in post_privileges_invalid_paylaod:
        test_post_privileges_invalid_data.append((
            case, 
            post_privileges_invalid_paylaod.get(case).get("payload"),
            post_privileges_invalid_paylaod.get(case).get("state_code"),
            post_privileges_invalid_paylaod.get(case).get("error"),
            post_privileges_invalid_paylaod.get(case).get("message")

        ))

    for case in revoke_privileges_invalid_payload:
        test_revoke_privileges_invalid_data.append((
            case,
            revoke_privileges_invalid_payload.get(case).get("payload"),
            revoke_privileges_invalid_payload.get(case).get("state_code"),
            revoke_privileges_invalid_payload.get(case).get("error"),
            revoke_privileges_invalid_payload.get(case).get("message")
        ))

    @allure.title("List all privileges for role superadmin ")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can list all privileges for role superadmin \n"
        "Description:  Can list all privileges for role superadmin \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_privileges_info(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        role = 'superuser'
        check_res = gsql.get_roles_privileges(role, cookie)
        check_res.assert_no_error()
        assert len(check_res.results[0]["privileges"]["global"]) == 31, "SuperAdmin lacks some privileges"
        assert check_res.results[0]["role"] == role, "Tha actual role is {}".format(check_res.results[0]["role"])

    @allure.title("List all privileges with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can list all privileges with invalid cookies \n"
        "Description:  Cannot list all privileges with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_privileges_info_with_invalid_cookies(self, case, cookie):
        #check the existing local files
        gsql = GSQLServer()
        role = 'superuser'
        check_res = gsql.get_roles_privileges(role, cookie)
        check_res.assert_api_no_authorized()
        check_res.assert_error()

    @allure.title("List all privileges with invalid param")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can list all privileges with invalid param \n"
        "Description:  Cannot list all privileges with invalid param \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_get_privileges_invalid_param_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_privileges_info_with_invalid_param(self, case, role, state_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        #check the existing local files
        gsql = GSQLServer()
        
        check_res = gsql.get_roles_privileges(role, cookie)
        check_res.assert_result(state_code, error, message)


    @allure.title("Assign privileges for the role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can assign privileges for the role with valid payload \n"
        "Description: Can assign privileges for the role with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_post_privileges_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_grant_privilege(self, case, payload):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        role_payload = self.default_roles

        #create a role at first
        gsql = GSQLServer()
        for role in role_payload["roles"]:
            if  not gsql.check_if_role_exist(role, cookie):
                check_res = gsql.create_new_role(role_payload, cookie)
                check_res.assert_no_error()
        payload["roles"] = role_payload["roles"]
        res = gsql.grant_privileges(payload, cookie)
        res.assert_no_error()

    @allure.title("Assign privileges for the role with invalid payload")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can assign privileges for the role with invalid payload \n"
        "Description: Cannot assign privileges for the role with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_post_privileges_invalid_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_grant_privilege_invalid_payload(self, case, payload, state_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        role_payload = self.default_roles

        #create a role at first
        gsql = GSQLServer()
        for role in role_payload["roles"]:
            if  not gsql.check_if_role_exist(role, cookie):
                check_res = gsql.create_new_role(role_payload, cookie)
                check_res.assert_no_error()
        if not payload["roles"]:
            payload["roles"] = role_payload["roles"]
        res = gsql.grant_privileges(payload, cookie)
        res.assert_result(state_code, error, message)

    @allure.title("Revoke privileges for the role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can revoke privileges for the role with valid payload \n"
        "Description: Can revoke privileges for the role with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_post_privileges_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_revoke_privilege(self, case, payload):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        role_payload = self.default_roles

        #create a role at first
        gsql = GSQLServer()
        for role in role_payload["roles"]:
            if  not gsql.check_if_role_exist(role, cookie):
                check_res = gsql.create_new_role(role_payload, cookie)
                check_res.assert_no_error()
        payload["roles"] = role_payload["roles"]
        res = gsql.grant_privileges(payload, cookie)
        res = gsql.revoke_privileges(payload, cookie)
        res.assert_no_error()

    @allure.title("Revoke privileges for the role with invalid payload")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can revoke privileges for the role with invalid payload \n"
        "Description: Cannot revoke privileges for the role with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_revoke_privileges_invalid_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_revoke_privilege_invalid_payload(self, case, payload, state_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        role_payload = self.default_roles

        #create a role at first
        gsql = GSQLServer()
        for role in role_payload["roles"]:
            if  not gsql.check_if_role_exist(role, cookie):
                check_res = gsql.create_new_role(role_payload, cookie)
                check_res.assert_no_error()
        if not payload["roles"]:
            payload["roles"] = role_payload["roles"]
        res = gsql.revoke_privileges(payload, cookie)
        res.assert_result(state_code, error, message)

        

        
        
