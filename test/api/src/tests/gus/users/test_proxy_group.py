import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.operation.gsql_server import GSQLServer
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestProxyGroup():

    general_data = data.get_yaml_data("gus/loading_data.yml")
    invalid_cookie = general_data.get("invalid_cookie")
    proxy_group_data = data.get_yaml_data("gus/api_proxy_group.yml")
    default_proxy_group_paylaod = proxy_group_data.get("default_proxy_group").get("payload")
    default_delete_group_payload = proxy_group_data.get("default_delete_proxy_group").get("payload")
    update_proxy_group_payload = proxy_group_data.get("update_proxy_group").get("payload")
    post_proxy_group_invalid_paylaod = proxy_group_data.get("post_proxy_group_invalid_payload")
    delete_proxy_group_invalid_payload = proxy_group_data.get("delete_proxy_group_invalid_payload")

    test_invalid_cookie_data = []
    post_proxy_group_invalid_paylaod_data = []
    delete_proxy_group_invalid_payload_data = []

    for case in invalid_cookie:
        test_invalid_cookie_data.append((
            case,
            invalid_cookie.get(case).get("cookie")
        ))

    for case in post_proxy_group_invalid_paylaod:
        post_proxy_group_invalid_paylaod_data.append((
            case,
            post_proxy_group_invalid_paylaod.get(case).get("payload"),
            post_proxy_group_invalid_paylaod.get(case).get("status_code"),
            post_proxy_group_invalid_paylaod.get(case).get("error"),
            post_proxy_group_invalid_paylaod.get(case).get("message")
        ))

    for case in delete_proxy_group_invalid_payload:
        delete_proxy_group_invalid_payload_data.append((
            case, 
            delete_proxy_group_invalid_payload.get(case).get("payload"),
            delete_proxy_group_invalid_payload.get(case).get("status_code"),
            delete_proxy_group_invalid_payload.get(case).get("error"),
            delete_proxy_group_invalid_payload.get(case).get("message")

        ))

    @allure.title("List all proxy group with valid cookies ")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can list all proxy groups info with valid cookies  \n"
        "Description:  Can list all proxy group with valid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_list_proxy_group(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.list_proxy_groups(cookie)
        check_res.assert_no_error()

    @allure.title("List all proxy group with invalid cookies ")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can lis all proxy groups info with invalid cookies  \n"
        "Description:  Unable to list all proxy group with invalid cookies,  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_list_proxy_group_invalid_cookies(self, case, cookie):

        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.list_proxy_groups(cookie)
        check_res.assert_api_no_authorized()
        check_res.assert_error()


    @allure.title("Create proxy group with valid paylaod")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can create a proxy group with valid payload  \n"
        "Description:  Can create proxy group successfully with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_proxy_group(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        payload = self.default_proxy_group_paylaod
        # check if the proxy group exists
        is_exists = gsql.check_if_the_proxy_Group_exist(payload["groupName"], cookie)

        if is_exists:
            Del_payload = self.default_delete_group_payload
            Del_payload["groupNames"][0] = payload["groupName"]
            res = gsql.delete_proxy_group(Del_payload, cookie)
            res.assert_no_error()
        
        check_res = gsql.create_proxy_group(payload ,cookie)
        check_res.assert_result(201, False, message="Successfully created group api_test")

        # check newly proxy group 
        res = gsql.list_proxy_groups(cookie).results
        assert payload["groupName"] in str(res), "Failed to create proxy group, pls check"

    @allure.title("Create proxy group with valid paylaod")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can create a proxy group with valid payload  \n"
        "Description:  Can create proxy group successfully with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_delete_proxy_group(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        delete_payload = self.default_delete_group_payload
        payload = self.default_proxy_group_paylaod
        
        # check if the proxy group exists
        is_exists = gsql.check_if_the_proxy_Group_exist(delete_payload["groupNames"][0], cookie)
        if not is_exists:
            payload["groupName"] = delete_payload["groupNames"][0]
            check_res = gsql.create_proxy_group(payload ,cookie)
            check_res.assert_result(201, False, message="Successfully created group api_test")
        # check newly proxy group 
        res = gsql.delete_proxy_group(delete_payload, cookie)
        res.assert_no_error()

    @allure.title("Update proxy group with valid paylaod")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can update a proxy group with valid payload  \n"
        "Description:  Can update proxy group successfully with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_update_proxy_group(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        payload = self.default_proxy_group_paylaod
        update_payload = self.update_proxy_group_payload
        # check if the proxy group exists
        is_exists = gsql.check_if_the_proxy_Group_exist(payload["groupName"], cookie)

        if  not is_exists:
            check_res = gsql.create_proxy_group(payload ,cookie)
            check_res.assert_result(201, False, message="Successfully created group api_test")
        
        update_payload["name"] = payload["groupName"]
        check_res = gsql.update_proxy_group(update_payload ,cookie)
        check_res.assert_no_error()

    @allure.title("Create proxy group with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can create a proxy group with invalid cookies  \n"
        "Description:  Cannot create proxy group with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_proxy_group_invalid_cookies(self, case, cookie):

        #check the existing local files
        gsql = GSQLServer()
        payload = self.default_proxy_group_paylaod
        payload["groupName"] = case
        
        check_res = gsql.create_proxy_group(payload ,cookie)
        check_res.assert_api_no_authorized()

    @allure.title("Delete proxy group with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can Delete a proxy group with invalid cookies  \n"
        "Description:  Cannot Delete proxy group with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_delete_proxy_group_invalid_cookies(self, case, cookie):

        #check the existing local files
        gsql = GSQLServer()
        delete_payload = self.default_delete_group_payload
        delete_payload["groupNames"][0] = case
        
        check_res = gsql.create_proxy_group(delete_payload ,cookie)
        check_res.assert_api_no_authorized()

    @allure.title("Update proxy group with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can update a proxy group with invalid cookies  \n"
        "Description:  Cannot update proxy group with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_update_proxy_group_invalid_cookies(self, case, cookie):

        #check the existing local files
        gsql = GSQLServer()
        payload = self.update_proxy_group_payload
        payload["name"] = case
        
        check_res = gsql.update_proxy_group(payload ,cookie)
        check_res.assert_api_no_authorized()

    
    @allure.title("Create proxy group with invalid paylaod")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can create a proxy group with invalid payload  \n"
        "Description:  Can create proxy group with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(post_proxy_group_invalid_paylaod_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_proxy_group_invalid_payload(self, case, payload, status_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        # check if the proxy group exists
        is_exists = gsql.check_if_the_proxy_Group_exist(payload["groupName"], cookie)

        if is_exists:
            Del_payload = self.default_delete_group_payload
            Del_payload["groupNames"][0] = payload["groupName"]
            res = gsql.delete_proxy_group(Del_payload, cookie)
            res.assert_no_error()
        
        check_res = gsql.create_proxy_group(payload ,cookie)
        check_res.assert_result(status_code, error, message)


    @allure.title("Delete proxy group with invalid payload")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if delete a proxy group with invalid payload  \n"
        "Description:  Cannot delete proxy group with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(delete_proxy_group_invalid_payload_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_delete_proxy_group_invalid_payload(self, case, payload, status_code, error, message):

        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        
        # delete proxy group 
        res = gsql.delete_proxy_group(payload, cookie)
        res.assert_result(status_code, error, message)
