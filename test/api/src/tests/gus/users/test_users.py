import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.operation.gsql_server import GSQLServer
from src.common.logger import logger
import time
from parameterized import parameterized
import allure

class TestUsers:
    general_data = data.get_yaml_data("gus/loading_data.yml")
    users_data = data.get_yaml_data("gus/api_users.yml")
    default_user = users_data.get("create_users").get("payload")
    post_user_invalid = users_data.get("post_user_invalid_payload")
    invalid_cookie = general_data.get("invalid_cookie")
    delete_secret_payload = users_data.get("delete_secret").get("payload")

    test_invalid_cookie_data = []
    test_post_user_invalid_payload_data = []
    for case in invalid_cookie:
        test_invalid_cookie_data.append((
            case,
            invalid_cookie.get(case).get("cookie")
        ))

    for case in post_user_invalid:
        test_post_user_invalid_payload_data.append((
            case,
            post_user_invalid.get(case).get("payload"),
            post_user_invalid.get(case).get("state_code"),
            post_user_invalid.get(case).get("error"),
            post_user_invalid.get(case).get("message")
        ))

    @allure.title("List all users")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can list all users with valid cookies \n"
        "Description: Can list all users with valid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_list_users(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.list_users(cookie)
        check_res.assert_no_error()

    @allure.title("List all users")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can list all users with invalid cookies \n"
        "Description: Cannot list all users with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_list_users_invalid_cookies(self,case, cookie):
        login = Login()
        result = login.login()

        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.list_users(cookie)
        check_res.assert_result(401, True, 'Authentication failed')

    @allure.title("Create a new user")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can create a new user with valid payload \n"
        "Description: Can create a new user with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_users(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        payload = self.default_user
        if gsql.check_if_user_exist(payload["username"], cookie):
            res = gsql.delete_user(payload["username"], cookie)
            res.assert_no_error()
        check_res = gsql.create_users(payload, cookie)
        check_res.assert_result(201, False, "Successfully created user")

    @allure.title("Create a new user with invalid payload")
    @allure.description(
        "TestType: Negatvie \n" 
        "Target: Check if can create a new user with invalid payload\n"
        "Description: Cannot create a new user with invalid payload\n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_post_user_invalid_payload_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_users_invalid_payload(self, case, payload, state_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        if gsql.check_if_user_exist(payload["username"], cookie):
            res = gsql.delete_user(payload["username"], cookie)
            res.assert_no_error()
        check_res = gsql.create_users(payload, cookie)
        check_res.assert_result(state_code, error, message)

    @allure.title("Change password for the user")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can change password for the user with valid payload \n"
        "Description: Can change password for the user with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_change_password(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        payload = self.default_user
        payload["name"] = "api-test-change-password"
        if not gsql.check_if_user_exist(payload["username"], cookie):
            res = gsql.create_users(payload, cookie)
            res.assert_no_error(201, False)
        
        # check the existing user again
        if not gsql.check_if_user_exist(payload["username"], cookie):
            time.sleep(3)
        update_payload = {"password":"12345"}
        check_res = gsql.change_password(payload["username"], update_payload, cookie)
        check_res.assert_no_error()

    @allure.title("Change password for the user")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can change password for the user with valid payload \n"
        "Description: Can change password for the user with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_delete_users(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        payload = self.default_user
        payload["username"] = "delete_user"
        if not gsql.check_if_user_exist(payload["username"], cookie):
            res = gsql.create_users(payload, cookie)
        check_res = gsql.delete_user(payload["username"], cookie)
        check_res.assert_no_error()

    @allure.title("Create a new secret")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can create a new global secret  \n"
        "Description: Can create a new global secret with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_secrets(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        alias = "api_test"
        delete_payload = self.delete_secret_payload
        delete_payload["secrets"][0] = alias
        if gsql.check_if_secret_exists(alias, cookie):
            res = gsql.delete_secret(delete_payload, cookie)
            res.assert_no_error()
        check_res = gsql.create_secrets(alias, cookie)
        check_res.assert_no_error()

    @allure.title("Create a new secret")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can create a new global secret  \n"
        "Description: Can create a new global secret with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_delete_secrets(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        alias = "delete_secret"
        delete_payload = self.delete_secret_payload
        delete_payload["secrets"][0] = alias
        if not gsql.check_if_secret_exists(alias, cookie):
            res = gsql.create_secrets(alias, cookie)
            res.assert_no_error()
        check_res = gsql.delete_secret(delete_payload, cookie)
        check_res.assert_no_error()
       