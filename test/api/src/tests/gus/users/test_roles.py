import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.operation.gsql_server import GSQLServer
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestRoles:
    general_data = data.get_yaml_data("gus/loading_data.yml")
    roles_data = data.get_yaml_data("gus/api_roles.yml")
    users_data = data.get_yaml_data("gus/api_users.yml")
    invalid_cookie = general_data.get("invalid_cookie")
    post_roles_data = roles_data.get("post_roles")
    post_roles_data_invalid = roles_data.get("post_roles_invalid")
    default_roles = post_roles_data.get("default_roles")
    grant_roles = roles_data.get("grant_roles").get("payload")
    default_user = users_data.get("create_users").get("payload")

    test_invalid_cookie_data = []
    test_post_roles_data = []
    test_post_roles_invalid_data = []

    for case in invalid_cookie:
        test_invalid_cookie_data.append((
            case,
            invalid_cookie.get(case).get("cookie")
        ))

    for case in post_roles_data:
        test_post_roles_data.append((
            case,
            post_roles_data.get(case).get("payload")
        ))

    for case in post_roles_data_invalid:
        test_post_roles_invalid_data.append((
            case,
            post_roles_data_invalid.get(case).get("payload"),
            post_roles_data_invalid.get(case).get("state_code"),
            post_roles_data_invalid.get(case).get("error"),
            post_roles_data_invalid.get(case).get("message")
        ))

    @allure.title("List all roles info")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can list all roles info with invalid cookies \n"
        "Description: Can list all roles info with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_list_all_roles(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.list_all_roles(cookie)
        check_res.assert_no_error()
        assert "superuser" in check_res.results["builtIn"]["global"][2], "Failed to list all roles"

    @allure.title("List all roles with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can list all roles with invalid cookies \n"
        "Description:  Cannot list all roles with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_list_roles_with_invalid_param(self, case, cookie):
        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.list_all_roles(cookie)
        check_res.assert_api_no_authorized()
        check_res.assert_error()

    @allure.title("Create a new role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can create a new role with valid payload \n"
        "Description: Can create a new role with valid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_post_roles_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_roles(self, case, role_payload):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        for role in role_payload["roles"]:
            if gsql.check_if_role_exist(role, cookie):
                res = gsql.delete_a_role(role, cookie)
                res.assert_no_error()
        check_res = gsql.create_new_role(role_payload, cookie)
        check_res.assert_no_error()
        assert role_payload["roles"][0] in check_res.results, "Failed to create new roles"

    @allure.title("Create a new role with invalid payload")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can create a new role with invalid payload \n"
        "Description: Cannot create a new role with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_post_roles_invalid_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_create_roles_invalid_payload(self, case, role_payload, state_code, error, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.create_new_role(role_payload, cookie)
        check_res.assert_result(state_code, error, message)


    @allure.title("Delete a role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can delete a role with valid cookies \n"
        "Description: Can create a role with valid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_delete_roles(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        role_payload = self.default_roles
        role_payload["roles"] = ["delete_role"]
        #check the existing local files
        gsql = GSQLServer()
        for role in role_payload["roles"]:
            if not gsql.check_if_role_exist(role, cookie):
                res = gsql.create_new_role(role_payload, cookie)
                res.assert_no_error()
            res = gsql.delete_a_role(role, cookie)
            res.assert_no_error()


    @allure.title("Delete a role with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can delete the role with invalid cookies \n"
        "Description: Cannot delete the role with invalid cookies \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_delete_roles_invalid_cookies(self, case, cookie):
        login = Login()
        result = login.login()

        role_payload = self.default_roles
        role_payload["roles"] = ["delete_role"]

        #check the existing local files
        gsql = GSQLServer()
        check_res = gsql.delete_a_role(role_payload["roles"][0], cookie)
        check_res.assert_api_no_authorized()

    @allure.title("List privileges for the specified role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can list privileges for the role globalobserver \n"
        "Description: Can list all privileges for the role globalobserver \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_roles_by_rolename(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        role_name = 'globalobserver'
        check_res = gsql.get_roles_by_rolesName(role_name, cookie)
        check_res.assert_no_error()

    @allure.title("List privileges for the specified role with invalid cookies")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can list privileges for the role globalobserver with invalid cookies \n"
        "Description: Cannot list all privileges for the role globalobserver with invalid cookies\n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @parameterized.expand(test_invalid_cookie_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_get_roles_by_rolename_invalid_cookies(self, case, cookie):
        login = Login()
        result = login.login()

        #check the existing local files
        gsql = GSQLServer()
        role_name = 'globalobserver'
        check_res = gsql.get_roles_by_rolesName(role_name, cookie)
        check_res.assert_api_no_authorized()

    @allure.title("Grant user a role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if grant user a role globalobserver \n"
        "Description: Can grant user a role globalobserver\n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_grant_roles(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        role_name = 'globalobserver'
        user = self.default_user
        role_payload = self.grant_roles
        user["username"] = role_payload["users"][0]

        # create the user if not exist
        if not gsql.check_if_user_exist(user["username"], cookie):
            res = gsql.create_users(user, cookie)
        check_res = gsql.grant_roles(role_payload, cookie)
        check_res.assert_no_error()

    @allure.title("Revoke role for the user")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if revoke role for the user  \n"
        "Description: Can revoke role for the user\n"
        "TestDesigner: Jing Yang \n"
        "Date: 2025-03-11 \n"
        "Link: https://graphsql.atlassian.net/browse/APPS-3625"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.gsqlserver
    def test_revoke_roles(self):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()

        #check the existing local files
        gsql = GSQLServer()
        role_name = 'globalobserver'
        user = self.default_user
        role_payload = self.grant_roles
        role_payload["users"] = ["revoke_users"]
        user["username"] = role_payload["users"][0]

        # create the user if not exist
        if not gsql.check_if_user_exist(user["username"], cookie):
            res = gsql.create_users(user, cookie)
        check_res = gsql.grant_roles(role_payload, cookie)
        check_res = gsql.revoke_roles(role_payload, cookie)
        check_res.assert_no_error()
        