import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure
from src.common.logger import logger

from src.operation.design_schema import Design_Schema


class TestLogin(APIBaseCase):

    health_Check_data = data.get_yaml_data("general/health_check.yml")
    get_health_check_data = health_Check_data.get("invalid_get_health_check")

    test_get_health_check = []

    for case in get_health_check_data:
        test_get_health_check.append((
            case,
            get_health_check_data.get(case).get("cookies")
        ))

    @allure.title("Health check for service")
    @allure.description(
        "TestType: Positive \n"
        "Target: Check if the service is active \n"
        "Description: Call ping api and check response \n"
        "TestDesigner: <PERSON> \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.preexec
    @pytest.mark.apihealthcheck
    @pytest.mark.run(order=0.1)
    def test_health_check(self):
        login = Login()
        gus = GUS()
        result = login.login()
        res = gus.get_health_check(result.get_cookies())
        res.assert_no_error()

    @allure.title("Health check for service")
    @allure.description(
        "TestType: Positive \n"
        "Target: Check if  can ping without cookies \n"
        "Description: Call ping api without cookies and it shoudl works \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2755"
    )
    @parameterized.expand(test_get_health_check, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.preexec
    @pytest.mark.apihealthcheck
    @pytest.mark.run(order=0.1)
    def test_health_check_invalid(self, case, cookies):
        gus = GUS()
        res = gus.get_health_check(cookies)
        res.assert_no_error()
