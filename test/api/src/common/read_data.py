import pytest
import yaml
import json
from pathlib import Path
from configparser import ConfigPars<PERSON>
from src.common.logger import logger
import os

class MyConfigParser(ConfigParser):
    # rewrite optionxform function of configparser，figure out the problem of  convert to lowercase letters  automatically in .ini files
    def __init__(self, defaults=None):
        ConfigParser.__init__(self, defaults=defaults)

    def optionxform(self, optionstr):
        return optionstr


class ReadFileData():
    BASE_PATH = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    def __init__(self):
        pass

    def load_yaml(self, file_path):
        #logger.info("load file: {} ......".format(file_path))
        with open(file_path, encoding='utf-8') as f:
            data = yaml.safe_load(f)
        #logger.info("reader data ==>>  {} ".format(data))
        return data

    def load_json(self, file_path):
        logger.info("load file: {} ......".format(file_path))
        with open(file_path, encoding='utf-8') as f:
            data = json.load(f)
        #logger.info("reader data ==>>  {} ".format(data))
        return data

    def load_ini(self, file_path):
        logger.info("load file: {} ......".format(file_path))
        config = MyConfigParser()
        config.read(file_path, encoding="UTF-8")
        data = dict(config._sections)
        logger.info("load file has done")
        logger.info("data content is {}".format(data))
        return data

    def load_txt(self, file_path):
        logger.info("load file: {} ......".format(file_path))
        f = open(file_path, "r")
        file = f.readlines()
        data = []
        for each in file:
            each = each.strip('\n')
            data.append(each)
        logger.info("data content is {}".format(data))
        return data
    
    def get_yaml_data(self, yaml_file_name):
        try:
            data_file_path = os.path.join(self.BASE_PATH, "../data", yaml_file_name)
            yaml_data = data.load_yaml(data_file_path)
        except Exception as ex:
            pytest.skip(str(ex))
        else:
            return yaml_data


    def get_txt_data(self, txt_file_name):
        try:
            data_file_path = os.path.join(self.BASE_PATH, "../data", txt_file_name)
            txt_data = data.load_txt(data_file_path)
        except Exception as ex:
            pytest.skip(str(ex))
        else:
            return txt_data
        
    def convert_data_to_json(self, data):
        try:
            # Convert Python dictionary to JSON
            json_data = json.loads(data)
        except Exception as ex:
            pytest.skip(str(ex))
        else:
            return json_data

    def read_test_data(self, file):
        file = os.path.join(self.BASE_PATH,  "../data", file)
        # LOGGER.info("file path:" + file)
        if Path(file).exists():
            with open(file) as f:
                raw_data = json.load(f)
            return raw_data
        else:
            return None


data = ReadFileData()
