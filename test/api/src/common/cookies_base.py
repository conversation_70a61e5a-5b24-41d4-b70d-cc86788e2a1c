from src.common.read_data import data
from src.common.update_data import updateData

class CookiesBase:
    def __init__(self, requests):
        self.session=requests.session
        self.status_code=200
        self.error_status=False
        self.message=""

    def get_cookies(self, cookie_name="TigerGraphApp"):
        cookies = "{}={}".format(cookie_name, self.session.cookies[cookie_name])
        if not cookies:
            self.status_code=400
            self.error_status=True
            self.message="Failed to get cloud tools cookie, please check cluster status"
        return cookies

    def assert_result(self, status_code, error_status, message):
        assert self.status_code==status_code, "the returned status code is: {}".format(self.status_code)
        assert self.error_status==error_status, "the returned error is: {}".format(self.error_status)
        assert message in self.message, "the returned message is: {}".format(self.message)
