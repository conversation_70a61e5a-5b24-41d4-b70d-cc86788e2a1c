from src.common.logger import logger
import json as complexjson

class ResultBase:
    def __init__(self, response):
        self.status_code = response.status_code
        self.response = response.json()
        self.headers = response.headers
        if 'message' in self.response:
            self.msg = self.response["message"]
        if 'results' in self.response:
            self.results = self.response["results"]
            self.response_log(self.msg, complexjson.dumps(self.results)[:200])
        #adapt the return format for api of v2
        if 'data' in self.response:
            self.v2data = self.response["data"]
            logger.info("response results ==>> {}".format(complexjson.dumps(self.v2data, indent=4, ensure_ascii=False)[:500]))
        if 'errors' in self.response:
            self.v2errors = self.response["errors"]
            logger.info("response results ==>> {}".format(complexjson.dumps(self.v2errors, indent=4, ensure_ascii=False)[:500]))
        
    def response_log(self, msg=None, res=None, **kwargs):
        logger.info("response status ==>> {}".format(self.status_code))
        logger.info("response message ==>> {}".format(complexjson.dumps(msg, indent=4, ensure_ascii=False)))
        logger.info("response results ==>> {}".format(complexjson.dumps(res, indent=4, ensure_ascii=False)))
        

    def is_successful(self):
        return self.status_code >= 200 and self.status_code < 300

    def json(self):
        return self.json()

    def text(self):
        return self.text
    
    def error(self):
        return self.response["error"]
    
    def message(self):
        return self.response["message"]
    
    def get_cookies(self):
        if "Set-Cookie" not in self.headers:
            # Key "Set-Cookie" doesn't exist in headers
            return ""
        else:
            return self.headers["Set-Cookie"].split(';')[0]
    
    def assert_result(self, state_code=200, error_state=False, message=""):
        assert self.status_code==state_code, "the returned status code is: {}".format(self.status_code)
        assert self.error()==error_state, "the returned error is: {}".format(self.error()) 
        assert message in self.message(), "the returned message is: {}".format(self.message()) 

    def assert_v2_result(self, state_code=200, errors="", data=""):
        assert self.status_code==state_code, "the returned status code is: {}".format(self.status_code)
        if 'data' in self.response:
            if data == "log_file_not_empty":
                assert self.v2data.get("Log").get("Content")[0].get("Data") != "", "the returned data is: {}".format(str(self.v2data)) 
            else:
                assert data in str(self.v2data), "the returned data is: {}".format(str(self.v2data)) 
        if 'errors' in self.response:
            assert errors in str(self.v2errors), "the returned errors is: {}".format(str(self.v2errors)) 
    
    def assert_v2_result_error(self, state_code=200, errors=""):
        assert self.status_code==state_code, "the returned status code is: {}".format(self.status_code)
        assert errors in str(self.v2errors), "the returned errors is: {}".format(str(self.v2errors)) 

    def assert_no_error(self, state_code=200, error_state=False):
        assert self.status_code==state_code, "the returned status code is: {}".format(self.status_code)
        assert self.error()==error_state, "the returned error is: {}".format(self.error()) 

    def assert_status(self, state_code=200):
        assert self.status_code==state_code, "the returned status code is: {}".format(self.status_code)

    def assert_error(self, state_code=401, error_state=True):
        assert self.status_code==state_code, "the returned status code is: {}".format(self.status_code)
        assert self.error()==error_state, "the returned error is: {}".format(self.error()) 

    def assert_api_no_authorized(self):
        self.assert_result(state_code=401, error_state=True, message="You are not authorized to use this API.")

    def assert_logout_success(self):
        self.assert_result(state_code=200, error_state=False, message="Successfully logged out.")
    
    def assert_keys(self, key):
        keys = key.split(",")
        for config in keys:
            assert config in self.results, "the returned result is: {}".format(self.results) 

    def assert_cert_key(self):
        cert_string = '-----BEGIN CERTIFICATE-----'
        key_string = '-----BEGIN PRIVATE KEY-----'
        assert cert_string in self.results["cert"], "the returned result is: {}".format(self.results) 
        assert key_string in self.results["privateKey"], "the returned result is: {}".format(self.results) 
      