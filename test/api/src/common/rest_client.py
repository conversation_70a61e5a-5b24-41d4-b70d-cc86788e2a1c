import requests
from src.common.logger import logger
import json as complexjson


class RestClient():

    def __init__(self, api_root_url):
        self.api_root_url = api_root_url
        self.session = requests.session()

    def get(self, url, **kwargs):
        return self.request(url, "GET", **kwargs)

    def post(self, url, data=None, json=None, **kwargs):
        return self.request(url, "POST", data, json, **kwargs)

    def put(self, url, data=None, **kwargs):
        return self.request(url, "PUT", data, **kwargs)

    def delete(self, url, **kwargs):
        return self.request(url, "DELETE", **kwargs)

    def delete_with_payload(self, url, payload, **kwargs):
        return self.request(url, "DELETE-data", data=payload, **kwargs)

    def patch(self, url, data=None, **kwargs):
        return self.request(url, "PATCH", data, **kwargs)

    def request(self, url, method, data=None, json=None, **kwargs):
        url = self.api_root_url + url
        headers = dict(**kwargs).get("headers")
        params = dict(**kwargs).get("params")
        self.request_log(url, method, data, params, headers)
        if method == "GET":
            # logger.info(self.session.__dict__)
            # return self.seself.session.getssion.get(url, **kwargs)
            return requests.request("GET", url, **kwargs)
        if method == "POST":
            # return self.session.post(url, data, json, **kwargs)
            return requests.post(url, data, json, **kwargs)
        if method == "PUT":
            if json:
                # Function of PUT and  PATCH doesn't support the way to use json attribute directly
                # so we should use "data" to transfer json data
                data = complexjson.dumps(json)
            # return self.session.put(url, data, **kwargs)
            return requests.put(url, data, **kwargs)
        if method == "DELETE":
            # logger.info(self.session.__dict__)
            # return self.session.delete(url, **kwargs)
            return requests.request("DELETE", url, **kwargs)
        if method == "DELETE-data":
            return requests.delete(url, data=data, **kwargs)
        if method == "PATCH":
            if json:
                data = complexjson.dumps(json)
            # return self.session.patch(url, data, **kwargs)
            return requests.patch(url, data, **kwargs)
    
    def request_session(self, url, method, data=None, json=None, **kwargs):
        url = self.api_root_url + url
        headers = dict(**kwargs).get("headers")
        params = dict(**kwargs).get("params")
        self.request_log(url, data, params, headers)
        if method == "GET":
            logger.info(self.session.__dict__)
            return self.session.get(url, **kwargs)
        if method == "POST":
            return self.session.post(url, data, json, **kwargs)
        if method == "PUT":
            if json:
                # Function of PUT and  PATCH doesn't support the way to use json attribute directly
                # so we should use "data" to transfer json data
                data = complexjson.dumps(json)
            return self.session.put(url, data, **kwargs)
        if method == "DELETE":
            logger.info(self.session.__dict__)
            return self.session.delete(url, **kwargs)
        if method == "PATCH":
            if json:
                data = complexjson.dumps(json)
            return self.session.patch(url, data, **kwargs)

    def request_log(self, url=None, method=None, data=None, params=None, headers=None,  **kwargs):
        logger.info("request URL ==>> {}".format(complexjson.dumps(url, indent=4, ensure_ascii=False)))
        logger.info("request method ==>> {}".format(complexjson.dumps(method, indent=4, ensure_ascii=False)))
        logger.info("request header ==>> {}".format(complexjson.dumps(headers, indent=4, ensure_ascii=False)))
        logger.info("request params ==>> {}".format(complexjson.dumps(params, indent=4, ensure_ascii=False)))
        try:
            json_data = complexjson.dumps(data, ensure_ascii=False)
        except Exception as e:
            print(f"Serialization failed: {e}")
            json_data = str(data)
        logger.info("request data ==>> {}".format(json_data))
