import json
import yaml
from configparser import ConfigParser
from src.common.logger import logger
import os


class UpdateFileDate():

    BASE_PATH = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))

    def __init__(self):
        pass

    def update_json_signle_data(self, key, value, file_path):
        # open JSON and read data
        with open(file_path, encoding='utf-8') as f:
            data = json.load(f)

        # update key and value
        data[key] = value
        logger.info('after data=' + str(data))

        # rewrite to file
        with open(file_path, "w") as f:
            json.dump(data, f)

    def update_json_data(self, jsonObject, filepath):
        data_file_path = os.path.join(self.BASE_PATH, "../data", filepath)
        # open JSON and read data
        with open(data_file_path, encoding='utf-8') as f:
            data = json.load(f)

        # rewrite to file
        with open(data_file_path, "w") as f:
            json.dump(jsonObject, f)


    def update_yml_data(self, key, value, file_path):
        data_file_path = os.path.join(self.BASE_PATH, "../data", file_path)
        # open YAML and read data
        with open(data_file_path, encoding='utf-8') as f:
            data = yaml.safe_load(f)

        data[key] = value
        logger.info('after data=' + str(data))

        # rewrite to file
        with open(data_file_path, "w") as f:
            yaml.dump(data, f)

updateData = UpdateFileDate()
