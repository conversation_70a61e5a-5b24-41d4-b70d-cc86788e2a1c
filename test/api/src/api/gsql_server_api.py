import os
from pathlib import Path
from src.common.logger import logger
from src.common.rest_client import RestClient
from src.common.read_data import data
from src.common.result_base import ResultBase
from src.operation.cloud import Cloud
from utils.data_util.data_resolver import read_test_data

CONFIG_DATA = read_test_data(file="tools_test_data.json")
domain = CONFIG_DATA.get("domain")
test_env = CONFIG_DATA.get("test_env")
if 'tgcloud' in test_env:
    TEST_ENV = domain
else:
    TEST_ENV = test_env


class GSQLServer(RestClient):
    def __init__(self, api_root_url, **kwargs):
        super(GSQLServer, self).__init__(api_root_url, **kwargs)
        #logger.info(f"API root url is {api_root_url}")

    def get_schema_info(self, graph_name, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/schema/graphs/{}".format(graph_name), **kwargs)

    def get_type_names(self,**kwargs):
        return self.get("/api/gsql-server/gsql/v1/internal/info?type=type-names", **kwargs)

    def post_schema(self, graph_name, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/schema/graphs?graphName={}".format(graph_name), payload, **kwargs)

    def delete_schema(self, graph_name, **kwargs):
        return self.delete("/api/gsql-server/gsql/v1/schema/graphs/{}?cascade=true".format(graph_name), **kwargs)

    def change_schema(self, graph_name, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/schema/change?graph_name={}".format(graph_name), payload, **kwargs )

    # Queries related APIs
    def query_codecheck(self, graph_name, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/internal/check/query?graph={}".format(graph_name), payload, **kwargs)

    def post_interpret_query(self, graph_name, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/queries/interpret?graph={}".format(graph_name), payload, **kwargs)

    def get_queries_info(self, graph_name, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/queries/interpret/query/info?graph={}".format(graph_name), payload, **kwargs)

    # Auth related APIs
    def get_authorization_info(self, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/auth/simple", **kwargs)

    def get_oidc_auth(self, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/oidc/authnrequest", **kwargs)

    def get_saml_auth(self, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/saml/authnrequest", **kwargs)
    
    # RDBMs related APIs
    def post_rdbms(self, type, server, port, database, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/rdbms/meta?type={}&server={}&port={}&database={}".format(type, server,port, database), payload, **kwargs)

    def post_rdbm_dumps(self, type, server, port, database, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/rdbms/dump?type={}&server={}&port={}&database={}".format(type, server,port, database), payload, **kwargs)

    # Proxy Group related APIs
    def get_proxy_group(self, parameter, **kwargs):
        return self.get("/api/gsql-server/gsql/scim/v2/Groups?{}".format(parameter), **kwargs)

    def post_proxy_group(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/scim/v2/Groups?gsqlFormat=true", payload, **kwargs)

    def delete_proxy_group(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/scim/v2/Groups?gsqlFormat=true&action=delete", payload, **kwargs)

    def put_proxy_group(self, payload, **kwargs):
        return self.patch("/api/gsql-server/gsql/scim/v2/Groups/api_test_delete?gsqlFormat=true", payload, **kwargs)

    # Privileges APIs
    def get_role_privilege_info(self, roles, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/privileges?role={}".format(roles), **kwargs)

    def post_privilege(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/privileges/grant", payload, **kwargs)
        
    def revoke_privilege(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/privileges/revoke", payload, **kwargs)

    # Users APIs
    def post_roles(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/roles", payload, **kwargs)  

    def get_roles(self, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/roles", **kwargs)

    def delete_roles(self, role_name, **kwargs):
        return self.delete("/api/gsql-server/gsql/v1/roles/{}".format(role_name), **kwargs)

    def get_roles_by_rolename(self, role_name, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/privileges?roles={}".format(role_name), **kwargs)

    def grant_roles(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/roles/grant", payload, **kwargs)

    def revoke_roles(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/roles/revoke", payload, **kwargs)

    def check_file_policy(self, path, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/internal/check/file-policy?path={}".format(path), **kwargs)

    def get_users(self, **kwargs):
        return self.get("/api/gsql-server/gsql/scim/v2/Users?gsqlFormat=true", **kwargs)

    def post_users(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/scim/v2/Users?gsqlFormat=true", payload, **kwargs)

    def put_users(self, user, payload, **kwargs):
        return self.put("/api/gsql-server/gsql/scim/v2/Users/<USER>".format(user), payload, **kwargs)

    def delete_user(self, user, **kwargs):
        return self.delete("/api/gsql-server/gsql/scim/v2/Users/<USER>".format(user), **kwargs)

    def create_secret(self, alias, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/secrets?alias={}".format(alias), **kwargs)

    def delete_secret(self, payload, **kwargs):
        return self.delete_with_payload("/api/gsql-server/gsql/v1/secrets", payload, **kwargs)

    # Data source related APIs
    def get_udt_tuples(self, graph_name, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/udt/tuples?graph={}".format(graph_name), **kwargs)

    def get_token_functions(self, **kwargs):
        return self.get("/api/gsql-server/gsql/v1/udt/token-functions", **kwargs)

    def get_sample_data(self, payload, **kwargs):
        return self.post("/api/gsql-server/gsql/v1/sample-data", payload, **kwargs)


gsqlserver=GSQLServer(TEST_ENV)