import os
import requests
from pathlib import Path
from src.common.logger import logger
from src.common.rest_client import RestClient
from src.common.read_data import data
from src.common.result_base import ResultBase
from utils.data_util.data_resolver import read_test_data

CONFIG_DATA = read_test_data(file="tools_test_data.json")
TEST_ENV = CONFIG_DATA.get("test_env")
AUTH_CONFIG_DATA = data.get_yaml_data("general/login_data.yml")
AUTH_DATA = AUTH_CONFIG_DATA.get("cloud_login_data_dev")
AUTH_TEST_ENV = "https://" + AUTH_DATA.get("authServerName")

class CloudAuth(RestClient):

    def __init__(self, api_root_url, **kwargs):
        super(CloudAuth, self).__init__(api_root_url, **kwargs)
        #logger.info(f"API root url is {api_root_url}")
    
    def get_state(self, client_id, init_state, init_nonce, code_challenge, client, authServerName=AUTH_TEST_ENV,  serverName=TEST_ENV, **kwargs):
        return self.request_session('/authorize?audience={}/api/v2&client_id={}&redirect_uri={}&_reactName=onClick&_targetInst=null&type=click&nativeEvent=%5Bobject%20PointerEvent%5D&target=%5Bobject%20HTMLButtonElement%5D&currentTarget=%5Bobject%20HTMLButtonElement%5D&eventPhase=3&bubbles=true&cancelable=true&timeStamp=38663.89999997616&defaultPrevented=false&isTrusted=true&view=%5Bobject%20Window%5D&detail=1&screenX=314&screenY=538&clientX=349&clientY=484&pageX=349&pageY=484&ctrlKey=false&shiftKey=false&altKey=false&metaKey=false&getModifierState=function%20Xo(a)%7Bvar%20s%3Dthis.nativeEvent%3Breturn%20s.getModifierState%3Fs.getModifierState(a)%3A!!(a%3Dwo%5Ba%5D)%26%26!!s%5Ba%5D%7D&button=0&buttons=0&relatedTarget=null&movementX=0&movementY=0&isDefaultPrevented=function%20kn()%7Breturn!1%7D&isPropagationStopped=function%20kn()%7Breturn!1%7D&scope=openid%20profile%20email%20offline_access&response_type=code&response_mode=query&state={}%3D%3D&nonce={}%3D%3D&code_challenge={}&code_challenge_method=S256&auth0Client={}%3D'.format(authServerName, client_id, serverName, init_state, init_nonce, code_challenge, client), "GET", **kwargs)

    def get_auth_cookie(self, state, **kwargs):
        return self.request_session("/u/organization?state={}".format(state), "GET", **kwargs)

    def check_org_state(self, state, json, **kwargs):
        return self.request_session("/u/organization?state={}".format(state), "POST", json, **kwargs)
    
    def get_cloud_cookie(self, state, **kwargs):
        return self.request_session("/u/login?state={}".format(state),"GET", **kwargs)

    def login_user(self, state, json, **kwargs):
        return self.request_session("/u/login?state={}".format(state),"POST", json, **kwargs)

    def get_login_code(self, state, **kwargs):
        return self.request_session("/authorize/resume?state={}".format(state),"GET", **kwargs)

    def get_id_token(self, json, **kwargs):
        return self.request_session("/oauth/token","POST", json, **kwargs)

    def get_autho0_authorize(self, url, **kwargs):
        return self.request_session(url, **kwargs)


class Cloud(RestClient):
    def __init__(self, api_root_url, **kwargs):
        super(Cloud, self).__init__(api_root_url, **kwargs)
        #logger.info(f"API root url is {api_root_url}")

    def get_cloud_cookie(self, code, init_state, **kwargs):
        return self.get("?code={}&state={}%3D%3D".format(code, init_state), **kwargs)

    def get_cluster_info(self, **kwargs):
        return self.get("api/solution", **kwargs)

class CloudTools(RestClient):
    def login_oidc_user(self, **kwargs):
        return self.request_session("/api/gsql-server/gsql/oidc/authnrequest", "GET", **kwargs)

    def login_callback(self, form_data, **kwargs):
        return self.request_session("/api/auth/oidc/callback", "POST", form_data, **kwargs)


cloudAuth = CloudAuth(AUTH_TEST_ENV)
cloud = Cloud(TEST_ENV)