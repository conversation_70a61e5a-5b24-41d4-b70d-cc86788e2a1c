[2023-11-03 14:49:29,090][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 14:49:29,097][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 14:58:55,237][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 14:58:55,241][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 14:58:55,717][design_schema.py 63][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 14:59:36,938][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 14:59:36,942][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 14:59:37,413][design_schema.py 63][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 15:03:02,375][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 15:03:02,379][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 15:03:02,827][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 15:03:34,025][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 15:03:34,028][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 15:03:34,452][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 15:05:55,766][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 15:05:55,770][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 15:05:56,203][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 15:06:19,157][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 15:06:19,161][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 15:06:19,587][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 15:06:19,587][design_schema.py 66][INFO]: URL: http://************:14240/api/system/import
[2023-11-03 15:06:19,591][design_schema.py 90][INFO]: header: {'Cookie': '', 'Content-Type': 'multipart/form-data', 'Content-type': 'multipart/form-data; boundary=wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'}
[2023-11-03 15:06:21,950][design_schema.py 92][INFO]: resp: {"error":true,"message":"You are not authorized to use this API.","results":null}
[2023-11-03 15:06:21,952][result_base.py 23][INFO]: response status ==>> 401
[2023-11-03 15:06:21,952][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2023-11-03 15:06:21,952][result_base.py 25][INFO]: response results ==>> "null"
[2023-11-03 15:06:54,342][tools_api.py 23][INFO]: API root url is 
[2023-11-03 15:06:54,345][gsql_server_api.py 22][INFO]: API root url is 
[2023-11-03 15:06:54,777][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 15:06:54,777][cloud.py 58][INFO]: Try to get org state
[2023-11-03 15:06:54,777][rest_client.py 81][INFO]: request URL ==>> "https://auth.tgcloud-dev.com/authorize?audience=https://auth.tgcloud-dev.com/api/v2&client_id=PBBtD7unto4oYb0eNc6KuJbUNyZQXzka&redirect_uri=https://tgcloud-dev.com&_reactName=onClick&_targetInst=null&type=click&nativeEvent=%5Bobject%20PointerEvent%5D&target=%5Bobject%20HTMLButtonElement%5D&currentTarget=%5Bobject%20HTMLButtonElement%5D&eventPhase=3&bubbles=true&cancelable=true&timeStamp=38663.89999997616&defaultPrevented=false&isTrusted=true&view=%5Bobject%20Window%5D&detail=1&screenX=314&screenY=538&clientX=349&clientY=484&pageX=349&pageY=484&ctrlKey=false&shiftKey=false&altKey=false&metaKey=false&getModifierState=function%20Xo(a)%7Bvar%20s%3Dthis.nativeEvent%3Breturn%20s.getModifierState%3Fs.getModifierState(a)%3A!!(a%3Dwo%5Ba%5D)%26%26!!s%5Ba%5D%7D&button=0&buttons=0&relatedTarget=null&movementX=0&movementY=0&isDefaultPrevented=function%20kn()%7Breturn!1%7D&isPropagationStopped=function%20kn()%7Breturn!1%7D&scope=openid%20profile%20email%20offline_access&response_type=code&response_mode=query&state=Ny02eUNjYmtxandSaUpwYUxXNmpoRzBMY1Y3Smkzc0dZQUlrSEt4RE5VLg%3D%3D&nonce=blRrUy1hdmF2UzgxV1lZMmZMSUZCUzNwWkRkUmtieGF5ZmVzaHVQT09uMA%3D%3D&code_challenge=KJP8QWvzESsCMa4jdBliNUNfwyEPz3IZJu6TbgO49ds&code_challenge_method=S256&auth0Client=eyJuYW1lIjoiYXV0aDAtcmVhY3QiLCJ2ZXJzaW9uIjoiMS4xMC4wIn0%3D"
[2023-11-03 15:06:54,777][rest_client.py 82][INFO]: request method ==>> null
[2023-11-03 15:06:54,777][rest_client.py 83][INFO]: request header ==>> null
[2023-11-03 15:06:54,777][rest_client.py 84][INFO]: request params ==>> {
    "Content-Type": ""
}
[2023-11-03 15:06:54,777][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:06:54,777][rest_client.py 62][INFO]: {'headers': {'User-Agent': 'python-requests/2.28.1', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive'}, 'auth': None, 'proxies': {}, 'hooks': {'response': []}, 'params': {}, 'stream': False, 'verify': True, 'cert': None, 'max_redirects': 30, 'trust_env': True, 'cookies': <RequestsCookieJar[]>, 'adapters': OrderedDict([('https://', <requests.adapters.HTTPAdapter object at 0x10737c490>), ('http://', <requests.adapters.HTTPAdapter object at 0x10737c2b0>)])}
[2023-11-03 15:06:55,988][cloud.py 66][INFO]: The temp state is hKFo2SBScUEtS1JOYlJidk9WMkM2VTZkWUw0d0trQ3Jyd0pBT6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE1PclRBOVlFbVlNbXM5cDA5OFZnblRKMERvOXpVTmlno2NpZNkgUEJCdEQ3dW50bzRvWWIwZU5jNkt1SmJVTnlaUVh6a2E
[2023-11-03 15:06:55,989][rest_client.py 81][INFO]: request URL ==>> "https://auth.tgcloud-dev.com/u/organization?state=hKFo2SBScUEtS1JOYlJidk9WMkM2VTZkWUw0d0trQ3Jyd0pBT6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE1PclRBOVlFbVlNbXM5cDA5OFZnblRKMERvOXpVTmlno2NpZNkgUEJCdEQ3dW50bzRvWWIwZU5jNkt1SmJVTnlaUVh6a2E"
[2023-11-03 15:06:55,989][rest_client.py 82][INFO]: request method ==>> null
[2023-11-03 15:06:55,989][rest_client.py 83][INFO]: request header ==>> null
[2023-11-03 15:06:55,989][rest_client.py 84][INFO]: request params ==>> {
    "Content-Type": ""
}
[2023-11-03 15:06:55,989][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:06:55,989][rest_client.py 62][INFO]: {'headers': {'User-Agent': 'python-requests/2.28.1', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive'}, 'auth': None, 'proxies': {}, 'hooks': {'response': []}, 'params': {}, 'stream': False, 'verify': True, 'cert': None, 'max_redirects': 30, 'trust_env': True, 'cookies': <RequestsCookieJar[Cookie(version=0, name='auth0', value='s%3Av1.gadzZXNzaW9ugqZoYW5kbGXEQOnOfxSMNADGNcsjxvfZGPqDR1t0sfqBY6977GVLmqt6d-o0BOXFWc0jDJza0me4P4a4faiGysIFE62I98FXVeqmY29va2llg6dleHBpcmVz1_8lboUAZUiQkK5vcmlnaW5hbE1heEFnZc4PcxQAqHNhbWVTaXRlpG5vbmU.BqXKFQxPJ08HjsD9t9DgbnlIHrlIu1zEKaI7n3ViGIo', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1699254416, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None, 'SameSite': 'None'}, rfc2109=False), Cookie(version=0, name='auth0_compat', value='s%3Av1.gadzZXNzaW9ugqZoYW5kbGXEQOnOfxSMNADGNcsjxvfZGPqDR1t0sfqBY6977GVLmqt6d-o0BOXFWc0jDJza0me4P4a4faiGysIFE62I98FXVeqmY29va2llg6dleHBpcmVz1_8lboUAZUiQkK5vcmlnaW5hbE1heEFnZc4PcxQAqHNhbWVTaXRlpG5vbmU.BqXKFQxPJ08HjsD9t9DgbnlIHrlIu1zEKaI7n3ViGIo', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1699254416, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='did', value='s%3Av0%3A92c95a90-7a17-11ee-a1bd-5be0fd3b97e6.rHywmxPHSFFZngkLTc0tlmzh6aC%2FOucACBjVd6eWjDY', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1730552815, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None, 'SameSite': 'None'}, rfc2109=False), Cookie(version=0, name='did_compat', value='s%3Av0%3A92c95a90-7a17-11ee-a1bd-5be0fd3b97e6.rHywmxPHSFFZngkLTc0tlmzh6aC%2FOucACBjVd6eWjDY', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1730552815, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False)]>, 'adapters': OrderedDict([('https://', <requests.adapters.HTTPAdapter object at 0x10737c490>), ('http://', <requests.adapters.HTTPAdapter object at 0x10737c2b0>)])}
[2023-11-03 15:06:56,522][rest_client.py 81][INFO]: request URL ==>> "https://auth.tgcloud-dev.com/u/organization?state=hKFo2SBScUEtS1JOYlJidk9WMkM2VTZkWUw0d0trQ3Jyd0pBT6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE1PclRBOVlFbVlNbXM5cDA5OFZnblRKMERvOXpVTmlno2NpZNkgUEJCdEQ3dW50bzRvWWIwZU5jNkt1SmJVTnlaUVh6a2E"
[2023-11-03 15:06:56,523][rest_client.py 82][INFO]: request method ==>> "state=hKFo2SBScUEtS1JOYlJidk9WMkM2VTZkWUw0d0trQ3Jyd0pBT6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE1PclRBOVlFbVlNbXM5cDA5OFZnblRKMERvOXpVTmlno2NpZNkgUEJCdEQ3dW50bzRvWWIwZU5jNkt1SmJVTnlaUVh6a2E&organizationName=cloud-app-org&action=default"
[2023-11-03 15:06:56,523][rest_client.py 83][INFO]: request header ==>> null
[2023-11-03 15:06:56,523][rest_client.py 84][INFO]: request params ==>> {
    "Content-Type": "application/x-www-form-urlencoded"
}
[2023-11-03 15:06:56,523][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:06:56,908][rest_client.py 81][INFO]: request URL ==>> "https://auth.tgcloud-dev.com/u/login?state=hKFo2SBScUEtS1JOYlJidk9WMkM2VTZkWUw0d0trQ3Jyd0pBT6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE1PclRBOVlFbVlNbXM5cDA5OFZnblRKMERvOXpVTmlno2NpZNkgUEJCdEQ3dW50bzRvWWIwZU5jNkt1SmJVTnlaUVh6a2E"
[2023-11-03 15:06:56,909][rest_client.py 82][INFO]: request method ==>> null
[2023-11-03 15:06:56,909][rest_client.py 83][INFO]: request header ==>> null
[2023-11-03 15:06:56,909][rest_client.py 84][INFO]: request params ==>> {
    "Content-Type": "application/x-www-form-urlencoded"
}
[2023-11-03 15:06:56,909][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:06:56,909][rest_client.py 62][INFO]: {'headers': {'User-Agent': 'python-requests/2.28.1', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive'}, 'auth': None, 'proxies': {}, 'hooks': {'response': []}, 'params': {}, 'stream': False, 'verify': True, 'cert': None, 'max_redirects': 30, 'trust_env': True, 'cookies': <RequestsCookieJar[Cookie(version=0, name='auth0', value='s%3Av1.gadzZXNzaW9ugqZoYW5kbGXEQOnOfxSMNADGNcsjxvfZGPqDR1t0sfqBY6977GVLmqt6d-o0BOXFWc0jDJza0me4P4a4faiGysIFE62I98FXVeqmY29va2llg6dleHBpcmVz1_8lboUAZUiQkK5vcmlnaW5hbE1heEFnZc4PcxQAqHNhbWVTaXRlpG5vbmU.BqXKFQxPJ08HjsD9t9DgbnlIHrlIu1zEKaI7n3ViGIo', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1699254416, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None, 'SameSite': 'None'}, rfc2109=False), Cookie(version=0, name='auth0_compat', value='s%3Av1.gadzZXNzaW9ugqZoYW5kbGXEQOnOfxSMNADGNcsjxvfZGPqDR1t0sfqBY6977GVLmqt6d-o0BOXFWc0jDJza0me4P4a4faiGysIFE62I98FXVeqmY29va2llg6dleHBpcmVz1_8lboUAZUiQkK5vcmlnaW5hbE1heEFnZc4PcxQAqHNhbWVTaXRlpG5vbmU.BqXKFQxPJ08HjsD9t9DgbnlIHrlIu1zEKaI7n3ViGIo', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1699254416, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='did', value='s%3Av0%3A92c95a90-7a17-11ee-a1bd-5be0fd3b97e6.rHywmxPHSFFZngkLTc0tlmzh6aC%2FOucACBjVd6eWjDY', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1730552815, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None, 'SameSite': 'None'}, rfc2109=False), Cookie(version=0, name='did_compat', value='s%3Av0%3A92c95a90-7a17-11ee-a1bd-5be0fd3b97e6.rHywmxPHSFFZngkLTc0tlmzh6aC%2FOucACBjVd6eWjDY', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1730552815, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False)]>, 'adapters': OrderedDict([('https://', <requests.adapters.HTTPAdapter object at 0x10737c490>), ('http://', <requests.adapters.HTTPAdapter object at 0x10737c2b0>)])}
[2023-11-03 15:06:57,437][rest_client.py 81][INFO]: request URL ==>> "https://auth.tgcloud-dev.com/u/login?state=hKFo2SBScUEtS1JOYlJidk9WMkM2VTZkWUw0d0trQ3Jyd0pBT6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE1PclRBOVlFbVlNbXM5cDA5OFZnblRKMERvOXpVTmlno2NpZNkgUEJCdEQ3dW50bzRvWWIwZU5jNkt1SmJVTnlaUVh6a2E"
[2023-11-03 15:06:57,438][rest_client.py 82][INFO]: request method ==>> "state=hKFo2SBScUEtS1JOYlJidk9WMkM2VTZkWUw0d0trQ3Jyd0pBT6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIE1PclRBOVlFbVlNbXM5cDA5OFZnblRKMERvOXpVTmlno2NpZNkgUEJCdEQ3dW50bzRvWWIwZU5jNkt1SmJVTnlaUVh6a2E&username=qe%40tigergraph.com&password=Cust12341234&action=default"
[2023-11-03 15:06:57,438][rest_client.py 83][INFO]: request header ==>> null
[2023-11-03 15:06:57,439][rest_client.py 84][INFO]: request params ==>> {
    "Content-Type": "application/x-www-form-urlencoded"
}
[2023-11-03 15:06:57,439][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:07:02,967][rest_client.py 81][INFO]: request URL ==>> "https://auth.tgcloud-dev.com/authorize/resume?state=MOrTA9YEmYMms9p098VgnTJ0Do9zUNig"
[2023-11-03 15:07:02,970][rest_client.py 82][INFO]: request method ==>> null
[2023-11-03 15:07:02,970][rest_client.py 83][INFO]: request header ==>> null
[2023-11-03 15:07:02,970][rest_client.py 84][INFO]: request params ==>> {
    "Content-Type": "application/x-www-form-urlencoded"
}
[2023-11-03 15:07:02,971][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:07:02,971][rest_client.py 62][INFO]: {'headers': {'User-Agent': 'python-requests/2.28.1', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive'}, 'auth': None, 'proxies': {}, 'hooks': {'response': []}, 'params': {}, 'stream': False, 'verify': True, 'cert': None, 'max_redirects': 30, 'trust_env': True, 'cookies': <RequestsCookieJar[Cookie(version=0, name='auth0', value='s%3ABlgrh0uY-wmzZG0zYFQ7hadnGCSwvaIF.BGBr5ayPNBlhrs2NnzdOdtH8QP0EWkHCiUZ8DX7bCzc', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1699254418, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None, 'SameSite': 'None'}, rfc2109=False), Cookie(version=0, name='auth0_compat', value='s%3ABlgrh0uY-wmzZG0zYFQ7hadnGCSwvaIF.BGBr5ayPNBlhrs2NnzdOdtH8QP0EWkHCiUZ8DX7bCzc', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1699254418, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False), Cookie(version=0, name='did', value='s%3Av0%3A92c95a90-7a17-11ee-a1bd-5be0fd3b97e6%3A3ba5fad0a74e57d4e23d9c63e2e6e703ac3e86d22fd70cedece80da97a88b61d.ck1LHrCnXqpEeD9ZXbRcqWYSFGLzns8aWb7K1HRwfiU', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1730552817, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None, 'SameSite': 'None'}, rfc2109=False), Cookie(version=0, name='did_compat', value='s%3Av0%3A92c95a90-7a17-11ee-a1bd-5be0fd3b97e6%3A3ba5fad0a74e57d4e23d9c63e2e6e703ac3e86d22fd70cedece80da97a88b61d.ck1LHrCnXqpEeD9ZXbRcqWYSFGLzns8aWb7K1HRwfiU', port=None, port_specified=False, domain='auth.tgcloud-dev.com', domain_specified=False, domain_initial_dot=False, path='/', path_specified=True, secure=True, expires=1730552817, discard=False, comment=None, comment_url=None, rest={'HttpOnly': None}, rfc2109=False)]>, 'adapters': OrderedDict([('https://', <requests.adapters.HTTPAdapter object at 0x10737c490>), ('http://', <requests.adapters.HTTPAdapter object at 0x10737c2b0>)])}
[2023-11-03 15:07:05,900][rest_client.py 81][INFO]: request URL ==>> "https://tgcloud-dev.com?code=swWzqxep4ImZJZmbJiT2FtpBEFdInS60OHOL7lzR8Z4if&state=Ny02eUNjYmtxandSaUpwYUxXNmpoRzBMY1Y3Smkzc0dZQUlrSEt4RE5VLg%3D%3D"
[2023-11-03 15:07:05,900][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-11-03 15:07:05,900][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/x-www-form-urlencoded"
}
[2023-11-03 15:07:05,901][rest_client.py 84][INFO]: request params ==>> null
[2023-11-03 15:07:05,901][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:07:06,964][rest_client.py 81][INFO]: request URL ==>> "https://auth.tgcloud-dev.com/oauth/token"
[2023-11-03 15:07:06,965][rest_client.py 82][INFO]: request method ==>> "{\"client_id\": \"PBBtD7unto4oYb0eNc6KuJbUNyZQXzka\", \"code_verifier\": \"1WQ1nUEODLFqOUIlFfGHX3pBg5RbUCF8um9Jq.l_PGZ\", \"grant_type\": \"authorization_code\", \"code\": \"swWzqxep4ImZJZmbJiT2FtpBEFdInS60OHOL7lzR8Z4if\", \"redirect_uri\": \"https://tgcloud-dev.com\"}"
[2023-11-03 15:07:06,965][rest_client.py 83][INFO]: request header ==>> null
[2023-11-03 15:07:06,965][rest_client.py 84][INFO]: request params ==>> {
    "Content-Type": "application/json"
}
[2023-11-03 15:07:06,965][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:07:07,370][rest_client.py 81][INFO]: request URL ==>> "https://tgcloud-dev.comapi/solution"
[2023-11-03 15:07:07,371][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-11-03 15:07:07,371][rest_client.py 83][INFO]: request header ==>> {
    "Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1rSXlSVEEzTnpVeE16RXlSVFpEUlVJek1rVkRRMEV5TlRFNFJqVXlRVGM0TnpNeE4wSkdOZyJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I5A9QsJ-2g0Jeig_TTZ01RKCwJ62uNTANXLAnLxFQAdjL3DpFYwvDlmOCuj3T7e1hSA0CmDk17sGCKSU6iAY-iFaAindHtyCrCG04siIEu2U12UomJ2FiIhCDLiCtFUdKSKNvhBRQ4pUitb-NdqCaDls445oeBFf5ZWu05Rxo4Scz1YsLK4nRpGfXxvhibyyeqxFgXXP7Lb-2tjs0rSBC4zGnxNXNGvU8EvcirMj9f1pCNV3tNtZ_AiDBxcqHbMl1YcY-VK8aE0IE_5GygKn5lY9Th5c_wyGIucRdTB52j-DDthGZ_pjB8gah0VpCoWKKTkwzOPNffPhYICSshpwRw"
}
[2023-11-03 15:07:07,372][rest_client.py 84][INFO]: request params ==>> null
[2023-11-03 15:07:07,372][rest_client.py 90][INFO]: request data ==>> null
[2023-11-03 15:07:23,980][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 15:07:23,984][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 15:07:24,428][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 15:07:24,429][design_schema.py 66][INFO]: URL: http://************:14240/api/system/import
[2023-11-03 15:07:24,431][design_schema.py 90][INFO]: header: {'Cookie': '', 'Content-Type': 'multipart/form-data', 'Content-type': 'multipart/form-data; boundary=wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'}
[2023-11-03 15:07:26,778][design_schema.py 92][INFO]: resp: {"error":true,"message":"You are not authorized to use this API.","results":null}
[2023-11-03 15:07:26,779][result_base.py 23][INFO]: response status ==>> 401
[2023-11-03 15:07:26,780][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2023-11-03 15:07:26,780][result_base.py 25][INFO]: response results ==>> "null"
[2023-11-03 15:09:58,987][tools_api.py 23][INFO]: API root url is http://************:14240
[2023-11-03 15:09:58,991][gsql_server_api.py 22][INFO]: API root url is http://************:14240
[2023-11-03 15:09:59,440][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/covid19.tar.gz
[2023-11-03 15:09:59,440][design_schema.py 66][INFO]: URL: http://************:14240/api/system/import
[2023-11-03 15:09:59,442][design_schema.py 90][INFO]: header: {'Cookie': '', 'Content-Type': 'multipart/form-data', 'Content-type': 'multipart/form-data; boundary=wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'}
[2023-11-03 15:10:01,794][design_schema.py 92][INFO]: resp: {"error":true,"message":"You are not authorized to use this API.","results":null}
[2023-11-03 15:10:01,795][result_base.py 23][INFO]: response status ==>> 401
[2023-11-03 15:10:01,796][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2023-11-03 15:10:01,796][result_base.py 25][INFO]: response results ==>> "null"
