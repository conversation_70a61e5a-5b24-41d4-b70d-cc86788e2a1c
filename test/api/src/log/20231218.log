[2023-12-18 11:03:17,705][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:03:17,705][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:03:17,705][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:03:17,705][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:17,705][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:03:18,251][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:03:18,252][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:03:18,252][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:03:18,252][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:03:18,253][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:03:18,254][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=ecfe5742-d1b8-4851-9d37-14e3354fd846"
}
[2023-12-18 11:03:18,255][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:18,255][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:03:18,781][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:03:18,782][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:03:18,782][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:03:18,846][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:03:18,846][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:03:18,846][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:03:18,846][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:18,846][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:03:19,419][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:03:19,419][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:03:19,419][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:03:19,419][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/local/"
[2023-12-18 11:03:19,419][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:03:19,419][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=18b1f97a-be0b-499c-80ed-9c47cfaa66b2"
}
[2023-12-18 11:03:19,419][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:19,419][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:03:19,983][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:03:19,983][result_base.py 24][INFO]: response message ==>> "Route /api/graph-styles/local/ not found."
[2023-12-18 11:03:19,983][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:03:19,985][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:03:19,985][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:03:19,985][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:03:19,985][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:19,985][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:03:20,517][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:03:20,518][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:03:20,518][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:03:20,518][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/local/none_graph"
[2023-12-18 11:03:20,518][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:03:20,518][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=7eb8b362-258b-4e57-9220-57db141b5334"
}
[2023-12-18 11:03:20,518][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:20,518][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:03:21,096][result_base.py 23][INFO]: response status ==>> 403
[2023-12-18 11:03:21,096][result_base.py 24][INFO]: response message ==>> "READ_SCHEMA privilege is required on graph none_graph."
[2023-12-18 11:03:21,096][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:03:21,098][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:03:21,098][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:03:21,098][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:03:21,098][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:21,098][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:03:21,700][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:03:21,700][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:03:21,700][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:03:21,700][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/local/graph@@@文%&¥¥"
[2023-12-18 11:03:21,700][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:03:21,700][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=f723365d-4fc5-48e6-b4a0-f2d60b829b51"
}
[2023-12-18 11:03:21,701][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:21,701][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:03:22,274][result_base.py 23][INFO]: response status ==>> 403
[2023-12-18 11:03:22,275][result_base.py 24][INFO]: response message ==>> "READ_SCHEMA privilege is required on graph graph@@@文%&¥¥."
[2023-12-18 11:03:22,275][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:03:22,279][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:03:22,279][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:03:22,279][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:03:22,279][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:22,280][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:03:22,815][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:03:22,815][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:03:22,816][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:03:22,816][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/local/MyGraph"
[2023-12-18 11:03:22,816][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:03:22,817][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": ""
}
[2023-12-18 11:03:22,817][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:22,817][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:03:24,328][result_base.py 23][INFO]: response status ==>> 401
[2023-12-18 11:03:24,328][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2023-12-18 11:03:24,329][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:03:24,330][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:03:24,330][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:03:24,330][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:03:24,331][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:24,331][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:03:24,880][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:03:24,881][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:03:24,881][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:03:24,881][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/local/MyGraph"
[2023-12-18 11:03:24,881][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:03:24,882][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a"
}
[2023-12-18 11:03:24,882][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:03:24,882][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:03:25,409][result_base.py 23][INFO]: response status ==>> 401
[2023-12-18 11:03:25,410][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2023-12-18 11:03:25,411][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:04:18,807][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:04:18,807][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:04:18,807][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:04:18,807][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:04:18,807][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:04:19,387][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:04:19,387][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:04:19,387][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:04:19,388][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:04:19,388][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:04:19,388][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=866f9aa2-ed00-4e78-be73-9893a472a57d"
}
[2023-12-18 11:04:19,389][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:04:19,389][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:04:20,913][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:04:20,914][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:04:20,914][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:06:18,971][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:06:18,971][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:06:18,971][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:06:18,971][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:18,971][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:06:19,617][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:06:19,617][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:06:19,618][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:06:19,618][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:06:19,618][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:06:19,618][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=3ea10897-4510-482f-8900-3fbdab3178b4"
}
[2023-12-18 11:06:19,621][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:19,623][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:06:20,153][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:06:20,154][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:06:20,154][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:06:43,527][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:06:43,527][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:06:43,527][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:06:43,527][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:43,527][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:06:44,155][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:06:44,156][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:06:44,156][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:06:44,156][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:06:44,156][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:06:44,156][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=914391bb-98eb-43b2-a6df-98d4dbdbfd42"
}
[2023-12-18 11:06:44,156][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:44,156][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:06:44,691][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:06:44,692][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:06:44,692][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:06:44,761][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:06:44,761][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:06:44,761][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:06:44,761][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:44,761][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:06:45,347][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:06:45,348][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:06:45,348][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:06:45,349][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:06:45,349][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:06:45,349][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=bd596400-6d52-4dca-b8fa-8cfa3d6dbf7f"
}
[2023-12-18 11:06:45,349][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:45,349][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:06:45,878][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:06:45,880][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:06:45,880][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:06:45,899][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:06:45,899][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:06:45,899][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:06:45,899][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:45,899][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:06:47,722][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:06:47,723][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:06:47,723][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:06:47,723][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:06:47,723][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:06:47,725][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=a538907c-b006-46b2-aefa-6401ed68950c"
}
[2023-12-18 11:06:47,725][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:47,726][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:06:48,285][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:06:48,285][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:06:48,285][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:06:48,297][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:06:48,297][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:06:48,297][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:06:48,297][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:48,298][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:06:48,880][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:06:48,881][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:06:48,881][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:06:48,881][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:06:48,881][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:06:48,882][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=5ee5c1d1-82b1-4245-9aee-2ce4c75d3e2a"
}
[2023-12-18 11:06:48,882][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:48,882][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:06:49,444][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:06:49,445][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:06:49,445][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:06:49,462][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:06:49,462][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:06:49,462][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:06:49,462][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:49,462][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:06:50,053][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:06:50,054][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:06:50,054][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:06:50,055][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:06:50,055][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:06:50,055][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=a6a5baf2-5406-494b-9e5c-8889b48202d2"
}
[2023-12-18 11:06:50,055][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:50,055][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:06:50,649][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:06:50,650][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:06:50,650][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:06:50,669][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:06:50,669][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:06:50,669][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:06:50,670][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:50,670][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:06:51,294][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:06:51,294][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:06:51,294][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:06:51,294][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:06:51,294][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:06:51,295][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=99953b4a-11fb-46a8-af8c-0cac5eb6a8fc"
}
[2023-12-18 11:06:51,295][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:06:51,295][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:06:51,869][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:06:51,869][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:06:51,869][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:20:31,744][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:20:31,745][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:20:31,745][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:20:31,745][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:20:31,745][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:20:32,316][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:20:32,316][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:20:32,316][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:20:32,316][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:20:32,316][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:20:32,316][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=c4ed8bdd-168c-43d4-baac-a4e120407cfe"
}
[2023-12-18 11:20:32,316][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:20:32,317][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:20:32,755][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:20:32,755][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:20:32,755][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:20:59,088][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:20:59,089][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:20:59,089][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:20:59,089][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:20:59,089][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:20:59,612][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:20:59,612][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:20:59,612][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOADINGJOB\", \"READ_QUERY\", \"WRI"
[2023-12-18 11:20:59,612][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:20:59,612][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:20:59,612][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=49d3aa88-71b8-493e-8b2b-e5f0c423472f"
}
[2023-12-18 11:20:59,612][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:20:59,612][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:21:00,092][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:21:00,093][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:21:00,093][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"vertex_type_1\": {\"fillColor\": \"#2ca02c\", \"other\": {}, \"x\": 0, \"y\": 0}}, \"edgeStyles\": {}}"
[2023-12-18 11:26:00,156][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:26:00,156][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:26:00,157][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:26:00,157][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:00,157][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:26:00,805][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:00,805][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:00,806][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:26:00,806][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:00,806][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:00,806][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=550ea4d7-76e8-441d-a427-0fa59913d220"
}
[2023-12-18 11:26:00,806][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:00,806][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:01,279][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:01,279][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:01,279][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:26:06,393][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:26:06,393][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:26:06,393][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:26:06,393][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:06,393][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:26:06,988][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:06,988][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:06,988][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:26:06,988][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:06,989][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:06,989][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=3aa91863-958e-42b2-8290-5aeb57ad98b8"
}
[2023-12-18 11:26:06,989][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:06,989][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:07,500][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:07,500][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:07,501][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:26:10,090][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:26:10,090][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:26:10,090][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:26:10,090][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:10,090][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:26:10,545][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:10,546][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:10,547][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:26:10,547][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:10,547][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:10,547][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=26aa691f-300d-4d7a-85b9-609872f70dd3"
}
[2023-12-18 11:26:10,547][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:10,548][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:10,986][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:10,986][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:10,986][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:26:39,241][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:26:39,241][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:26:39,242][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:26:39,242][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:39,242][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:26:39,686][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:39,688][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:39,688][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:26:39,689][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:39,689][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:39,689][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=54dd1c81-ba2d-4ff2-946d-f173375c2037"
}
[2023-12-18 11:26:39,690][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:39,695][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:40,188][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:40,189][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:40,190][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:26:40,190][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:40,190][rest_client.py 82][INFO]: request method ==>> "DELETE"
[2023-12-18 11:26:40,190][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=54dd1c81-ba2d-4ff2-946d-f173375c2037"
}
[2023-12-18 11:26:40,190][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:40,191][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:40,676][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:40,678][result_base.py 24][INFO]: response message ==>> "Successfully deleted global graph style."
[2023-12-18 11:26:40,678][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:26:40,678][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:40,678][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:40,679][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=54dd1c81-ba2d-4ff2-946d-f173375c2037"
}
[2023-12-18 11:26:40,679][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:40,679][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:41,405][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:26:41,406][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:26:41,407][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:26:41,407][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:41,408][rest_client.py 82][INFO]: request method ==>> "PUT"
[2023-12-18 11:26:41,408][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=54dd1c81-ba2d-4ff2-946d-f173375c2037"
}
[2023-12-18 11:26:41,408][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:41,409][rest_client.py 90][INFO]: request data ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03385642636556}, \"Day_\": {\"fillColor\": \"#cedb9c\", \"other\": {}, \"x\": 345.88308460251034, \"y\": 205.66850196800428}, \"InfectionCase\": {\"fillColor\": \"#e6240d\", \"icon\": \"icon_101\", \"other\": {}, \"x\": 13.453191803715455, \"y\": -185.70717*********}, \"Month_\": {\"fillColor\": \"#5254a3\", \"other\": {}, \"x\": 591.6030658118458, \"y\": 201.06885096635207}, \"Patient\": {\"fillColor\": \"#54d971\", \"icon\": \"icon_275\", \"other\": {}, \"x\": 466.87771312600324, \"y\": -176.69950410515114}, \"Province\": {\"fillColor\": \"#c7c7c7\", \"other\": {}, \"x\": -137.02860155449173, \"y\": -460.57363776678636}, \"SearchStat\": {\"fillColor\": \"#ce6dbd\", \"icon\": \"icon_057\", \"other\": {}, \"x\": -48.038823666079, \"y\": 314.8176857659119}, \"TravelEvent\": {\"fillColor\": \"#1f77b4\", \"other\": {}, \"x\": 189.64778493825196, \"y\": 47.472620818482724}, \"WeatherStat\": {\"fillColor\": \"#3182bd\", \"icon\": \"icon_060\", \"other\": {}, \"x\": -306.81265572197464, \"y\": 206.689226318446}, \"Year_\": {\"fillColor\": \"#1f77b4\", \"other\": {}, \"x\": 758.6052320538302, \"y\": 17.968467101958538}}, \"edgeStyles\": {\"BELONGS_TO_CASE\": {\"fillColor\": \"#fdae6b\", \"other\": {}}, \"BIRTH_STAMP\": {\"fillColor\": \"#c6dbef\", \"other\": {}}, \"CASE_IN_CITY\": {\"fillColor\": \"#a1d99b\", \"other\": {}}, \"CASE_IN_PROVINCE\": {\"fillColor\": \"#756bb1\", \"other\": {}}, \"CITY_IN_PROVINCE\": {\"fillColor\": \"#e7cb94\", \"other\": {}}, \"CONFIRM_STAMP\": {\"fillColor\": \"#a55194\", \"other\": {}}, \"DECEASE_STAMP\": {\"fillColor\": \"#fd8d3c\", \"other\": {}}, \"INFECTED_BY\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"MONTH_DAY\": {\"fillColor\": \"#e7cb94\", \"other\": {}}, \"PATIENT_FROM_CITY\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"PATIENT_FROM_COUNTRY\": {\"fillColor\": \"#e7969c\", \"other\": {}}, \"PATIENT_FROM_PROVINCE\": {\"fillColor\": \"#ad494a\", \"other\": {}}, \"PATIENT_TRAVELED\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"PROVINCE_IN_COUNTRY\": {\"fillColor\": \"#cedb9c\", \"other\": {}}, \"RELEASED_STAMP\": {\"fillColor\": \"#6baed6\", \"other\": {}}, \"SEARCH_STAMP\": {\"fillColor\": \"#ad494a\", \"other\": {}}, \"SYMPTOM_STAMP\": {\"fillColor\": \"#de9ed6\", \"other\": {}}, \"TRAVEL_EVENT_IN\": {\"fillColor\": \"#8c6d31\", \"other\": {}}, \"TRAVEL_STAMP\": {\"fillColor\": \"#fdd0a2\", \"other\": {}}, \"WEATHER_CONDITION\": {\"fillColor\": \"#9ecae1\", \"other\": {}}, \"WEATHER_STAMP\": {\"fillColor\": \"#e7969c\", \"other\": {}}, \"YEAR_MONTH\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}}}"
[2023-12-18 11:26:41,844][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:41,844][result_base.py 24][INFO]: response message ==>> "Successfully upserted global graph style."
[2023-12-18 11:26:41,844][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:26:41,844][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:41,845][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:41,845][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=54dd1c81-ba2d-4ff2-946d-f173375c2037"
}
[2023-12-18 11:26:41,845][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:41,845][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:42,299][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:42,299][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:42,299][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:26:45,710][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:26:45,710][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:26:45,710][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:26:45,710][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:45,710][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:26:46,185][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:46,186][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:46,186][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:26:46,186][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:46,186][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:46,187][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8980f9fc-2ff3-4c2d-8177-76bfc52a11e3"
}
[2023-12-18 11:26:46,187][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:46,187][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:46,644][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:46,645][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:46,646][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:26:46,646][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:46,646][rest_client.py 82][INFO]: request method ==>> "DELETE"
[2023-12-18 11:26:46,646][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8980f9fc-2ff3-4c2d-8177-76bfc52a11e3"
}
[2023-12-18 11:26:46,647][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:46,647][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:47,125][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:47,126][result_base.py 24][INFO]: response message ==>> "Successfully deleted global graph style."
[2023-12-18 11:26:47,126][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:26:47,126][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:47,126][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:47,126][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8980f9fc-2ff3-4c2d-8177-76bfc52a11e3"
}
[2023-12-18 11:26:47,127][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:47,127][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:47,640][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:26:47,642][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:26:47,642][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:26:47,642][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:47,643][rest_client.py 82][INFO]: request method ==>> "PUT"
[2023-12-18 11:26:47,643][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8980f9fc-2ff3-4c2d-8177-76bfc52a11e3"
}
[2023-12-18 11:26:47,643][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:47,643][rest_client.py 90][INFO]: request data ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03385642636556}, \"Day_\": {\"fillColor\": \"#cedb9c\", \"other\": {}, \"x\": 345.88308460251034, \"y\": 205.66850196800428}, \"InfectionCase\": {\"fillColor\": \"#e6240d\", \"icon\": \"icon_101\", \"other\": {}, \"x\": 13.453191803715455, \"y\": -185.70717*********}, \"Month_\": {\"fillColor\": \"#5254a3\", \"other\": {}, \"x\": 591.6030658118458, \"y\": 201.06885096635207}, \"Patient\": {\"fillColor\": \"#54d971\", \"icon\": \"icon_275\", \"other\": {}, \"x\": 466.87771312600324, \"y\": -176.69950410515114}, \"Province\": {\"fillColor\": \"#c7c7c7\", \"other\": {}, \"x\": -137.02860155449173, \"y\": -460.57363776678636}, \"SearchStat\": {\"fillColor\": \"#ce6dbd\", \"icon\": \"icon_057\", \"other\": {}, \"x\": -48.038823666079, \"y\": 314.8176857659119}, \"TravelEvent\": {\"fillColor\": \"#1f77b4\", \"other\": {}, \"x\": 189.64778493825196, \"y\": 47.472620818482724}, \"WeatherStat\": {\"fillColor\": \"#3182bd\", \"icon\": \"icon_060\", \"other\": {}, \"x\": -306.81265572197464, \"y\": 206.689226318446}, \"Year_\": {\"fillColor\": \"#1f77b4\", \"other\": {}, \"x\": 758.6052320538302, \"y\": 17.968467101958538}}, \"edgeStyles\": {\"BELONGS_TO_CASE\": {\"fillColor\": \"#fdae6b\", \"other\": {}}, \"BIRTH_STAMP\": {\"fillColor\": \"#c6dbef\", \"other\": {}}, \"CASE_IN_CITY\": {\"fillColor\": \"#a1d99b\", \"other\": {}}, \"CASE_IN_PROVINCE\": {\"fillColor\": \"#756bb1\", \"other\": {}}, \"CITY_IN_PROVINCE\": {\"fillColor\": \"#e7cb94\", \"other\": {}}, \"CONFIRM_STAMP\": {\"fillColor\": \"#a55194\", \"other\": {}}, \"DECEASE_STAMP\": {\"fillColor\": \"#fd8d3c\", \"other\": {}}, \"INFECTED_BY\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"MONTH_DAY\": {\"fillColor\": \"#e7cb94\", \"other\": {}}, \"PATIENT_FROM_CITY\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"PATIENT_FROM_COUNTRY\": {\"fillColor\": \"#e7969c\", \"other\": {}}, \"PATIENT_FROM_PROVINCE\": {\"fillColor\": \"#ad494a\", \"other\": {}}, \"PATIENT_TRAVELED\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"PROVINCE_IN_COUNTRY\": {\"fillColor\": \"#cedb9c\", \"other\": {}}, \"RELEASED_STAMP\": {\"fillColor\": \"#6baed6\", \"other\": {}}, \"SEARCH_STAMP\": {\"fillColor\": \"#ad494a\", \"other\": {}}, \"SYMPTOM_STAMP\": {\"fillColor\": \"#de9ed6\", \"other\": {}}, \"TRAVEL_EVENT_IN\": {\"fillColor\": \"#8c6d31\", \"other\": {}}, \"TRAVEL_STAMP\": {\"fillColor\": \"#fdd0a2\", \"other\": {}}, \"WEATHER_CONDITION\": {\"fillColor\": \"#9ecae1\", \"other\": {}}, \"WEATHER_STAMP\": {\"fillColor\": \"#e7969c\", \"other\": {}}, \"YEAR_MONTH\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}}}"
[2023-12-18 11:26:48,390][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:48,391][result_base.py 24][INFO]: response message ==>> "Successfully upserted global graph style."
[2023-12-18 11:26:48,391][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:26:48,392][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:48,392][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:48,392][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8980f9fc-2ff3-4c2d-8177-76bfc52a11e3"
}
[2023-12-18 11:26:48,393][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:48,393][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:48,849][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:48,851][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:48,851][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:26:52,310][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:26:52,311][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:26:52,311][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:26:52,311][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:52,311][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:26:52,833][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:52,834][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:52,834][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:26:52,834][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:52,834][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:52,834][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=cde787cf-e23f-4bed-aa00-ff16a8d9b5bb"
}
[2023-12-18 11:26:52,834][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:52,834][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:53,357][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:53,358][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:53,358][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:26:57,123][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:26:57,123][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:26:57,123][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:26:57,123][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:57,123][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:26:59,008][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:59,010][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:59,010][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:26:59,010][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:26:59,010][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:26:59,011][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=29ee84d0-dfee-453e-899e-90d7a730d8c1"
}
[2023-12-18 11:26:59,011][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:26:59,011][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:26:59,429][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:26:59,430][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:26:59,431][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:27:30,570][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:27:30,570][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:27:30,570][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:27:30,571][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:27:30,571][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:27:31,007][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:27:31,008][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:27:31,008][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:27:31,009][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:27:31,009][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:27:31,009][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=4c8b5062-c105-4d83-9376-80a70531115c"
}
[2023-12-18 11:27:31,009][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:27:31,009][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:27:31,505][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:27:31,506][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:27:31,506][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:27:31,506][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:27:31,507][rest_client.py 82][INFO]: request method ==>> "DELETE"
[2023-12-18 11:27:31,507][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=4c8b5062-c105-4d83-9376-80a70531115c"
}
[2023-12-18 11:27:31,507][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:27:31,507][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:27:32,029][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:27:32,030][result_base.py 24][INFO]: response message ==>> "Successfully deleted global graph style."
[2023-12-18 11:27:32,031][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:27:32,031][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:27:32,031][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:27:32,031][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=4c8b5062-c105-4d83-9376-80a70531115c"
}
[2023-12-18 11:27:32,032][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:27:32,032][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:27:32,558][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:27:32,559][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:27:32,560][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:27:32,560][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:27:32,561][rest_client.py 82][INFO]: request method ==>> "PUT"
[2023-12-18 11:27:32,561][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=4c8b5062-c105-4d83-9376-80a70531115c"
}
[2023-12-18 11:27:32,561][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:27:32,561][rest_client.py 90][INFO]: request data ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03385642636556}, \"Day_\": {\"fillColor\": \"#cedb9c\", \"other\": {}, \"x\": 345.88308460251034, \"y\": 205.66850196800428}, \"InfectionCase\": {\"fillColor\": \"#e6240d\", \"icon\": \"icon_101\", \"other\": {}, \"x\": 13.453191803715455, \"y\": -185.70717*********}, \"Month_\": {\"fillColor\": \"#5254a3\", \"other\": {}, \"x\": 591.6030658118458, \"y\": 201.06885096635207}, \"Patient\": {\"fillColor\": \"#54d971\", \"icon\": \"icon_275\", \"other\": {}, \"x\": 466.87771312600324, \"y\": -176.69950410515114}, \"Province\": {\"fillColor\": \"#c7c7c7\", \"other\": {}, \"x\": -137.02860155449173, \"y\": -460.57363776678636}, \"SearchStat\": {\"fillColor\": \"#ce6dbd\", \"icon\": \"icon_057\", \"other\": {}, \"x\": -48.038823666079, \"y\": 314.8176857659119}, \"TravelEvent\": {\"fillColor\": \"#1f77b4\", \"other\": {}, \"x\": 189.64778493825196, \"y\": 47.472620818482724}, \"WeatherStat\": {\"fillColor\": \"#3182bd\", \"icon\": \"icon_060\", \"other\": {}, \"x\": -306.81265572197464, \"y\": 206.689226318446}, \"Year_\": {\"fillColor\": \"#1f77b4\", \"other\": {}, \"x\": 758.6052320538302, \"y\": 17.968467101958538}}, \"edgeStyles\": {\"BELONGS_TO_CASE\": {\"fillColor\": \"#fdae6b\", \"other\": {}}, \"BIRTH_STAMP\": {\"fillColor\": \"#c6dbef\", \"other\": {}}, \"CASE_IN_CITY\": {\"fillColor\": \"#a1d99b\", \"other\": {}}, \"CASE_IN_PROVINCE\": {\"fillColor\": \"#756bb1\", \"other\": {}}, \"CITY_IN_PROVINCE\": {\"fillColor\": \"#e7cb94\", \"other\": {}}, \"CONFIRM_STAMP\": {\"fillColor\": \"#a55194\", \"other\": {}}, \"DECEASE_STAMP\": {\"fillColor\": \"#fd8d3c\", \"other\": {}}, \"INFECTED_BY\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"MONTH_DAY\": {\"fillColor\": \"#e7cb94\", \"other\": {}}, \"PATIENT_FROM_CITY\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"PATIENT_FROM_COUNTRY\": {\"fillColor\": \"#e7969c\", \"other\": {}}, \"PATIENT_FROM_PROVINCE\": {\"fillColor\": \"#ad494a\", \"other\": {}}, \"PATIENT_TRAVELED\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}, \"PROVINCE_IN_COUNTRY\": {\"fillColor\": \"#cedb9c\", \"other\": {}}, \"RELEASED_STAMP\": {\"fillColor\": \"#6baed6\", \"other\": {}}, \"SEARCH_STAMP\": {\"fillColor\": \"#ad494a\", \"other\": {}}, \"SYMPTOM_STAMP\": {\"fillColor\": \"#de9ed6\", \"other\": {}}, \"TRAVEL_EVENT_IN\": {\"fillColor\": \"#8c6d31\", \"other\": {}}, \"TRAVEL_STAMP\": {\"fillColor\": \"#fdd0a2\", \"other\": {}}, \"WEATHER_CONDITION\": {\"fillColor\": \"#9ecae1\", \"other\": {}}, \"WEATHER_STAMP\": {\"fillColor\": \"#e7969c\", \"other\": {}}, \"YEAR_MONTH\": {\"fillColor\": \"#ff7f0e\", \"other\": {}}}}"
[2023-12-18 11:27:33,023][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:27:33,023][result_base.py 24][INFO]: response message ==>> "Successfully upserted global graph style."
[2023-12-18 11:27:33,023][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:27:33,023][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:27:33,023][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:27:33,024][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=4c8b5062-c105-4d83-9376-80a70531115c"
}
[2023-12-18 11:27:33,024][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:27:33,024][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:27:33,533][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:27:33,534][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:27:33,534][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:28:01,465][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:28:01,466][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:28:01,466][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:28:01,466][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:28:01,466][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:28:01,983][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:28:01,984][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:28:01,984][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:28:01,984][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:28:01,984][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:28:01,985][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=0898ed58-757a-46a7-b765-b45839b03feb"
}
[2023-12-18 11:28:01,985][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:28:01,987][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:28:02,460][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:28:02,461][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:28:02,461][result_base.py 25][INFO]: response results ==>> "{\"vertexStyles\": {\"City\": {\"fillColor\": \"#94c1c4\", \"other\": {}, \"x\": -134.97098956529467, \"y\": 55.26633968771526}, \"Country\": {\"fillColor\": \"#8688c9\", \"other\": {}, \"x\": 284.5149601276953, \"y\": -453.03"
[2023-12-18 11:28:02,462][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:28:02,462][rest_client.py 82][INFO]: request method ==>> "DELETE"
[2023-12-18 11:28:02,462][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=0898ed58-757a-46a7-b765-b45839b03feb"
}
[2023-12-18 11:28:02,463][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:28:02,463][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:28:02,923][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:28:02,925][result_base.py 24][INFO]: response message ==>> "Successfully deleted global graph style."
[2023-12-18 11:28:02,926][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:28:02,926][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:28:02,926][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:28:02,926][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=0898ed58-757a-46a7-b765-b45839b03feb"
}
[2023-12-18 11:28:02,926][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:28:02,927][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:28:03,351][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:28:03,352][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:28:03,352][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:28:07,120][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:28:07,120][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:28:07,120][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:28:07,120][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:28:07,120][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:28:07,566][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:28:07,567][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:28:07,567][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:28:07,567][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:28:07,567][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:28:07,568][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=28b469fd-c917-4340-be44-baf968a78d1e"
}
[2023-12-18 11:28:07,568][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:28:07,568][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:28:08,090][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:28:08,093][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:28:08,093][result_base.py 25][INFO]: response results ==>> "null"
[2023-12-18 11:28:20,382][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/auth/login"
[2023-12-18 11:28:20,382][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-12-18 11:28:20,382][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-12-18 11:28:20,382][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:28:20,383][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-12-18 11:28:20,860][result_base.py 23][INFO]: response status ==>> 200
[2023-12-18 11:28:20,861][result_base.py 24][INFO]: response message ==>> ""
[2023-12-18 11:28:20,861][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2023-12-18 11:28:20,862][rest_client.py 81][INFO]: request URL ==>> "http://35.184.244.202:14240/api/graph-styles/global"
[2023-12-18 11:28:20,862][rest_client.py 82][INFO]: request method ==>> "GET"
[2023-12-18 11:28:20,862][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=e5b2deec-ab4b-4de4-8f58-3912a5e5982f"
}
[2023-12-18 11:28:20,863][rest_client.py 84][INFO]: request params ==>> null
[2023-12-18 11:28:20,864][rest_client.py 90][INFO]: request data ==>> null
[2023-12-18 11:28:21,301][result_base.py 23][INFO]: response status ==>> 404
[2023-12-18 11:28:21,301][result_base.py 24][INFO]: response message ==>> "Global graph style cannot be found."
[2023-12-18 11:28:21,301][result_base.py 25][INFO]: response results ==>> "null"
