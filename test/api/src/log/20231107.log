[2023-11-07 14:09:04,182][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2023-11-07 14:09:04,183][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-11-07 14:09:04,183][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-11-07 14:09:04,183][rest_client.py 84][INFO]: request params ==>> null
[2023-11-07 14:09:04,183][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-11-07 14:09:04,759][result_base.py 23][INFO]: response status ==>> 200
[2023-11-07 14:09:04,759][result_base.py 24][INFO]: response message ==>> ""
[2023-11-07 14:09:04,759][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"], \"mysql_yuling\": [\"superuser\"], \"postgresql_yuling\": [\"superuser\"]}, \"privileges\": {\"1\": {\"p"
[2023-11-07 14:09:04,759][test_login.py 157][INFO]: my_cookies: TigerGraphApp=baabf325-20d0-4bbd-b975-687e7da9d321
[2023-11-07 14:09:04,759][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/wrong_internal_zip.tar.gz
[2023-11-07 14:09:04,759][design_schema.py 66][INFO]: URL: http://************:14240/api/system/import
[2023-11-07 14:09:04,765][design_schema.py 90][INFO]: header: {'Cookie': 'TigerGraphApp=baabf325-20d0-4bbd-b975-687e7da9d321', 'Content-Type': 'multipart/form-data', 'Content-type': 'multipart/form-data; boundary=wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'}
[2023-11-07 14:09:05,382][design_schema.py 92][INFO]: resp: {"error":true,"message":"Failed to import graph: GSQL exited with code 212","results":null}
[2023-11-07 14:09:05,382][result_base.py 23][INFO]: response status ==>> 500
[2023-11-07 14:09:05,382][result_base.py 24][INFO]: response message ==>> "Failed to import graph: GSQL exited with code 212"
[2023-11-07 14:09:05,383][result_base.py 25][INFO]: response results ==>> "null"
[2023-11-07 14:11:41,018][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2023-11-07 14:11:41,018][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-11-07 14:11:41,018][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-11-07 14:11:41,018][rest_client.py 84][INFO]: request params ==>> null
[2023-11-07 14:11:41,018][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-11-07 14:11:41,714][result_base.py 23][INFO]: response status ==>> 200
[2023-11-07 14:11:41,715][result_base.py 24][INFO]: response message ==>> ""
[2023-11-07 14:11:41,715][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"ldbc_snb\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOAD"
[2023-11-07 14:11:41,715][test_login.py 157][INFO]: my_cookies: TigerGraphApp=f07874be-811b-4ca4-94e3-44a067c45986
[2023-11-07 14:11:41,716][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/wrong_internal_zip.tar.gz
[2023-11-07 14:11:41,716][design_schema.py 66][INFO]: URL: http://************:14240/api/system/import
[2023-11-07 14:11:41,721][design_schema.py 90][INFO]: header: {'Cookie': 'TigerGraphApp=f07874be-811b-4ca4-94e3-44a067c45986', 'Content-Type': 'multipart/form-data', 'Content-type': 'multipart/form-data; boundary=wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'}
[2023-11-07 14:11:42,377][design_schema.py 92][INFO]: resp: {"error":true,"message":"Failed to import graph: ExternalError (Failed to send TransferFiles cmd to executor, spanId [GUI]@1699337502326858791:file-transfer; EvalSymlinks failed for path /home/<USER>/tigergraph/tmp/gui/imports/import_1633857038/graph/ExportedGraph.zip; lstat /home/<USER>/tigergraph/tmp/gui/imports/import_1633857038/graph: no such file or directory)","results":null}
[2023-11-07 14:11:42,377][result_base.py 23][INFO]: response status ==>> 500
[2023-11-07 14:11:42,378][result_base.py 24][INFO]: response message ==>> "Failed to import graph: ExternalError (Failed to send TransferFiles cmd to executor, spanId [GUI]@1699337502326858791:file-transfer; EvalSymlinks failed for path /home/<USER>/tigergraph/tmp/gui/imports/import_1633857038/graph/ExportedGraph.zip; lstat /home/<USER>/tigergraph/tmp/gui/imports/import_1633857038/graph: no such file or directory)"
[2023-11-07 14:11:42,378][result_base.py 25][INFO]: response results ==>> "null"
[2023-11-07 14:12:27,004][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2023-11-07 14:12:27,004][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-11-07 14:12:27,004][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-11-07 14:12:27,004][rest_client.py 84][INFO]: request params ==>> null
[2023-11-07 14:12:27,004][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-11-07 14:12:27,758][result_base.py 23][INFO]: response status ==>> 200
[2023-11-07 14:12:27,761][result_base.py 24][INFO]: response message ==>> ""
[2023-11-07 14:12:27,761][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"ldbc_snb\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOAD"
[2023-11-07 14:12:27,762][test_login.py 157][INFO]: my_cookies: TigerGraphApp=988c5429-875d-41df-8677-ec9148e10903
[2023-11-07 14:12:27,762][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/wrong_internal_zip.tar.gz
[2023-11-07 14:12:27,762][design_schema.py 66][INFO]: URL: http://************:14240/api/system/import
[2023-11-07 14:12:27,770][design_schema.py 90][INFO]: header: {'Cookie': 'TigerGraphApp=988c5429-875d-41df-8677-ec9148e10903', 'Content-Type': 'multipart/form-data', 'Content-type': 'multipart/form-data; boundary=wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'}
[2023-11-07 14:12:28,421][design_schema.py 92][INFO]: resp: {"error":true,"message":"Failed to import graph: ExternalError (Failed to send TransferFiles cmd to executor, spanId [GUI]@1699337548385301287:file-transfer; EvalSymlinks failed for path /home/<USER>/tigergraph/tmp/gui/imports/import_791332192/graph/ExportedGraph.zip; lstat /home/<USER>/tigergraph/tmp/gui/imports/import_791332192/graph: no such file or directory)","results":null}
[2023-11-07 14:12:28,422][result_base.py 23][INFO]: response status ==>> 500
[2023-11-07 14:12:28,422][result_base.py 24][INFO]: response message ==>> "Failed to import graph: ExternalError (Failed to send TransferFiles cmd to executor, spanId [GUI]@1699337548385301287:file-transfer; EvalSymlinks failed for path /home/<USER>/tigergraph/tmp/gui/imports/import_791332192/graph/ExportedGraph.zip; lstat /home/<USER>/tigergraph/tmp/gui/imports/import_791332192/graph: no such file or directory)"
[2023-11-07 14:12:28,423][result_base.py 25][INFO]: response results ==>> "null"
[2023-11-07 14:16:43,208][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2023-11-07 14:16:43,208][rest_client.py 82][INFO]: request method ==>> "POST"
[2023-11-07 14:16:43,208][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2023-11-07 14:16:43,209][rest_client.py 84][INFO]: request params ==>> null
[2023-11-07 14:16:43,209][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2023-11-07 14:16:43,949][result_base.py 23][INFO]: response status ==>> 200
[2023-11-07 14:16:43,950][result_base.py 24][INFO]: response message ==>> ""
[2023-11-07 14:16:43,950][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"ldbc_snb\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXECUTE_LOADINGJOB\", \"WRITE_LOAD"
[2023-11-07 14:16:43,950][test_login.py 157][INFO]: my_cookies: TigerGraphApp=2c2cee0c-a3ce-48c5-84fc-02d0232808c7
[2023-11-07 14:16:43,950][design_schema.py 64][INFO]: file_path: /Users/<USER>/Desktop/tigergraph/code/tools/tests/api/data/solution/wrong_internal_zip.tar.gz
[2023-11-07 14:16:43,950][design_schema.py 66][INFO]: URL: http://************:14240/api/system/import
[2023-11-07 14:16:43,956][design_schema.py 90][INFO]: header: {'Cookie': 'TigerGraphApp=2c2cee0c-a3ce-48c5-84fc-02d0232808c7', 'Content-Type': 'multipart/form-data', 'Content-type': 'multipart/form-data; boundary=wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'}
[2023-11-07 14:16:44,494][design_schema.py 92][INFO]: resp: {"error":true,"message":"Failed to import graph: ExternalError (Failed to send TransferFiles cmd to executor, spanId [GUI]@1699337804563120350:file-transfer; EvalSymlinks failed for path /home/<USER>/tigergraph/tmp/gui/imports/import_1279097562/graph/ExportedGraph.zip; lstat /home/<USER>/tigergraph/tmp/gui/imports/import_1279097562/graph: no such file or directory)","results":null}
[2023-11-07 14:16:44,495][result_base.py 23][INFO]: response status ==>> 500
[2023-11-07 14:16:44,496][result_base.py 24][INFO]: response message ==>> "Failed to import graph: ExternalError (Failed to send TransferFiles cmd to executor, spanId [GUI]@1699337804563120350:file-transfer; EvalSymlinks failed for path /home/<USER>/tigergraph/tmp/gui/imports/import_1279097562/graph/ExportedGraph.zip; lstat /home/<USER>/tigergraph/tmp/gui/imports/import_1279097562/graph: no such file or directory)"
[2023-11-07 14:16:44,496][result_base.py 25][INFO]: response results ==>> "null"
