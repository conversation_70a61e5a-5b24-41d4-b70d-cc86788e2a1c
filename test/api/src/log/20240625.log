[2024-06-25 11:27:45,391][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:27:45,392][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:27:45,392][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:27:45,392][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:27:45,392][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:27:46,922][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:27:46,923][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:27:46,923][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:27:46,923][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:27:46,923][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:27:46,923][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=67e5593a-19c0-4bf7-8388-92862c1aa161"
}
[2024-06-25 11:27:46,923][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:27:46,923][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:27:47,685][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:27:47,685][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:27:47,686][result_base.py 25][INFO]: response results ==>> "{\"name\": \"one_vertex\", \"username\": \"tigergraph\", \"timestamp\": 1719285658270, \"previewImage\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMMAAAE/CAYAAAD7WHd8AAAAAXNSR0IArs4c6QAACBtJREFUeF7t1zEOwkAU"
[2024-06-25 11:27:47,686][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:27:47,686][rest_client.py 82][INFO]: request method ==>> "DELETE"
[2024-06-25 11:27:47,686][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=67e5593a-19c0-4bf7-8388-92862c1aa161"
}
[2024-06-25 11:27:47,686][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:27:47,686][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:27:48,203][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:27:48,204][result_base.py 24][INFO]: response message ==>> "Successfully deleted exploration result 'one_vertex' from graph 'MyGraph'."
[2024-06-25 11:27:48,205][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:27:48,206][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:27:48,206][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:27:48,206][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=67e5593a-19c0-4bf7-8388-92862c1aa161"
}
[2024-06-25 11:27:48,206][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:27:48,206][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:27:48,728][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:27:48,728][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:27:48,729][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:27:48,729][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:27:48,729][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:27:48,730][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=67e5593a-19c0-4bf7-8388-92862c1aa161"
}
[2024-06-25 11:27:48,730][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:27:48,730][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:27:49,511][result_base.py 23][INFO]: response status ==>> 201
[2024-06-25 11:27:49,511][result_base.py 24][INFO]: response message ==>> "Successfully created exploration result 'one_vertex' for graph 'MyGraph'."
[2024-06-25 11:27:49,512][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:27:49,512][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:27:49,512][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:27:49,512][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=67e5593a-19c0-4bf7-8388-92862c1aa161"
}
[2024-06-25 11:27:49,513][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:27:49,513][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:27:50,276][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:27:50,277][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:27:50,277][result_base.py 25][INFO]: response results ==>> "{\"name\": \"one_vertex\", \"username\": \"tigergraph\", \"timestamp\": 1698031291611, \"previewImage\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ4AAACVCAYAAABozJx4AAAAAXNSR0IArs4c6QAAEZJJREFUeF7t3VloXdUe"
[2024-06-25 11:30:10,484][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:30:10,484][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:30:10,484][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:30:10,485][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:10,485][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:30:11,025][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:11,026][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:11,026][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:30:11,027][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:30:11,028][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:11,028][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=3a194b88-64da-4d47-a8a2-b8111b131810"
}
[2024-06-25 11:30:11,028][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:11,028][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:11,553][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:11,554][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:11,554][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:30:11,555][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:30:11,555][rest_client.py 82][INFO]: request method ==>> "PUT"
[2024-06-25 11:30:11,555][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=3a194b88-64da-4d47-a8a2-b8111b131810"
}
[2024-06-25 11:30:11,555][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:11,555][rest_client.py 90][INFO]: request data ==>> "{\"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}}"
[2024-06-25 11:30:12,815][result_base.py 23][INFO]: response status ==>> 400
[2024-06-25 11:30:12,816][result_base.py 24][INFO]: response message ==>> "Invalid payload."
[2024-06-25 11:30:12,816][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:12,831][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:30:12,831][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:30:12,831][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:30:12,831][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:12,831][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:30:13,424][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:13,424][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:13,425][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:30:13,426][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/none_graph"
[2024-06-25 11:30:13,426][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:13,427][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=723f9d9d-340d-4a49-a9ee-7cbf9f55239f"
}
[2024-06-25 11:30:13,427][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:13,427][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:13,943][result_base.py 23][INFO]: response status ==>> 404
[2024-06-25 11:30:13,944][result_base.py 24][INFO]: response message ==>> "The graph none_graph not found. Please provide a valid graph name"
[2024-06-25 11:30:13,944][result_base.py 25][INFO]: response results ==>> "{\"error\": true, \"message\": \"The graph none_graph not found. Please provide a valid graph name\"}"
[2024-06-25 11:30:13,944][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/none_graph/one_vertex"
[2024-06-25 11:30:13,944][rest_client.py 82][INFO]: request method ==>> "PUT"
[2024-06-25 11:30:13,944][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=723f9d9d-340d-4a49-a9ee-7cbf9f55239f"
}
[2024-06-25 11:30:13,945][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:13,945][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": {\"error\": true, \"message\": \"The graph none_graph not found. Please provide a valid graph name\"}, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:30:14,706][result_base.py 23][INFO]: response status ==>> 404
[2024-06-25 11:30:14,707][result_base.py 24][INFO]: response message ==>> "Graph none_graph does not exist or you don't have privileges on it."
[2024-06-25 11:30:14,707][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:14,712][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:30:14,712][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:30:14,712][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:30:14,712][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:14,712][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:30:15,247][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:15,248][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:15,249][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:30:15,250][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:30:15,251][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:15,251][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=526e7e60-a1a7-4e99-8dea-248d6762f423"
}
[2024-06-25 11:30:15,251][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:15,251][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:15,804][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:15,804][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:15,805][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:30:15,805][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/none_file"
[2024-06-25 11:30:15,805][rest_client.py 82][INFO]: request method ==>> "PUT"
[2024-06-25 11:30:15,805][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=526e7e60-a1a7-4e99-8dea-248d6762f423"
}
[2024-06-25 11:30:15,806][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:15,806][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:30:16,708][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:16,709][result_base.py 24][INFO]: response message ==>> "Successfully upserted exploration result 'none_file' for graph 'MyGraph'."
[2024-06-25 11:30:16,710][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:16,715][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:30:16,715][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:30:16,715][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:30:16,715][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:16,715][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:30:17,242][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:17,242][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:17,244][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:30:17,245][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:30:17,245][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:17,245][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=c76d2c30-dd3b-49d6-bd62-bd715b3f5797"
}
[2024-06-25 11:30:17,246][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:17,246][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:17,764][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:17,764][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:17,765][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:30:17,765][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/other_file"
[2024-06-25 11:30:17,765][rest_client.py 82][INFO]: request method ==>> "PUT"
[2024-06-25 11:30:17,766][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=c76d2c30-dd3b-49d6-bd62-bd715b3f5797"
}
[2024-06-25 11:30:17,766][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:17,766][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:30:18,618][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:18,619][result_base.py 24][INFO]: response message ==>> "Successfully upserted exploration result 'other_file' for graph 'MyGraph'."
[2024-06-25 11:30:18,620][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:18,627][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:30:18,627][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:30:18,627][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:30:18,627][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:18,628][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:30:19,184][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:19,185][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:19,185][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:30:19,185][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:30:19,186][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:19,186][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8c069bc6-1aac-4a5f-a262-3a3163a36230"
}
[2024-06-25 11:30:19,186][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:19,186][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:19,702][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:19,702][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:19,703][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:30:19,703][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:30:19,704][rest_client.py 82][INFO]: request method ==>> "PUT"
[2024-06-25 11:30:19,704][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8c069bc6-1aac-4a5f-a262-3a3163a36230"
}
[2024-06-25 11:30:19,704][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:19,704][rest_client.py 90][INFO]: request data ==>> "{\"no_graph\": \"sssssssssssss&*@4\", \"user\": \"invalid user\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}}"
[2024-06-25 11:30:21,628][result_base.py 23][INFO]: response status ==>> 400
[2024-06-25 11:30:21,628][result_base.py 24][INFO]: response message ==>> "Invalid payload."
[2024-06-25 11:30:21,628][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:21,632][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:30:21,632][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:30:21,632][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:30:21,632][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:21,632][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:30:23,164][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:23,165][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:23,165][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:30:23,166][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:30:23,166][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:23,166][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": ""
}
[2024-06-25 11:30:23,166][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:23,167][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:23,703][result_base.py 23][INFO]: response status ==>> 401
[2024-06-25 11:30:23,704][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2024-06-25 11:30:23,705][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:23,705][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:30:23,706][rest_client.py 82][INFO]: request method ==>> "PUT"
[2024-06-25 11:30:23,706][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": ""
}
[2024-06-25 11:30:23,706][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:23,707][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": null, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:30:24,471][result_base.py 23][INFO]: response status ==>> 401
[2024-06-25 11:30:24,471][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2024-06-25 11:30:24,471][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:24,474][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:30:24,474][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:30:24,474][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:30:24,474][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:24,474][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:30:25,005][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:25,005][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:25,006][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:30:25,006][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:30:25,006][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:25,007][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a"
}
[2024-06-25 11:30:25,007][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:25,007][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:25,515][result_base.py 23][INFO]: response status ==>> 401
[2024-06-25 11:30:25,515][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2024-06-25 11:30:25,516][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:25,516][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:30:25,517][rest_client.py 82][INFO]: request method ==>> "PUT"
[2024-06-25 11:30:25,517][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a"
}
[2024-06-25 11:30:25,518][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:25,519][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": null, \"previewImage\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ4AAACVCAYAAABozJx4AAAAAXNSR0IArs4c6QAAEZJJREFUeF7t3VloXdUex/H/mZO0GZqmqR1sTTqkaUunO1TER33QF8UBH3xSUHwSfBAHUBBRRHFCwQEcXhTFAZwQxQkEtb14O9hqbe1gY6Ntk7RpmmY40+W/ri0nZ0iTnnNW1tnru6FI7Tl7rfX5b/ix9l5n7VA2m80KBwIIIIAAApYEQgSPJWmaQQABBBAwAgQPFwICCCCAgFUBgscqN40hgAACCBA8XAMIIIAAAlYFCB6r3DSGAAIIIEDwcA0ggAACCFgVIHisctMYAggggADBwzWAAAIIIGBVgOCxyk1jCCCAAAIED9cAAggggIBVAYLHKjeNIYAAAggQPFwDCCCAAAJWBQgeq9w0hgACCCBA8HANIIAAAghYFSB4rHLTGAIIIIAAwcM1gAACCCBgVYDgscpNYwgggAACBA/XAAIIIICAVQGCxyo3jSGAAAIIEDxcAwgggAACVgUIHqvcNIYAAgggQPBwDSCAAAIIWBUgeKxy0xgCCCCAAMHDNYAAAgggYFWA4LHKTWMIIIAAAgQP1wACCCCAgFUBgscqN40hgAACCBA8XAMIIIAAAlYFCB6r3DSGAAIIIEDwcA0ggAACCFgVIHisctMYAggggADBwzWAAAIIIGBVgOCxyk1jCCCAAAIED9cAAggggIBVAYLHKjeNIYAAAggQPFwDCCCAAAJWBQgeq9w0hgACCCBA8HANIIAAAghYFSB4rHLTGAIIIIAAwcM1gAACCCBgVYDgscpNYwgggAACBA/XAAIIIICAVQGCxyo3jSGAAAIIEDxcAwhMIpDJZGRoaEhOnz4tyWRywp9UKiXRaFRisdiEP42NjTJ79mwJh8PYIoBAEQGCh8sCgTwBDZiTJ0/K4OCgnDp1SrLZ7LSNQqGQNDU1SXNzs7S0tJhg4kAAgf8LEDxcCQj8LTA2NiZHjhyREydOVNxkzpw5smjRIkkkEhU/NydEoNYECJ5aqxj9rbiAznB6e3ulr6+v4ufOP2FbW5ssXLiQGVDVpWnAZQGCx+Xq0LeqC+jttAMHDog+y7F16LOfzs5OcxuOAwEfBQgeH6vOmI2AznL+/PPPaWmMZrMyls3KeCYryaxILCQSD4ckEQpLXWhap5IFCxaY2Q8HAr4JEDy+VZzxmtmNznJ0tjPZkRKRU+mMDKbS5r8jU1hkUB8KSXM0LE2RiDRFwhI9j7fOenT2wwo4LkyfBAgen6rNWGVkZET2798vupCg1DGeFTk8Ni596fJvv7VFwrIkEZf4JLMhXXCwYsUKFh5wfXojQPB4U2oGqmHz888/l3yek8xm5UgyJUeTaZn+AurSvpo582MRWRSLSixUPIH090CrVq0ifLhMvRAgeLwoM4PU22saOqVmOkPpjPw6Oi56e61ah95266qLS2Ok+A9LdebT3d0tkUikWl3gvAg4IUDwOFEGOlFtgX379pkfgxY7+lJp2T+WrOgsp9R4dL6zLBGTtmjxcNEfneptNw4EgixA8AS5uozNCOiPQv/666+iGofHk9KbTFuXWhiNyJJE8d0MLrroIvNjUw4EgipA8AS1sozLCAwPD8uePXuKaujznJ7xat5cm7wIHYmYzC8x8+nq6jL7vXEgEEQBgieIVWVM5wQ0dDR88o+TqbTsGUvOqJTedutKxKSlSPho6Gj4cCAQRAGCJ4hVZUxGQDf61KXT+cdIJiO7RsbF/g22wsLok5619XGpL7KTtT7r0Wc+HAgETYDgCVpFGc85gd27d8vo6GiByK6RMTmdqeSC6fLQ9Yemq+viBSepr6+X1atXl3dyvo2AgwIEj4NFoUvlCxw/flwOHz5ccKKBdEb2jo6X30CFz7CqLiYtRZZRX3LJJTJ37twKt8bpEJhZAYJnZv1pvUoCxZ7t6Ht1do6MT2nrmyp1q+RpdauddfVx0ff45B4867FdCdqzIUDw2FCmDasC+pqDnTt3FrR5NJWWgzO8oGAyCP19z7y8hQYaROvXr+dHpVavIBqrtgDBU21hzm9doL+/Xw4dOlTQrmvPdvI72BwJS3eRZz0dHR3S2tpq3ZEGEaiWAMFTLVnOO2MCuvN0/ltE9TUG/x0pvTHojHU2p2G9yfaPhoRE82636dtLdQdrDgSCIkDwBKWSjMMI6HOc7du3F2wEevzvbXFcZ1peZDsd3btNb7flP/9xfSz0D4FSAgQP10agBPS1B7oZaP6xbywp/SkXfrkzOffcaERWFNlKZ82aNVJXVxeoWjEYfwUIHn9rH8iR60aguiFo/rHtzJh5c6jrR10oJBsaEgXdXLlypTQ2NrreffqHwJQECJ4pMfGhWhEotbBgy/Cold2ny3XSnQz+NatwZsMCg3Jl+b5LAgSPS9WgL2UL6C7Uuht17pHJZmXrGbcXFuT2998NCQnnLTC4+OKLpb29vWwfToCACwIEjwtVoA8VE+jp6ZFjx45NON9YJivbHF/RltvhTfUJiYcn/pCUVyVU7BLhRA4IEDwOFIEuVE6g2Lt3ai14NtYnJEHwVO6i4EzOCRA8zpWEDpUjoLMdnfXU8q22zQ2JgqXT3Gor56rgu64JEDyuVYT+lCUwMDAgBw8eLDjHf4ZHnXgNwvkGFwuFzI9I8w8WF5xPjn+vJQGCp5aqRV/PKzA0NCR79+4t+Nz2M2MyWgPLqRvCYbNZaP7Bcurzlp4P1JAAwVNDxaKr5xfQ9+/oe3jyj1r5AaluEqqbheYf/ID0/LXnE7UjQPDUTq3o6RQEdMucHTt2SDo9cZcCtsyZAh4fQcCSAMFjCZpm7Ano6671tde5R62sbPsnm4Tau1BoacYECJ4Zo6fhagmUevuovgTuTCZTrWbLPu/scEjW1hcuLOAtpGXTcgLHBAgexwpCd8oXGB8fl59++qngRIfHk9KbdHej0MXxqCyORQv6vW7dOonFCp/7lC/FGRCYGQGCZ2bcabXKArt27ZKxsYnb5KSyWdHNQl2MHo0b3Rw0/1089fX1snr16iprcXoE7AoQPHa9ac2SQLEdDLTpI8mU9IynLPVi6s0sjUdlQZHZDlvlTN2QT9aOAMFTO7Wip9MQ0FVterstf3Wbbhiqs57kNM5V7Y8mQiFZXx8v2Bg0HA6bF8DpfzkQCJIAwROkajKWCQLFdqrWD5xIpeXXMTeiR7cC7a6LS1OkMFwWLVokOuPhQCBoAgRP0CrKeM4J6G96du7cKalU4a01V265dSRiMj+qb+GZeMTjcVm7di2vu+Z6DqQAwRPIsjKoswKlllbrv+8bHZf+9Mwtr26PRqSzyC4F2jeWUHMNB1mA4AlydRmbEdBXYesrsfOPdDYru0f1tz32X4ndGA6ZW2z5L3zTPjY1NcmKFSuoHgKBFSB4AltaBnZWQBcY/PLLLwXLq/XfdbHBofGUHEvZW2St+7F1xqNFb6MlEgmzfJoFBVy/QRYgeIJcXcZ2TkB/06Phk7/K7ewHjiZTJoCqOffRhQQd8ai0F1k2rf3QsNHQ0fDhQCDIAgRPkKvL2CYI6O02ve1W6hhOZ+RwMiWDVXju0xQOS0ciKvUllkaHQiFZvny5uc3GgUDQBQieoFeY8U0QKLXEOvdDJ1NpM/upxPt76kIhEzjNkcKVa7ltLlmyRObNm0e1EPBCgODxoswMMldgeHhYdAfrZHLy3/L0p9IykErLyXRmWtvsaMTMiYSlNRaV1iK/z8nti+7BtmzZMpk1axZFQsAbAYLHm1Iz0FwBDR297TYyMjIlGL39diqdllRWRFfD6S+DdDFcOCSi+6xFQiGJh8Oiq9WK/Ri0WCO6D5uuXmMD0CmVgA8FSIDgCVAxGcr0BDKZjPz+++8yMDAwvS9W4NNz5syRjo4OfiBaAUtOUXsCBE/t1YweV1jgxIkTopuK5u9mXeFmzOnq6upEt8JpaWmpxuk5JwI1IUDw1ESZ6GQlBPr7++XYsWOiD/LPPlPRd/foH/27/ntvb2/JZz+jo6NmhnIhy511C5yFCxfK3LlzJx3K2f7Mnj27EkPmHAg4KUDwOFkWOlVJAV1Gfffdd8sXX3xhAkYXFzzxxBNy3XXXyRtvvCEffvihvP3226ZJvf2mr80eHBw0f3J/9/P444/LypUr5dprr51S9yKRiDQ3N5s/ra2tU/pOfn+m9CU+hECNCRA8NVYwujt9gQcffFC2bNliQqatrU3effddueeee2Tr1q3ngmbp0qVmNtTQ0CAHDx6U7u5us7no7t27Zf78+WZW9NBDD5nFAJs2bTKznvb2dtMZ3Yy0p6fH3KrT3+IsWLDAhI2G1oEDB6Szs9P8vdihMyz9rs7C9Hu6t5wGH1vmTL/OfKN2BAie2qkVPb0AAQ2Prq4uee655+Tqq682Z9BA0F2rdRnzRx99ZGY8DzzwgFxzzTWyZs0ac8tNb3XpdzVgzpw5I19//bXccccdsm3bNrOB548//ij333+/3HzzzXLvvffKd999Z2ZDet5PPvlEjh49Krfeeqvoa6v1/7344oty2WWXTRjBp59+agJQg+zbb7+VF154wQRP7gzsAobMVxBwXoDgcb5EdLAcgb6+Ptm8ebO8//775qVqzzzzjHz//ffmlHfeeaccOnRoQvBoAJw+fVquuuoqMzPSGcyGDRvks88+M7fndDXaY489ZoLkq6++krfeekteeeUVufLKK83znxtvvNHMjF577TW54oor5KabbjK38T7++GN5/fXXzbY9eugsSvv0zTffyMMPP2zCrbGx0cywCJ5yKs53a0GA4KmFKtHHCxbQZzZ62+rJJ580z2Z+++03cytLA0FDSJ//5M549N/1lpvOTn744QcTNDpj0uDQz2/cuNHMfDQ0NEjee+89M2vZsWOHmd18+eWXJph0FrR48eJzz3b0Nzu33HKLmSXpoc+XdLb0/PPPm9mU3m575JFHzGyM4LngcvPFGhEgeGqkUHTzwgUeffRR+fzzz+XVV18VfZajYXHfffdVJHh0FqS38PR5kf4Q9PLLLxdt780335RLL73UzKr0ltqePXvkrrvumjCIl156yTwf0iB79tlnTQDpjIngufBa883aECB4aqNO9LIMAd2dQG9/vfPOO+YsZxcA3HDDDWbG88EHH5x7xpM749EFCTrj0Wc3+tzm6aefLpjx6G2066+/Xv744w9zq0x/pxONRuWpp56S22+/XYaGhkybL7/8sgmi3EPPf9ttt5nv6FJtnfHo57U/Z1fZlTFsvoqAswIEj7OloWOVFtBVZ3qbTZ+vVPLQ22O6+4GGlN7a02dEunxaZzO6UEF/LKphVOzQBQz6Xf2M/taHAwEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBAgeh4pBVxBAAAEfBAgeH6rMGBFAAAGHBP4HTrgKqz5sSgMAAAAASUVORK5CYII=\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:30:26,321][result_base.py 23][INFO]: response status ==>> 401
[2024-06-25 11:30:26,322][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2024-06-25 11:30:26,322][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:39,411][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:30:39,411][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:30:39,411][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:30:39,411][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:39,411][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:30:39,938][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:39,939][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:39,939][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:30:39,943][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:30:39,944][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:39,944][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8ea1c10c-9d31-4355-9a30-b81752df3113"
}
[2024-06-25 11:30:39,944][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:39,945][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:40,769][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:40,770][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:40,770][result_base.py 25][INFO]: response results ==>> "{\"name\": \"one_vertex\", \"username\": \"tigergraph\", \"timestamp\": 1698031291611, \"previewImage\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ4AAACVCAYAAABozJx4AAAAAXNSR0IArs4c6QAAEZJJREFUeF7t3VloXdUe"
[2024-06-25 11:30:40,771][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:30:40,771][rest_client.py 82][INFO]: request method ==>> "PUT"
[2024-06-25 11:30:40,772][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8ea1c10c-9d31-4355-9a30-b81752df3113"
}
[2024-06-25 11:30:40,772][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:40,772][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"username\": \"tigergraph\", \"timestamp\": 1698031291611, \"previewImage\": \"data:image/png;base64,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\", \"description\": \"\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}, \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 166.89235127478744, \"y\": 93.7025495750708}, {\"id\": \"Sejong\", \"type\": \"Province\", \"attrs\": {\"area\": 0, \"population\": 0, \"province\": \"\"}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 0, \"y\": 0}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}, {\"type\": \"Province\", \"id\": \"Sejong\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"randomPickVertexNumber\": 1, \"vertexTypeFilter\": {\"City\": false, \"Country\": false, \"Day_\": false, \"InfectionCase\": false, \"Month_\": false, \"Patient\": false, \"Province\": true, \"SearchStat\": false, \"TravelEvent\": false, \"WeatherStat\": false, \"Year_\": false}}}}"
[2024-06-25 11:30:41,802][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:41,802][result_base.py 24][INFO]: response message ==>> "Successfully upserted exploration result 'one_vertex' for graph 'MyGraph'."
[2024-06-25 11:30:41,803][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:30:41,803][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:30:41,804][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:30:41,804][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=8ea1c10c-9d31-4355-9a30-b81752df3113"
}
[2024-06-25 11:30:41,804][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:30:41,804][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:30:42,571][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:30:42,571][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:30:42,572][result_base.py 25][INFO]: response results ==>> "{\"name\": \"one_vertex\", \"username\": \"tigergraph\", \"timestamp\": 1698031291611, \"previewImage\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZ4AAABsCAYAAABaUdSpAAAAAXNSR0IArs4c6QAAEVpJREFUeF7tnVtoXFX7"
[2024-06-25 11:31:28,497][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:31:28,498][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:28,498][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:31:28,498][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:28,498][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:31:29,037][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:29,038][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:29,038][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:31:29,038][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:31:29,038][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:31:29,038][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=a173ed45-bc4d-4f9b-94e7-c629d0d109e7"
}
[2024-06-25 11:31:29,038][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:29,038][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:31:29,602][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:29,603][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:29,603][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:31:29,604][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/"
[2024-06-25 11:31:29,604][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:29,604][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=a173ed45-bc4d-4f9b-94e7-c629d0d109e7"
}
[2024-06-25 11:31:29,604][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:29,605][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:31:32,006][result_base.py 23][INFO]: response status ==>> 405
[2024-06-25 11:31:32,006][result_base.py 24][INFO]: response message ==>> "Method not allowed."
[2024-06-25 11:31:32,006][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:31:32,012][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:31:32,012][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:32,012][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:31:32,012][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:32,012][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:31:32,540][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:32,540][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:32,540][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:31:32,540][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:31:32,540][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:31:32,540][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=a0886970-47d9-4a1f-9542-e163fc7bb1f1"
}
[2024-06-25 11:31:32,540][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:32,540][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:31:33,056][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:33,057][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:33,057][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:31:33,059][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/invalid_test"
[2024-06-25 11:31:33,059][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:33,061][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=a0886970-47d9-4a1f-9542-e163fc7bb1f1"
}
[2024-06-25 11:31:33,062][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:33,062][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"invalid_test\", \"timestamp\": 1698031291611, \"username\": \"\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:31:34,168][result_base.py 23][INFO]: response status ==>> 400
[2024-06-25 11:31:34,169][result_base.py 24][INFO]: response message ==>> "Invalid payload."
[2024-06-25 11:31:34,169][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:31:34,172][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:31:34,172][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:34,172][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:31:34,172][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:34,172][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:31:34,735][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:34,736][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:34,736][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:31:34,737][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:31:34,738][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:31:34,738][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=bd25678d-76be-4846-b43e-86579be3df81"
}
[2024-06-25 11:31:34,738][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:34,738][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:31:35,268][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:35,271][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:35,271][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:31:35,273][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/invalid_test"
[2024-06-25 11:31:35,274][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:35,274][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=bd25678d-76be-4846-b43e-86579be3df81"
}
[2024-06-25 11:31:35,274][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:35,274][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"invalid_test\", \"timestamp\": 1698031291611, \"username\": \"\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}, \"previewImage\": \"\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:31:36,036][result_base.py 23][INFO]: response status ==>> 400
[2024-06-25 11:31:36,037][result_base.py 24][INFO]: response message ==>> "Invalid payload."
[2024-06-25 11:31:36,037][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:31:36,042][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:31:36,043][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:36,043][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:31:36,043][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:36,043][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:31:36,608][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:36,608][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:36,608][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:31:36,609][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:31:36,609][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:31:36,609][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": ""
}
[2024-06-25 11:31:36,609][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:36,610][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:31:37,114][result_base.py 23][INFO]: response status ==>> 401
[2024-06-25 11:31:37,114][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2024-06-25 11:31:37,114][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:31:37,114][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/invalid_test"
[2024-06-25 11:31:37,114][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:37,114][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": ""
}
[2024-06-25 11:31:37,115][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:37,115][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"invalid_test\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": null, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:31:38,116][result_base.py 23][INFO]: response status ==>> 401
[2024-06-25 11:31:38,116][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2024-06-25 11:31:38,117][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:31:38,122][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:31:38,122][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:38,122][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:31:38,122][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:38,122][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:31:39,542][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:39,542][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:39,542][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:31:39,543][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:31:39,543][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:31:39,543][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a"
}
[2024-06-25 11:31:39,543][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:39,544][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:31:40,054][result_base.py 23][INFO]: response status ==>> 401
[2024-06-25 11:31:40,055][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2024-06-25 11:31:40,056][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:31:40,057][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:31:40,058][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:40,058][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "Tigergraph=c1c96341-864b-482a-91f8-1162a48ee26a"
}
[2024-06-25 11:31:40,059][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:40,059][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": null, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:31:41,315][result_base.py 23][INFO]: response status ==>> 401
[2024-06-25 11:31:41,316][result_base.py 24][INFO]: response message ==>> "You are not authorized to use this API."
[2024-06-25 11:31:41,316][result_base.py 25][INFO]: response results ==>> "null"
[2024-06-25 11:31:41,320][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/auth/login"
[2024-06-25 11:31:41,320][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:41,320][rest_client.py 83][INFO]: request header ==>> {
    "Content-Type": "application/json"
}
[2024-06-25 11:31:41,320][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:41,320][rest_client.py 90][INFO]: request data ==>> "{\"username\": \"tigergraph\", \"password\": \"tigergraph\"}"
[2024-06-25 11:31:41,842][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:41,843][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:41,843][result_base.py 25][INFO]: response results ==>> "{\"name\": \"tigergraph\", \"roles\": {\"1\": [\"superuser\"], \"MyGraph\": [\"superuser\"], \"MyMultiEdge\": [\"superuser\"]}, \"privileges\": {\"1\": {\"privileges\": [\"READ_SCHEMA\", \"WRITE_SCHEMA\", \"READ_LOADINGJOB\", \"EXE"
[2024-06-25 11:31:41,844][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/gsql-server/gsql/v1/schema/graphs/MyGraph"
[2024-06-25 11:31:41,844][rest_client.py 82][INFO]: request method ==>> "GET"
[2024-06-25 11:31:41,844][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=eb9a43ca-eda9-4f20-a776-f121837f549f"
}
[2024-06-25 11:31:41,845][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:41,845][rest_client.py 90][INFO]: request data ==>> null
[2024-06-25 11:31:42,365][result_base.py 23][INFO]: response status ==>> 200
[2024-06-25 11:31:42,365][result_base.py 24][INFO]: response message ==>> ""
[2024-06-25 11:31:42,365][result_base.py 25][INFO]: response results ==>> "{\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \""
[2024-06-25 11:31:42,366][rest_client.py 81][INFO]: request URL ==>> "http://************:14240/api/exploration-results/MyGraph/one_vertex"
[2024-06-25 11:31:42,366][rest_client.py 82][INFO]: request method ==>> "POST"
[2024-06-25 11:31:42,366][rest_client.py 83][INFO]: request header ==>> {
    "Cookie": "TigerGraphApp=eb9a43ca-eda9-4f20-a776-f121837f549f"
}
[2024-06-25 11:31:42,366][rest_client.py 84][INFO]: request params ==>> null
[2024-06-25 11:31:42,366][rest_client.py 90][INFO]: request data ==>> "{\"name\": \"one_vertex\", \"timestamp\": 1698031291611, \"username\": \"tigergraph\", \"schema\": {\"EdgeTypes\": [{\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Country\", \"IsDirected\": false, \"Name\": \"PROVINCE_IN_COUNTRY\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"City\", \"IsDirected\": false, \"Name\": \"CITY_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [{\"AttributeName\": \"wc_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}], \"Config\": {}, \"FromVertexTypeName\": \"Province\", \"IsDirected\": false, \"Name\": \"WEATHER_CONDITION\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"InfectionCase\", \"IsDirected\": false, \"Name\": \"CASE_IN_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"YEAR_MONTH\", \"ToVertexTypeName\": \"Month_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Month_\", \"IsDirected\": false, \"Name\": \"MONTH_DAY\", \"ToVertexTypeName\": \"Day_\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SEARCH_STAMP\", \"ToVertexTypeName\": \"SearchStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"WEATHER_STAMP\", \"ToVertexTypeName\": \"WeatherStat\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"TravelEvent\", \"IsDirected\": false, \"Name\": \"TRAVEL_EVENT_IN\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"TRAVEL_STAMP\", \"ToVertexTypeName\": \"TravelEvent\"}, {\"Attributes\": [], \"Config\": {\"REVERSE_EDGE\": \"reverse_INFECTED_BY\"}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": true, \"Name\": \"INFECTED_BY\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"BELONGS_TO_CASE\", \"ToVertexTypeName\": \"InfectionCase\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"CONFIRM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"SYMPTOM_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"RELEASED_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Year_\", \"IsDirected\": false, \"Name\": \"BIRTH_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Day_\", \"IsDirected\": false, \"Name\": \"DECEASE_STAMP\", \"ToVertexTypeName\": \"Patient\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_CITY\", \"ToVertexTypeName\": \"City\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_PROVINCE\", \"ToVertexTypeName\": \"Province\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_FROM_COUNTRY\", \"ToVertexTypeName\": \"Country\"}, {\"Attributes\": [], \"Config\": {}, \"FromVertexTypeName\": \"Patient\", \"IsDirected\": false, \"Name\": \"PATIENT_TRAVELED\", \"ToVertexTypeName\": \"TravelEvent\"}], \"GraphName\": \"MyGraph\", \"VertexTypes\": [{\"Attributes\": [{\"AttributeName\": \"city\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"elementary_school_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"kindergarten_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"university_count\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"academy_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_population_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"elderly_alone_ratio\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"nursing_home_count\", \"AttributeType\": {\"Name\": \"UINT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"City\", \"PrimaryId\": {\"AttributeName\": \"city_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"province\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"population\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"area\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Province\", \"PrimaryId\": {\"AttributeName\": \"province_id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"country\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Country\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"cold\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"flu\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"pneumonia\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"coronavirus\", \"AttributeType\": {\"Name\": \"FLOAT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"SearchStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"code\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"avg_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"min_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_temp\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"precipitation\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"max_wind_speed\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"most_wind_direction\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}, {\"AttributeName\": \"avg_relative_humidity\", \"AttributeType\": {\"Name\": \"DOUBLE\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"WeatherStat\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"confirmed\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"ic_group\", \"AttributeType\": {\"Name\": \"BOOL\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"InfectionCase\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}, {\"Attributes\": [{\"AttributeName\": \"year_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Year_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"month_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Month_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"day_\", \"AttributeType\": {\"Name\": \"INT\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Day_\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"latitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"longitude\", \"AttributeType\": {\"Name\": \"FLOAT\"}}, {\"AttributeName\": \"visited_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"travel_type\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"TravelEvent\", \"PrimaryId\": {\"AttributeName\": \"id\", \"AttributeType\": {\"Name\": \"STRING\"}}}, {\"Attributes\": [{\"AttributeName\": \"global_num\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"birth_year\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"infection_case\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"contact_number\", \"AttributeType\": {\"Name\": \"UINT\"}}, {\"AttributeName\": \"symptom_onset_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"confirmed_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"released_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"deceased_date\", \"AttributeType\": {\"Name\": \"DATETIME\"}}, {\"AttributeName\": \"state\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"disease\", \"AttributeType\": {\"Name\": \"STRING\"}}, {\"AttributeName\": \"sex\", \"AttributeType\": {\"Name\": \"STRING\"}}], \"Config\": {\"PRIMARY_ID_AS_ATTRIBUTE\": true, \"STATS\": \"OUTDEGREE_BY_EDGETYPE\"}, \"Name\": \"Patient\", \"PrimaryId\": {\"AttributeName\": \"patient_id\", \"AttributeType\": {\"Name\": \"STRING\"}, \"PrimaryIdAsAttribute\": true}}]}, \"previewImage\": \"data:image/png;base64,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\", \"data\": {\"explorationResult\": {\"latestGraph\": {\"nodes\": [{\"id\": \"Gimhae-si\", \"type\": \"City\", \"attrs\": {\"academy_ratio\": 0, \"city\": \"\", \"elderly_alone_ratio\": 0, \"elderly_population_ratio\": 0, \"elementary_school_count\": 0, \"kindergarten_count\": 0, \"nursing_home_count\": 0, \"university_count\": 0}, \"styles\": {}, \"others\": {}, \"labels\": {}, \"x\": 766, \"y\": 276.5}], \"links\": []}, \"latestSelections\": {\"nodes\": [{\"type\": \"City\", \"id\": \"Gimhae-si\"}], \"links\": []}, \"latestLayout\": \"force\"}, \"graphChartConfig\": null, \"graphExplorerConfig\": {\"vertexTypeFilter\": {\"City\": true, \"Province\": false, \"Country\": false, \"SearchStat\": false, \"WeatherStat\": false, \"InfectionCase\": false, \"Year_\": false, \"Month_\": false, \"Day_\": false, \"TravelEvent\": false, \"Patient\": false}, \"randomPickVertexNumber\": 1}}, \"description\": \"\"}"
[2024-06-25 11:31:43,444][result_base.py 23][INFO]: response status ==>> 409
[2024-06-25 11:31:43,445][result_base.py 24][INFO]: response message ==>> "Exploration result 'one_vertex' already exists on graph 'MyGraph'."
[2024-06-25 11:31:43,445][result_base.py 25][INFO]: response results ==>> "null"
