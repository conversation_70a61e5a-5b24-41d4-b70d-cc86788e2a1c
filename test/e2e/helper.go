package e2e

import (
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/tigergraph/cqrs/util/rand"
)

const (
	domain = "http://localhost:14240"
	TGApp  = "TigerGraphApp"
)

func login(t *testing.T) string {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		domain+"/api/auth/login",
		strings.NewReader(`{"username":"tigergraph","password":"tigergraph"}`),
	)

	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	if http.StatusOK != res.StatusCode {
		require.FailNow(t, string(b))
	}
	cookieV := ""
	for _, cookie := range res.Cookies() {
		if cookie.Name == TGApp {
			cookieV = cookie.Value
			break
		}
	}
	if cookieV == "" {
		require.FailNow(t, "cookie is empty")
	}
	return cookieV
}

type closer func(t *testing.T)

func setupDatabase(t *testing.T, cookie string) (string, closer) {
	client := http.Client{}

	graphName := "gus_e2e_test_" + rand.String(3)

	// create schema
	req, _ := http.NewRequest(
		http.MethodPost,
		domain+"/api/gsql-server/gsql/schema",
		strings.NewReader(fmt.Sprintf(`{
			"GraphName": "%s",
			"VertexTypes": [],
			"EdgeTypes": []
		}`, graphName)),
	)
	req.Header.Set("Cookie", fmt.Sprintf("%s=%s", TGApp, cookie))
	res, _ := client.Do(req)
	body, _ := io.ReadAll(res.Body)
	require.Equal(t, http.StatusOK, res.StatusCode, string(body))
	require.Equal(t, `{"error":false,"message":"","results":"The graph `+graphName+` is created.\n"}`, string(body))

	return graphName, func(t *testing.T) {
		// delete the graph
		req, _ := http.NewRequest(
			http.MethodDelete,
			domain+"/api/gsql-server/gsql/schema?graph="+graphName,
			nil,
		)
		req.Header.Set("Cookie", fmt.Sprintf("%s=%s", TGApp, cookie))
		res, _ := client.Do(req)
		body, _ := io.ReadAll(res.Body)
		require.Equal(t, http.StatusOK, res.StatusCode, string(body))
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, ``, string(b))
	}
}

func stream(t *testing.T, cookie string) io.ReadCloser {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodGet,
		"http://localhost:14240/api/stream",
		nil,
	)
	req.Header.Set("Cookie", fmt.Sprintf("%s=%s", TGApp, cookie))
	res, _ := client.Do(req)
	require.Equal(t, http.StatusOK, res.StatusCode)
	fmt.Println("stream request id", res.Header.Get("Request-Id"))
	return res.Body
}
