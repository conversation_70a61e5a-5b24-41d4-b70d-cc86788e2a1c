package e2e

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func createRandom(name string) string {
	n, _ := rand.Int(rand.Reader, big.NewInt(1000))

	return fmt.Sprintf("%s_%d", name, n)
}

// https://graphsql.atlassian.net/browse/GST-2034
func TestRequiredPrivileges(t *testing.T) {
	if testing.Short() {
		t.Skip("short mode skip e2e tests")
	}
	// login with superuser
	suCookie := loginWithUser(t, "tigergraph", "tigergraph")

	// create dependencies
	graphName := createRandom("graph")
	userName := createRandom("user")
	roleName := createRandom("role")
	createGraph(t, suCookie, graphName)
	createUser(t, suCookie, userName, userName)
	createRole(t, suCookie, roleName, graphName)
	addPrivilegeToRole(t, suCookie, graphName, "WRITE_QUERY", roleName)
	addUserToRole(t, suCookie, graphName, userName, roleName)

	// check required privileges

	cookie := loginWithUser(t, userName, userName)
	createQueryDraft(t, cookie, graphName, "hello")
	getQueryDraft(t, cookie, graphName, "hello")

}

type Response struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Result  interface{} `json:"results"`
}

func loginWithUser(t *testing.T, username string, password string) string {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		"http://localhost:14240/api/auth/login",
		strings.NewReader(fmt.Sprintf(`{"username":"%s","password":"%s"}`, username, password)),
	)

	res, _ := client.Do(req)
	cookieV := ""
	for _, cookie := range res.Cookies() {
		if cookie.Name == "TigerGraphApp" {
			cookieV = cookie.Value
			break
		}
	}
	if cookieV == "" {
		require.FailNow(t, "cookie is empty")
	}
	return cookieV
}

func createUser(t *testing.T, cookie string, username string, password string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		"http://localhost:14240/api/gsql-server/gsql/users",
		strings.NewReader(fmt.Sprintf(`{"name":"%s","password":"%s"}`, username, password)),
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	response := Response{}
	_ = json.Unmarshal(b, &response)
	require.Equal(t, false, response.Error, response.Message)
}

func createRole(t *testing.T, cookie string, role string, graphName string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("http://localhost:14240/api/gsql-server/gsql/roles?graph=%s", graphName),
		strings.NewReader(fmt.Sprintf(`{"roles":["%s"]}`, role)),
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	response := Response{}
	_ = json.Unmarshal(b, &response)
	require.Equal(t, false, response.Error, response.Message)

}

func addPrivilegeToRole(t *testing.T, cookie string, graphName string, privilege string, role string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("http://localhost:14240/api/gsql-server/gsql/role?graph=%s", graphName),
		strings.NewReader(fmt.Sprintf(`{"privileges":["%s"],"roles":["%s"]}`, privilege, role)),
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	response := Response{}
	_ = json.Unmarshal(b, &response)
	require.Equal(t, false, response.Error, response.Message)
}

func createGraph(t *testing.T, cookie string, graphName string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		"http://localhost:14240/api/gsql-server/gsql/schema",
		strings.NewReader(fmt.Sprintf(`{"GraphName":"%s","VertexTypes":[],"EdgeTypes":[]}`, graphName)),
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	response := Response{}
	_ = json.Unmarshal(b, &response)
	require.Equal(t, false, response.Error, response.Message)
}

func addUserToRole(t *testing.T, cookie string, graphName string, username string, role string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("http://localhost:14240/api/gsql-server/gsql/user?graph=%s", graphName),
		strings.NewReader(fmt.Sprintf(`{"roles":["%s"],"usernames":["%s"]}`, role, username)),
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	response := Response{}
	_ = json.Unmarshal(b, &response)
	require.Equal(t, false, response.Error, response.Message)
}

func createQueryDraft(t *testing.T, cookie string, graphName string, query string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("http://localhost:14240/api/queries/%s/info/%s/draft", graphName, query),
		strings.NewReader(`{"name":"hello","code":"CREATE QUERY hello(/* Parameters here */) FOR GRAPH g1 { \n  /* Write query logic here */ \n  PRINT \"hello works!\"; \n}","graphUpdate":false}`),
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	response := Response{}
	_ = json.Unmarshal(b, &response)
	require.Equal(t, false, response.Error, response.Message)
}

func getQueryDraft(t *testing.T, cookie string, graphName string, query string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodGet,
		fmt.Sprintf("http://localhost:14240/api/queries/%s/info/%s", graphName, query),
		nil,
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	response := Response{}
	_ = json.Unmarshal(b, &response)
	require.Equal(t, true, response.Error)
	require.Equal(t, fmt.Sprintf("READ_QUERY privilege is required on graph %s.", graphName), response.Message)
}
