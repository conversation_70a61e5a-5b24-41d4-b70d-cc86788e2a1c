package e2e

import (
	"bufio"
	"bytes"
	_ "embed"
	"encoding/csv"
	"io"
	"io/fs"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	tgFS "github.com/tigergraph/gus/lib/fs"
)

func TestImportSolution(t *testing.T) {
	if testing.Short() {
		t.Skip("short mode skip e2e tests")
	}

	// setup
	cookie := login(t)
	client := http.Client{}

	// Each test takes about 65 seconds

	t.Run("sse import request", func(t *testing.T) {
		req, _ := generateReq("testdata/export.tar.gz")
		req.Header.Set("Cookie", "TigerGraphApp="+cookie)

		res, _ := client.Do(req)
		defer res.Body.Close()

		// Verify SSE headers
		require.Equal(t, http.StatusOK, res.StatusCode)
		require.Equal(t, "text/event-stream", res.Header.Get("Content-Type"))
		require.Equal(t, "no-cache", res.Header.Get("Cache-Control"))
		require.Equal(t, "keep-alive", res.Header.Get("Connection"))

		// Read and verify SSE events
		var lastEvent string
		var foundSuccessMessage bool
		var foundDoneEvent bool

		reader := bufio.NewReader(res.Body)
		for {
			line, err := reader.ReadString('\n')
			if err != nil {
				break
			}

			// Look for SSE data lines
			if strings.HasPrefix(line, "data: ") {
				data := strings.TrimPrefix(line, "data: ")
				data = strings.TrimSpace(data)
				lastEvent = data

				// Check for success message
				if strings.Contains(data, "Successfully imported solution") {
					foundSuccessMessage = true
				}

				// Check for done event
				if strings.Contains(data, `"done":true`) {
					foundDoneEvent = true
					break
				}

				// Check for error events
				if strings.Contains(data, `"error":true`) {
					t.Fatalf("Import failed with error: %s", data)
				}

				// Print progress for debugging
				t.Logf("SSE Event: %s", data)
			}
		}

		require.True(t, foundSuccessMessage, "Should receive success message")
		require.True(t, foundDoneEvent, "Should receive done event")
		require.Contains(t, lastEvent, "Successfully imported solution", "Final event should contain success message")
	})

}

func TestExportAndImport(t *testing.T) {
	if testing.Short() {
		t.Skip("short mode skip e2e tests")
	}

	// setup
	cookie := login(t)
	client := http.Client{}

	// test the data export and import
	t.Run("compare the count of data records between twice export operations", func(t *testing.T) {
		count1, filePath, err := exportThenCountRecords(cookie)
		if err != nil {
			require.FailNow(t, "export or count data records failed")
		}
		defer os.Remove(filePath)
		req, err := generateReq(filePath)
		if err != nil {
			require.FailNow(t, "export or count data records failed")
		}
		req.Header.Set("Cookie", "TigerGraphApp="+cookie)

		res, _ := client.Do(req)
		require.Equal(t, http.StatusOK, res.StatusCode)

		count2, filePath2, err := exportThenCountRecords(cookie)
		if err != nil {
			require.FailNow(t, "export or count data records failed")
		}
		defer os.Remove(filePath2)
		require.Equal(t, count1, count2)
	})

}

func generateReq(filePath string) (*http.Request, error) {
	// form data
	file, _ := os.Open(filePath)
	defer file.Close()
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", filepath.Base(file.Name()))
	io.Copy(part, file)
	writer.Close()

	req, err := http.NewRequest(
		http.MethodPost,
		domain+"/api/system/import",
		body,
	)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	return req, err
}

func exportThenCountRecords(sessionID string) (int, string, error) {
	var count int = 0

	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodGet,
		"http://localhost:14240/api/system/export?isExportData=1",
		nil)
	req.Header.Set("Cookie", "TigerGraphApp="+sessionID)
	resp, err := client.Do(req)
	if err != nil {
		return count, "", err
	}

	pwd, err := os.Getwd()
	if err != nil {
		return count, "", err
	}
	tempDir := path.Join(pwd, "testdata")
	out, err := os.CreateTemp(tempDir, "export_*.tar.gz")
	if err != nil {
		return count, "", err
	}
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return count, "", err
	}
	file := out.Name()
	tempExportDir, err := os.MkdirTemp(tempDir, "export_data_*")
	if err != nil {
		return count, "", err
	}
	defer os.RemoveAll(tempExportDir)

	srcFile, err := os.Open(file)
	if err != nil {
		return count, "", err
	}
	defer srcFile.Close()
	if err := tgFS.Decompress(srcFile, tempExportDir); err != nil {
		return count, "", err
	}

	tempExportGraphDir := path.Join(tempExportDir, "graph")
	fileSystem := os.DirFS(tempExportGraphDir)
	var recordCount int = 0
	err = fs.WalkDir(fileSystem, ".", func(p string, d fs.DirEntry, err error) error {
		file := path.Join(tempExportGraphDir, p)
		if err != nil {
			return err
		}
		if !d.IsDir() && strings.HasSuffix(d.Name(), ".csv") {
			recordCount, err = countCsvRecords(file)
			if err != nil {
				return err
			}
			count += recordCount
		}
		return nil
	})
	if err != nil {
		return count, " ", err
	}

	return count, file, nil
}

func countCsvRecords(file string) (int, error) {
	csvFile, _ := os.Open(file)
	csvR := csv.NewReader(csvFile)
	defer csvFile.Close()

	var count int = 0
	_, err := csvR.Read()
	if err != nil {
		return count, err
	}
	for {
		_, err := csvR.Read()
		if err != nil && err != io.EOF {
			return count, err
		}
		if err == io.EOF {
			break
		}
		count += 1
	}

	return count, nil
}
