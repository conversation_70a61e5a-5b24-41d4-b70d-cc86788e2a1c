package e2e

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/tigergraph/gus/router"
)

func TestLogPath(t *testing.T) {
	if testing.Short() {
		t.Skip("short mode skip e2e tests")
	}

	cookie := login(t)
	client := http.Client{}

	testCases := []struct {
		name     string
		query    string
		expected string
	}{
		{
			name: "log list query",
			query: `{
				Log {
				  List(
					FilePath: "../../../../proc/cpuinfo",
					ServiceName: "EXE",
					Replica: 0,
					Partition: 0,
					HostID: "m1",
					FileRegxFilter: ""
				  ) {
					Infos {
					  Name
					  Path
					  Size
					  IsDir
					  ModTime
					  SourcePath
					}
				  }
				}
			  }`,
			expected: `{"errors":[{"message":"Path '../../../../proc/cpuinfo' is not inside /home/<USER>/tigergraph/log","path":["Log","List"]}],"data":null}`,
		},
		{
			name: "log content query",
			query: `{
				Log {
				  Content(
					FilePath: "../../../../proc/cpuinfo",
					ServiceName: "EXE",
					HostID: "m1",
					Page: { Offset: 0, Length: 1000 }
				  ) {
					Data
				  }
				}
			  }`,
			expected: `{"errors":[{"message":"Path '../../../../proc/cpuinfo' is not inside /home/<USER>/tigergraph/log","path":["Log","Content"]}],"data":null}`,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			payload := router.Params{
				Query:         testCase.query,
				OperationName: "",
			}
			body, _ := json.Marshal(payload)
			req, _ := http.NewRequest(
				http.MethodPost,
				"http://localhost:14240/api/v2",
				bytes.NewReader(body),
			)
			req.Header.Set("Cookie", "TigerGraphApp="+cookie)

			res, _ := client.Do(req)
			b, _ := io.ReadAll(res.Body)
			require.Equal(t, testCase.expected, string(b))
		})
	}

}
