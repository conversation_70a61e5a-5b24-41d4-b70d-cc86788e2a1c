package e2e

import (
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestLogin(t *testing.T) {
	if testing.Short() {
		t.<PERSON>p("short mode skip e2e tests")
	}

	t.Run("success", func(t *testing.T) {
		login(t)
	})
	t.Run("fail", func(t *testing.T) {
		client := http.Client{}
		req, _ := http.NewRequest(
			http.MethodPost,
			domain+"/api/auth/login",
			strings.NewReader(`{"username":"wrong","password":"wrong"}`),
		)

		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, http.StatusUnauthorized, res.StatusCode)
		require.Equal(t, `{"error":true,"message":"Failed to authenticate with GSQL server.","results":null}`, string(b))
	})
}
