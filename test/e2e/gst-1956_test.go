package e2e

import (
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestLog(t *testing.T) {
	if testing.Short() {
		t.<PERSON><PERSON>("short mode skip e2e tests")
	}
	// https://graphsql.atlassian.net/browse/GST-1956
	cookie := login(t)

	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodGet,
		"http://localhost:14240/api/log?path=../../../../../etc",
		strings.NewReader(`{"username":"tigergraph","password":"tigergraph"}`),
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)

	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	// fmt.Println(string(b))
	require.Equal(t, `{"error":true,"message":"Path '../../../../../etc' is not inside /home/<USER>/tigergraph/log","results":null}`, string(b))
}
