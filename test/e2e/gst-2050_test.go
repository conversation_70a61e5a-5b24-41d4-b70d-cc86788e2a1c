package e2e

import (
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

// https://graphsql.atlassian.net/browse/GST-2050
func TestSearchFile(t *testing.T) {
	if testing.Short() {
		t.Skip("short mode skip e2e tests")
	}
	login := func(t *testing.T) string {
		client := http.Client{}
		req, _ := http.NewRequest(
			http.MethodPost,
			"http://localhost:14240/api/auth/login",
			strings.NewReader(`{"username":"tigergraph","password":"tigergraph"}`),
		)

		res, _ := client.Do(req)
		cookieV := ""
		for _, cookie := range res.Cookies() {
			if cookie.Name == TGApp {
				cookieV = cookie.Value
				break
			}
		}
		if cookieV == "" {
			require.FailNow(t, "cookie is empty")
		}
		return cookieV
	}

	sessionID := login(t)
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodGet,
		"http://localhost:14240/api/log/search?pattern=sdf&component=gui&hostID=NonExist",
		nil,
	)
	req.Header.Set("Cookie", "TigerGraphApp="+sessionID)

	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	require.Equal(t, http.StatusOK, res.StatusCode)
	require.Equal(t, `{"error":false,"message":"","results":{"NonExist":[]}}`, string(b))
}
