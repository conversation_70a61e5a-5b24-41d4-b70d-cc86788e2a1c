package e2e

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// https://graphsql.atlassian.net/browse/GST-1959
func TestAbleToReceiveSseIn2Clients(t *testing.T) {
	if testing.Short() {
		t.Skip("short mode skip e2e tests")
	}

	// setup
	cookie := login(t)
	graphName, close := setupDatabase(t, cookie)
	defer close(t)

	res1 := stream(t, cookie)
	fmt.Println(2)
	res2 := stream(t, cookie)

	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodGet,
		"http://localhost:14240/api/gsql-server/interpreted_query",
		strings.NewReader(fmt.Sprintf(`INTERPRET QUERY () FOR GRAPH %s { PRINT "demo1 works!"; }`, graphName)),
	)
	req.Header.Set("<PERSON>ie", "TigerGraphApp="+cookie)
	req.Header.Set("request-type", "long")
	res, _ := client.Do(req)
	body, _ := io.ReadAll(res.Body)
	require.Equal(t, http.StatusAccepted, res.StatusCode, string(body))

	go io.Copy(os.Stdout, res1)
	go io.Copy(os.Stdout, res2)

	time.Sleep(time.Millisecond * 200)

}
