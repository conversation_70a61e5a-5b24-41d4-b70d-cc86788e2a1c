package e2e

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
)

// https://graphsql.atlassian.net/browse/GST-2075
func TestLocalGraphStyles(t *testing.T) {
	if testing.Short() {
		t.Skip("short mode skip e2e tests")
	}

	sessionID := login(t)
	t.Run("vertexType inValid", func(t *testing.T) {
		styles := "{\"vertexStyles\":{\"@@@@文%……&¥¥……&\":{}},\"edgeStyles\":{\"@@@@文%……123231\":{},\"e1\":{}}}"
		res, _ := localGraphStyleReq(styles, sessionID)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, http.StatusBadRequest, res.StatusCode)
		fmt.Println(string(b))
	})
}

func localGraphStyleReq(styles, sessionID string) (*http.Response, error) {
	client := http.Client{}
	payload, _ := json.Marshal(styles)
	req, _ := http.NewRequest(
		http.MethodPut,
		"http://localhost:14240/api/graph-styles/local/test_graph",
		bytes.NewReader(payload),
	)
	req.Header.Set("Cookie", "TigerGraphApp="+sessionID)
	res, err := client.Do(req)
	return res, err
}
