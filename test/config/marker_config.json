{"all_customized_markers": [{"marker": "dailyrun_api", "tools_cases": [{"case": "api and preexec", "n_cpus": 1, "reruns": 2, "order": 1}, {"case": "api and dependency10", "n_cpus": 10, "reruns": 2, "order": 2}, {"case": "api and dependency20", "n_cpus": 10, "reruns": 2, "order": 3}, {"case": "smoke and api and not preexec and not dependency10 and not dependency20 and not teardown", "n_cpus": 20, "reruns": 2, "order": 4}, {"case": "smoke and api and teardown", "n_cpus": 1, "reruns": 2, "order": 5}]}, {"marker": "dailyrun_api_cloud", "tools_cases": [{"case": "apilogin_cloud", "n_cpus": 1, "reruns": 2, "order": 1}, {"case": "api and preexec and not onprem", "n_cpus": 1, "reruns": 2, "order": 2}, {"case": "smoke and api and not preexec and not apilogout and not onprem", "n_cpus": 10, "reruns": 2, "order": 3}, {"case": "smoke and api and teardown", "n_cpus": 10, "reruns": 2, "order": 5}, {"case": "apilogout and not onprem", "n_cpus": 10, "reruns": 2, "order": 4}]}, {"marker": "login_test", "tools_cases": [{"case": "login10-1", "order": 10}, {"case": "login10-2", "order": 10}, {"case": "login1", "order": 9}, {"case": "login2-1", "order": 11}, {"case": "login2-2", "order": 11}]}]}