{"all_customized_markers": [{"marker": "dailyrun_ui", "tools_cases": [{"case": "preexec and not api", "n_cpus": 1, "reruns": 2, "order": 1}, {"case": "basecases and not api", "n_cpus": 2, "reruns": 2, "order": 2}, {"case": "smoke and tools and not preexec and not basecases and not teardown and not api", "parallelize": true, "n_cpus": 6, "reruns": 3, "order": 10}, {"case": "teardown and not api", "n_cpus": 1, "reruns": 3, "order": 12}]}, {"marker": "dailyrun_api", "tools_cases": [{"case": "api and preexec", "n_cpus": 1, "reruns": 2, "order": 1}, {"case": "api and dependency10", "n_cpus": 10, "reruns": 2, "order": 2}, {"case": "api and dependency20", "n_cpus": 10, "reruns": 2, "order": 3}, {"case": "smoke and api and not preexec and not dependency10 and not dependency20 and not teardown", "n_cpus": 20, "reruns": 2, "order": 4}, {"case": "smoke and api and teardown", "n_cpus": 1, "reruns": 2, "order": 5}]}, {"marker": "dailyrun_api_cloud", "tools_cases": [{"case": "apilogin_cloud", "n_cpus": 1, "reruns": 2, "order": 1}, {"case": "api and preexec and not onprem", "n_cpus": 1, "reruns": 2, "order": 2}, {"case": "smoke and api and not preexec and not apilogout and not onprem", "n_cpus": 10, "reruns": 2, "order": 3}, {"case": "smoke and api and teardown", "n_cpus": 10, "reruns": 2, "order": 5}, {"case": "apilogout and not onprem", "n_cpus": 10, "reruns": 2, "order": 4}]}, {"marker": "tools_dailyrun", "tools_cases": [{"case": "preexec and not onprem and not api", "n_cpus": 1, "reruns": 2, "order": 1}, {"case": "basecases and not onprem and not api", "n_cpus": 2, "reruns": 2, "order": 2}, {"case": "smoke and tools and not preexec and not basecases and not onprem", "parallelize": true, "n_cpus": 5, "reruns": 2, "order": 10}]}, {"marker": "tools_wip_e2e", "tools_cases": [{"case": "preexec and not offline and not api", "n_cpus": 1, "reruns": 2, "order": 1}, {"case": "basecases and not offline and not api", "n_cpus": 2, "reruns": 2, "order": 2}, {"case": "smoke and tools and not preexec and not basecases and not offline and not teardown and not api", "parallelize": true, "n_cpus": 3, "reruns": 5, "order": 10}, {"case": "teardown and not api", "n_cpus": 1, "reruns": 3, "order": 12}]}, {"marker": "k8s_operator", "tools_cases": [{"case": "preexec and not onprem", "n_cpus": 1, "reruns": 2, "order": 1}, {"case": "basecases and not onprem", "n_cpus": 2, "reruns": 2, "order": 2}, {"case": "smoke and tools and not preexec and not basecases and not onprem and not gapsecret", "parallelize": true, "n_cpus": 5, "reruns": 2, "order": 10}]}, {"marker": "onprem_benchmark", "tools_cases": [{"case": "onprem_benchmark_setup", "n_cpus": 1, "reruns": 3, "order": 0}, {"case": "import_solution and benchmark", "n_cpus": 1, "reruns": 3, "order": 1}, {"case": "load_data and benchmark", "n_cpus": 1, "order": 2}, {"case": "install_query and benchmark", "n_cpus": 1, "reruns": 3, "order": 4}, {"case": "run_installed_query and benchmark", "reruns": 3, "order": 5}]}, {"marker": "login_test", "tools_cases": [{"case": "login10-1", "order": 10}, {"case": "login10-2", "order": 10}, {"case": "login1", "order": 9}, {"case": "login2-1", "order": 11}, {"case": "login2-2", "order": 11}]}]}