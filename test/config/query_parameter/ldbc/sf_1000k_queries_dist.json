{"Name": "QueryParameters", "SolutionList": [{"Solution": "ldbc", "Description": "ldbc solution managed by QA for benchmark testing", "Tag": "ldbc", "SolutionURL": "", "DataURL": "", "GraphName": "ldbc-snb", "Queries": [{"Query": "bi_1_dist", "Sequence": 0, "Parameters": [{"parameter": "maxDate", "type": "datetime", "value": "2011-07-22 00:00:00"}]}, {"Query": "bi_2_dist", "Sequence": 1, "Parameters": [{"parameter": "startDate", "type": "datetime", "value": "2010-01-01 00:00:00"}, {"parameter": "endDate", "type": "datetime", "value": "2010-11-08 00:00:00"}, {"parameter": "country1Name", "type": "string", "value": "Ethiopia"}, {"parameter": "country2Name", "type": "string", "value": "Spain"}]}, {"Query": "bi_3_dist", "Sequence": 2, "Parameters": [{"parameter": "year1", "type": "INT", "value": 2010}, {"parameter": "month1", "type": "INT", "value": 10}]}, {"Query": "bi_4_dist", "Sequence": 3, "Parameters": [{"parameter": "tagClassName", "type": "string", "value": "MusicalArtist"}, {"parameter": "countryName", "type": "string", "value": "Burma"}]}, {"Query": "bi_5_dist", "Sequence": 4, "Parameters": [{"parameter": "countryName", "type": "string", "value": "Belarus"}]}, {"Query": "bi_6_dist", "Sequence": 5, "Parameters": [{"parameter": "tagName", "type": "string", "value": "Abbas_I_of_Persia"}]}, {"Query": "bi_7_dist", "Sequence": 6, "Parameters": [{"parameter": "tagName", "type": "string", "value": "<PERSON><PERSON>"}]}, {"Query": "bi_8_dist", "Sequence": 7, "Parameters": [{"parameter": "tagName", "type": "string", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"Query": "bi_9_dist", "Sequence": 8, "Parameters": [{"parameter": "tagClass1Name", "type": "string", "value": "BaseballPlayer"}, {"parameter": "tagClass2Name", "type": "string", "value": "ChristianBishop"}, {"parameter": "threshold", "type": "INT", "value": "200"}]}, {"Query": "bi_10_dist", "Sequence": 9, "Parameters": [{"parameter": "tagName", "type": "string", "value": "<PERSON><PERSON>"}, {"parameter": "minDate", "type": "datetime", "value": "2012-01-22 00:00:00"}]}, {"Query": "bi_11_dist", "Sequence": 10, "Parameters": [{"parameter": "countryName", "type": "string", "value": "Mexico"}, {"parameter": "blacklist", "type": "set", "setElementType": "string", "value": ["has", "Green"]}]}, {"Query": "bi_12_dist", "Sequence": 11, "Parameters": [{"parameter": "minDate", "type": "datetime", "value": "2011-07-21 22:00:00"}, {"parameter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "INT", "value": 400}]}, {"Query": "bi_13_dist", "Sequence": 12, "Parameters": [{"parameter": "countryName", "type": "string", "value": "Burma"}]}, {"Query": "bi_14_dist", "Sequence": 13, "Parameters": [{"parameter": "startDate", "type": "datetime", "value": "2012-05-31 22:00:00"}, {"parameter": "endDate", "type": "datetime", "value": "2012-06-30 22:00:00"}]}, {"Query": "bi_15_dist", "Sequence": 14, "Parameters": [{"parameter": "countryName", "type": "string", "value": "Turkey"}]}, {"Query": "bi_16_dist", "Sequence": 15, "Parameters": [{"parameter": "personId", "type": "Vertex id", "value": "19791209310731"}, {"parameter": "countryName", "type": "string", "value": "Pakistan"}, {"parameter": "tagClassName", "type": "string", "value": "MusicalArtist"}, {"parameter": "minPathDistance", "type": "INT", "value": 3}, {"parameter": "maxPathDistance", "type": "INT", "value": 5}]}, {"Query": "bi_17_dist", "Sequence": 16, "Parameters": [{"parameter": "countryName", "type": "string", "value": "Spain"}]}, {"Query": "bi_18_dist", "Sequence": 17, "Parameters": [{"parameter": "minDate", "type": "datetime", "value": "2011-08-15 00:00:00"}, {"parameter": "lengthThreshold", "type": "INT", "value": 97}, {"parameter": "languages", "type": "set", "setElementType": "string", "value": ["tk"]}]}, {"Query": "bi_19_dist", "Sequence": 18, "Parameters": [{"parameter": "minDate", "type": "datetime", "value": "1989-01-01 00:00:00"}, {"parameter": "tagClass1Name", "type": "string", "value": "MusicalArtist"}, {"parameter": "tagClass2Name", "type": "string", "value": "OfficeHolder"}]}, {"Query": "bi_20_dist", "Sequence": 19, "Parameters": [{"parameter": "tagClassNames", "type": "set", "setElementType": "string", "value": ["Writer", "Single", "Country"]}]}, {"Query": "bi_21_dist", "Sequence": 20, "Parameters": [{"parameter": "countryName", "type": "string", "value": "Ethiopia"}, {"parameter": "endDate", "type": "datetime", "value": "2013-01-01 00:00:00"}]}, {"Query": "bi_22_dist", "Sequence": 21, "Parameters": [{"parameter": "country1Name", "type": "string", "value": "Mexico"}, {"parameter": "country2Name", "type": "string", "value": "Indonesia"}]}, {"Query": "bi_23_dist", "Sequence": 22, "Parameters": [{"parameter": "countryName", "type": "string", "value": "Ethiopia"}]}, {"Query": "bi_24_dist", "Sequence": 23, "Parameters": [{"parameter": "tagClassName", "type": "string", "value": "Single"}]}]}]}