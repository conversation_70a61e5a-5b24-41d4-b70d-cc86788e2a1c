{"Name": "QueryParameters", "SolutionList": [{"Solution": "COVID-19 Analysis", "Enable": true, "Description": "Detect hubs of infection and track the movements of potential spreaders", "Tag": "covid-19", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/COVID19-Analysis.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/covid19_data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "age_distribution", "Sequence": 0}, {"Query": "all_connection", "Sequence": 1, "Parameters": [{"parameter": "p", "type": "Vertex id", "vertexType": "Patient", "value": **********}]}, {"Query": "edge_crawl", "Sequence": 2}, {"Query": "infection_subgraph", "Sequence": 3, "Parameters": [{"parameter": "p", "type": "Vertex id", "vertexType": "Patient", "value": **********}]}, {"Query": "list_patients_infected_by", "Sequence": 4, "Parameters": [{"parameter": "p", "type": "Vertex id", "vertexType": "Patient", "value": **********}]}, {"Query": "most_direct_infections", "Sequence": 5}, {"Query": "page_rank", "Sequence": 6, "Parameters": [{"parameter": "max_change", "type": "FLOAT", "value": 10}, {"parameter": "max_iter", "type": "INT", "value": 5}, {"parameter": "damping", "type": "FLOAT", "value": 0.5}, {"parameter": "display", "type": "BOOL", "inputType": "dropdownlist", "value": "true"}, {"parameter": "output_limit", "type": "INT", "value": 10}]}, {"Query": "use_map", "Sequence": 7}], "cityVertexIds": ["Jongno-gu", "Dong-gu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saha-gu", "Seocho-gu", "Nowon-gu", "<PERSON><PERSON><PERSON>gun", "Uijeongbu<PERSON>si", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dong<PERSON>cheon-si"]}, {"Solution": "GSQL 101", "Enable": true, "Description": "TigerGraph Introductory Solution", "Tag": "gsql101", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/GSQL101-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/gsql101-data.tar.gz", "GraphName": "social", "Queries": [{"Query": "hello", "Sequence": 0, "Parameters": [{"parameter": "p", "type": "Vertex id", "value": "<PERSON>"}]}, {"Query": "hello2", "Sequence": 1, "Parameters": [{"parameter": "p", "type": "Vertex id", "value": "Nancy"}]}]}, {"Solution": "Customer 360 - Attribution and Engagement Graph", "Enable": true, "Description": "Create a real-time 360-degree view of the customer journey for attribution and engagement insights", "Tag": "customer-360-attribution", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Customer-Attribution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/custattribution_sfdc/2.5/custattribution_sfdc-data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "_README_c360", "Sequence": 0}, {"Query": "_cleanup_customer_journey_path", "Sequence": 1}, {"Query": "_list_campaign_types", "Sequence": 2}, {"Query": "customer_interactions", "Sequence": 3, "Parameters": [{"parameter": "customer", "type": "Vertex id", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"Query": "customer_journey", "Sequence": 4, "Parameters": [{"parameter": "customer", "type": "Vertex id", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameter": "campaign_type_set", "type": "set", "setElementType": "string", "value": ["Webinar", "Demo Signup / Trial"]}, {"parameter": "start_time", "type": "datetime", "value": "2018-06-01"}, {"parameter": "end_time", "type": "datetime", "value": "2018-10-01"}]}, {"Query": "customer_journey_path", "Sequence": 5, "Parameters": [{"parameter": "customer", "type": "Vertex id", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameter": "campaign_type_set", "type": "set", "setElementType": "string", "value": ["Webinar", "Demo Signup / Trial"]}, {"parameter": "start_time", "type": "datetime", "value": "2018-06-01"}, {"parameter": "end_time", "type": "datetime", "value": "2018-10-01"}]}, {"Query": "similar_contacts", "Sequence": 6, "Parameters": [{"parameter": "source_customer", "type": "Vertex id", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameter": "campaign_types", "type": "set", "setElementType": "string", "value": ["Webinar", "Demo Signup / Trial"]}, {"parameter": "top_k", "type": "INT", "value": 5}]}]}, {"Solution": "Cybersecurity Threat Detection - IT", "Enable": true, "Description": "Block cybersecurity threats by detecting interconnected events, devices and people", "Tag": "cyber-security", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Cyber-Security-Threat-Detectiontar.gz.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/cybersecurity_data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "alert_source_tracking", "Sequence": 0, "Parameters": [{"parameter": "alert_type", "type": "string", "value": "Data Corrupted"}, {"parameter": "num_days", "type": "INT", "value": "7"}, {"parameter": "top_ip", "type": "INT", "value": "3"}]}, {"Query": "firewall_bypass_detection", "Sequence": 1}, {"Query": "flooding_detection", "Sequence": 2, "Parameters": [{"parameter": "n_sigma", "type": "float", "value": "3"}]}, {"Query": "footprinting_detection", "Sequence": 3, "Parameters": [{"parameter": "n_sigma", "type": "float", "value": "3"}, {"parameter": "start_date", "type": "datetime", "value": "2019-05-01 11:10:00"}, {"parameter": "end_date", "type": "datetime", "value": "2019-05-01 11:15:00"}]}, {"Query": "suspicious_ip_detection", "Sequence": 4, "Parameters": [{"parameter": "input_ip", "type": "Vertex id", "value": "*************"}, {"parameter": "depth", "type": "INT", "value": "6"}, {"parameter": "diplay_paths", "type": "BOOL", "value": "true"}]}]}, {"Solution": "Data Lineage", "Enable": true, "Description": "Improve data governance with the ability to trace information back to its source", "Tag": "data-lineage", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/data-lineage.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/dataLineage_data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "all_updates", "Sequence": 0, "Parameters": [{"parameter": "input", "type": "Vertex id", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"Query": "contact_when", "Sequence": 1, "Parameters": [{"parameter": "contact", "type": "Vertex id", "value": "<PERSON><PERSON><PERSON>"}, {"parameter": "time", "type": "datetime", "value": "2019-06-05"}]}, {"Query": "cust_journey_subgraph", "Sequence": 2, "Parameters": [{"parameter": "customer", "type": "Vertex id", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameter": "opportunity", "type": "Vertex id", "value": "0063600000gEoe0AAC"}]}, {"Query": "customer_journey", "Sequence": 3, "Parameters": [{"parameter": "customer", "type": "Vertex id", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameter": "campaign_types", "type": "set", "setElementType": "string", "value": ["Webinar", "Demo Signup / Trial"]}, {"parameter": "start_time", "type": "datetime", "value": "2018-06-01"}, {"parameter": "end_time", "type": "datetime", "value": "2018-10-01"}]}, {"Query": "most_updated_contacts", "Sequence": 4}, {"Query": "updated_contacts", "Sequence": 5}]}, {"Solution": "Enterprise Knowledge Graph (Corporate)", "Enable": true, "Description": "Knowledge Graph example featuring corporates", "Tag": "knowledge-graph-corporate", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Enterprise-Corporate-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/enterprise-data-small.tar.gz", "GraphName": "EnterpriseGraph", "Queries": [{"Query": "company_holders", "Sequence": 0, "Parameters": [{"parameter": "company", "type": "Vertex id", "value": "Hospice Mocha Frame"}, {"parameter": "step", "type": "uint", "value": 5}]}, {"Query": "key_relationship", "Sequence": 1, "Parameters": [{"parameter": "company", "type": "Vertex id", "value": "Hospice Mocha Frame"}, {"parameter": "step", "type": "uint", "value": 5}]}, {"Query": "people_with_key_positions_in_at_least_two_companies", "Sequence": 2}]}, {"Solution": "Enterprise Knowledge Graph (Crunchbase)", "Enable": true, "Description": "Knowledge Graph example featuring Crunchbase data with startups, founders and companies", "Tag": "knowledge-graph-crunchbase", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Enterprise-Crunchbase-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/crunchbase-data-small.tar.gz", "GraphName": "CrunchBasePre_2013", "Queries": [{"Query": "top_startups_based_on_board", "Sequence": 0, "Parameters": [{"parameter": "k_orgs", "type": "INT", "value": 10}, {"parameter": "num_persons", "type": "INT", "value": 2}, {"parameter": "max_funding_round", "type": "string", "value": "b"}, {"parameter": "past_n_years", "type": "INT", "value": 15}]}, {"Query": "top_startups_based_on_leader", "Sequence": 1, "Parameters": [{"parameter": "max_funding_round", "type": "string", "value": "c"}, {"parameter": "return_size", "type": "INT", "value": 3}, {"parameter": "sector", "type": "string", "value": "software"}]}, {"Query": "investor_successful_exits", "Sequence": 2, "Parameters": [{"parameter": "investor_name", "type": "string", "value": "Accel Partners"}, {"parameter": "investor_type", "type": "string", "value": "financialORG"}, {"parameter": "years", "type": "int", "value": "3"}]}, {"Query": "key_role_discovery", "Sequence": 3, "Parameters": [{"parameter": "company_name", "type": "string", "value": "FriendFeed"}, {"parameter": "k", "type": "INT", "value": 5}]}]}, {"Solution": "Entity Resolution (MDM)", "Enable": true, "Description": "Identify, link and merge entities such as customers with analysis of attributes and relationships", "Tag": "entity-resolution-mdm", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/entity-resolution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/entity-resolution-data.tar.gz", "GraphName": "Entity_Resolution", "Queries": [{"Query": "account_matching", "Sequence": 0, "Parameters": [{"parameter": "input_acc", "type": "Vertex id", "value": 407}, {"parameter": "threshold", "type": "float", "value": 0.6}]}, {"Query": "get_account_interest", "Sequence": 1, "Parameters": [{"parameter": "input_acc", "type": "Vertex id", "value": 407}, {"parameter": "k", "type": "INT", "value": 5}]}, {"Query": "main_query", "Sequence": 2, "Parameters": [{"parameter": "threshold", "type": "float", "value": 0.6}]}, {"Query": "output_file", "Sequence": 3}]}, {"Solution": "Financial Services (Payments) - Fraud Detection", "Enable": true, "Description": "Detect and stop fraudulent payments in real-time", "Tag": "payment-fraud-detection", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/financial-services.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/financial-services_data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "common_customers", "Sequence": 0, "Parameters": [{"parameter": "Merchant1", "type": "Vertex id", "value": "merchant1"}, {"parameter": "Merchant2", "type": "Vertex id", "value": "merchant2"}]}, {"Query": "recipient_user_fraudulent_device", "Sequence": 1}, {"Query": "k_hop_flagged", "Sequence": 2, "Parameters": [{"parameter": "input", "type": "Vertex", "vertexType": "payment", "vertexId": "payment1"}, {"parameter": "k", "type": "INT", "value": 5}]}]}, {"Solution": "Fraud and Money Laundering Detection (Fin. Services)", "Enable": true, "Description": "Payments graph for detecting multiple types of fraud and money laundering patterns", "Tag": "anti-money-laundering", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Fraud-and-Money-Laundering-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/antifraud-cloud_data.tar.gz", "GraphName": "<PERSON><PERSON><PERSON><PERSON>", "Queries": [{"Query": "invited_user_behavior", "Sequence": 0, "Parameters": [{"parameter": "input_user", "type": "Vertex id", "value": "5354357"}]}, {"Query": "multi_transaction", "Sequence": 1, "Parameters": [{"parameter": "input_transaction", "type": "Vertex id", "value": "499"}]}, {"Query": "repeated_user", "Sequence": 2, "Parameters": [{"parameter": "recipient", "type": "Vertex id", "value": "1223"}]}, {"Query": "same_receiver_sender", "Sequence": 3, "Parameters": [{"parameter": "input_transaction", "type": "Vertex id", "value": "999"}]}, {"Query": "transferred_amount", "Sequence": 4, "Parameters": [{"parameter": "sender", "type": "Vertex id", "value": "499"}, {"parameter": "num_hops", "type": "INT", "value": "4"}, {"parameter": "start_date", "type": "dateTime", "value": "2000/12/31"}, {"parameter": "end_date", "type": "dateTime", "value": "2020/12/31"}]}, {"Query": "circle_detection", "Sequence": 5, "Parameters": [{"parameter": "source_id", "type": "Vertex id", "value": "499"}]}, {"Query": "fraud_connectivity", "Sequence": 6, "Parameters": [{"parameter": "input_user", "type": "Vertex id", "value": "499"}, {"parameter": "trust_score", "type": "FLOAT", "value": "0.1"}]}]}, {"Solution": "Graph Analytics - Centrality Algorithms", "Enable": true, "Description": "Discover ways, including PageRank, to measure the most centrally located entities", "Tag": "centrality", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Graph-Centrality-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/centrality_data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "_README_algorithms", "Sequence": 0}, {"Query": "_calculate_route_length", "Sequence": 1, "Parameters": [{"parameter": "e_type", "type": "string", "value": "flight_to"}, {"parameter": "overwrite", "type": "BOOL", "value": "true"}]}, {"Query": "_search_for_vertex", "Sequence": 2, "Parameters": [{"parameter": "v_type", "type": "string", "value": "Airport"}, {"parameter": "attr_name", "type": "string", "value": "name"}, {"parameter": "substring", "type": "string", "value": "Shanghai"}]}, {"Query": "tg_betweenness_cent", "Sequence": 3, "Parameters": [{"parameter": "v_type", "type": "set", "setElementType": "string", "value": ["Airport"]}, {"parameter": "e_type", "type": "set", "setElementType": "string", "value": ["flight_to"]}, {"parameter": "rev_e_type", "type": "set", "setElementType": "string", "value": ["reverse_flight_to"]}, {"parameter": "max_hops", "type": "INT", "value": 1}, {"parameter": "top_k", "type": "INT", "value": 10}, {"parameter": "print_accum", "type": "BOOL", "value": "true"}, {"parameter": "display_edges", "type": "BOOL", "value": "false"}]}, {"Query": "tg_closeness_cent", "Sequence": 4, "Parameters": [{"parameter": "v_type", "type": "set", "setElementType": "string", "value": ["Airport"]}, {"parameter": "e_type", "type": "set", "setElementType": "string", "value": ["flight_to"]}, {"parameter": "rev_e_type", "type": "set", "setElementType": "string", "value": ["reverse_flight_to"]}, {"parameter": "max_hops", "type": "INT", "value": 1}, {"parameter": "top_k", "type": "INT", "value": 10}, {"parameter": "wf", "type": "BOOL", "value": "true"}, {"parameter": "print_accum", "type": "BOOL", "value": "true"}, {"parameter": "display_edges", "type": "BOOL", "value": "false"}]}, {"Query": "tg_scc", "Sequence": 5, "Parameters": [{"parameter": "v_type_set", "type": "set", "setElementType": "string", "value": ["Airport"]}, {"parameter": "e_type_set", "type": "set", "setElementType": "string", "value": ["flight_to"]}, {"parameter": "reverse_e_type_set", "type": "set", "setElementType": "string", "value": ["reverse_flight_to"]}, {"parameter": "top_k_dist", "type": "INT", "value": 1}, {"parameter": "print_limit", "type": "INT", "value": 10}, {"parameter": "maximum_iteration", "type": "INT", "value": 500}, {"parameter": "iter_wcc", "type": "INT", "value": 5}, {"parameter": "print_results", "type": "BOOL", "value": "true"}]}, {"Query": "tg_scc_mod", "Sequence": 6, "Parameters": [{"parameter": "v_type_set", "type": "set", "setElementType": "string", "value": ["Airport"]}, {"parameter": "e_type_set", "type": "set", "setElementType": "string", "value": ["flight_to"]}, {"parameter": "reverse_e_type_set", "type": "set", "setElementType": "string", "value": ["reverse_flight_to"]}, {"parameter": "top_k_dist", "type": "INT", "value": 1}, {"parameter": "print_limit", "type": "INT", "value": 10}, {"parameter": "maximum_iteration", "type": "INT", "value": 500}, {"parameter": "iter_wcc", "type": "INT", "value": 5}, {"parameter": "print_results", "type": "BOOL", "value": "true"}]}, {"Query": "page_rank", "Sequence": 7, "Parameters": [{"parameter": "max_change", "type": "FLOAT", "value": 0.001}, {"parameter": "max_iter", "type": "INT", "value": 20}, {"parameter": "damping", "type": "FLOAT", "value": 0.85}, {"parameter": "display", "type": "BOOL", "value": "false"}, {"parameter": "output_limit", "type": "INT", "value": 100}]}, {"Query": "page_rank_by_country", "Sequence": 8, "Parameters": [{"parameter": "max_change", "type": "FLOAT", "value": 0.001}, {"parameter": "max_iter", "type": "INT", "value": 20}, {"parameter": "damping", "type": "FLOAT", "value": 0.85}, {"parameter": "country", "type": "string", "value": "United States"}, {"parameter": "display", "type": "BOOL", "value": "false"}, {"parameter": "output_limit", "type": "INT", "value": 100}]}, {"Query": "page_rank_pers", "Sequence": 9, "Parameters": [{"parameter": "source", "type": "set", "setElementType": "Vertex", "value": [{"vertexType": "Airport", "vertexId": "SLM-1238"}]}, {"parameter": "max_change", "type": "FLOAT", "value": 0.1}, {"parameter": "max_iter", "type": "INT", "value": 10}, {"parameter": "damping", "type": "FLOAT", "value": 0.5}, {"parameter": "output_limit", "type": "INT", "value": 10}]}, {"Query": "tg_shortest_ss_no_wt", "Sequence": 10, "Parameters": [{"parameter": "source", "type": "Vertex", "vertexType": "Airport", "vertexId": "SLM-1238"}, {"parameter": "v_type", "type": "set", "setElementType": "string", "value": ["Airport"]}, {"parameter": "e_type", "type": "set", "setElementType": "string", "value": ["flight_to"]}, {"parameter": "output_limit", "type": "INT", "value": "10"}, {"parameter": "print_accum", "type": "BOOL", "value": "true"}, {"parameter": "display_edges", "type": "BOOL", "value": "false"}]}, {"Query": "tg_shortest_ss_pos_wt_tb", "Sequence": 11, "Parameters": [{"parameter": "source", "type": "Vertex", "vertexType": "Airport", "vertexId": "SLM-1238"}, {"parameter": "v_type_set", "type": "set", "setElementType": "string", "value": ["Airport"]}, {"parameter": "e_type_set", "type": "set", "setElementType": "string", "value": ["flight_to"]}, {"parameter": "weight_attribute", "type": "string", "value": "miles"}, {"parameter": "weight_type", "type": "string", "value": "INT"}, {"parameter": "epsilon", "type": "float", "value": "0.001"}, {"parameter": "print_results", "type": "BOOL", "value": "true"}, {"parameter": "print_limit", "type": "int", "value": "10"}, {"parameter": "display_edges", "type": "BOOL", "value": "false"}, {"parameter": "write_size", "type": "uint", "value": "10000"}]}, {"Query": "tg_shortest_ss_mod", "Sequence": 12, "Parameters": [{"parameter": "source", "type": "Vertex", "vertexType": "Airport", "vertexId": "SLM-1238"}, {"parameter": "v_type", "type": "set", "setElementType": "string", "value": ["Airport"]}, {"parameter": "e_type", "type": "set", "setElementType": "string", "value": ["flight_to"]}, {"parameter": "output_limit", "type": "INT", "value": "10"}, {"parameter": "print_accum", "type": "BOOL", "value": "true"}, {"parameter": "display_edges", "type": "BOOL", "value": "false"}]}, {"Query": "tg_shortest_ss_pos_wt_tb_mod", "Sequence": 13, "Parameters": [{"parameter": "source", "type": "Vertex", "vertexType": "Airport", "vertexId": "SLM-1238"}, {"parameter": "v_type_set", "type": "set", "setElementType": "string", "value": ["Airport"]}, {"parameter": "e_type_set", "type": "set", "setElementType": "string", "value": ["flight_to"]}, {"parameter": "weight_attribute", "type": "string", "value": "miles"}, {"parameter": "weight_type", "type": "string", "value": "INT"}, {"parameter": "epsilon", "type": "float", "value": "0.001"}, {"parameter": "print_results", "type": "BOOL", "value": "true"}, {"parameter": "print_limit", "type": "int", "value": "10"}, {"parameter": "display_edges", "type": "BOOL", "value": "false"}, {"parameter": "write_size", "type": "uint", "value": "10000"}]}]}, {"Solution": "Graph Analytics - Community Detection Algorithms", "Enable": true, "Description": "Find the natural groupings of connected individuals in a network", "Tag": "community-detection", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Community-Detection-Solutions.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/community_detection_data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "insert_all_referrals", "Sequence": 0}, {"Query": "A_README", "Sequence": 1}, {"Query": "print_community", "Sequence": 2, "Parameters": [{"parameter": "input_prescriber", "type": "Vertex id", "value": "pre38"}]}, {"Query": "algo_louvain", "Sequence": 3, "Parameters": [{"parameter": "iter1", "type": "INT", "value": 10}, {"parameter": "iter2", "type": "INT", "value": 10}, {"parameter": "iter3", "type": "INT", "value": 10}, {"parameter": "split", "type": "INT", "value": 10}, {"parameter": "output_level", "type": "INT", "value": 0}, {"parameter": "sort_by_pre_ID", "type": "BOOL", "value": "true"}, {"parameter": "sort_by_comm_ID", "type": "BOOL", "value": "true"}]}, {"Query": "algo_louvain_enhanced", "Sequence": 4, "Parameters": [{"parameter": "vertex_type", "type": "string", "value": "Prescriber"}, {"parameter": "edge_type", "type": "string", "value": "referral"}, {"parameter": "rev_edge_type", "type": "string", "value": "reverse_referral"}, {"parameter": "iter1", "type": "INT", "value": 10}, {"parameter": "iter2", "type": "INT", "value": 10}, {"parameter": "iter3", "type": "INT", "value": 10}, {"parameter": "split", "type": "INT", "value": 10}, {"parameter": "output_level", "type": "INT", "value": 0}, {"parameter": "sort_by_pre_ID", "type": "BOOL", "value": "true"}, {"parameter": "sort_by_comm_ID", "type": "BOOL", "value": "true"}]}, {"Query": "algo_page_rank", "Sequence": 5, "Parameters": [{"parameter": "max_change", "type": "FLOAT", "value": 0.001}, {"parameter": "max_iter", "type": "INT", "value": 25}, {"parameter": "damping", "type": "FLOAT", "value": 0.85}, {"parameter": "output_limit", "type": "INT", "value": 10}]}, {"Query": "conn_comp", "Sequence": 6, "Parameters": [{"parameter": "vertex_type", "type": "string", "value": "Prescriber"}, {"parameter": "edge_type", "type": "string", "value": "referral"}, {"parameter": "rev_edge_type", "type": "string", "value": "reverse_referral"}]}, {"Query": "conn_comp_enhanced", "Sequence": 7, "Parameters": [{"parameter": "vertex_types", "type": "set", "setElementType": "Vertex", "value": [{"vertexType": "Prescriber", "vertexId": "pre38"}]}, {"parameter": "vt2", "type": "string", "value": "pre30"}, {"parameter": "edge_type", "type": "string", "value": "referral"}, {"parameter": "rev_edge_type", "type": "string", "value": "reverse_referral"}, {"parameter": "output_level", "type": "INT", "value": 10}]}, {"Query": "get_community", "Sequence": 8, "Parameters": [{"parameter": "prescriber_Id", "type": "string", "value": ""}, {"parameter": "community_Id", "type": "string", "value": "73400323"}]}, {"Query": "insert_referrals", "Sequence": 9, "Parameters": [{"parameter": "input_prescriber", "type": "Vertex id", "value": "pre38"}]}, {"Query": "kcore_decomp", "Sequence": 10, "Parameters": [{"parameter": "vertex_type", "type": "string", "value": "Prescriber"}, {"parameter": "edge_type", "type": "string", "value": "referral"}, {"parameter": "k_min", "type": "INT", "value": 1}, {"parameter": "k_max", "type": "INT", "value": -1}, {"parameter": "show_membership", "type": "BOOL", "value": "false"}, {"parameter": "show_shells", "type": "BOOL", "value": "true"}]}, {"Query": "kcore_max", "Sequence": 11, "Parameters": [{"parameter": "vertex_type", "type": "string", "value": "Prescriber"}, {"parameter": "edge_type", "type": "string", "value": "referral"}, {"parameter": "induced_edges", "type": "BOOL", "value": "true"}, {"parameter": "verbosity", "type": "INT", "value": 3}]}, {"Query": "kcore_sub", "Sequence": 12, "Parameters": [{"parameter": "vertexType", "type": "string", "value": "Prescriber"}, {"parameter": "edgeType", "type": "string", "value": "referral"}, {"parameter": "verbosity", "type": "INT", "value": 3}]}, {"Query": "scc", "Sequence": 13, "Parameters": [{"parameter": "iter", "type": "INT", "value": 500}, {"parameter": "iter_wcc", "type": "INT", "value": 5}, {"parameter": "top_k_dist", "type": "INT", "value": 10}]}, {"Query": "scc_enhanced", "Sequence": 14, "Parameters": [{"parameter": "iter", "type": "INT", "value": 500}, {"parameter": "iter_wcc", "type": "INT", "value": 5}, {"parameter": "top_k_dist", "type": "INT", "value": 10}]}, {"Query": "select_subgraph", "Sequence": 15, "Parameters": [{"parameter": "vertex_type", "type": "string", "value": "Prescriber"}, {"parameter": "edge_type", "type": "string", "value": "referral"}]}]}, {"Solution": "Graph Analytics - Shortest Path Algorithms", "Enable": true, "Description": "Use shortest path algorithms to find the shortest and cheapest routes", "Tag": "shortest-path", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Shortest-Path-Solutions.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/shortestpath_data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "add_weights", "Sequence": 0, "Parameters": [{"parameter": "overwrite", "type": "BOOL", "value": "true"}]}, {"Query": "A_README", "Sequence": 1}, {"Query": "shortest_ss_no_wt", "Sequence": 2, "Parameters": [{"parameter": "source", "type": "Vertex", "vertexType": "Airport", "vertexId": "JFR-5440"}, {"parameter": "display", "type": "BOOL", "value": "false"}]}, {"Query": "shortest_ss_pos_wt", "Sequence": 3, "Parameters": [{"parameter": "source", "type": "Vertex", "vertexType": "Airport", "vertexId": "JFR-5440"}, {"parameter": "display", "type": "BOOL", "value": "false"}]}, {"Query": "shortest_ss_pos_wt_limits", "Sequence": 3, "Parameters": [{"parameter": "source", "type": "Vertex", "vertexType": "Airport", "vertexId": "JFR-5440"}, {"parameter": "display", "type": "BOOL", "value": "false"}, {"parameter": "maxHops", "type": "INT", "value": 5}, {"parameter": "maxDest", "type": "INT", "value": 5}]}]}, {"Solution": "Graph Convolutional Networks", "Enable": true, "Tag": "ml", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Convolutional-Networks-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/GCNonCitationGraph_data.tar.gz", "GraphName": "CitationGraph", "Queries": [{"Query": "initialization", "Sequence": 0}, {"Query": "weight_initialization", "Sequence": 1}, {"Query": "training", "Sequence": 2, "Parameters": [{"parameter": "alpha0", "type": "DOUBLE", "value": 0.4}, {"parameter": "<PERSON>", "type": "BOOL", "value": "true"}, {"parameter": "beta1", "type": "DOUBLE", "value": 0.9}, {"parameter": "beta2", "type": "DOUBLE", "value": 0.999}, {"parameter": "keepProb", "type": "DOUBLE", "value": 1}, {"parameter": "lambda", "type": "DOUBLE", "value": 5e-05}, {"parameter": "MaxIter", "type": "INT", "value": 10}]}, {"Query": "predicting", "Sequence": 3}, {"Query": "README", "Sequence": 4}]}, {"Solution": "Healthcare - Referral networks, Hub(PageRank) &amp; Community Detection", "Enable": true, "Description": "Analyze member(patient) claims to establish referral networks, identify most influential prescribers(doctors) and discover the connected prescriber communities", "Tag": "healthcare-analytics", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/healthcare-analytics.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/healthcare-analytics-data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "_README_Referral", "Sequence": 0}, {"Query": "_delete_referral_edges", "Sequence": 1, "Parameters": [{"parameter": "DELETE_ALL_REFERRAL_EDGES", "type": "string", "value": "no"}]}, {"Query": "get_referral_community", "Sequence": 2, "Parameters": [{"parameter": "input_prescriber", "type": "Vertex id", "value": "pre38"}]}, {"Query": "get_joint_prescribers", "Sequence": 3, "Parameters": [{"parameter": "input_prescriber", "type": "Vertex id", "value": "pre38"}]}, {"Query": "tg_pagerank", "Sequence": 4, "Parameters": [{"parameter": "v_type", "type": "string", "value": "Prescriber"}, {"parameter": "e_type", "type": "string", "value": "referral"}, {"parameter": "max_change", "type": "FLOAT", "value": 10}, {"parameter": "max_iter", "type": "INT", "value": 10}, {"parameter": "damping", "type": "int", "value": 0.5}, {"parameter": "print_accum", "type": "BOOL", "value": "true"}, {"parameter": "display_edges", "type": "BOOL", "value": "false"}]}, {"Query": "get_common_patients", "Sequence": 5, "Parameters": [{"parameter": "prescriber1", "type": "Vertex id", "value": "pre38"}, {"parameter": "prescriber2", "type": "Vertex id", "value": "pre6"}]}, {"Query": "infer_referrals", "Sequence": 6, "Parameters": [{"parameter": "input_prescriber", "type": "Vertex id", "value": "pre38"}, {"parameter": "max_days", "type": "int", "value": 30}]}, {"Query": "infer_all_referrals", "Sequence": 7, "Parameters": [{"parameter": "max_days", "type": "int", "value": 30}]}, {"Query": "get_claims_of_prescriber", "Sequence": 8, "Parameters": [{"parameter": "input_prescriber", "type": "Vertex id", "value": "pre38"}]}, {"Query": "get_k_hop_neighbors", "Sequence": 9, "Parameters": [{"parameter": "k", "type": "int", "value": "1"}, {"parameter": "input", "type": "Vertex", "vertexType": "Prescriber", "vertexId": "pre38"}]}, {"Query": "get_patients_of_prescriber", "Sequence": 10, "Parameters": [{"parameter": "input_prescriber", "type": "Vertex id", "value": "pre38"}]}, {"Query": "algo_louvain", "Sequence": 11, "Parameters": [{"parameter": "iter1", "type": "int", "value": "10"}, {"parameter": "iter2", "type": "int", "value": "10"}, {"parameter": "iter3", "type": "int", "value": "10"}, {"parameter": "split", "type": "int", "value": "10"}, {"parameter": "output_level", "type": "int", "value": "1"}, {"parameter": "print_accum", "type": "BOOL", "value": "false"}]}]}, {"Solution": "Healthcare Graph (Drug Interaction/FAERS)", "Enable": true, "Tag": "healthcare", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Healthcare-Graph-Drug-Interaction-FAERS-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/healthcare-data-small.tar.gz", "GraphName": "faers", "Queries": [{"Query": "_README_drug_interaction", "Sequence": 0}, {"Query": "jac<PERSON>_nbor_reaction", "Sequence": 1, "Parameters": [{"parameter": "source", "type": "Vertex", "vertexType": "ReportedCase", "vertexId": "*********"}, {"parameter": "etype", "type": "string", "value": "hasReactions"}, {"parameter": "top_k", "type": "INT", "value": 100}, {"parameter": "sampSize", "type": "INT", "value": 100}]}, {"Query": "most_reported_drugs_for_company", "Sequence": 2, "Parameters": [{"parameter": "company_name", "type": "string", "value": "PFIZER"}, {"parameter": "k", "type": "INT", "value": 5}, {"parameter": "role", "type": "string", "value": "PS"}]}, {"Query": "top_side_effects_for_top_drugs", "Sequence": 3, "Parameters": [{"parameter": "company_name", "type": "string", "value": "PFIZER"}, {"parameter": "k", "type": "INT", "value": 5}, {"parameter": "role", "type": "string", "value": "PS"}]}]}, {"Solution": "In-Database Machine Learning Recommendation", "Enable": true, "Description": "Provide content and products suggestions using an in-database machine learning recommendation system", "Tag": "ml-recommend", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Machine-Learning-Recommendation-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/recommendation-latent-factor-model_data.tar.gz", "GraphName": "Recommender", "Queries": [{"Query": "split_data", "Sequence": 0}, {"Query": "normalization", "Sequence": 1}, {"Query": "initialization", "Sequence": 2, "Parameters": [{"parameter": "num_latent_factors", "type": "INT", "value": 19}]}, {"Query": "training", "Sequence": 3, "Parameters": [{"parameter": "learning_rate", "type": "DOUBLE", "value": 0.001}, {"parameter": "regularization_factor", "type": "DOUBLE", "value": 5e-05}, {"parameter": "<PERSON><PERSON>", "type": "INT", "value": 100}]}, {"Query": "test", "Sequence": 4, "Parameters": [{"parameter": "user", "type": "Vertex id", "value": 105}]}, {"Query": "recommend", "Sequence": 5, "Parameters": [{"parameter": "user", "type": "Vertex id", "value": 105}]}, {"Query": "cal_avg_rating", "Sequence": 6}, {"Query": "README", "Sequence": 7}]}, {"Solution": "In-Database Machine Learning for Big Data Entity Resolution", "Enable": true, "Description": "Match, link and group entities for creating a single identity across large datasets with in-database machine learning", "Tag": "er-ml", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Machine-Learning-Entity-Resolution-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/er-ml-data.tar.gz", "GraphName": "Entity_Resolution", "Queries": [{"Query": "README", "Sequence": 0}, {"Query": "initialize_users", "Sequence": 1}, {"Query": "connect_jaccard_sim", "Sequence": 2, "Parameters": [{"parameter": "threshold", "type": "FLOAT", "value": 0.5}, {"parameter": "topK", "type": "INT", "value": 100}, {"parameter": "verbose", "type": "BOOL", "value": "false"}]}, {"Query": "merge_connected_users", "Sequence": 3, "Parameters": [{"parameter": "verbose", "type": "BOOL", "value": "false"}]}, {"Query": "connect_weighted_match", "Sequence": 4, "Parameters": [{"parameter": "threshold", "type": "FLOAT", "value": 0.2}, {"parameter": "verbose", "type": "BOOL", "value": "false"}]}, {"Query": "get_account_subgraph", "Sequence": 5, "Parameters": [{"parameter": "account_ids", "type": "set", "setElementType": "string", "value": ["308"]}, {"parameter": "include_attributes", "type": "BOOL", "value": "false"}]}, {"Query": "recommend_videos", "Sequence": 6, "Parameters": [{"parameter": "inputAcc", "type": "Vertex id", "vertexType": "Account", "value": 407}, {"parameter": "k", "type": "int", "value": 5}]}, {"Query": "score_similar_attributes", "Sequence": 7, "Parameters": [{"parameter": "do_last_name", "type": "BOOL", "value": "true"}, {"parameter": "do_address", "type": "BOOL", "value": "true"}, {"parameter": "print_only", "type": "BOOL", "value": "false"}]}, {"Query": "util_count_vertices", "Sequence": 8, "Parameters": [{"parameter": "v_type", "type": "string", "value": "User"}]}, {"Query": "util_delete_users", "Sequence": 9, "Parameters": [{"parameter": "are_you_sure", "type": "BOOL", "value": "false"}]}, {"Query": "util_print_vertices", "Sequence": 10, "Parameters": [{"parameter": "v_type", "type": "string", "value": "Weights"}]}, {"Query": "util_set_weights", "Sequence": 11, "Parameters": [{"parameter": "ip_wt", "type": "DOUBLE", "value": 0.5}, {"parameter": "email_wt", "type": "DOUBLE", "value": 0.5}, {"parameter": "phone_wt", "type": "DOUBLE", "value": 0.5}, {"parameter": "last_name_wt", "type": "DOUBLE", "value": 0.75}, {"parameter": "address_wt", "type": "DOUBLE", "value": 0.5}, {"parameter": "device_wt", "type": "DOUBLE", "value": 0.5}]}]}, {"Solution": "Low-Rank Approximation Machine Learning", "Enable": true, "Description": "Implement the low-rank approximation algorithm natively in-database to deliver personalized recommendations", "Tag": "low-rank-approx", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Low-Rank-Approximation-Machine-Learning-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/LowRankApproximation_data.tar.gz", "GraphName": "LowRankApproximation", "Queries": [{"Query": "initialization", "Sequence": 0, "Parameters": [{"parameter": "sdv", "type": "float", "value": 0.1}, {"parameter": "mean", "type": "float", "value": 0.1}]}, {"Query": "factorization", "Sequence": 1, "Parameters": [{"parameter": "learning_rate", "type": "DOUBLE", "value": 0.001}, {"parameter": "regularization_factor", "type": "DOUBLE", "value": 5e-05}, {"parameter": "<PERSON><PERSON>", "type": "INT", "value": 30}]}, {"Query": "compare_approximation", "Sequence": 2, "Parameters": [{"parameter": "row_index", "type": "Vertex id", "value": 251}]}, {"Query": "print_result", "Sequence": 3}, {"Query": "README", "Sequence": 4}]}, {"Solution": "Machine Learning and Real-time Fraud Detection", "Enable": true, "Description": "Mobile industry example for detecting fraud in real-time and generating graph-based features for training the machine learning solution ", "Tag": "ml-antifraud", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Machine-Learning-and-Real-time-Fraud-Detection-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/china-mobile_data.tar.gz", "GraphName": "sdmGraph", "Queries": [{"Query": "feature_collection", "Sequence": 0, "Parameters": [{"parameter": "phoneId", "type": "Vertex id", "value": "10"}, {"parameter": "durationLimit", "type": "INT", "value": 600}, {"parameter": "numOfCallLimit", "type": "INT", "value": 10}]}]}, {"Solution": "Network and IT Resource Optimization", "Enable": true, "Description": "Network and IT resource graph for modeling and analyzing the impact of the hardware outage on workloads", "Tag": "hardware-impact-analysis", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Network-IT-Resource-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/visa-network-impact-cloud_data.tar.gz", "GraphName": "Storage", "Queries": [{"Query": "app_impact", "Sequence": 0, "Parameters": [{"parameter": "a", "type": "Vertex id", "value": "862"}, {"parameter": "decay", "type": "float", "value": 10}, {"parameter": "k", "type": "INT", "value": 10}]}, {"Query": "storage_impact", "Sequence": 1, "Parameters": [{"parameter": "vertexType", "type": "string", "value": "Application"}, {"parameter": "input", "type": "Vertex", "vertexType": "Service", "vertexId": "752"}]}, {"Query": "warning_impact", "Sequence": 2, "Parameters": [{"parameter": "inputWarn", "type": "Vertex id", "value": "2000821"}]}]}, {"Solution": "Recommendation Engine (Movie Recommendation)", "Enable": true, "Description": "Graph-based movie recommendation engine built with public data", "Tag": "movie-recommend", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Movie-Recommendation-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/movie-rec_data.tar.gz", "GraphName": "MyGraph", "Queries": [{"Query": "recommend_movies", "Sequence": 1, "Parameters": [{"parameter": "p", "type": "Vertex id", "value": 28}, {"parameter": "k1", "type": "int", "value": 5}, {"parameter": "k2", "type": "int", "value": 5}]}]}, {"Solution": "Recommendation Engine 2.0 (Hyper-Personalized Marketing)", "Enable": true, "Description": "Hyper-personalized recommendation engine to create dynamic offers in real-time for higher click-through and revenue", "Tag": "recommend-engine", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Recommendation-Engine-2-Personallized-Marketing-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/recommendation2.0_data.tar.gz", "GraphName": "recommendation", "Queries": [{"Query": "_README_recommend2", "Sequence": 0}, {"Query": "_nested_expr_demo2", "Sequence": 1}, {"Query": "_nested_expression", "Sequence": 2, "Parameters": [{"parameter": "p", "type": "set", "setElementType": "Vertex id", "value": ["C001"]}, {"parameter": "c", "type": "set", "setElementType": "Vertex id", "value": ["GW"]}]}, {"Query": "display_full_graph", "Sequence": 3}, {"Query": "display_top_demographic", "Sequence": 4}, {"Query": "recomm_by_customer_and_context", "Sequence": 5, "Parameters": [{"parameter": "input_customer_set", "type": "set", "setElementType": "Vertex id", "value": ["C001"]}, {"parameter": "input_context_set", "type": "set", "setElementType": "Vertex id", "value": ["GW"]}]}, {"Query": "recomm_by_features_and_context", "Sequence": 6, "Parameters": [{"parameter": "source_customer", "type": "Vertex id", "value": "C001"}, {"parameter": "weather", "type": "string", "value": "GW"}, {"parameter": "time_of_day", "type": "string", "value": "T1"}, {"parameter": "top_k", "type": "INT", "value": 5}]}]}, {"Solution": "Social Network Analysis", "Enable": true, "Description": "Social network example for understanding and analyzing relationships", "Tag": "socialgraph", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Social-Network-Analysis-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/socialgraph-data-small.tar.gz", "GraphName": "connectivity", "Queries": [{"Query": "addresses_with_most_case_reports", "Sequence": 0}, {"Query": "discover_social_connections", "Sequence": 1, "Parameters": [{"parameter": "A", "type": "Vertex id", "value": "115062"}, {"parameter": "B", "type": "Vertex id", "value": "198915"}, {"parameter": "k", "type": "INT", "value": 6}]}, {"Query": "person_knows_who", "Sequence": 2, "Parameters": [{"parameter": "source", "type": "Vertex id", "value": "274"}]}, {"Query": "person_same_as_who", "Sequence": 3, "Parameters": [{"parameter": "source", "type": "Vertex id", "value": "55521"}, {"parameter": "topK", "type": "INT", "value": 5}]}]}, {"Solution": "Supply Chain Analysis", "Enable": true, "Description": "Example covering inventory planning and impact analysis", "Tag": "supplychain", "SolutionURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/3.9/Supply-Chain-Analysis-Solution.tar.gz", "DataURL": "https://tigergraph-testdrive-testdata.s3.amazonaws.com/supplychain-data.tgz", "GraphName": "demo_graph", "Queries": [{"Query": "check_stocking", "Sequence": 0, "Parameters": [{"parameter": "input", "type": "Vertex id", "value": "CAR WHEEL SET"}, {"parameter": "amount", "type": "uint", "value": 5}]}, {"Query": "impact_analysis", "Sequence": 1, "Parameters": [{"parameter": "affectedSites", "type": "set", "setElementType": "Vertex id", "value": ["site5"]}, {"parameter": "max<PERSON><PERSON><PERSON>", "type": "uint", "value": 10}]}, {"Query": "price_prediction", "Sequence": 2, "Parameters": [{"parameter": "input", "type": "set", "setElementType": "string", "value": ["STEEL RODS,0.2"]}, {"parameter": "maxIteration", "type": "INT", "value": 10}, {"parameter": "doUpdate", "type": "BOOL", "value": "false"}]}, {"Query": "show_whole_graph", "Sequence": 3}]}]}