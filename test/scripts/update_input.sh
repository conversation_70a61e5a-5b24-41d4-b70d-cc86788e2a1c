#!/bin/bash
set -x

# Check and update the inputs
project_path=$(cd $(dirname $0)/../ && pwd)
source "${project_path}/environment.properties"

# Trim and format inputs
TEST_ENV=$(echo ${TEST_ENV,,} | sed 's/ *$//g')
HEADER=$(echo ${HEADER,,} | sed 's/ *$//g')

# Replace the TEST_ENV
if [  "$TEST_ENV" == "ci" ]; then
    TEST_ENV="https://test.tgcloud-dev.com"
    sed -i 's/TEST_ENV.*/TEST_ENV=https:\/\/test.tgcloud-dev.com/g' "${project_path}/environment.properties"
fi

# Check TEST_ENV=test.tgcloud-dev.com
if [  "$TEST_ENV" == "https://test.tgcloud-dev.com" ];then
    # Check if HEADER is null
    if [  -z "$HEADER" ]; then
        echo "Warning: Accessing the test.tgcloud-dev environment without providing HEADER."
    fi
    # autoset the MARKER to smoke
    MARKER=smoke
    sed -i 's/RERUNS.*/RERUNS=1/g' "${project_path}/environment.properties"
    sed -i 's/MARKER.*/MARKER="smoke and cloud"/g' "${project_path}/environment.properties"
fi

if [  "$TEST_ENV" == "https://tgcloud.io" ] || [  "$TEST_ENV" == "prod" ] ;then
    # autoset the MARKER to prod
    MARKER=prod
    TEST_ENV="https://tgcloud.io"
fi

# Update the test_data.json based on FoT_PARAM
python3 "${project_path}/api/utils/data_util/update_test_data.py" --test_env "$TEST_ENV" --fot_param "$FoT_PARAM"
# Update the test_data.json based on CLOUD_PARAM
python3 "${project_path}/api/utils/data_util/update_test_data.py" --test_env "$TEST_ENV" --cloud_param "$CLOUD_PARAM"