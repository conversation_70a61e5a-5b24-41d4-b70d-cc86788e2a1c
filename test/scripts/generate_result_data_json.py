#coding:utf-8
import sys
import os
import glob
import json
import datetime
import configparser
from pathlib import Path
import xml.etree.ElementTree as ET

current_path = os.path.dirname(os.path.abspath(__file__))
work_path = Path(__file__).resolve().parents[1]
junit_path = Path.joinpath(work_path, "junit")

k_environment_properties = "environment.properties"

def get_environment_properties():
    """get the env_props from the environment.properties file"""
    config = configparser.ConfigParser()
    with open(Path.joinpath(work_path, k_environment_properties)) as stream:
        config.read_string("[e2e]\n" + stream.read())
    global env_props
    env_props = {s: dict(config.items(s)) for s in config.sections()}["e2e"]

def generate_testcase_edge_json(file_path):
    file_pattern = "test-results-*.xml"
    testcase_dict = {}
    edge_dict = {}
    # Find the XML file paths
    xml_files = glob.glob(os.path.join(file_path, file_pattern))
    for xmlfile in xml_files:
        # Parse the XML file
        tree = ET.parse(xmlfile)
        # Get the root element
        root = tree.getroot()
        testsuite = root.find('testsuite')
        # Iterate over the testcase elements
        for testcase in testsuite.findall('testcase'):
            name = testcase.get('name')
            time = testcase.get('time')
            #check the test case is success or not in the xml
            case_res = "success"
            if testcase.find('failure') is not None:
                case_res = "failed" 
 
            id = "%s_%s_%s"%(env_props["job_name"].replace('"', ''), env_props["job_id"].replace('"', ''), name)
            testcase_dict[id] = {
                "case_name":{"value": name},
                "case_type":{"value": "e2e"},
                "execute_date":{"value": env_props["start_time"].replace('"', '')},
                "time_cost":{"value": float(time)},
                "result":{"value": case_res}
            }
            edge_dict[id] = {}
    return testcase_dict, edge_dict

def generate_job_json():
    job_dict = {}
    end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    id = "%s_%s"%(env_props["job_name"].replace('"', ''), env_props["job_id"].replace('"', ''))
    job_dict[id] = {}
    job_dict[id]["job_name"] = {"value":env_props["job_name"].replace('"', '')}
    job_dict[id]["test_env"] = {"value":env_props["test_env"].replace('"', '')}
    job_dict[id]["start_time"] = {"value":env_props["start_time"].replace('"', '')}
    job_dict[id]["end_time"] = {"value":end_time}
    job_dict[id]["status"] = {"value":env_props["status"].replace('"', '')}
    job_dict[id]["version"] = {"value":env_props["version"].replace('"', '')}
    job_dict[id]["marker"] = {"value":env_props["marker"].replace('"', '')}
    return job_dict


if __name__ == "__main__":
    get_environment_properties()
    job_dict = generate_job_json()
    testcase_dict, edge_dict = generate_testcase_edge_json(junit_path)
    primary_id = list(job_dict.keys())[0]
    data_dict={"vertices":{"test_case":testcase_dict, "test_job": job_dict},"edges":{"test_job":{primary_id:{"contains":{"test_case":edge_dict}}}}}
    with open(os.path.join(current_path, "data.json"), "w") as f:
        f.write(json.dumps(data_dict, ensure_ascii=False, indent=2))