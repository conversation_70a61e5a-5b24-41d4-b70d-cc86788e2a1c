import os
import glob
from pathlib import Path
import xml.etree.ElementTree as ET

current_path = os.path.dirname(os.path.abspath(__file__))
work_path = Path(__file__).resolve().parents[1]
junit_path = Path.joinpath(work_path, "junit")

failed_testcase_file_name = "tool_e2e_failure_casename.txt"

def generate_failed_testcase_with_newfile(file_path):

    file_pattern = "test-results-*.xml"
    # Find the XML file paths
    xml_files = glob.glob(os.path.join(file_path, file_pattern))
    res_files = work_path / failed_testcase_file_name

    # Check if the file exists before attempting to remove it
    if os.path.exists(res_files):
        os.remove(res_files)
        print(f"{res_files} has been successfully removed.")
    else:
        print(f"{res_files} does not exist.")

    # Open the file in write mode
    with open(res_files, "w") as file:
        for xmlfile in xml_files:
            # Parse the XML file
            tree = ET.parse(xmlfile)
            # Get the root element
            root = tree.getroot()
            testsuite = root.find('testsuite')
            # Iterate over the testcase elements
            for testcase in testsuite.findall('testcase'):
                name = testcase.get('name')
                if testcase.find('failure') is not None:
                    # Write the name of the failed test to the file
                    file.write(name + ",")
    print("The Failure test names have been written to", res_files)

    # Check if the file exists
    if res_files.exists():
        # Read the content of the file
        with open(res_files, 'r') as file:
            content = file.read()

        # Check if the content is empty
        if not content:
            # Delete the file if the content is empty
            res_files.unlink()
            print(f"{res_files} has been successfully removed because its content was empty.")
        else:
            # Remove the last comma from the content
            modified_content = content.rstrip(',')
            # Write the modified content back to the file
            with open(res_files, "w") as file:
                file.write(modified_content)

    else:
        print(f"{file_path} does not exist.")


if __name__ == "__main__":
    generate_failed_testcase_with_newfile(junit_path) 