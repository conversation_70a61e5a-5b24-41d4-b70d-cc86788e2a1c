#!/bin/bash
cwd=$(cd `dirname $0` && pwd)
cd $cwd
#generate the vertex and edge and check the data file exists
python3 generate_result_data_json.py
if [ -f "$cwd/data.json" ]; then
    echo "The file data.json exists."
else
    echo "The file data.json does not exist."
    exit 1
fi

# push the data.json into mit server and with graph of cloud_test
if curl -sIL "https://mit392.i.tgcloud.io:443" | grep "HTTP/2 200" > /dev/null; then
    tg_url="https://mit392.i.tgcloud.io:443/restpp"
    tg_token=$(curl -s -X POST $tg_url/requesttoken -d '{"secret": "5tj5bq5909na4m232n656bhohg38ja8m", "lifetime": "100000"}'|jq -r .token)
else
    if [[ -n "$(echo ${jenkins_url} | grep '192.168.55.21')" ]]; then
        tg_url="http://192.168.55.21:30090"
    elif [[ -n "$(echo ${jenkins_url} | grep '35.226.74.244')" ]]; then
        tg_url="http://10.244.240.203:9000"
    elif [[ -n "$(echo ${jenkins_url} | grep '192.168.99.101')" ]]; then
        tg_url="http://192.168.99.101:30090"
    fi
    tg_token=""
fi
if [[ $tg_url == *"tgcloud"* ]]; then
  curl --data-binary @data.json -H "Authorization: Bearer ${tg_token}" "${tg_url}/graph/cloud_test"
else
  curl --data-binary @data.json -u "tigergraph:TigergraphQE" "${tg_url}/graph/cloud_test"
fi

echo "----------------------END----------------------"

