#!/bin/bash

echo "
##############################################################################################################
# This script is adapted the cloud e2e test
# E) TEST_ENV = \$1 -- execute test environment
# H) HEADER = \$2 -- the HEADER used to identify test.tgcloud-dev.com PR
# M) MARKER = \$3 -- execute test cases by MARKER
# N) N_CPUS = \$4 -- simultaneous test number
# R) RERUNS = \$5 -- repeat times if failed
# F) FoT_PARAM=\$6 -- FoT params
# C) CLOUD_PARAM=\$7 -- Cloud params
# Example:  sh run.sh -E dev -M normal -N 2 -R 0
##############################################################################################################

"