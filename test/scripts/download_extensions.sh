#!/bin/bash

# Download the extensions to specified folder
current_path=$(cd $(dirname "$0") && pwd)
source $current_path/../environment.properties

declare -a modheader_chrome_crx_url=(
    "https://qe-test.s3.amazonaws.com/tgcloud/modheader/modheader.crx"
    "https://raw.githubusercontent.com/modheader/modheader_selenium/main/chrome-modheader/modheader.crx"
)

declare -a modheader_firefox_xpi_url=(
    "https://raw.githubusercontent.com/modheader/modheader_selenium/main/firefox-modheader/modheader.xpi"
    "https://qe-test.s3.amazonaws.com/tgcloud/modheader/modheader.xpi"
)

download_file(){
    local output=$1
    local url=$2
    local status_code
    status_code=$(curl --write-out %\{http_code\} "$url" -o "$output" -s )
    echo "$status_code"
}

download_extension(){
    local output=$1
    shift
    local url_array=("$@")
    local status_code
    for url in "${url_array[@]}"; do
	    status_code=$(download_file "$url" "$output")
        if [ "$status_code" == "200" ]; then break; fi
    done
    echo "$status_code"
}

BROWSER="${BROWSER:=chrome}"

if [ -n "$HEADER" ] || [ -n "$FoT_PARAM" ]; then
    if [ "$BROWSER" == "chrome" ]; then
        status_code=$(download_file "$extensions/modheader.crx" "${modheader_chrome_crx_url[@]}" )
        unzip "$extensions/modheader.crx" -d "$extensions/modheader"
        echo "unzip modheader to folder done"
    elif [ "$BROWSER" == "firefox" ]; then
        status_code=$(download_file "$extensions/modheader.xpi" "${modheader_firefox_xpi_url[@]}")
    else
        echo "The testing framework does not yet support the use of the modheader extension on this browser type: $BROWSER."
    fi
    if [ "$status_code" != "200" ]; then
        echo "Failed to download modheader extension for $BROWSER."
        exit 1
    fi
    echo "Downloaded modheader extension to $extensions."
else
    echo "HEADER or FoT_PARAM is not specified, so no need to download modheader extension."
fi

