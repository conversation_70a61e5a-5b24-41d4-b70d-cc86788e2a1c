import json
import sys
import os
import configparser
import subprocess
from pathlib import Path
import shutil

cur_path = Path(__file__).resolve().parent
work_path = Path(__file__).resolve().parents[2]
k_config = "config"
k_marker_config = "marker_config.json"
k_job_config = "job_config.json"
k_solutin_config = "starter_kit/starter_kits.json"
k_environment_properties = "environment.properties"
download_script = os.path.join(work_path, "scripts/download_solution.sh")
command_file_path = os.path.join(os.path.abspath('/'), 'tmp', 'e2e', 'pytest_command')
tools_commands_file = os.path.join(command_file_path, 'tools_commands_file')

def prepare_dir():

    if os.path.isdir(command_file_path):
        shutil.rmtree(command_file_path)
    os.makedirs(command_file_path)


def get_environment_properties():
    """get the env_props from the environment.properties file"""
    config = configparser.ConfigParser()
    with open(Path.joinpath(work_path, k_environment_properties)) as stream:
        config.read_string("[e2e]\n" + stream.read())
    global env_props
    env_props = {s: dict(config.items(s)) for s in config.sections()}["e2e"]


def get_marker_config_dict():
    """get the config file from marker_config.json"""
    global marker_config
    marker_config = {}
    with open(Path.joinpath(work_path, k_config, k_marker_config), "r") as config:
        marker_config = json.load(config)

def get_job_config_dict():
    """get the config file from job_config.json"""
    global job_config
    job_config = {}
    with open(Path.joinpath(work_path, k_config, k_job_config), "r") as config:
        job_config = json.load(config)

def get_solution_dict():
    """get the config file from starter_kits.json"""
    global solution_config
    solution_config = {}
    with open(Path.joinpath(work_path, k_config, k_solutin_config), "r") as config:
        solution_config = json.load(config)

#startkits:covid-19,gsql101
def is_execute_startkits():
    print(marker)
    if "startkits:" in marker:
        return True
    else:
        return False

def update_query_parameters():
    global marker
    if is_execute_startkits():
        input_list = marker.split(":")
        kits_str = input_list[1].strip()
        kits_list = kits_str.split(",")
        print("will execute starterkits: " + str(kits_list))

        #start download the startkits and local files
        download_solution(kits_list)

        #disable the solution and default is enabled
        disable_solution_if_not_provide(kits_list)
        
        #update the global marker to "startkits"
        marker="startkits"

#download the starterkits with solution tag
def download_solution(kits_list):
    global solution_config
    if kits_list[0] == "all":
        for solution in solution_config["SolutionList"]:
            execute_cmd = "sudo " + download_script + " " + solution["Tag"]
            subprocess.call(execute_cmd, shell=True)
    else:
        for kit in kits_list:
            execute_cmd = "sudo " + download_script + " " + kit
            subprocess.call(execute_cmd, shell=True)

#disable the solution
def disable_solution_if_not_provide(kits_list):
    global solution_config
    for solution in solution_config["SolutionList"]:
        if kits_list[0] != "all" and solution["Tag"] not in kits_list:
            solution["Enable"] = False

    with open(Path.joinpath(work_path, k_config, k_solutin_config), "w") as config:
        solution_config = json.dump(solution_config, config)

# Initialize global variables at the module level
headless = None
extension = None
browser_flag = None
skip_cases_flag = "None"
def get_header_parameters():
    """Read the configuration file, and determine the value of headless and extension
    according to whether the header is empty. `headless2` allows the use of extension,
    and `extension` specifies the location of the extension."""
    header = env_props["header"].strip('"').strip("'")
    fot_header = env_props["fot_param"].strip('"').strip("'")
    extensions = env_props["extensions"].strip('"').strip("'")
    browser = env_props["browser"].strip('"').strip("'")
    skip_cases = env_props["skip_test"].strip('"').strip("'")

    global headless, extension, browser_flag, skip_cases_flag
    if header or fot_header:
        # plan 1
        # headless = "--headless2"
        # extension = f"--extension-zip={extensions}/modheader.crx"
        # plan 2
        headless = "--headless"
        extension = f"--extension-dir={extensions}/modheader"
    else:
        headless = "--headless"
        extension = ""

    # set browser
    if browser == "chrome":
        browser_flag = "--browser chrome"
    elif browser == "edge":
        browser_flag = "--browser edge"
    elif browser == "firefox":
        browser_flag = "--browser firefox"
    elif browser == "safari":
        browser_flag = "--browser safari"
    else:
        browser_flag = "--browser chrome"

    # set skip-test parameter
    if skip_cases == "":
        skip_cases_flag = '--skip-tests ""'
    else:
        skip_cases_flag = '--skip-tests "'+skip_cases+'"'

def get_commands():
    """Read the environment property file and marker setting file, analyze whether it
    is a customized marker, and generate the pytest command of `tools`, and
    save it to the corresponding command file.
    If it is a non-customized marker, it is directly used for testing."""
    # Read configs
    get_environment_properties()
    get_job_config_dict()
    get_marker_config_dict()
    get_solution_dict()
    get_header_parameters()
    prepare_dir()
    # Get parameters
    all_markers = marker_config["all_customized_markers"]
    global marker 
    marker = env_props["marker"].strip('"').strip("'")

    #rewrite the marker as defined in the job configs
    if marker in job_config:
        print("will rewrite the marker to: {}".format(job_config[marker]))
        marker = job_config[marker]

    #update the solution config file if test startkits
    update_query_parameters()
    global tools_e2e_log_dir
    tools_e2e_log_dir = env_props["tools_e2e_log_dir"].strip('"').strip("'")
    n_cpus = env_props["n_cpus"].strip('"').strip("'")
    reruns = env_props["reruns"].strip('"').strip("'")
    # split the marker as array if contains ','
    marker_temp = []
    if "," in marker and "startkits" not in marker:
        marker_temp = marker.split(",")
    else:
        marker_temp = [marker]
    # If this marker is a customized marker, then get the pytest commands based on the config
    for c_marker in marker_temp:
        if any(i["marker"] == c_marker for i in all_markers):
            marker_info = [info for info in all_markers if info["marker"] == c_marker][0]
            tools_cases = marker_info.get("tools_cases")
            tools_commands = generate_commands(tools_cases)
        else:
            # If this marker is not a customized marker, then we can build commands directly.
            tools_commands = build_commands(c_marker, n_cpus, reruns)
        # write tools pytest commands to file then as log and run it with shell
        with open(tools_commands_file, 'a') as f:
            f.truncate()
            for item in tools_commands:
                f.write(str(item) + '\n')


def generate_commands(cases):
    """Generate commands according to the passed marker case info in the marker_config
    configuration file. case is a required item, It is best to assign clear values to
    order, parallelize, n_cpus, and reruns. If not, the default value will be used.
    default order = current order, n_cpus=5, reruns=0, parallelize=True"""
    # Sort the cases by order and parallelize
    cases = sorted(
        cases,
        key=lambda x: (x.get('order', sys.maxsize), x.get('parallelize', True)),
        reverse=False,
    )
    # Initial parameters
    pytest_commands = []
    # Constant
    MIN_N_CPUS = 1
    MIN_RERUNS = 0
    MAX_N_CPUS = 10
    MAX_RERUNS = 10
    # Vary variables
    current_order = -1
    # Vary variables need to be reset
    current_case_marker = ""
    current_n_cpus = 1
    current_reruns = 0
    # Get the pytest commands
    case_size = len(cases)
    for index, case_info in enumerate(cases):
        next_index = index + 1
        if current_case_marker:
            current_case_marker = f"{current_case_marker} or ({case_info['case']})"
        else:
            current_case_marker = f"({case_info['case']})"
        current_n_cpus = max(current_n_cpus, case_info.get('n_cpus', 5))
        current_reruns = max(current_reruns, case_info.get('reruns', 0))
        current_order = case_info.get('order', current_order)
        #  If adding a marker for the next case, continue
        if (
            next_index < case_size
            and case_info.get('order') == cases[next_index].get('order')
            and case_info.get('parallelize', True) == True
            and cases[next_index].get('parallelize', True) == True
        ):
            continue
        # Otherwise create the command, and reset the parameters
        else:
            current_n_cpus = min(max(MIN_N_CPUS, current_n_cpus), MAX_N_CPUS)
            current_reruns = min(max(MIN_RERUNS, current_reruns), MAX_RERUNS)
            pytest_commands.extend(
                build_commands(current_case_marker, current_n_cpus, current_reruns)
            )
            # reset parameters
            current_case_marker = ""
            current_n_cpus = 1
            current_reruns = 0
    return pytest_commands


def build_commands(marker, n_cpus, reruns):
    """Build the pytest command directly according to the value passed in,
    please try to keep this as the only command building entry. Dynamic parameters
    can be set in this function, while relatively fixed or fixed parameters
    should be set in pytest.ini.
    """
    marker_log = marker.replace("(", "").replace(")", "").replace(" ", "_")
    log_file = f"{tools_e2e_log_dir}/{marker_log}_result.log"
    return [
        f""" pytest -m "{marker}" -n {n_cpus}  {skip_cases_flag} --reruns {reruns} {headless} {extension} {browser_flag}  --junitxml=junit/test-results-$(date +"%Y%m%d%H%M%S").xml | tee "{log_file}" """
    ]


if __name__ == "__main__":
    get_commands()
