#!/bin/bash
set -x

# It is a temp solution to install required utils.

initial_env() {
    current_path=$(cd "$(dirname "$0")" && pwd)
    source "${current_path}/sys_utils.sh"
    project_path="$current_path/../"
    cd ${project_path}
    OS=$(get_os_type)
    echo $OS
    python_venv="e2e_env"
    use_local_hub="true"
    chrome_version=108.0.5359.71
    firefox_version=108.0
    edge_version=109.0.1518.78
    echo $Browser
}

install_utils() {
    if [ "$OS" = "UBUNTU" ]; then
        install_utils_on_ubuntu
    else
        install_utils_on_centos
    fi
}

install_utils_on_ubuntu() {
    sudo apt update
    # Install java
    if ! command -v java &>/dev/null; then
        sudo apt-get -y -q install openjdk-8-jdk
    fi
    # Install python3.10
    if ! command -v python3.10 &>/dev/null; then
        sudo add-apt-repository ppa:deadsnakes/ppa
        sudo apt-get update
        sudo apt-get -y -q install python3.10
        sudo ln -sf /usr/bin/python3.10 /usr/bin/python3
        python3 --version
#        sudo apt-get -y -q install python3
    fi
    # Install venv
    python_minor_version=$(get_python_minor_version)
    sudo apt-get -y -q install "python${python_minor_version}-venv"
    # Install moreutils
    sudo apt-get install moreutils -y
    # Install AWS CLI
    install_aws_cli
}

install_aws_cli() {
    # The AWS CLI will be migrated from Active Directory Graph API to Microsoft Graph API, need to change it later
    if ! command -v aws &> /dev/null; then
        sudo apt-get -y -q install unzip
        sudo curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "/opt/awscliv2.zip"
        sudo unzip -o "/opt/awscliv2.zip" -d /opt
        sudo rm -f "/opt/awscliv2.zip"
        sudo "/opt/aws/install"
    fi
}

install_utils_on_centos() {
    sudo yum update -y
    # Install java
    if ! command -v java &>/dev/null; then
        sudo yum install -y java-1.8.0-openjdk.x86_64
    fi
    # Install python3
    if ! command -v python3 &>/dev/null; then
        sudo yum update -y
        sudo yum install -y https://repo.ius.io/ius-release-el$(rpm -E '%{rhel}').rpm
        sudo yum update -y
        sudo yum install -y python3
    fi
    # Install venv
    python3 -m venv python3-virtualenv
    # Install moreutils
    sudo yum install epel-release -y
    sudo yum install moreutils -y
}

setup_python_venv() {
    python3 -m venv ${python_venv}
    # shellcheck source=/dev/null
    source ${python_venv}/bin/activate
    python3 --version
    sudo python3 -m pip install --upgrade pip setuptools
    pip install --upgrade pip
    pip3 --version
    pip3 install -r requirements.txt
}

setup_selenium_base() {
    sbase get chromedriver $chrome_version
    pkill chromedriver
    sbase grid-hub restart &
    sbase grid-node restart &
    sleep 1
}

setup_my_selenium_base() {
    if [ "$Browser" == "chrome" ]; then
        sbase get chromedriver $chrome_version
        pkill chromedriver
    elif [ "$Browser" == "firefox" ]; then
        #RemotingName=firefox Version=108.0 BuildID=20221208122842
        #sbase get geckodriver v0.34.0 for V120.0, V0.32 for V108
        sbase get geckodriver v0.32.0
        pkill geckodriver
    elif [ "$Browser" == "edge" ]; then
        sbase get edgedriver $edge_version
        pkill edgedriver
    else
        echo "Unsupported browser: $Browser"
        exit 1
    fi

    sbase grid-hub restart &
    sbase grid-node restart &
    sleep 1
}

setup_browser() {
    #get_sbase_chrome_version
    current_chrome_version=$(google-chrome -version | cut -d ' ' -f 3 || echo "none")
    if [ "$chrome_version" != "$current_chrome_version" ]; then
        if [ "$OS" == "UBUNTU" ]; then
            install_chrome_on_ubuntu
        else
            install_chrome_on_centos
        fi
    fi
}

setup_my_browser() {
    if [ "$Browser" == "chrome" ]; then
        current_chrome_version=$(google-chrome -version | cut -d ' ' -f 3 || echo "none")
        echo "current_version: $current_chrome_version"
        if [ "$chrome_version" != "$current_chrome_version" ]; then
            if [ "$OS" == "UBUNTU" ]; then
                install_my_browser_on_ubuntu
            elif [ "$OS" == "CENTOS" ]; then
                install_my_browser_on_centos
            else
                echo "Unsupported OS: $OS"
                exit 1
            fi
        fi
    elif [ "$Browser" == "firefox" ]; then
        current_firefox_version=$(firefox -version | cut -d ' ' -f 3 || echo "none")
        echo "current_version: $current_firefox_version"
        if [ "$firefox_version" != "$current_firefox_version" ]; then
            if [ "$OS" == "UBUNTU" ]; then
                install_my_browser_on_ubuntu
            elif [ "$OS" == "CENTOS" ]; then
                install_my_browser_on_centos
            else
                echo "Unsupported OS: $OS"
                exit 1
            fi
        fi
    elif [ "$Browser" == "edge" ]; then
        current_edge_version=$(microsoft-edge-stable --version | cut -d ' ' -f 3 || echo "none")
        echo "current_version: $current_edge_version"
        if [ "$edge_version" != "$current_edge_version" ]; then
            if [ "$OS" == "UBUNTU" ]; then
                install_my_browser_on_ubuntu
            elif [ "$OS" == "CENTOS" ]; then
                install_my_browser_on_centos
            else
                echo "Unsupported OS: $OS"
                exit 1
            fi
        fi
    else
        echo "Unsupported browser: $Browser"
        exit 1
    fi
}

get_sbase_chrome_version() {
    python_minor_version=$(get_python_minor_version)
    chromediver="${project_path}/${python_venv}/lib/python${python_minor_version}/site-packages/seleniumbase/drivers/chromedriver"
    sudo chmod +x chromediver
    chrome_version=$("${chromediver}" -version | cut -d ' ' -f 2)
}

get_python_minor_version() {
    local python_path=$(command -v python3)
    local python_version=$($python_path --version | cut -d ' ' -f 2)
    local python_minor_version=${python_version%.*}
    echo "$python_minor_version"
}

install_chrome_on_centos() {
    if [ "none" != "$current_chrome_version" ]; then
        sudo yum remove google-chrome -y
    fi
    sudo yum install -y https://qe-test.s3.amazonaws.com/browser/google-chrome-stable-${chrome_version}-1.x86_64.rpm
}

install_chrome_on_ubuntu() {
    if [ "none" != "$current_chrome_version" ]; then
        sudo dpkg -r google-chrome-stable
    fi
    chrome_ubuntu_url="https://qe-test.s3.amazonaws.com/browser/google-chrome-stable_${chrome_version}-1_amd64.deb"
    if curl --output /dev/null --silent --head --fail "$chrome_ubuntu_url"; then
        wget -O /tmp/chrome.deb "$chrome_ubuntu_url"
        sudo apt install -y /tmp/chrome.deb
    else
        wget -O /tmp/chrome.deb https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
        sudo dpkg -i /tmp/chrome.deb
        sudo apt-get install -f -y
        sudo dpkg -i /tmp/chrome.deb
    fi
    sudo rm /tmp/chrome.deb
}

install_my_browser_on_ubuntu() {
    if [ "$Browser" == "chrome" ]; then
        sudo dpkg -r google-chrome-stable
        echo "Uninstalling chrome browser..."
    elif [ "$Browser" == "firefox" ]; then
        sudo apt-get remove --purge firefox
        sudo rm -rf /opt/firefox
        echo "Uninstalling firefox browser..."
    elif [ "$Browser" == "edge" ]; then
        sudo apt remove microsoft-edge-stable -y
        echo "Uninstalling Edge browser..."
    fi

    if [ "$Browser" == "chrome" ]; then
        browser_url="https://qe-test.s3.amazonaws.com/browser/google-chrome-stable_${chrome_version}-1_amd64.deb"
    elif [ "$Browser" == "firefox" ]; then
        browser_url="https://qe-test.s3.amazonaws.com/browser/firefox-${firefox_version}.tar.bz2"
    elif [ "$Browser" == "edge" ]; then
        browser_url="https://qe-test.s3.amazonaws.com/browser/microsoft-edge-stable_${edge_version}-1_amd64.deb"
        echo "Downloading Edge browser..."
    else
        echo "Unsupported browser: $Browser"
        exit 1
    fi

    if [ "$Browser" == "firefox" ]; then
        wget "$browser_url"
#        tar -xvf firefox-120.0.tar.bz2
        tar -xvf firefox-${firefox_version}.tar.bz2
        sudo rm -fr /opt/firefox
        sudo mv firefox /opt/
        sudo rm /usr/local/bin/firefox
        sudo ln -s /opt/firefox/firefox /usr/local/bin/firefox
        # update libdbus-glib-1.so.2:
        sudo apt-get update
        sudo apt-get install libdbus-glib-1-2 -y
        sudo apt-get install libgtk-3-0 -y
        firefox -v
    elif curl --output /dev/null --silent --head --fail "$browser_url"; then
        wget -O /tmp/browser.deb "$browser_url"
        sudo apt install -y /tmp/browser.deb
        echo "Succeed to install $Browser browser."
        if [ "$Browser" == "chrome" ]; then
            google-chrome -version
        elif [ "$Browser" == "edge" ]; then
            microsoft-edge-stable --version
            sleep 20
        fi
    else
        echo "Failed to download $Browser browser."
        echo "Browser download site: $browser_url"
        exit 1
    fi

    sudo rm /tmp/browser.deb
}

install_my_browser_on_centos() {
    if [ "$Browser" == "chrome" ]; then
        sudo yum remove -y google-chrome-stable
    elif [ "$Browser" == "firefox" ]; then
        sudo yum remove -y firefox
    elif [ "$Browser" == "edge" ]; then
        sudo yum remove -y microsoft-edge-stable
    fi

    if [ "$Browser" == "chrome" ]; then
        browser_url="https://qe-test.s3.amazonaws.com/browser/google-chrome-stable-${chrome_version}-1.x86_64.rpm"
        echo "browser download site: $browser_url"
    elif [ "$Browser" == "firefox" ]; then
        browser_url="https://qe-test.s3.amazonaws.com/browser/firefox-${firefox_version}.tar.bz2"
        echo "browser download site: $browser_url"
    elif [ "$Browser" == "edge" ]; then
        browser_url="https://qe-test.s3.amazonaws.com/browser/microsoft-edge-stable-${edge_version}-1.x86_64.rpm"
        echo "edge browser download site: $browser_url"
    else
        echo "Unsupported browser: $Browser"
        exit 1
    fi

    if [ "$Browser" == "firefox" ]; then
        wget "$browser_url"
#        tar -xvf firefox-120.0.tar.bz2
        tar -xvf firefox-${firefox_version}.tar.bz2
        sudo rm -fr /opt/firefox
        sudo mv firefox /opt/
        sudo rm /usr/local/bin/firefox
        sudo ln -s /opt/firefox/firefox /usr/local/bin/firefox
    elif curl --output /dev/null --silent --head --fail "$browser_url"; then
        wget -O /tmp/browser.rpm "$browser_url"
        sudo yum install -y /tmp/browser.rpm
        echo "Succeed to install $Browser browser."
    else
        echo "Failed to download $Browser browser."
        echo "Browser download site: $browser_url"
        exit 1
    fi

    sudo rm /tmp/browser.rpm
}

create_folder(){
    mkdir -p "$project_path/downloaded_files"
    mkdir -p "$project_path/alluredir"
    sudo chmod -R 777 "$project_path"
}

prepare_env() {
    initial_env
    install_utils
    setup_python_venv
#    setup_selenium_base
    setup_my_selenium_base
#    setup_browser
    setup_my_browser
    create_folder
}

result_folder="/tmp/tools_e2e/result_$(date +%s)"
mkdir -p $result_folder
prepare_env >"$result_folder/prepare_env_log.$$" 2>&1
