#!/bin/bash
set +x
####################################################################################
# This script is used to download the tarballs and datafiles of solution
# SOLUTION = $1 -- the solution tag. Can specific solution tag or all solutions.
# Example:  sh download_solution.sh $SOLUTION
# 1. If all solutions or multiple solutions are required, split them into single solution download tasks.
# 2. For a single solution, will check if there are files already exist. Return if exist.
# 3. Then check if the URL is null. Return if the URL is null.
# 4. Download the solution tarball in separate folders.
# 5. Download the datafiles in separate folders, uncompress tarball to CSV file, delete datafiles tarball.
####################################################################################
SOLUTION=$1

if [[ $# != 1 ]]; then
    echo "The count of input parameter SOLUTION $# is more than need (1). Please separate them with semicolons(;)."
    exit 1
fi

CURRENT_PATH=$(
    cd "$(dirname -- "$0")" || exit
    pwd
)
STARTER_KITS_CONFIG=$CURRENT_PATH/../config/starter_kit/starter_kits.json
SOLUTION_FOLDER=$CURRENT_PATH/../data/solution/$SOLUTION
DATA_FILE_FOLDER=$CURRENT_PATH/../data/datafiles/$SOLUTION

echo "Start to download files for $SOLUTION."
#sudo yum install jq -y >/dev/null

download_solution_file() {
    #  download solution
    if [ "$1" = "null" ]; then
        echo "The SolutionURL is empty"
    elif [ -d "$SOLUTION_FOLDER" ] && [ -f "$SOLUTION_FOLDER"/"${1##*/}" ]; then
        echo "Will remove the Solution: $SOLUTION_FOLDER"
        rm -rf $SOLUTION_FOLDER
    fi
    echo "The SolutionURL is: $1"
    (mkdir -p "$SOLUTION_FOLDER" && cd "$_" && curl -s -O "$1")
    echo "Solution is downloaded to $SOLUTION_FOLDER"
}

download_data_file() {
    # download and uncompress file
    if [ "$1" = "null" ]; then
        echo "The DatafileURL is empty"
    elif [ -d "$DATA_FILE_FOLDER" ] && [ "$(ls -l "$DATA_FILE_FOLDER" | grep .csv | wc -l)" -gt 0 ]; then
        echo "Datafiles exist in $DATA_FILE_FOLDER"
        exit 0
    else
        echo "The DatafileURL is: $1"
        (mkdir -p "$DATA_FILE_FOLDER" && cd "$_" && curl -s -O "$1" && tar -xvf ./*.t* >>/dev/null && rm -rf $(ls -a| grep -v ".csv"))
        echo "Datafiles are downloaded to $DATA_FILE_FOLDER"
    fi
}

main() {
    # If all solutions are required, split them into single solution download tasks.
    if [[ $SOLUTION = "ALL_SOLUTIONS" ]]; then
        ALL_SOLUTIONS=$(cat <QUERY_PARAMETERS.json | jq -r '.SolutionList[] | .Tag')
        for SOLUTION_NAME in ${ALL_SOLUTIONS}; do
            bash "$0" "$SOLUTION_NAME"
        done
    # If multiple solutions are required, split them into single solution download tasks.
    elif [[ ${SOLUTION} =~ ',' ]]; then
        # set -f
        SOLUTION_ARRAY=($(echo "$SOLUTION" | tr ',' '\n'))
        for SOLUTION_NAME in "${SOLUTION_ARRAY[@]}"; do
            bash "$0" "$SOLUTION_NAME"
        done
    # single solution download tasks.
    else
        SOLUTION_URL=$(cat <"$STARTER_KITS_CONFIG" | jq -r " .SolutionList[]  | select(.Tag == "\"$SOLUTION\"") | .SolutionURL")
        DATA_URL=$(cat <"$STARTER_KITS_CONFIG" | jq -r " .SolutionList[]  | select(.Tag == "\"$SOLUTION\"") | .DataURL")
        download_solution_file "$SOLUTION_URL"
        download_data_file "$DATA_URL"
    fi
}
main
