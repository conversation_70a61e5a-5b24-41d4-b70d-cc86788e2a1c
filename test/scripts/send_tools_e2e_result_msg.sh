#!/usr/bin/env bash

send_tools_e2e_result_msg(){

local file_name=${1}
local test_res=${2}
local test_env=${3}
local test_case=${4}
local job_url=${5}
local allure_report=${6}
local job_prefix=${7}

cwd=$(cd `dirname $0` && pwd)
mkdir -p "$HOME/tools_e2e_result"
log_file="$HOME/tools_e2e_result/send_tgcloud_e2e_result_msg.log"
base_package="$(basename "$build_package_url")"

current_time=$(date +"%s")
qa_bot_oauth_token="********************************************************"
notice_message_block_json=$(cat "$cwd/notice_message_block.json")
printf -v notice_message "${notice_message_block_json}" \
"$current_time" "$test_res" "$test_env" "$test_case" "$job_prefix/$job_url" "$job_url" "$job_prefix/$allure_report" "$allure_report" 

receiver_groups=("cloud-e2e-daily-result,B0493SXKMUK/QNhLd6Wc2n02jSDAhjtAAM3r" )

for group in "${receiver_groups[@]}"; do
    ifs=$IFS; IFS=, read -ra array <<< "${group//, /,}"; IFS=$ifs
    declare -p array > /dev/null
    channel=${array[0]}
    webhook_url=${array[1]}
    echo  "send message to channel $channel." | tee -a "$log_file"
    curl -X POST -H "Content-Type: application/json; charset=utf-8" \
      -H "Authorization: Bearer ${qa_bot_oauth_token}" \
      -d "{\"channel\": \"${channel}\",${notice_message}}" \
      "https://hooks.slack.com/services/T9GCNQ003/${webhook_url}"  | tee -a "$log_file"
done

}

meta_json_file=${1}
test_res=${2}
test_env=${3}
test_case=${4}
job_url=${5}
allure_report=${6}
job_prefix=${7}


send_tools_e2e_result_msg "$meta_json_file" "$test_res" "$test_env" "$test_case"  "$job_url" "$allure_report" "$job_prefix"