#!groovy

// Utils
def run_bash(String command, Boolean showCmd = true) {
  if (showCmd) {
    echo command
  }
  sh "#!/bin/bash \n  ${command}"
}

/*
*  param: run_bash_mod is used to run command together.
*  get std output
*/
def run_cmd_get_stdout(String cmd, Boolean run_bash_mod = false, Boolean showCmd = true) {
  if (run_bash_mod) {
    if (showCmd) {
      echo cmd
    }
    cmd = "#!/bin/bash \n ${cmd}"
  }
  def result = sh(script: cmd, returnStdout: true)
  while (result.endsWith('\n')) {
    result = result.substring(0, result.length() - 1)
  }
  return result
}

/*
*  param: run_bash_mod is used to run command together.
*  get std output
*/
def run_cmd_get_stdout_array(String cmd, Boolean run_bash_mod = false, Boolean showCmd = true) {
  result = run_cmd_get_stdout(cmd,run_bash_mod,showCmd)
  return result ? result.trim().split('\n') : []
}

/*
* runs the given command and gets the return code
*/
def run_cmd_get_rc(String cmd, Boolean run_bash_mod = false, Boolean showCmd = true) {
  if (run_bash_mod) {
    if (showCmd) {
      echo cmd
    }
    cmd = "#!/bin/bash \n ${cmd}"
  }
  def rc = sh(script: cmd, returnStatus: true)
  return rc
}

return this
