#!/bin/bash

get_os_type(){
  if [ -f /etc/os-release ]; then
    os_id=$(awk -F= '/^ID=/{print $2}' /etc/os-release | tr -d '"')

    # judge os
    if [ "$os_id" == "ubuntu" ]; then
        echo UBUNTU
    elif [ "$os_id" == "centos" ]; then
        echo CENTOS
    else
        echo "Unsupported OS. Please contact QA Team."
        exit 2
    fi
  else
        warn "Unknown OS. Please contact QA Team."
        exit 2
  fi
}

get_os_version(){
  if [ -f "/etc/apt/sources.list" ]
  then
    os_version=$(cat /etc/lsb-release | grep  "DISTRIB_RELEASE" | cut -d= -f2 |cut -d. -f1)
    if [ "$os_version" -lt 12 ]
    then
      warn "Unsupported OS. Please upgrade to Ubuntu 12.x or above."
      exit 2
    else
      echo UBUNTU
    fi
  elif [ -d "/etc/yum.repos.d" ]
  then
    os_version="$(cat /etc/system-release | grep -o ' [0-9]')"
    if [ "$os_version" -lt 6 ]
    then
      warn "Unsupported OS. Please upgrade to RHEL or CentOS 6.x or above."
      exit 2
    else
      echo RHEL
    fi
  else
    warn "Unknown OS. Please contact GraphSQL support."
    exit 2
  fi
  echo "$os_version"
}