#!/bin/bash
set -x

# Check if DB_VERSION and LICENSE are provided
if [ -z "\$1" ] || [ -z "\$2" ]; then
  echo "Error: DB_VERSION and LICENSE arguments are required."
  echo "Usage: \$0 <DB_VERSION> <LICENSE>"
  exit 1
fi

# Define variables
DB_VERSION=$1
LICENSE=$2
URL="https://storage.googleapis.com/qe-test-data/local-packages/tigergraph-${DB_VERSION}-offline.tar.gz"
FILENAME=$(basename $URL)
TARGET_DIR="tigergraph-${DB_VERSION}-offline"
CURRENT_PATH=$(pwd)
GADMIN="/home/<USER>/tigergraph/app/cmd/gadmin"


# Function to check if a command executed successfully
check_command() {
  if [ $? -eq 0 ]; then
    echo "Success: \$1"
  else
    echo "Error: \$1"
    exit 1
  fi
}

# Check if curl/wget is installed
if command -v curl &> /dev/null
then
    DOWNLOADER="curl -O"
elif command -v wget &> /dev/null
then
    DOWNLOADER="wget"
else
    echo "Error: Neither curl nor wget is installed."
    exit 1
fi

# Download the file
echo "Downloading $URL... into ${CURRENT_PATH}"
$DOWNLOADER $URL
check_command "Downloading $URL"

# Check if the file was downloaded
if [ ! -f $FILENAME ]; then
  echo "Error: File $FILENAME not found."
  exit 1
fi

# Extract the tar file
echo "Extracting $FILENAME..."
tar -xzvf $FILENAME
check_command "Extracting $FILENAME"

# Check if the extraction was successful
if [ ! -d $TARGET_DIR ]; then
  echo "Error: Directory $TARGET_DIR not found after extraction."
  exit 1
fi

#Install Tigergraph Database
cd $TARGET_DIR
#sudo ./install_tools.sh
sudo apt-get update
sudo apt-get install net-tools
check_command "Install Required Tools"

./install.sh -n
check_command "Install Database"

#Set a free license for database
sudo su - tigergraph -c "$GADMIN license set $LICENSE"
sudo su - tigergraph -c "$GADMIN config apply -y"
sudo su - tigergraph -c "$GADMIN restart -y"

check_command "Set License"

#Waiting 30s for service online
sleep 30 

echo "Install Tigergraph script executed successfully."

