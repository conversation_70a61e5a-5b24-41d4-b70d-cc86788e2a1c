package tests

import (
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestLogView(t *testing.T) {
	server := setupServer(t)
	client := http.Client{}
	cookieV := login(t, server)
	_, _ = client, cookieV

	testTable := []struct {
		description string
		path        string
		expected    string
	}{
		{
			description: "invalid path 1",
			path:        "../../../../proc/cpuinfo",
			expected:    `{"error":true,"message":"Path:'../../../../proc/cpuinfo' is invalid","results":null}`,
		},
		{
			description: "invalid path 2",
			path:        "../../../../etc/passwd",
			expected:    `{"error":true,"message":"Path:'../../../../etc/passwd' is invalid","results":null}`,
		},
		{
			description: "empty path",
			path:        "",
			expected:    `{"error":true,"message":"Invalid query.","results":null}`,
		},
	}
	for _, scenario := range testTable {
		t.Run(scenario.description, func(t *testing.T) {
			req, _ := http.NewRequest(
				http.MethodGet,
				server.URL+"/api/log/view?path="+scenario.path,
				nil,
			)
			req.Header.Set("Cookie", "TigerGraphApp="+cookieV)

			res, _ := client.Do(req)
			b, _ := io.ReadAll(res.Body)
			require.Equal(t, scenario.expected, string(b))
		})
	}

}
