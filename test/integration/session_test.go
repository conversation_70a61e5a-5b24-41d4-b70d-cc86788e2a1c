package tests

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/tigergraph/gus/controller"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	"github.com/tigergraph/gus/middleware"
)

func TestSessionExpiration(t *testing.T) {
	auth := middleware.InMemoryAuthenticationService()
	// setup controller
	gsqlClient := NewFakeRequestGSQLClient()
	c, err := controller.New(
		controller.ControllerDependencies{
			AuthenticationService: auth,
			GSQLAuthenticator: func(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {
				return &model.UserInfo{
					IsSuperUser: false,
				}, nil
			},
			ExpCheckInterval:              time.Second * 2,
			DaoManager:                    fakeDaoManager{},
			DatabaseManager:               fakeDatabaseManager{},
			CheckAndCreateCategoryDirFunc: CheckAndCreateCategoryDir,
			UpsertSAMLResponse:            func(hash, samlResp string) error { return nil },
			CntlrClient:                   nil,
			LoadingJobService:             &fakeLoadingJobService{},
			RequestGSQLClient:             (&gsqlClient).RequestGSQLClient,
			SchemaSyncer:                  &fakeSchemaSyncer{},
			TokenService:                  &fakeTokenService{},
			InsightsService:               &fakeInsightsService{},
			SecurityChecker: func(c context.Context, cfg *config.Config, creds *model.UserCredentials) (*model.GSQLLoginResponse, error) {
				return &model.GSQLLoginResponse{}, nil
			},
		},
		controller.ConfigFromFile("test.cfg", 1))
	require.NoError(t, err)
	require.NoError(t, c.SetUp())
	r := c.Router()
	// setup server
	server := httptest.NewServer(r)
	// defer server.Close()
	test(t, server)
}

// Rest API tests
func test(t *testing.T, server *httptest.Server) {
	client := &http.Client{}
	_ = cfg(t)
	t.Run("session expires", func(t *testing.T) {
		cookie := login(t, server)
		defer logout(t, server, cookie)
		// prepare request

		req, _ := http.NewRequest("GET", server.URL+"/api/ping2", nil)
		req.Header.Set("Cookie", "TigerGraphApp="+cookie)
		// wait for session to expire
		time.Sleep(time.Second * 3)
		// make request
		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		// assert
		require.Equal(t, http.StatusUnauthorized, res.StatusCode)
		require.Equal(t, `{"error":true,"message":"You are not authorized to use this API.","results":null}`, string(b))
	})
	// t.Run("extend session expiration", func(t2 *testing.T) {
	// 	cookie := login(t, server)
	// 	defer logout(t, server, cookie)
	// 	requestClaims, _ := auth.Authenticate(context.Background(), cookie)

	// 	// prepare request
	// 	req, _ := http.NewRequest("GET", server.URL+"/api/ping2", nil)
	// 	time.Sleep(time.Second * 1)
	// 	req.Header.Set("Cookie", "TigerGraphApp="+cookie)

	// 	// make request
	// 	res, _ := client.Do(req)
	// 	b, _ := io.ReadAll(res.Body)
	// 	// assert
	// 	assert.Equal(t, http.StatusOK, res.StatusCode)
	// 	require.Equal(t, `{"error":false,"message":"pong","results":null}`, string(b))

	// 	// check if this session's expiration time is extended
	// 	claims, _ := auth.Authenticate(context.Background(), cookie)

	// 	require.Truef(t,
	// 		requestClaims.ExpiresAt < claims.ExpiresAt,
	// 		"%v %v", requestClaims.ExpiresAt, claims.ExpiresAt)
	// 	require.Truef(t,
	// 		claims.ExpiresAt < requestClaims.ExpiresAt+int64(cfg(t).ProtoConf.GUI.Cookie.DurationSec),
	// 		"%v %v", claims.ExpiresAt, requestClaims.ExpiresAt+int64(cfg(t).ProtoConf.GUI.Cookie.DurationSec))
	// })
	// t.Run("sse notifies frontend when jwt expires", func(t2 *testing.T) {
	// 	cookie := login(t, server)
	// 	defer logout(t, server, cookie)

	// 	req, _ := http.NewRequest("GET", server.URL+"/api/stream", nil)

	// 	duration := time.Second
	// 	_, deadline := fakeJwtWithDeadline(t, duration)
	// 	req.Header.Set("Cookie", "TigerGraphApp="+cookie)

	// 	// make request
	// 	res, _ := client.Do(req)

	// 	// assert
	// 	assert.Equal(t, http.StatusOK, res.StatusCode)

	// 	// wait for request to finish
	// 	io.ReadAll(res.Body)
	// 	// require.Equal(t, "id:bcd6089f-a27b-4688-9d94-dc9bfe6cbd52\nevent:heartbeat\ndata:1630828269\n\n", string(b))
	// 	doneTime := time.Now()

	// 	// the error margin is within 1 sec
	// 	if -time.Second < doneTime.Sub(deadline) && doneTime.Sub(deadline) < time.Second {
	// 	} else {
	// 		require.FailNow(t, "the error margin > 1 sec", fmt.Sprintf("%v", doneTime.Sub(deadline)))
	// 	}
	// })
	// t.Run("sse notifies frontend when extended jwt expires", func(t *testing.T) {
	// 	cookie := login(t, server)
	// 	defer logout(t, server, cookie)

	// 	req, _ := http.NewRequest("GET", server.URL+"/api/stream", nil)

	// 	req.Header.Set("Cookie", "TigerGraphApp="+cookie)

	// 	// make request
	// 	res, _ := client.Do(req)

	// 	// assert
	// 	assert.Equal(t, http.StatusOK, res.StatusCode)

	// 	c := make(chan struct {
	// 		ok  bool
	// 		dur time.Duration
	// 	})
	// 	go func() {
	// 		// should not finished before 3s because the expiration time has been extended for 1 seconds
	// 		start := time.Now()
	// 		yes := longerThan(time.Second*3, func() {
	// 			io.ReadAll(res.Body)
	// 		})
	// 		c <- struct {
	// 			ok  bool
	// 			dur time.Duration
	// 		}{
	// 			ok:  yes,
	// 			dur: time.Since(start),
	// 		}
	// 	}()

	// 	time.Sleep(1 * time.Second)      // extend the expiration time for 1 second
	// 	ping2(t, client, server, cookie) // should extend expiration time for SSE
	// 	result := <-c
	// 	fmt.Printf("finished in %v\n", result.dur)
	// 	if !result.ok {
	// 		require.FailNowf(t, "", "finished in %v", result.dur)
	// 	}
	// })
}

func TestLogout(t *testing.T) {
	server := setupServer(t)
	// Logout will succeed even if it has never been logged in.
	// This is counter-intuitive, but Frontend relies on this behavior.
	client := http.Client{}

	t.Run("can logout without cookie", func(t *testing.T) {
		req, _ := http.NewRequest(
			http.MethodPost,
			server.URL+"/api/auth/logout",
			nil,
		)
		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":false,"message":"Successfully logged out.","results":null}`, string(b))
	})
	t.Run("can logout with wrong cookie", func(t *testing.T) {
		req, _ := http.NewRequest(
			http.MethodPost,
			server.URL+"/api/auth/logout",
			nil,
		)
		req.Header.Set("Cookie", "TigerGraphApp=wrong")
		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":false,"message":"Successfully logged out.","results":null}`, string(b))
	})
}

func TestLoginLogout(t *testing.T) {
	// When logout, the session jwt token should be invalidated as well
	// Meaning request that uses the same jwt should be rejected.
	gsqlClient := NewFakeRequestGSQLClient()
	c, err := controller.New(
		controller.ControllerDependencies{
			AuthenticationService: middleware.InMemoryAuthenticationService(),
			GSQLAuthenticator: func(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {
				return &model.UserInfo{
					IsSuperUser: false,
				}, nil
			},
			ExpCheckInterval:              time.Second * 2,
			DaoManager:                    fakeDaoManager{},
			DatabaseManager:               fakeDatabaseManager{},
			CheckAndCreateCategoryDirFunc: CheckAndCreateCategoryDir,
			LoadingJobService:             &fakeLoadingJobService{},
			UpsertSAMLResponse:            func(hash, samlResp string) error { return nil },
			CntlrClient:                   nil,
			RequestGSQLClient:             (&gsqlClient).RequestGSQLClient,
			SchemaSyncer:                  &fakeSchemaSyncer{},
			TokenService:                  &fakeTokenService{},
			InsightsService:               &fakeInsightsService{},
			SecurityChecker: func(c context.Context, cfg *config.Config, creds *model.UserCredentials) (*model.GSQLLoginResponse, error) {
				return &model.GSQLLoginResponse{}, nil
			},
		},
		controller.ConfigFromFile("test.cfg", 1))
	require.NoError(t, err)
	require.NoError(t, c.SetUp())
	r := c.Router()
	// setup server
	server := httptest.NewServer(r)

	// prepare request
	client := http.Client{}

	/////////////////////////////////////
	// First, login to get a jwt token //
	/////////////////////////////////////
	cookieV := login(t, server)

	////////////////////
	// Second, logout //
	////////////////////
	logout(t, server, cookieV)

	//////////////////////////////////
	// Third, request a private API //
	//////////////////////////////////
	req, _ := http.NewRequest(
		http.MethodGet,
		server.URL+"/api/ping2",
		nil,
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookieV)

	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	require.Equal(t, `{"error":true,"message":"You are not authorized to use this API.","results":null}`, string(b))
}

func login(t *testing.T, server *httptest.Server) string {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		server.URL+"/api/auth/login",
		strings.NewReader(`{"username":"tigergraph","password":"tigergraph"}`),
	)

	res, _ := client.Do(req)
	cookieV := ""
	for _, cookie := range res.Cookies() {
		if cookie.Name == "TigerGraphApp" {
			cookieV = cookie.Value
			break
		}
	}
	if cookieV == "" {
		require.FailNow(t, "cookie is empty")
	}
	return cookieV
}

func logout(t *testing.T, server *httptest.Server, cookieV string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		server.URL+"/api/auth/logout",
		nil,
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookieV)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	require.Equal(t, `{"error":false,"message":"Successfully logged out.","results":null}`, string(b))
}

func setupServer(t *testing.T) *httptest.Server {
	auth := middleware.InMemoryAuthenticationService()
	// setup controller
	cfg, _ := config.New("test.cfg", 1)
	gsqlClient := NewFakeRequestGSQLClient()
	c, err := controller.New(
		controller.ControllerDependencies{
			AuthenticationService: auth,
			GSQLAuthenticator: func(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {
				return &model.UserInfo{
					IsSuperUser: true,
					Privileges: model.Privileges{
						"ldbc_snb": struct {
							Privileges []model.Privilege "json:\"privileges\" binding:\"required\""
						}{
							[]model.Privilege{
								model.READ_SCHEMA,
								model.WRITE_SCHEMA,
								model.READ_LOADINGJOB,
								model.EXECUTE_LOADINGJOB,
								model.WRITE_LOADINGJOB,
								model.READ_QUERY,
								model.WRITE_QUERY,
								model.READ_DATA,
								model.WRITE_DATA,
								model.WRITE_DATASOURCE,
								model.READ_ROLE,
								model.WRITE_ROLE,
								model.READ_USER,
								model.WRITE_USER,
								model.READ_PROXYGROUP,
								model.WRITE_PROXYGROUP,
								model.READ_FILE,
								model.WRITE_FILE,
								model.DROP_GRAPH,
								model.EXPORT_GRAPH,
								model.CLEAR_GRAPHSTORE,
								model.DROP_ALL,
								model.ACCESS_TAG,
							},
						},
					},
				}, nil
			},
			SecurityChecker: func(c context.Context, cfg *config.Config, creds *model.UserCredentials) (*model.GSQLLoginResponse, error) {
				return &model.GSQLLoginResponse{}, nil
			},
			ExpCheckInterval:              time.Second * 2,
			DaoManager:                    fakeDaoManager{},
			DatabaseManager:               fakeDatabaseManager{},
			CheckAndCreateCategoryDirFunc: CheckAndCreateCategoryDir,
			TGFileSystem:                  fs.NewTGFilesystem(cfg),
			CntlrClient:                   nil,
			LoadingJobService:             &fakeLoadingJobService{},
			UpsertSAMLResponse:            func(hash, samlResp string) error { return nil },
			RequestGSQLClient:             (&gsqlClient).RequestGSQLClient,
			SchemaSyncer:                  &fakeSchemaSyncer{},
			TokenService:                  &fakeTokenService{},
			InsightsService:               &fakeInsightsService{},
		},
		controller.ConfigFromFile("test.cfg", 1))
	require.NoError(t, err)
	require.NoError(t, c.SetUp())
	r := c.Router()
	// setup server
	server := httptest.NewServer(r)
	return server
}

func TestCORSForLogout(t *testing.T) {
	server := setupServer(t)
	client := http.Client{}
	cookieV := login(t, server)
	_, _ = client, cookieV
	req, _ := http.NewRequest(
		http.MethodOptions,
		server.URL+"/api/auth/logout",
		nil,
	)
	req.Header.Add("origin", "https://tgcloud-dev.com")
	resp, err := client.Do(req)
	require.NoError(t, err)
	require.Equal(t, http.StatusNoContent, resp.StatusCode)
}
