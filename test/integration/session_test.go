package tests

import (
	"context"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/tigergraph/gus/controller"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
)

func TestLogout(t *testing.T) {
	server := setupServer(t)
	// Logout will succeed even if it has never been logged in.
	// This is counter-intuitive, but Frontend relies on this behavior.
	client := http.Client{}

	t.Run("can logout without cookie", func(t *testing.T) {
		req, _ := http.NewRequest(
			http.MethodPost,
			server.URL+"/api/auth/logout",
			nil,
		)
		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":false,"message":"Successfully logged out.","results":null}`, string(b))
	})
	t.Run("can logout with wrong cookie", func(t *testing.T) {
		req, _ := http.NewRequest(
			http.MethodPost,
			server.URL+"/api/auth/logout",
			nil,
		)
		req.Header.Set("Cookie", "TigerGraphApp=wrong")
		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":false,"message":"Successfully logged out.","results":null}`, string(b))
	})
}

func TestLoginLogout(t *testing.T) {
	// When logout, the session jwt token should be invalidated as well
	// Meaning request that uses the same jwt should be rejected.
	gsqlClient := NewFakeRequestGSQLClient()
	c, err := controller.New(
		controller.ControllerDependencies{
			RequestNewGsqlToken: func(c context.Context, gsqlUsername, gsqlPassword, graphName string, lifetime int64) (string, error) {
				return TestGsqlToken, nil
			},
			GSQLAuthenticator: func(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {
				if creds.AuthType == model.GsqlTokenAuthType && creds.GsqlToken != TestGsqlToken {
					return nil, errors.New("invalid gsql token")
				}
				if creds.Password != "tigergraph" {
					return nil, errors.New("invalid password")
				}

				return &model.UserInfo{
					IsSuperUser: false,
				}, nil
			},
			ExpCheckInterval:              time.Second * 2,
			DaoManager:                    fakeDaoManager{},
			DatabaseManager:               fakeDatabaseManager{},
			CheckAndCreateCategoryDirFunc: CheckAndCreateCategoryDir,
			LoadingJobService:             &fakeLoadingJobService{},
			UpsertSAMLResponse:            func(hash, samlResp string) error { return nil },
			CntlrClient:                   nil,
			RequestGSQLClient:             (&gsqlClient).RequestGSQLClient,
			SchemaSyncer:                  &fakeSchemaSyncer{},
			TokenService:                  &fakeTokenService{},
			InsightsService:               &fakeInsightsService{},
		},
		controller.ConfigFromFile("test.cfg", 1))
	require.NoError(t, err)
	require.NoError(t, c.SetUp())
	r := c.Router()
	// setup server
	server := httptest.NewServer(r)

	// prepare request
	client := http.Client{}

	/////////////////////////////////////
	// First, login to get a jwt token //
	/////////////////////////////////////
	cookieV := login(t, server)

	////////////////////
	// Second, logout //
	////////////////////
	logout(t, server, cookieV)

	//////////////////////////////////
	// Third, request a private API //
	//////////////////////////////////
	req, _ := http.NewRequest(
		http.MethodGet,
		server.URL+"/api/ping2",
		nil,
	)
	req.Header.Set("Cookie", "TigerGraphApp=invalid")

	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	require.Equal(t, `{"error":true,"message":"You are not authorized to use this API.","results":null}`, string(b))
}

func login(t *testing.T, server *httptest.Server) string {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		server.URL+"/api/auth/login",
		strings.NewReader(`{"username":"tigergraph","password":"tigergraph"}`),
	)

	res, _ := client.Do(req)
	cookieV := ""
	for _, cookie := range res.Cookies() {
		if cookie.Name == "TigerGraphApp" {
			cookieV = cookie.Value
			break
		}
	}
	if cookieV == "" {
		require.FailNow(t, "cookie is empty")
	}
	return cookieV
}

func logout(t *testing.T, server *httptest.Server, cookieV string) {
	client := http.Client{}
	req, _ := http.NewRequest(
		http.MethodPost,
		server.URL+"/api/auth/logout",
		nil,
	)
	req.Header.Set("Cookie", "TigerGraphApp="+cookieV)
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	require.Equal(t, `{"error":false,"message":"Successfully logged out.","results":null}`, string(b))
}

func setupServer(t *testing.T) *httptest.Server {
	// setup controller
	cfg, _ := config.New("test.cfg", 1)
	gsqlClient := NewFakeRequestGSQLClient()
	c, err := controller.New(
		controller.ControllerDependencies{
			RequestNewGsqlToken: func(c context.Context, gsqlUsername, gsqlPassword, graphName string, lifetime int64) (string, error) {
				return TestGsqlToken, nil
			},
			GSQLAuthenticator: func(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {

				return &model.UserInfo{
					IsSuperUser: true,
					Privileges: model.Privileges{
						"ldbc_snb": struct {
							Privileges []model.Privilege "json:\"privileges\" binding:\"required\""
						}{
							[]model.Privilege{
								model.READ_SCHEMA,
								model.WRITE_SCHEMA,
								model.READ_LOADINGJOB,
								model.EXECUTE_LOADINGJOB,
								model.WRITE_LOADINGJOB,
								model.READ_QUERY,
								model.WRITE_QUERY,
								model.READ_DATA,
								model.WRITE_DATA,
								model.WRITE_DATASOURCE,
								model.READ_ROLE,
								model.WRITE_ROLE,
								model.READ_USER,
								model.WRITE_USER,
								model.READ_PROXYGROUP,
								model.WRITE_PROXYGROUP,
								model.READ_FILE,
								model.WRITE_FILE,
								model.DROP_GRAPH,
								model.EXPORT_GRAPH,
								model.CLEAR_GRAPHSTORE,
								model.DROP_ALL,
								model.ACCESS_TAG,
							},
						},
						"1": {
							Privileges: []model.Privilege{model.APP_ACCESS_LOG},
						},
					},
				}, nil
			},
			ExpCheckInterval:              time.Second * 2,
			DaoManager:                    fakeDaoManager{},
			DatabaseManager:               fakeDatabaseManager{},
			CheckAndCreateCategoryDirFunc: CheckAndCreateCategoryDir,
			TGFileSystem:                  fs.NewTGFilesystem(cfg),
			CntlrClient:                   nil,
			LoadingJobService:             &fakeLoadingJobService{},
			UpsertSAMLResponse:            func(hash, samlResp string) error { return nil },
			RequestGSQLClient:             (&gsqlClient).RequestGSQLClient,
			SchemaSyncer:                  &fakeSchemaSyncer{},
			TokenService:                  &fakeTokenService{},
			InsightsService:               &fakeInsightsService{},
		},
		controller.ConfigFromFile("test.cfg", 1))
	require.NoError(t, err)
	require.NoError(t, c.SetUp())
	r := c.Router()
	// setup server
	server := httptest.NewServer(r)
	return server
}

func TestCORSForLogout(t *testing.T) {
	server := setupServer(t)
	client := http.Client{}
	cookieV := login(t, server)
	_, _ = client, cookieV
	req, _ := http.NewRequest(
		http.MethodOptions,
		server.URL+"/api/auth/logout",
		nil,
	)
	req.Header.Add("origin", "https://tgcloud-dev.com")
	resp, err := client.Do(req)
	require.NoError(t, err)
	require.Equal(t, http.StatusNoContent, resp.StatusCode)
}
