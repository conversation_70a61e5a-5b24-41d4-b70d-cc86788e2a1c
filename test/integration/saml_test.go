package tests

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/tigergraph/gus/controller"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
)

const (
	TestGsqlToken = "gsql_token"
)

func TestSAMl(t *testing.T) {

	setupServer := func(t *testing.T) *httptest.Server {
		// setup controller
		gsqlClient := NewFakeRequestGSQLClient()
		c, err := controller.New(
			controller.ControllerDependencies{
				RequestNewGsqlToken: func(c context.Context, gsqlUsername, gsqlPassword, graphName string, lifetime int64) (string, error) {
					return TestGsqlToken, nil
				},
				GSQLAuthenticator: func(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error) {
					return &model.UserInfo{
						IsSuperUser: false,
					}, nil
				},
				ExpCheckInterval:              time.Second * 2,
				DaoManager:                    fakeDaoManager{},
				DatabaseManager:               fakeDatabaseManager{},
				CheckAndCreateCategoryDirFunc: CheckAndCreateCategoryDir,
				UpsertSAMLResponse:            func(hash, samlResp string) error { return nil },
				CntlrClient:                   nil,
				LoadingJobService:             &fakeLoadingJobService{},
				RequestGSQLClient:             (&gsqlClient).RequestGSQLClient,
				SchemaSyncer:                  &fakeSchemaSyncer{},
				TokenService:                  &fakeTokenService{},
				InsightsService:               &fakeInsightsService{},
			},
			controller.ConfigFromFile("test.cfg", 1))
		require.NoError(t, err)
		require.NoError(t, c.SetUp())
		r := c.Router()
		// setup server
		server := httptest.NewServer(r)
		return server
	}

	server := setupServer(t)
	client := &http.Client{}
	client.CheckRedirect = func(req *http.Request, via []*http.Request) error { return http.ErrUseLastResponse } // Do not follow redirect

	t.Run("empty request body", func(t *testing.T) {
		req, _ := http.NewRequest(
			http.MethodPost,
			server.URL+"/api/auth/saml/acs",
			strings.NewReader(``),
		)
		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":true,"message":"SAML response doesn't exist.","results":null}`, string(b))
	})

	t.Run("able to access private API after SAML login", func(t *testing.T) {
		form := url.Values{}
		form.Add("SAMLResponse", "x")
		url := server.URL + "/api/auth/saml/acs"
		res, _ := client.PostForm(url, form)

		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":false,"message":"","results":{"name":"","creators":null,"roles":null,"privileges":null,"secrets":null,"isSuperUser":false}}`, string(b))

		ping2(t, client, server, getCookie(t, res))
	})

}
