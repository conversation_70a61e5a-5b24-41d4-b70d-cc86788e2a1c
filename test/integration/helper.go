package tests

import (
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// ping2 is a private ping API for testing
func ping2(t *testing.T, client *http.Client, server *httptest.Server, cookie string) {
	req, _ := http.NewRequest("GET", server.URL+"/api/ping2", nil)
	req.Header.Set("Cookie", "TigerGraphApp="+cookie)
	// make request
	res, _ := client.Do(req)
	b, _ := io.ReadAll(res.Body)
	// assert
	assert.Equal(t, http.StatusOK, res.StatusCode)
	require.Equal(t, `{"error":false,"message":"pong","results":null}`, string(b))
}

func getCookie(t *testing.T, res *http.Response) string {
	cookieV := ""
	for _, cookie := range res.Cookies() {
		if cookie.Name == "TigerGraphApp" {
			cookieV = cookie.Value
			break
		}
	}
	if cookieV == "" {
		require.FailNow(t, "cookie is empty")
	}
	return cookieV
}
