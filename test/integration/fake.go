package tests

import (
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
	"github.com/tigergraph/cqrs/tutopia/common/config"
	"github.com/tigergraph/graphql/gsql/client"
	"github.com/tigergraph/gus/dao/model"
	libconfig "github.com/tigergraph/gus/lib/config"
)

func cfg(t *testing.T) *config.Config {
	cfg, err := config.NewConfigFromFile("test.cfg")
	require.NoError(t, err)
	return cfg
}

// code should run longer than time, otherwise fail
// func longerThan(dur time.Duration, code func()) bool {
// 	c := make(chan struct{})
// 	go func() {
// 		code()
// 		c <- struct{}{}
// 	}()
// 	select {
// 	case <-c:
// 		return false
// 	case <-time.After(dur):
// 		return true
// 	}
// }

type fakeDaoManager struct{}

func (fakeDaoManager) DeleteAll() error                             { return nil }
func (fakeDaoManager) Import(data map[string][]byte) error          { return nil }
func (fakeDaoManager) Export(key string) (map[string][]byte, error) { return nil, nil }
func (fakeDaoManager) IsImporting() (bool, error)                   { return false, nil }
func (fakeDaoManager) SetImport(isImporting bool) error             { return nil }
func (fakeDaoManager) GetAllKeys(key string) ([]string, error)      { return nil, nil }
func (fakeDaoManager) GetLoadingJobLog(graphName, jobName string) (*model.LoadingJobLog, error) {
	return nil, nil
}
func (fakeDaoManager) GetAllLoadingJobLogs(graphName string) ([]model.LoadingJobLog, error) {
	return nil, nil
}
func (fakeDaoManager) CreateLoadingJobLog(graphName, jobName string, log model.LoadingJobLog) error {
	return nil
}
func (fakeDaoManager) UpsertLoadingJobLog(graphName, jobName string, log model.LoadingJobLog) error {
	return nil
}
func (fakeDaoManager) DeleteLoadingJobLog(graphName, jobName string) error   { return nil }
func (fakeDaoManager) DeleteAllLoadingJobLogs(graphName string) error        { return nil }
func (fakeDaoManager) GetSAMLResponse(hash string) (string, error)           { return "", nil }
func (fakeDaoManager) CreateSAMLResponse(hash string, samlResp string) error { return nil }
func (fakeDaoManager) UpsertSAMLResponse(hash string, samlResp string) error { return nil }
func (fakeDaoManager) DeleteSAMLResponse(hash string) error                  { return nil }
func (fakeDaoManager) GetExplorationResult(graphName, eName string) (*model.ExplorationResult, error) {
	return nil, nil
}
func (fakeDaoManager) GetAllExplorationResults(graphName string) ([]model.ExplorationResultPreview, error) {
	return nil, nil
}
func (fakeDaoManager) CreateExplorationResult(graphName, eName string, exploration model.ExplorationResult) error {
	return nil
}
func (fakeDaoManager) UpsertExplorationResult(graphName, eName string, exploration model.ExplorationResult) error {
	return nil
}
func (fakeDaoManager) DeleteExplorationResult(graphName, eName string) error { return nil }
func (fakeDaoManager) DeleteAllExplorationResults(graphName string) error    { return nil }
func (fakeDaoManager) GetGraphStyle(graphName string) (*model.GraphStyle, error) {
	return &model.GraphStyle{}, nil
}
func (fakeDaoManager) CreateGraphStyle(graphName string, style model.GraphStyle, schema model.Schema) error {
	return nil
}
func (fakeDaoManager) UpsertGraphStyle(graphName string, style model.GraphStyle, schema model.Schema) error {
	return nil
}
func (fakeDaoManager) DeleteGraphStyle(graphName string) error { return nil }
func (fakeDaoManager) GetLoadingJobInfo(graphName string) ([]model.LoadingJobInfo, error) {
	return nil, nil
}
func (fakeDaoManager) CreateLoadingJobInfo(graphName string, info []interface{}) error { return nil }
func (fakeDaoManager) UpsertLoadingJobInfo(graphName string, info []interface{}) error { return nil }
func (fakeDaoManager) DeleteLoadingJobInfo(graphName string) error                     { return nil }
func (fakeDaoManager) GetAllQueryDrafts(graphName string) ([]model.QueryDraft, error) {
	return nil, nil
}
func (fakeDaoManager) CreateQueryDraft(graphName, queryName string, draft model.QueryDraft) error {
	return nil
}
func (fakeDaoManager) UpsertQueryDraft(graphName, queryName string, draft model.QueryDraft) error {
	return nil
}
func (fakeDaoManager) DeleteQueryDraft(graphName, queryName string) error { return nil }
func (fakeDaoManager) DeleteAllQueryDraft(graphName string) error         { return nil }
func (fakeDaoManager) GetVisualPattern(graphName, patternName string) (interface{}, error) {
	return nil, nil
}
func (fakeDaoManager) GetAllVisualPatterns(graphName string) ([]interface{}, error) { return nil, nil }
func (fakeDaoManager) CreateVisualPattern(graphName, patternName string, pattern interface{}) error {
	return nil
}
func (fakeDaoManager) UpsertVisualPattern(graphName, patternName string, pattern interface{}) error {
	return nil
}
func (fakeDaoManager) DeleteVisualPattern(graphName, patternName string) error { return nil }
func (fakeDaoManager) DeleteAllVisualPatterns(graphName string) error          { return nil }

func (fakeDaoManager) GetDataSource(graphName, dataSourceType, dataSourceName string) (interface{}, error) {
	return nil, nil
}
func (fakeDaoManager) CreateDataSource(graphName, dataSourceType, dataSourceName string, dataSource interface{}) error {
	return nil
}
func (fakeDaoManager) DeleteDataSource(graphName, dataSourceType, dataSourceName string) error {
	return nil
}
func (fakeDaoManager) GetAllDataSourceNames(graphName, dataSourceType string) ([]string, error) {
	return nil, nil
}
func (fakeDaoManager) DeleteAllDataSources(graphName string) error { return nil }

func (fakeDaoManager) UpsertBackupSchedule(schedule model.BackupSchedule) error { return nil }
func (fakeDaoManager) GetBackupSchedule() (*model.BackupSchedule, error)        { return nil, nil }
func (fakeDaoManager) UpsertPassword(password string, authToken string) error   { return nil }
func (fakeDaoManager) IsGBARInProgress() (bool, error)                          { return false, nil }
func (fakeDaoManager) SetGBARInProgress(inProgress bool) error                  { return nil }
func (fakeDaoManager) DeleteScheduledBackupStatus(backupName, timestamp string) error {
	return nil
}
func (fakeDaoManager) GetPassword(authToken string) (string, error) { return "", nil }
func (fakeDaoManager) GetAllScheduledBackupStatus() ([]model.ScheduledBackupStatus, error) {
	return nil, nil
}
func (fakeDaoManager) CreateScheduledBackupStatus(backupStatus model.ScheduledBackupStatus) error {
	return nil
}
func (fakeDaoManager) GetInsightsAppPermission(appId string) (*model.AppPermission, error) {
	return nil, nil
}
func (fakeDaoManager) UpsertInsightsAppPermission(appId string, appPermission model.AppPermission) error {
	return nil
}
func (fakeDaoManager) GetAllInsightsAppPermission() (map[string]*model.AppPermission, error) {
	return nil, nil
}

// Implement DatabaseManager
type fakeDatabaseManager struct{}

func (fakeDatabaseManager) SetUp() error                                 { return nil }
func (fakeDatabaseManager) TearDown()                                    {}
func (fakeDatabaseManager) Get(key string) ([]byte, error)               { return nil, nil }
func (fakeDatabaseManager) GetAll(key string) ([][]byte, error)          { return nil, nil }
func (fakeDatabaseManager) GetAllKeys(key string) ([]string, error)      { return nil, nil }
func (fakeDatabaseManager) Create(key string, value []byte) error        { return nil }
func (fakeDatabaseManager) Upsert(key string, value []byte) error        { return nil }
func (fakeDatabaseManager) Delete(key string) error                      { return nil }
func (fakeDatabaseManager) DeleteAll(key string) error                   { return nil }
func (fakeDatabaseManager) Import(data map[string][]byte) error          { return nil }
func (fakeDatabaseManager) Export(key string) (map[string][]byte, error) { return nil, nil }

func CheckAndCreateCategoryDir(config *libconfig.Config, cat string, userName string) (bool, error) {
	return false, nil
}

type fakeLoadingJobService struct{}

func (f *fakeLoadingJobService) SetUp() error { return nil }
func (f *fakeLoadingJobService) TearDown()    {}
func (f *fakeLoadingJobService) StartJobs(ctx *gin.Context, graphName string, jobInfo []model.StartJobInfo) model.LoadingJobCommandResult {
	return nil
}
func (f *fakeLoadingJobService) PauseJobs(ctx *gin.Context, graphName string, jobNames []string) model.LoadingJobCommandResult {
	return nil
}
func (f *fakeLoadingJobService) StopJobs(ctx *gin.Context, graphName string, jobNames []string) model.LoadingJobCommandResult {
	return nil
}
func (f *fakeLoadingJobService) GetJobsProgress(ctx *gin.Context, graphName string, jobNames []string) model.LoadingJobCommandResult {
	return nil
}
func (f *fakeLoadingJobService) ResumeJobs(ctx *gin.Context, graphName string, jobNames []string) model.LoadingJobCommandResult {
	return nil
}

// Implement SchemaSyncer
type fakeSchemaSyncer struct{}

func (f *fakeSchemaSyncer) Schema(host, authorizationToken, graphName string, skipSSL bool) (*client.GsqlSchemaResponse, error) {
	return nil, nil
}

type fakeTokenService struct{}

func (f *fakeTokenService) Create(ctx *gin.Context, creds *model.UserCredentials) (*model.APIToken, error) {
	return nil, nil
}

func (f *fakeTokenService) Get(ctx *gin.Context, token string) (*model.APIToken, error) {
	return nil, nil
}

func (f *fakeTokenService) Delete(ctx *gin.Context, token string) error {
	return nil
}

func (f *fakeTokenService) List(ctx *gin.Context) ([]*model.APIToken, error) {
	return nil, nil
}

func (f *fakeTokenService) Parse(ctx *gin.Context, token string) (*model.UserCredentials, error) {
	return nil, nil
}

type fakeInsightsService struct{}

func (f *fakeInsightsService) GetToken(ctx *gin.Context, appId string) (*model.APIToken, error) {
	return nil, nil
}

func (f *fakeInsightsService) CreateToken(ctx *gin.Context, appId string, creds *model.UserCredentials) (*model.APIToken, error) {
	return nil, nil
}

func (f *fakeInsightsService) DeleteToken(ctx *gin.Context, appId string) error {
	return nil
}
