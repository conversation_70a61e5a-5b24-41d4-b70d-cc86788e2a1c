package tests

import (
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestLoadingJobStart(t *testing.T) {
	server := setupServer(t)
	client := http.Client{}
	cookieV := login(t, server)
	_, _ = client, cookieV

	t.Run("streaming key no exist", func(t *testing.T) {

		payload := `[{"name":"load_job","dataSources":[{"filename":"MyDataSource","name":"file","path":"aaa.csv"}]}]`
		req, _ := http.NewRequest(
			http.MethodPost,
			server.URL+"/api/loading-jobs/ldbc_snb/loading/start",
			strings.NewReader(payload),
		)
		req.Header.Set("Cookie", "TigerGraphApp="+cookieV)

		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":true,"message":"Invalid payload.","results":null}`, string(b))
	})

	t.Run("streaming key false", func(t *testing.T) {
		payload := `[{"streaming":false,"name":"load_job","dataSources":[{"filename":"MyDataSource","name":"file","path":"aaa.csv"}]}]`
		req, _ := http.NewRequest(
			http.MethodPost,
			server.URL+"/api/loading-jobs/ldbc_snb/loading/start",
			strings.NewReader(payload),
		)
		req.Header.Set("Cookie", "TigerGraphApp="+cookieV)

		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":false,"message":"","results":null}`, string(b))
	})

	t.Run("streaming key true", func(t *testing.T) {
		payload := `[{"streaming":true,"name":"load_job","dataSources":[{"filename":"MyDataSource","name":"file","path":"aaa.csv"}]}]`
		req, _ := http.NewRequest(
			http.MethodPost,
			server.URL+"/api/loading-jobs/ldbc_snb/loading/start",
			strings.NewReader(payload),
		)
		req.Header.Set("Cookie", "TigerGraphApp="+cookieV)

		res, _ := client.Do(req)
		b, _ := io.ReadAll(res.Body)
		require.Equal(t, `{"error":false,"message":"","results":null}`, string(b))
	})

}
