package tests

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/handler/query"
	libconfig "github.com/tigergraph/gus/lib/config"
)

func TestQueryAdd(t *testing.T) {
	server := setupServer(t)
	client := http.Client{}
	cookieV := login(t, server)

	testCases := []struct {
		name string
		args []query.QueryInfo
		want string
	}{
		{
			name: "query 1 is invalid, query 2 is valid",
			args: []query.QueryInfo{
				{
					Name:     "query1",
					NeedDrop: true,
				},
				{
					Name:     "query2",
					NeedDrop: true,
				},
			},
			want: `{"error":false,"message":"","results":{"failed":[{"name":"query1","errorMessage":"Failed to add query draft query1"}],"success":[{"name":"query2","errorMessage":""}]}}`,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			a, _ := json.Marshal(testCase.args)
			req, _ := http.NewRequest(
				http.MethodPost,
				server.URL+"/api/queries/ldbc_snb/gsql/add",
				bytes.NewReader(a),
			)
			req.Header.Set("Cookie", "TigerGraphApp="+cookieV)
			req.Header.Set("Content-Type", "application/json")

			res, _ := client.Do(req)
			b, _ := io.ReadAll(res.Body)
			require.Equal(t, testCase.want, string(b))
		})
	}
}

func (fakeDaoManager) GetQueryDraft(graphName, queryName string) (*model.QueryDraft, error) {
	switch queryName {
	case "query1":
		return &model.QueryDraft{
			Name: "query1",
			Code: `CREATE QUERY query1(/* Parameters here */) FOR GRAPH graph_name { 
					 /* Write query logic here */ 
					 PRINT "query1 works!";；
				   }`,
			GraphUpdate: false,
		}, nil
	case "query2":
		return &model.QueryDraft{
			Name: "query2",
			Code: `CREATE QUERY query2(/* Parameters here */) FOR GRAPH graph_name { 
					 /* Write query logic here */ 
					 PRINT "query2 works! "; 
			  	   }`,
			GraphUpdate: false,
		}, nil
	default:
		return nil, errors.New("no corresponding query draft")
	}
}

type fakeRequestGSQLClient struct {
	query2Existed bool
}

func NewFakeRequestGSQLClient() fakeRequestGSQLClient {
	return fakeRequestGSQLClient{true}
}

func (f *fakeRequestGSQLClient) RequestGSQLClient(_ context.Context, cfg *libconfig.Config, graphName string, creds *model.UserCredentials, command string) ([]byte, error) {
	if strings.HasPrefix(command, "DROP QUERY query1") {
		return []byte("Failed to delete query query1"), errors.New("failed to delete query query1")
	} else if strings.HasPrefix(command, "DROP QUERY query2") {
		f.query2Existed = false
		return nil, nil
	} else if strings.HasPrefix(command, "CREATE QUERY query1") {
		return []byte("Failed to add query draft query1"), errors.New("failed to add query draft query1")
	} else if strings.HasPrefix(command, "CREATE QUERY query2") {
		if f.query2Existed {
			return []byte("Failed to add query draft query2"), errors.New("failed to add query draft query2")
		} else {
			return nil, nil
		}
	} else {
		return nil, nil
	}
}
