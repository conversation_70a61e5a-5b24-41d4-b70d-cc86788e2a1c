import pytest

def pytest_addoption(parser):
    parser.addoption(
        "--skip-tests", action="store", default="", help="Comma-separated list of test names to skip"
    )

def pytest_collection_modifyitems(config, items):
    skip_tests = config.getoption("--skip-tests").split(',')
    skip_marker = pytest.mark.skip(reason="Skipped by --skip-tests option")

    for item in items:
        if item.name in skip_tests:
            item.add_marker(skip_marker)
