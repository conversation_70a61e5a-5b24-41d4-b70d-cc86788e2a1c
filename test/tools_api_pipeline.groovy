#!/bin/groovy
import groovy.time.*
import groovy.json.*

wrap([$class: 'BuildUser']) {
    if (user_full_name == "" || user_full_name == null) {
        user_email = "${env.BUILD_USER_EMAIL}"
                     str_split = user_email.split('@')
                                 user_full_name = str_split[0]
    }
    currentBuild.displayName = "#${currentBuild.number} ${user_full_name} ${TEST_ENV} ${MARKER}"
}

timestamps {
    wrap([$class: 'AnsiColorBuildWrapper']) {
        STAGE = load("test/scripts/stage.groovy")
        try {
            if ("${MARKER}" == "show_help") {
                sh "bash test/scripts/show_help.sh"
            } else {
                STAGE.init()
                STAGE.e2e_test()
                STAGE.upload_result()
                STAGE.fail_on_error("normal")
                STAGE.notice()
                println "TigerGraph Tools API Test Finished."
            }
        } catch (err) {
            STAGE.upload_result()
            STAGE.fail_on_error("tools_api_failed")
            STAGE.notice()
            println "TigerGraph Tools API Test Failed"
            throw err
        } finally {
            sh 'sudo chmod -R o+xw test/alluredir/tools'
            allure includeProperties: false, jdk: '', report: 'test/alluredir/tools/html', results: [[path: 'test/alluredir/tools']]
        }
    }
}