[pytest]
log_format = %(asctime)s %(levelname)s %(message)s
log_date_format = %Y-%m-%d %H:%M:%S

log_cli = True
log_cli_level=DEBUG
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format=%Y-%m-%d %H:%M:%S

log_file = data/log/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_file_date_format=%Y-%m-%d %H:%M:%S

# Display console output, disable cacheprovider, and have the ipdb debugger replace pdb:
addopts = -rA -v -s --showlocals --window-size=1920,1080 --alluredir alluredir/tools --junitxml=junit/test-results.xml -p no:cacheprovider --pdbcls=IPython.terminal.debugger:TerminalPdb --chromium-arg="w3c=true"

# Ignore warnings such as DeprecationWarning and PytestUnknownMarkWarning
filterwarnings =
    ignore::pytest.PytestWarning
    ignore:.*U.*mode is deprecated:DeprecationWarning

junit_family = xunit2

# Set pytest discovery rules:
# (Most of the rules here are similar to the default rules.)
# (unittest.TestCase rules override the rules here for classes and functions.)
python_files = test_*.py *_test.py *_tests.py *_suite.py
python_classes = Test* *Test* *Test *Tests *Suite
python_functions = test_*
markers =
    high: mark a test as a high priority test case.
    medium: mark a test as a medium priority test case.
    low: mark a test as low priority test case.

    smoke: mark a test as a smoke test
    benchmark: mark a test as a benchmark test
    regression: mark a test as a regression test

    cloud: mark a test as a cloud related case
    tools: mark a test as a application related case
    insights: mark a test as a insights related test case