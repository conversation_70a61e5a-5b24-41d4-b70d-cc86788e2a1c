name: deploy gus to cloud
env:
  AWS_S3_BUCKET: tigergraph-build-artifacts
  AWS_REGION: us-west-1

on:
  workflow_dispatch:
    inputs:
      profile:
        required: true
        type: choice
        description: switch profile(which env to deploy)
        options:
          - dev
          - staging
          - prod

jobs:
  update_shorline_deployment:
    runs-on: ubuntu-latest
    name: Update deployment
    environment: ${{inputs.profile}}
    steps:
      - uses: actions/checkout@v3
      - name: Upload deployment file to S3
        uses: jakejarvis/s3-sync-action@master
        env:
          AWS_S3_BUCKET: ${{ env.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.AWS_REGION }}
          SOURCE_DIR: deployment
          DEST_DIR: ${{ github.repository }}/release/cloud-deployment
