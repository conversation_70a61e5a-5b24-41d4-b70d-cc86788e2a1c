name: deploy gus

on:
  workflow_dispatch:
    inputs:
      instance:
        required: true
        type: choice
        description: Select deploy instance
        options:
          - app-dev
          - nala
          - on-premise-dev

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          cache: false
          go-version: "1.24.4"

      - name: Set up GitHub access
        run: make access-github

      - name: Run lint
        uses: golangci/golangci-lint-action@v2
        with:
          version: v1.45
          args: -c .golangci.yaml

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          cache: false
          go-version: "1.24.4"

      - name: Cache Go modules
        uses: actions/cache@v2
        env:
          cache-name: go-modules
        with:
          path: ~/go
          key: ${{ runner.os }}-${{ env.cache-name }}-${{ hashFiles('go.sum') }}

      - name: Set up GitHub access
        run: make access-github

      - name: Build binary
        run: make build

      - name: Generate third-party license report
        run: make report-license

      - name: Upload build artifacts
        uses: actions/upload-artifact@v2
        with:
          name: build
          path: release

  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          cache: false
          go-version: "1.24.4"

      - name: Run test
        run: make test

      - name: Upload coverage report
        uses: actions/upload-artifact@v2
        with:
          name: coverage
          path: coverage

  deploy-gus-app-dev:
    name: Deploy GUS to app-dev
    if: github.event.inputs.instance == 'app-dev'
    needs: [lint, build, test]
    runs-on: [self-hosted, app-dev]
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build
          path: release

      - name: upgrade GUS
        shell: bash
        run: |
          export APPROOT='gadmin config get System.AppRoot --file /home/<USER>/.tg.cfg'    
          gadmin stop gui -y
          cp release/tg_app_guid $($APPROOT)/bin/gui/tg_app_guid
          chmod a+x $($APPROOT)/bin/gui/tg_app_guid
          gadmin start gui

      - name: Get commit SHA
        id: get-commit
        shell: bash
        run: |
          sha=${{ github.sha }}
          if [ "${{ github.event_name }}" == "pull_request" ]; then sha=${{ github.event.pull_request.head.sha }}; fi
          echo "::set-output name=sha::$(echo $sha)"

      - name: Slack Message
        run: |-
          curl -X POST ${{secrets.APP_DEPLOYMENT_SLACK_APP_DEPLOYMENT_WEBHOOK_URL}} -H 'Content-type:application/json' \
          --data "{'text':'Hi all, gus git version ${{ steps.get-commit.outputs.sha }} has been successfully deployed to  https://tgcloud-dev.com/app/tools/GraphStudio/e61b60a4-1526-4506-8a59-5bda7a7b1c32'}'}"

  deploy-gus-nala:
    name: Deploy GUS to nala
    if: github.event.inputs.instance == 'nala'
    needs: [lint, build, test]
    runs-on: [self-hosted, nala]
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build
          path: release

      - name: upgrade GUS
        shell: bash
        run: |
          export APPROOT='gadmin config get System.AppRoot --file /home/<USER>/.tg.cfg'    
          gadmin stop gui -y
          cp release/tg_app_guid $($APPROOT)/bin/gui/tg_app_guid
          chmod a+x $($APPROOT)/bin/gui/tg_app_guid
          gadmin start gui

      - name: Get commit SHA
        id: get-commit
        shell: bash
        run: |
          sha=${{ github.sha }}
          if [ "${{ github.event_name }}" == "pull_request" ]; then sha=${{ github.event.pull_request.head.sha }}; fi
          echo "::set-output name=sha::$(echo $sha)"

      - name: Slack Message
        run: |-
          curl -X POST ${{secrets.APP_DEPLOYMENT_SLACK_APP_DEPLOYMENT_WEBHOOK_URL}} -H 'Content-type:application/json' \
          --data "{'text':'Hi all, gus git version ${{ steps.get-commit.outputs.sha }} has been successfully deployed to https://nala.i.tgcloud-dev.com'}"

  deploy-on-premise-dev:
    name: Deploy GUS to on-premise dev
    if: github.event.inputs.instance == 'on-premise-dev'
    needs: [lint, build, test]
    runs-on: [self-hosted, on-premise-dev]
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build
          path: release

      - name: upgrade GUS
        shell: bash
        run: |
          export APPROOT='gadmin config get System.AppRoot --file /home/<USER>/.tg.cfg'    
          gadmin stop gui -y
          cp release/tg_app_guid $($APPROOT)/bin/gui/tg_app_guid
          chmod a+x $($APPROOT)/bin/gui/tg_app_guid
          gadmin start gui

      - name: Get commit SHA
        id: get-commit
        shell: bash
        run: |
          sha=${{ github.sha }}
          if [ "${{ github.event_name }}" == "pull_request" ]; then sha=${{ github.event.pull_request.head.sha }}; fi
          echo "::set-output name=sha::$(echo $sha)"

      - name: Slack Message
        run: |-
          curl -X POST ${{secrets.APP_DEPLOYMENT_SLACK_APP_DEPLOYMENT_WEBHOOK_URL}} -H 'Content-type:application/json' \
          --data "{'text':'Hi all, gus git version ${{ steps.get-commit.outputs.sha }} has been successfully deployed to http://35.222.150.148:14240'}"
