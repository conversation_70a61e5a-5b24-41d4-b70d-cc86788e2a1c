name: GUS build

env:
  AWS_S3_BUCKET: tigergraph-build-artifacts
  AWS_REGION: us-west-1
  CICD_GITHUB_TOKEN: ${{ secrets.CICD_GITHUB_TOKEN }}
  
on:
  push:
    branches:
      - "master"
      - "tg_[0-9]*.[0-9]*.[0-9]*_dev"
      - "tg_[0-9]*.[0-9]*_dev"
  pull_request:
    branches:
      - "**"
  workflow_dispatch:

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up GitHub access
        run: make access-github

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          cache: false
          go-version: "1.24.4"
          check-latest: true

      - name: Run lint
        uses: golangci/golangci-lint-action@v4
        with:
          version: v1.64.2
          args: -c .golangci.yaml
          skip-cache: true

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up GitHub access
        run: make access-github

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.4"
          check-latest: true

      - name: Get commit SHA
        id: get-commit
        shell: bash
        run: |
          sha=${{ github.sha }}
          if [ "${{ github.event_name }}" == "pull_request" ]; then sha=${{ github.event.pull_request.head.sha }}; fi
          echo "sha=$sha" >> $GITHUB_OUTPUT

      - name: Build binary
        env:
          GIT_COMMIT: ${{ steps.get-commit.outputs.sha }}
        run: make build

      - name: Generate third-party license report
        run: make report-license

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: release

  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up GitHub access
        run: make access-github

      - name: Set up Go 1.24
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.4"
          check-latest: true

      - name: Run test
        run: make test

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage
          path: coverage

  publish:
    name: Publish artifacts
    needs: [lint, build, test]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.CICD_GITHUB_TOKEN }}

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build
          path: release

      - name: Download coverage report
        uses: actions/download-artifact@v4
        with:
          name: coverage
          path: coverage

      - name: Get commit SHA
        id: get-commit
        shell: bash
        run: |
          sha=${{ github.sha }}
          if [ "${{ github.event_name }}" == "pull_request" ]; then sha=${{ github.event.pull_request.head.sha }}; fi
          echo "sha=$sha" >> $GITHUB_OUTPUT

      - name: Upload build artifacts to S3
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --follow-symlinks --delete
        env:
          AWS_S3_BUCKET: ${{ env.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.AWS_REGION }}
          SOURCE_DIR: "release"
          DEST_DIR: ${{ github.repository }}/${{ steps.get-commit.outputs.sha }}/release

      - name: Upload coverage report to S3
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --follow-symlinks --delete
        env:
          AWS_S3_BUCKET: ${{ env.AWS_S3_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_S3_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_S3_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ env.AWS_REGION }}
          SOURCE_DIR: "coverage"
          DEST_DIR: ${{ github.repository }}/${{ steps.get-commit.outputs.sha }}/coverage

      - name: Calculate Checksum
        if: github.event_name == 'pull_request'
        run: |
          echo -e '**Checksum**\n```' | tee comment.txt
          sha256sum release/* | tee -a comment.txt
          echo -e '```' | tee -a comment.txt

      - name: Generate PR comment
        if: github.event_name == 'pull_request'
        run: >-
          python3 scripts/pr-comment.py
          --s3_bucket ${{ env.AWS_S3_BUCKET }}
          --region ${{ env.AWS_REGION }}
          --repo ${{ github.repository }}
          --sha ${{ steps.get-commit.outputs.sha }}
          --files_dir release
          --coverage $(go tool cover -func coverage/coverage.prof | awk 'END{print $3}')
          --coverage_file coverage.html | tee -a comment.txt

      - name: Comment on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            github.rest.pulls.createReview({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.payload.pull_request.number,
              event: 'COMMENT',
              body: fs.readFileSync('comment.txt', 'utf8')
            });
