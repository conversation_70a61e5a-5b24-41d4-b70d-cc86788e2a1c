package controller

import (
	"crypto/rand"
	"math/big"
	"os"
	"time"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	"github.com/tigergraph/gus/service/cron"
)

const MaxCronWaitMilloSecond = 1000

func createTempDirCleanJob(cfg *config.Config) cron.Job {
	task := func() {
		path := cfg.GetTempDirPath()
		retentionDay := cfg.GetTempFileMaxDuration()
		ts := time.Now().AddDate(0, 0, -int(retentionDay))

		log.Infof("Running clean job under tmp dir %s with retention day %d", path, retentionDay)
		if err := fs.RemoveOldFiles(path, ts); err != nil && !os.IsNotExist(err) {
			log.Warnf("Failed to clean up tmp dir %s: %v", path, err)
		}
		log.Infof("Finished running clean job under tmp dir %s with retention day %d", path, retentionDay)
	}
	return cron.NewJob("tmp_dir_cleaning", "@hourly", task)
}

func purgeExpiredSession(authenticateService interfaces.AuthenticationService) cron.Job {
	task := func() {
		n, _ := rand.Int(rand.Reader, big.NewInt(MaxCronWaitMilloSecond))
		time.Sleep(time.Millisecond * time.Duration(n.Int64()))
		err := authenticateService.Purge()
		if err != nil {
			log.Warnf("Failed to purge expired session: %v", err)
		}
		log.Infof("Finished running purge expired session")
	}
	return cron.NewJob("purgeExpiredSession", "@hourly", task)
}
