package controller

import (
	"os"
	"time"

	"github.com/tigergraph/gotools/log"
	"github.com/tigergraph/gus/lib/config"
	"github.com/tigergraph/gus/lib/fs"
	"github.com/tigergraph/gus/service/cron"
)

const MaxCronWaitMilloSecond = 1000

func createTempDirCleanJob(cfg *config.Config) cron.Job {
	task := func() {
		path := cfg.GetTempDirPath()
		retentionDay := cfg.GetTempFileMaxDuration()
		ts := time.Now().AddDate(0, 0, -int(retentionDay))

		log.Infof("Running clean job under tmp dir %s with retention day %d", path, retentionDay)
		if err := fs.RemoveOldFiles(path, ts); err != nil && !os.IsNotExist(err) {
			log.Warnf("Failed to clean up tmp dir %s: %v", path, err)
		}
		log.Infof("Finished running clean job under tmp dir %s with retention day %d", path, retentionDay)
	}
	return cron.NewJob("tmp_dir_cleaning", "@hourly", task)
}
