package test

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/tigergraph/gus/controller/interfaces"
	"github.com/tigergraph/gus/lib/jwt"
)

func CorrectnessTestAuthenticationService(t *testing.T, auth interfaces.AuthenticationService) {
	claims1 := jwt.Claims{
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Unix(),
		},
	}
	s, err := auth.Register(context.Background(), claims1)
	require.NoError(t, err)

	claims2, err := auth.Authenticate(context.Background(), s)
	require.NoError(t, err)

	require.Equal(t, claims1, claims2)

	s, err = auth.Register(context.Background(), jwt.Claims{
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Unix() - 1,
		},
	})
	require.NoError(t, err)

	_, err = auth.Authenticate(context.Background(), s)
	require.Regexp(t, `Session is expired`, err.Error())
	err = auth.Purge()
	require.NoError(t, err)
	_, err = auth.Authenticate(context.Background(), s)
	require.Regexp(t, `Session is invalid`, err.Error())

}

func ConcurrentTestAuthenticationService(t *testing.T, auth interfaces.AuthenticationService) {
	wg := sync.WaitGroup{}
	for i := 0; i < 8192; i++ {
		wg.Add(1)
		go func(ith int) {
			token, err := auth.Register(context.Background(), jwt.Claims{
				StandardClaims: jwt.StandardClaims{
					ExpiresAt: time.Now().Add(time.Minute).Unix(),
				},
			})
			require.NoError(t, err, ith)
			err = auth.Revoke(token)
			require.NoError(t, err, ith)
			wg.Done()
		}(i)
	}
	wg.Wait()
}
