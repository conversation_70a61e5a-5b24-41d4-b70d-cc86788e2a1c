package interfaces

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/tigergraph/cqrs/tutopia/common/pb"

	"github.com/tigergraph/gus/dao/model"
	"github.com/tigergraph/gus/lib/config"
)

type DaoManager interface {
	DeleteAll() error
	Import(data map[string][]byte) error
	Export(key string) (map[string][]byte, error)
	IsImporting() (bool, error)
	SetImport(isImporting bool) error
	GetAllKeys(key string) ([]string, error)
	GetLoadingJobLog(graphName, jobName string) (*model.LoadingJobLog, error)
	GetAllLoadingJobLogs(graphName string) ([]model.LoadingJobLog, error)
	CreateLoadingJobLog(graphName, jobName string, log model.LoadingJobLog) error
	UpsertLoadingJobLog(graphName, jobName string, log model.LoadingJobLog) error
	DeleteLoadingJobLog(graphName, jobName string) error
	DeleteAllLoadingJobLogs(graphName string) error
	GetSAMLResponse(hash string) (string, error)
	CreateSAMLResponse(hash string, samlResp string) error
	DeleteSAMLResponse(hash string) error
	GetExplorationResult(graphName, eName string) (*model.ExplorationResult, error)
	GetAllExplorationResults(graphName string) ([]model.ExplorationResultPreview, error)
	CreateExplorationResult(graphName, eName string, exploration model.ExplorationResult) error
	UpsertExplorationResult(graphName, eName string, exploration model.ExplorationResult) error
	DeleteExplorationResult(graphName, eName string) error
	DeleteAllExplorationResults(graphName string) error
	GetGraphStyle(graphName string) (*model.GraphStyle, error)
	CreateGraphStyle(graphName string, style model.GraphStyle, schema model.Schema) error
	UpsertGraphStyle(graphName string, style model.GraphStyle, schema model.Schema) error
	DeleteGraphStyle(graphName string) error
	GetLoadingJobInfo(graphName string) ([]model.LoadingJobInfo, error)
	CreateLoadingJobInfo(graphName string, info []interface{}) error
	UpsertLoadingJobInfo(graphName string, info []interface{}) error
	DeleteLoadingJobInfo(graphName string) error
	GetQueryDraft(graphName, queryName string) (*model.QueryDraft, error)
	GetAllQueryDrafts(graphName string) ([]model.QueryDraft, error)
	CreateQueryDraft(graphName, queryName string, draft model.QueryDraft) error
	UpsertQueryDraft(graphName, queryName string, draft model.QueryDraft) error
	DeleteQueryDraft(graphName, queryName string) error
	DeleteAllQueryDraft(graphName string) error
	GetVisualPattern(graphName, patternName string) (interface{}, error)
	GetAllVisualPatterns(graphName string) ([]interface{}, error)
	CreateVisualPattern(graphName, patternName string, pattern interface{}) error
	UpsertVisualPattern(graphName, patternName string, pattern interface{}) error
	DeleteVisualPattern(graphName, patternName string) error
	DeleteAllVisualPatterns(graphName string) error
	GetDataSource(graphName, dataSourceType, dataSourceName string) (interface{}, error)
	CreateDataSource(graphName, dataSourceType, dataSourceName string, dataSource interface{}) error
	DeleteDataSource(graphName, dataSourceType, dataSourceName string) error
	GetAllDataSourceNames(graphName, dataSourceType string) ([]string, error)
	UpsertDataSourceDataset(graphName, dataSourceType, dataSourceName string, dataSourceUri string, dataset model.DataSourceDataset) error
	DeleteDataSourceDataset(graphName, dataSourceType, dataSourceName string, dataSourceUri string) error
	GetDataSourceDataset(graphName, dataSourceType, dataSourceName string, dataSourceUri string) (*model.DataSourceDataset, error)
	GetDataSourceTypeUris(graphName, dataSourceType string) (map[string][]string, error)
	GetDataSourceUris(graphName, dataSourceType string, dataSourceName string) ([]string, error)
	DeleteAllDataSources(graphName string) error
	UpsertBackupSchedule(schedule model.BackupSchedule) error
	GetBackupSchedule() (*model.BackupSchedule, error)
	UpsertPassword(password string, authToken string) error
	IsGBARInProgress() (bool, error)
	SetGBARInProgress(inProgress bool) error
	DeleteScheduledBackupStatus(backupName, timestamp string) error
	GetPassword(authToken string) (string, error)
	GetAllScheduledBackupStatus() ([]model.ScheduledBackupStatus, error)
	CreateScheduledBackupStatus(backupStatus model.ScheduledBackupStatus) error
	GetInsightsAppPermission(appId string) (*model.AppPermission, error)
	UpsertInsightsAppPermission(appId string, appPermission model.AppPermission) error
	GetAllInsightsAppPermission() (map[string]*model.AppPermission, error)
	GetRunningHealthChecks() ([]model.RunningHealthCheck, error)
	SaveRunningHealthChecks(healthChecks []model.RunningHealthCheck) error
	DeleteRunningHealthChecks() error
	GetInstalledQueryResultCache(graphName, queryName string, queryParams interface{}) (*model.QueryResultCache, error)
	GetInterpretQueryResultCache(query string, queryParams interface{}) (*model.QueryResultCache, error)
	UpsertInstalledQueryResultCache(graphName, queryName string, queryParams interface{}, queryCache model.QueryResultCache) error
	UpsertInterpretQueryResultCache(query string, queryParams interface{}, queryCache model.QueryResultCache) error
	DeleteInstalledQueryResultCache(graphName, queryName string, queryParams interface{}) error
	DeleteInterpretQueryResultCache(query string, queryParams interface{}) error
	UpsertUserPreference(userID string, preferences map[string]interface{}) error
	GetUserPreference(userID string) (map[string]interface{}, error)
	DeleteUserPreference(userID string) error
}

type UpsertSAMLResponse func(hash string, samlResp string) error

type DatabaseManager interface {
	SetUp() error
	TearDown()
	Get(key string) ([]byte, error)
	GetAll(key string) ([][]byte, error)
	GetAllKeys(key string) ([]string, error)
	Create(key string, value []byte) error
	Upsert(key string, value []byte) error
	Delete(key string) error
	DeleteAll(key string) error
	Import(data map[string][]byte) error
	Export(key string) (map[string][]byte, error)
}

type LifeCycle interface {
	// SetUp is the preparation step before run.
	// Construction logic of singleton fields should go here.
	SetUp() error

	// TearDown is the cleanup step.
	// Logic to free up resources should go here.
	TearDown()
}

// Runnable is an interface for long running instance.
type Runnable interface {
	LifeCycle

	// Run is the long running step.
	// It is executed in a goroutine by the Runner.
	// The context is used as a signal to break blocking logic.
	Run(ctx context.Context) error
}

type CheckAndCreateCategoryDirFunc func(config *config.Config, cat string, userName string) (bool, error)

type TGFileSystem interface {
	Read(cntlrClient pb.ControllerClient, path string, offset, length int64, hostID string) ([]byte, error)
}
type LoadingJobService interface {
	LifeCycle

	StartJobs(ctx *gin.Context, graphName string, jobInfo []model.StartJobInfo) model.LoadingJobCommandResult
	PauseJobs(ctx *gin.Context, graphName string, jobNames []string) model.LoadingJobCommandResult
	StopJobs(ctx *gin.Context, graphName string, jobNames []string) model.LoadingJobCommandResult
	GetJobsProgress(ctx *gin.Context, graphName string, jobNames []string) model.LoadingJobCommandResult
	ResumeJobs(ctx *gin.Context, graphName string, jobNames []string) model.LoadingJobCommandResult
}

type GSQLAuthenticator func(c context.Context, cfg *config.Config, creds *model.UserCredentials, userLogin bool) (*model.UserInfo, error)
type RequestNewGsqlToken func(c context.Context, gsqlUsername, gsqlPassword, graphName string, lifetime int64) (string, error)
type RequestGSQLClient func(c context.Context, cfg *config.Config, graphName string, creds *model.UserCredentials, command string) ([]byte, error)

type GSQLHTTPClient interface {
	Request(c *gin.Context, creds *model.UserCredentials, request *model.Request, response interface{}) error
}

type SchemaService interface {
	Get(ctx *gin.Context, creds *model.UserCredentials, graph string) (*model.GetSchemaResponse, error)
}

type APITokenService interface {
	Create(ctx *gin.Context, creds *model.UserCredentials) (*model.APIToken, error)
	Get(ctx *gin.Context, token string) (*model.APIToken, error)
	Delete(ctx *gin.Context, token string) error
	List(ctx *gin.Context) ([]*model.APIToken, error)
	Parse(ctx *gin.Context, token string) (*model.UserCredentials, error)
}

type InsightsService interface {
	GetToken(ctx *gin.Context, appId string) (*model.APIToken, error)
	CreateToken(ctx *gin.Context, appId string, creds *model.UserCredentials) (*model.APIToken, error)
	DeleteToken(ctx *gin.Context, appId string) error
}

type AuthMiddleware interface {
	Allow(c *gin.Context) bool
	AuthUser(c *gin.Context) (u *model.UserInfoWithCredentials, err error)
	GetSessionID(c *gin.Context) string
}

type UDFService interface {
	GetUDF(ctx *gin.Context, creds *model.UserCredentials, filename string) (string, error)
	SetUDF(ctx *gin.Context, creds *model.UserCredentials, filename string, udf string) error
}
